#!/usr/bin/env python3
"""
Simple test script for intent recognition service
"""

import asyncio
import json
from src.services.intent_recognition import IntentRecognitionService
from src.services.entity_extraction import EntityExtractionService
from src.models.schemas import IntentRecognitionRequest


async def test_intent_recognition():
    """Test intent recognition with sample commands"""
    
    print("🧠 Testing Intent Recognition Service")
    print("=" * 50)
    
    try:
        # Initialize services
        intent_service = IntentRecognitionService()
        entity_service = EntityExtractionService()
        
        # Test commands
        test_commands = [
            "Show me recent transactions",
            "Create invoice for ZESCO",
            "What's my profit this month?",
            "I paid K2500 for electricity",
            "Connect my MTN Mobile Money account",
            "Help me categorize expenses",
            "What can you do?"
        ]
        
        for i, command in enumerate(test_commands, 1):
            print(f"\n{i}. Testing: '{command}'")
            print("-" * 40)
            
            # Create request
            request = IntentRecognitionRequest(
                command=command,
                user_id="test_user",
                session_id="test_session"
            )
            
            try:
                # Test intent recognition
                response = await intent_service.recognize_intent(request)
                
                print(f"Intent: {response.intent.name.value}")
                print(f"Confidence: {response.intent.confidence:.2f}")
                print(f"Description: {response.intent.description}")
                
                if response.entities:
                    print("Entities:")
                    for entity_type, entities in response.entities.items():
                        for entity in entities:
                            print(f"  - {entity_type.value}: {entity.value} (conf: {entity.confidence:.2f})")
                
                if response.suggestions:
                    print(f"Suggestions: {response.suggestions}")
                
                if response.fallback:
                    print(f"Fallback: {response.fallback.message}")
                
                print(f"Processing time: {response.processing_time_ms}ms")
                
            except Exception as e:
                print(f"❌ Error processing command: {e}")
        
        print("\n" + "=" * 50)
        print("✅ Intent recognition test completed!")
        
        # Test entity extraction separately
        print("\n🔍 Testing Entity Extraction Service")
        print("=" * 50)
        
        entity_test_texts = [
            "I spent K2500 on ZESCO electricity bill yesterday",
            "Create invoice for John Banda for K15000",
            "Show me transactions from last month",
            "Connect my MTN Mobile Money account"
        ]
        
        for text in entity_test_texts:
            print(f"\nExtracting from: '{text}'")
            entities = entity_service.extract_entities(text)
            
            if entities:
                for entity_type, entity_list in entities.items():
                    print(f"  {entity_type.value}:")
                    for entity in entity_list:
                        print(f"    - {entity.value} (conf: {entity.confidence:.2f})")
            else:
                print("  No entities found")
        
        print("\n✅ Entity extraction test completed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_intent_recognition())
