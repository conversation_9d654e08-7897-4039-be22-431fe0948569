package com.intellifin.service;

import com.intellifin.dto.financial.MTNOAuthResponseDto;
import com.intellifin.exception.ValidationException;
import com.intellifin.model.AccountConnection;
import com.intellifin.model.FinancialAccount;
import com.intellifin.repository.FinancialAccountRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * Service for MTN Mobile Money integration
 * Since live MTN API is not available, this service provides mock implementation
 * that can be easily replaced with real MTN API integration
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MTNIntegrationService {

    private final FinancialAccountRepository financialAccountRepository;
    private final EncryptionService encryptionService;

    @Value("${mtn.api.enabled:false}")
    private boolean mtnApiEnabled;

    @Value("${mtn.oauth.client-id:mock-client-id}")
    private String clientId;

    @Value("${mtn.oauth.redirect-uri:http://localhost:3000/settings/accounts/mtn/callback}")
    private String redirectUri;

    @Value("${mtn.oauth.base-url:https://mock-mtn-api.com}")
    private String baseUrl;

    /**
     * Initiate OAuth flow with MTN
     */
    @Transactional
    public MTNOAuthResponseDto initiateOAuthFlow(FinancialAccount account) {
        log.info("Initiating OAuth flow for MTN account: {}", account.getId());

        if (!mtnApiEnabled) {
            log.warn("MTN API is disabled, using mock OAuth flow");
            return createMockOAuthResponse(account);
        }

        try {
            // In real implementation, this would:
            // 1. Generate OAuth state parameter
            // 2. Create authorization URL with MTN
            // 3. Store connection details
            
            String state = generateOAuthState();
            String authUrl = buildAuthorizationUrl(state);
            OffsetDateTime expiresAt = OffsetDateTime.now().plusMinutes(15);

            // Create account connection record
            AccountConnection connection = AccountConnection.builder()
                    .financialAccount(account)
                    .connectionType(AccountConnection.ConnectionType.OAUTH)
                    .authorizationUrl(authUrl)
                    .oauthState(state)
                    .build();

            // In real implementation, save connection to database
            // accountConnectionRepository.save(connection);

            return MTNOAuthResponseDto.builder()
                    .authorizationUrl(authUrl)
                    .state(state)
                    .expiresAt(expiresAt)
                    .build();

        } catch (Exception e) {
            log.error("Failed to initiate MTN OAuth flow for account: {}", account.getId(), e);
            account.markAsError("Failed to initiate connection: " + e.getMessage());
            financialAccountRepository.save(account);
            throw new ValidationException("Failed to initiate MTN connection");
        }
    }

    /**
     * Handle OAuth callback from MTN
     */
    @Transactional
    public void handleOAuthCallback(String code, String state) {
        log.info("Handling OAuth callback with state: {}", state);

        if (!mtnApiEnabled) {
            log.warn("MTN API is disabled, using mock callback handling");
            handleMockOAuthCallback(code, state);
            return;
        }

        try {
            // In real implementation, this would:
            // 1. Validate state parameter
            // 2. Exchange authorization code for access token
            // 3. Store encrypted tokens
            // 4. Update account status

            // Find account by OAuth state
            // AccountConnection connection = findConnectionByState(state);
            // FinancialAccount account = connection.getFinancialAccount();

            // Exchange code for tokens
            // OAuthTokenResponse tokens = exchangeCodeForTokens(code);
            
            // Encrypt and store tokens
            // String encryptedCredentials = encryptionService.encrypt(tokens.toJson());
            // connection.setEncryptedCredentials(encryptedCredentials);
            // connection.setAccessTokenExpiresAt(tokens.getAccessTokenExpiresAt());
            // connection.setRefreshTokenExpiresAt(tokens.getRefreshTokenExpiresAt());

            // Update account status
            // account.markAsConnected();
            // financialAccountRepository.save(account);

            log.info("Successfully handled OAuth callback for state: {}", state);

        } catch (Exception e) {
            log.error("Failed to handle OAuth callback for state: {}", state, e);
            throw new ValidationException("Failed to complete MTN connection");
        }
    }

    /**
     * Disconnect MTN account
     */
    @Transactional
    public void disconnectAccount(FinancialAccount account) {
        log.info("Disconnecting MTN account: {}", account.getId());

        if (!mtnApiEnabled) {
            log.warn("MTN API is disabled, using mock disconnect");
            return;
        }

        try {
            // In real implementation, this would:
            // 1. Revoke OAuth tokens with MTN
            // 2. Clear stored credentials
            // 3. Update connection status

            // Revoke tokens if they exist
            // revokeOAuthTokens(account);
            
            // Clear connection details
            // clearAccountConnections(account);

            log.info("Successfully disconnected MTN account: {}", account.getId());

        } catch (Exception e) {
            log.error("Failed to disconnect MTN account: {}", account.getId(), e);
            // Don't throw exception for disconnect failures
            // Just log the error and continue
        }
    }

    /**
     * Create mock OAuth response for testing
     */
    private MTNOAuthResponseDto createMockOAuthResponse(FinancialAccount account) {
        String state = generateOAuthState();
        String mockAuthUrl = "https://mock-mtn-oauth.com/authorize?client_id=" + clientId + 
                            "&redirect_uri=" + redirectUri + "&state=" + state + "&response_type=code";

        // Simulate successful connection after a short delay
        simulateSuccessfulConnection(account);

        return MTNOAuthResponseDto.builder()
                .authorizationUrl(mockAuthUrl)
                .state(state)
                .expiresAt(OffsetDateTime.now().plusMinutes(15))
                .build();
    }

    /**
     * Handle mock OAuth callback
     */
    private void handleMockOAuthCallback(String code, String state) {
        // In mock mode, just log the callback
        log.info("Mock OAuth callback handled - code: {}, state: {}", code, state);
    }

    /**
     * Simulate successful connection for testing
     */
    private void simulateSuccessfulConnection(FinancialAccount account) {
        // In a real implementation, this would be handled by the actual OAuth callback
        // For testing, we'll mark the account as connected after a short delay.
        // NOTE: Running this synchronously to avoid transactional issues with raw threads.
        // A proper async implementation would use Spring's @Async annotation.
        try {
            Thread.sleep(2000); // Simulate processing time
            account.markAsConnected();
            financialAccountRepository.save(account);
            log.info("Mock connection completed for account: {}", account.getId());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("Mock connection simulation interrupted");
        }
    }


    /**
     * Generate OAuth state parameter
     */
    private String generateOAuthState() {
        return UUID.randomUUID().toString();
    }

    /**
     * Build authorization URL
     */
    private String buildAuthorizationUrl(String state) {
        return baseUrl + "/oauth/authorize" +
               "?client_id=" + clientId +
               "&redirect_uri=" + redirectUri +
               "&state=" + state +
               "&response_type=code" +
               "&scope=account:read transactions:read";
    }
}
