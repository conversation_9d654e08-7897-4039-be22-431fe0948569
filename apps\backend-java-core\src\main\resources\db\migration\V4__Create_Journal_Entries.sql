-- V4__Create_Journal_Entries.sql
-- Create journal_entries and journal_entry_lines tables for double-entry bookkeeping

-- Create the journal_entries table
CREATE TABLE journal_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    entry_number VARCHAR(50) UNIQUE,
    user_id UUID NOT NULL,
    transaction_id UUID,
    description TEXT NOT NULL,
    total_amount DECIMAL(15,2) NOT NULL CHECK (total_amount > 0),
    status VARCHAR(20) NOT NULL DEFAULT 'DRAFT' CHECK (status IN ('DRAFT', 'POSTED', 'REVERSED')),
    entry_date TIMESTAMPTZ NOT NULL,
    reference_number VARCHAR(100),
    source_type VARCHAR(50),
    source_id VARCHAR(100),
    created_by UUI<PERSON>,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    posted_at TIMESTAMPTZ,
    posted_by <PERSON><PERSON><PERSON>,
    reversed_at TIMESTAMPTZ,
    reversed_by UUID,
    reversal_reason TEXT,
    reversal_entry_id UUID,
    CONSTRAINT fk_journal_entry_user FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT fk_journal_entry_created_by FOREIGN KEY (created_by) REFERENCES users(id),
    CONSTRAINT fk_journal_entry_posted_by FOREIGN KEY (posted_by) REFERENCES users(id),
    CONSTRAINT fk_journal_entry_reversed_by FOREIGN KEY (reversed_by) REFERENCES users(id),
    CONSTRAINT fk_journal_entry_reversal FOREIGN KEY (reversal_entry_id) REFERENCES journal_entries(id)
);

-- Create the journal_entry_lines table
CREATE TABLE journal_entry_lines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    journal_entry_id UUID NOT NULL,
    account_id UUID NOT NULL,
    debit_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00 CHECK (debit_amount >= 0),
    credit_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00 CHECK (credit_amount >= 0),
    description TEXT NOT NULL,
    reference_number VARCHAR(100),
    line_number INTEGER,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT fk_journal_line_entry FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id) ON DELETE CASCADE,
    CONSTRAINT fk_journal_line_account FOREIGN KEY (account_id) REFERENCES accounts(id),
    CONSTRAINT chk_debit_or_credit CHECK (
        (debit_amount > 0 AND credit_amount = 0) OR 
        (credit_amount > 0 AND debit_amount = 0)
    )
);

-- Create indexes for performance
CREATE INDEX idx_journal_entries_user_id ON journal_entries(user_id);
CREATE INDEX idx_journal_entries_transaction_id ON journal_entries(transaction_id);
CREATE INDEX idx_journal_entries_status ON journal_entries(status);
CREATE INDEX idx_journal_entries_entry_date ON journal_entries(entry_date);
CREATE INDEX idx_journal_entries_source ON journal_entries(source_type, source_id);
CREATE INDEX idx_journal_entries_created_at ON journal_entries(created_at);

CREATE INDEX idx_journal_entry_lines_journal_entry_id ON journal_entry_lines(journal_entry_id);
CREATE INDEX idx_journal_entry_lines_account_id ON journal_entry_lines(account_id);
CREATE INDEX idx_journal_entry_lines_line_number ON journal_entry_lines(journal_entry_id, line_number);

-- Create a function to generate journal entry numbers
CREATE OR REPLACE FUNCTION generate_journal_entry_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.entry_number IS NULL THEN
        NEW.entry_number := 'JE' || TO_CHAR(NOW(), 'YYYY') || '-' || 
                           LPAD(NEXTVAL('journal_entry_sequence')::TEXT, 6, '0');
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create sequence for journal entry numbers
CREATE SEQUENCE journal_entry_sequence START 1;

-- Create trigger to auto-generate entry numbers
CREATE TRIGGER trigger_generate_journal_entry_number
    BEFORE INSERT ON journal_entries
    FOR EACH ROW
    EXECUTE FUNCTION generate_journal_entry_number();

-- Create a function to validate journal entry balance
CREATE OR REPLACE FUNCTION validate_journal_entry_balance()
RETURNS TRIGGER AS $$
DECLARE
    total_debits DECIMAL(15,2);
    total_credits DECIMAL(15,2);
    entry_status VARCHAR(20);
BEGIN
    -- Get the journal entry status
    SELECT status INTO entry_status 
    FROM journal_entries 
    WHERE id = COALESCE(NEW.journal_entry_id, OLD.journal_entry_id);
    
    -- Only validate balance for POSTED entries
    IF entry_status = 'POSTED' THEN
        -- Calculate total debits and credits for the journal entry
        SELECT 
            COALESCE(SUM(debit_amount), 0),
            COALESCE(SUM(credit_amount), 0)
        INTO total_debits, total_credits
        FROM journal_entry_lines
        WHERE journal_entry_id = COALESCE(NEW.journal_entry_id, OLD.journal_entry_id);
        
        -- Check if debits equal credits
        IF total_debits != total_credits THEN
            RAISE EXCEPTION 'Journal entry must be balanced: debits (%) must equal credits (%)', 
                total_debits, total_credits;
        END IF;
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create trigger to validate balance on journal entry line changes
CREATE TRIGGER trigger_validate_journal_balance_insert
    AFTER INSERT ON journal_entry_lines
    FOR EACH ROW
    EXECUTE FUNCTION validate_journal_entry_balance();

CREATE TRIGGER trigger_validate_journal_balance_update
    AFTER UPDATE ON journal_entry_lines
    FOR EACH ROW
    EXECUTE FUNCTION validate_journal_entry_balance();

CREATE TRIGGER trigger_validate_journal_balance_delete
    AFTER DELETE ON journal_entry_lines
    FOR EACH ROW
    EXECUTE FUNCTION validate_journal_entry_balance();

-- Create a view for account balances based on journal entries
CREATE OR REPLACE VIEW account_balances AS
SELECT 
    a.id as account_id,
    a.code,
    a.name,
    a.type,
    a.normal_balance,
    COALESCE(
        CASE 
            WHEN a.normal_balance = 'DEBIT' THEN 
                SUM(jel.debit_amount) - SUM(jel.credit_amount)
            ELSE 
                SUM(jel.credit_amount) - SUM(jel.debit_amount)
        END, 
        0
    ) as current_balance,
    COUNT(jel.id) as transaction_count,
    MAX(je.posted_at) as last_transaction_date
FROM accounts a
LEFT JOIN journal_entry_lines jel ON a.id = jel.account_id
LEFT JOIN journal_entries je ON jel.journal_entry_id = je.id AND je.status = 'POSTED'
WHERE a.is_active = true
GROUP BY a.id, a.code, a.name, a.type, a.normal_balance
ORDER BY a.code;

-- Create a view for journal entry summaries
CREATE OR REPLACE VIEW journal_entry_summary AS
SELECT 
    je.id,
    je.entry_number,
    je.description,
    je.total_amount,
    je.status,
    je.entry_date,
    je.created_at,
    u.email as created_by_email,
    COUNT(jel.id) as line_count,
    SUM(jel.debit_amount) as total_debits,
    SUM(jel.credit_amount) as total_credits,
    (SUM(jel.debit_amount) = SUM(jel.credit_amount)) as is_balanced
FROM journal_entries je
LEFT JOIN journal_entry_lines jel ON je.id = jel.journal_entry_id
LEFT JOIN users u ON je.created_by = u.id
GROUP BY je.id, je.entry_number, je.description, je.total_amount, 
         je.status, je.entry_date, je.created_at, u.email
ORDER BY je.created_at DESC;
