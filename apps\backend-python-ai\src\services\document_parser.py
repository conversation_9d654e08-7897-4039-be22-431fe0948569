"""
Document parser service for extracting transaction data from MTN Mobile Money statements
"""

import asyncio
import json
import logging
import re
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from decimal import Decimal
import pandas as pd
import PyPDF2
from io import BytesIO
import openpyxl

from .messaging_service import MessagingService, MessageEvent

logger = logging.getLogger(__name__)

@dataclass
class Transaction:
    """Represents a parsed transaction"""
    date: datetime
    description: str
    amount: Decimal
    transaction_type: str  # 'DEBIT' or 'CREDIT'
    reference: Optional[str] = None
    balance: Optional[Decimal] = None
    counterparty: Optional[str] = None
    category: Optional[str] = None

@dataclass
class DocumentParsingResult:
    """Result of document parsing operation"""
    transactions: List[Transaction]
    confidence_score: float
    processing_time_ms: int
    metadata: Dict[str, Any]
    errors: List[str]

class DocumentParser:
    """Service for parsing financial documents and extracting transaction data"""
    
    def __init__(self, messaging_service: MessagingService):
        self.messaging_service = messaging_service
        
        # MTN Mobile Money transaction patterns
        self.mtn_patterns = {
            'transaction_line': re.compile(
                r'(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})\s+'  # Date
                r'([^0-9]+?)\s+'  # Description
                r'([\d,]+\.?\d*)\s+'  # Amount
                r'([\d,]+\.?\d*)'  # Balance
            ),
            'amount_pattern': re.compile(r'[\d,]+\.?\d*'),
            'date_pattern': re.compile(r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}'),
            'reference_pattern': re.compile(r'[A-Z]{2}\d{8,12}')
        }

    async def process_document(self, upload_id: str, file_path: str, file_type: str) -> DocumentParsingResult:
        """Process a document and extract transaction data"""
        start_time = datetime.now()
        
        try:
            logger.info(f"Starting document processing for upload: {upload_id}")
            
            # Publish processing started event
            await self._publish_processing_event(upload_id, "STARTED", 0)
            
            # Parse document based on file type
            if file_type.upper() == 'PDF':
                result = await self._parse_pdf(file_path)
            elif file_type.upper() == 'CSV':
                result = await self._parse_csv(file_path)
            elif file_type.upper() in ['EXCEL', 'XLS', 'XLSX']:
                result = await self._parse_excel(file_path)
            else:
                raise ValueError(f"Unsupported file type: {file_type}")
            
            # Calculate processing time
            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
            result.processing_time_ms = processing_time
            
            # Publish completion event
            await self._publish_completion_event(upload_id, result)
            
            logger.info(f"Document processing completed for upload: {upload_id}, "
                       f"extracted {len(result.transactions)} transactions")
            
            return result
            
        except Exception as e:
            logger.error(f"Document processing failed for upload: {upload_id}", exc_info=True)
            
            # Publish failure event
            await self._publish_failure_event(upload_id, str(e))
            
            # Return empty result with error
            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
            return DocumentParsingResult(
                transactions=[],
                confidence_score=0.0,
                processing_time_ms=processing_time,
                metadata={'error': str(e)},
                errors=[str(e)]
            )

    async def _parse_pdf(self, file_path: str) -> DocumentParsingResult:
        """Parse PDF document"""
        logger.debug(f"Parsing PDF file: {file_path}")
        
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
            
            # Extract transactions from text
            transactions = self._extract_transactions_from_text(text)
            confidence = self._calculate_confidence(transactions, text)
            
            return DocumentParsingResult(
                transactions=transactions,
                confidence_score=confidence,
                processing_time_ms=0,  # Will be set by caller
                metadata={
                    'file_type': 'PDF',
                    'pages': len(pdf_reader.pages),
                    'text_length': len(text)
                },
                errors=[]
            )
            
        except Exception as e:
            logger.error(f"Failed to parse PDF: {file_path}", exc_info=True)
            raise

    async def _parse_csv(self, file_path: str) -> DocumentParsingResult:
        """Parse CSV document"""
        logger.debug(f"Parsing CSV file: {file_path}")
        
        try:
            # Try different encodings
            encodings = ['utf-8', 'latin-1', 'cp1252']
            df = None
            
            for encoding in encodings:
                try:
                    df = pd.read_csv(file_path, encoding=encoding)
                    break
                except UnicodeDecodeError:
                    continue
            
            if df is None:
                raise ValueError("Could not read CSV file with any supported encoding")
            
            # Extract transactions from DataFrame
            transactions = self._extract_transactions_from_dataframe(df)
            confidence = self._calculate_confidence_from_dataframe(df, transactions)
            
            return DocumentParsingResult(
                transactions=transactions,
                confidence_score=confidence,
                processing_time_ms=0,  # Will be set by caller
                metadata={
                    'file_type': 'CSV',
                    'rows': len(df),
                    'columns': list(df.columns)
                },
                errors=[]
            )
            
        except Exception as e:
            logger.error(f"Failed to parse CSV: {file_path}", exc_info=True)
            raise

    async def _parse_excel(self, file_path: str) -> DocumentParsingResult:
        """Parse Excel document"""
        logger.debug(f"Parsing Excel file: {file_path}")
        
        try:
            # Read Excel file
            workbook = openpyxl.load_workbook(file_path)
            
            # Try to find the main data sheet
            sheet = None
            for sheet_name in workbook.sheetnames:
                if any(keyword in sheet_name.lower() for keyword in ['transaction', 'statement', 'history']):
                    sheet = workbook[sheet_name]
                    break
            
            if sheet is None:
                sheet = workbook.active
            
            # Convert to DataFrame
            data = []
            for row in sheet.iter_rows(values_only=True):
                data.append(row)
            
            if not data:
                raise ValueError("Excel file contains no data")
            
            # Create DataFrame with first row as headers
            df = pd.DataFrame(data[1:], columns=data[0])
            
            # Extract transactions from DataFrame
            transactions = self._extract_transactions_from_dataframe(df)
            confidence = self._calculate_confidence_from_dataframe(df, transactions)
            
            return DocumentParsingResult(
                transactions=transactions,
                confidence_score=confidence,
                processing_time_ms=0,  # Will be set by caller
                metadata={
                    'file_type': 'Excel',
                    'sheets': workbook.sheetnames,
                    'active_sheet': sheet.title,
                    'rows': len(df),
                    'columns': list(df.columns)
                },
                errors=[]
            )
            
        except Exception as e:
            logger.error(f"Failed to parse Excel: {file_path}", exc_info=True)
            raise

    def _extract_transactions_from_text(self, text: str) -> List[Transaction]:
        """Extract transactions from raw text"""
        transactions = []
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Try to match MTN transaction pattern
            match = self.mtn_patterns['transaction_line'].search(line)
            if match:
                try:
                    date_str, description, amount_str, balance_str = match.groups()
                    
                    # Parse date
                    date = self._parse_date(date_str)
                    if not date:
                        continue
                    
                    # Parse amounts
                    amount = self._parse_amount(amount_str)
                    balance = self._parse_amount(balance_str)
                    
                    # Determine transaction type
                    transaction_type = self._determine_transaction_type(description, amount)
                    
                    # Extract reference if available
                    reference = self._extract_reference(line)
                    
                    transaction = Transaction(
                        date=date,
                        description=description.strip(),
                        amount=amount,
                        transaction_type=transaction_type,
                        reference=reference,
                        balance=balance
                    )
                    
                    transactions.append(transaction)
                    
                except Exception as e:
                    logger.warning(f"Failed to parse transaction line: {line}", exc_info=True)
                    continue
        
        return transactions

    def _extract_transactions_from_dataframe(self, df: pd.DataFrame) -> List[Transaction]:
        """Extract transactions from pandas DataFrame"""
        transactions = []
        
        # Try to identify columns
        column_mapping = self._identify_columns(df)
        
        if not column_mapping.get('date') or not column_mapping.get('amount'):
            logger.warning("Could not identify required columns in DataFrame")
            return transactions
        
        for _, row in df.iterrows():
            try:
                # Extract date
                date_value = row[column_mapping['date']]
                date = self._parse_date(str(date_value))
                if not date:
                    continue
                
                # Extract amount
                amount_value = row[column_mapping['amount']]
                amount = self._parse_amount(str(amount_value))
                if amount is None:
                    continue
                
                # Extract description
                description = ""
                if column_mapping.get('description'):
                    description = str(row[column_mapping['description']])
                
                # Extract balance if available
                balance = None
                if column_mapping.get('balance'):
                    balance = self._parse_amount(str(row[column_mapping['balance']]))
                
                # Determine transaction type
                transaction_type = self._determine_transaction_type(description, amount)
                
                # Extract reference if available
                reference = None
                if column_mapping.get('reference'):
                    reference = str(row[column_mapping['reference']])
                
                transaction = Transaction(
                    date=date,
                    description=description,
                    amount=abs(amount),  # Ensure positive amount
                    transaction_type=transaction_type,
                    reference=reference,
                    balance=balance
                )
                
                transactions.append(transaction)
                
            except Exception as e:
                logger.warning(f"Failed to parse transaction row: {row.to_dict()}", exc_info=True)
                continue
        
        return transactions

    def _identify_columns(self, df: pd.DataFrame) -> Dict[str, str]:
        """Identify relevant columns in DataFrame"""
        column_mapping = {}
        columns = [col.lower() for col in df.columns]

        # Date column patterns
        date_patterns = ['date', 'transaction date', 'trans date', 'datetime', 'timestamp']
        for pattern in date_patterns:
            for col in df.columns:
                if pattern in col.lower():
                    column_mapping['date'] = col
                    break
            if 'date' in column_mapping:
                break

        # Amount column patterns
        amount_patterns = ['amount', 'value', 'debit', 'credit', 'transaction amount']
        for pattern in amount_patterns:
            for col in df.columns:
                if pattern in col.lower():
                    column_mapping['amount'] = col
                    break
            if 'amount' in column_mapping:
                break

        # Description column patterns
        desc_patterns = ['description', 'details', 'transaction details', 'narration', 'memo']
        for pattern in desc_patterns:
            for col in df.columns:
                if pattern in col.lower():
                    column_mapping['description'] = col
                    break
            if 'description' in column_mapping:
                break

        # Balance column patterns
        balance_patterns = ['balance', 'running balance', 'account balance']
        for pattern in balance_patterns:
            for col in df.columns:
                if pattern in col.lower():
                    column_mapping['balance'] = col
                    break
            if 'balance' in column_mapping:
                break

        # Reference column patterns
        ref_patterns = ['reference', 'ref', 'transaction id', 'trans id', 'receipt']
        for pattern in ref_patterns:
            for col in df.columns:
                if pattern in col.lower():
                    column_mapping['reference'] = col
                    break
            if 'reference' in column_mapping:
                break

        return column_mapping

    def _parse_date(self, date_str: str) -> Optional[datetime]:
        """Parse date string into datetime object"""
        if not date_str or date_str.lower() in ['nan', 'none', '']:
            return None

        # Common date formats
        date_formats = [
            '%d/%m/%Y', '%d-%m-%Y', '%d.%m.%Y',
            '%d/%m/%y', '%d-%m-%y', '%d.%m.%y',
            '%Y-%m-%d', '%Y/%m/%d',
            '%m/%d/%Y', '%m-%d-%Y',
            '%d %b %Y', '%d %B %Y',
            '%Y-%m-%d %H:%M:%S',
            '%d/%m/%Y %H:%M:%S'
        ]

        for fmt in date_formats:
            try:
                return datetime.strptime(date_str.strip(), fmt)
            except ValueError:
                continue

        logger.warning(f"Could not parse date: {date_str}")
        return None

    def _parse_amount(self, amount_str: str) -> Optional[Decimal]:
        """Parse amount string into Decimal"""
        if not amount_str or amount_str.lower() in ['nan', 'none', '']:
            return None

        try:
            # Remove currency symbols and spaces
            cleaned = re.sub(r'[^\d.,\-]', '', str(amount_str))

            # Handle negative amounts
            is_negative = '-' in cleaned or '(' in str(amount_str)

            # Remove commas and convert to decimal
            cleaned = cleaned.replace(',', '').replace('-', '')

            if not cleaned:
                return None

            amount = Decimal(cleaned)
            return -amount if is_negative else amount

        except Exception as e:
            logger.warning(f"Could not parse amount: {amount_str}", exc_info=True)
            return None

    def _determine_transaction_type(self, description: str, amount: Decimal) -> str:
        """Determine if transaction is debit or credit"""
        description_lower = description.lower()

        # Credit indicators
        credit_keywords = ['received', 'deposit', 'credit', 'incoming', 'transfer in', 'airtime purchase']
        if any(keyword in description_lower for keyword in credit_keywords):
            return 'CREDIT'

        # Debit indicators
        debit_keywords = ['sent', 'payment', 'debit', 'outgoing', 'transfer out', 'withdrawal']
        if any(keyword in description_lower for keyword in debit_keywords):
            return 'DEBIT'

        # Default based on amount sign
        return 'CREDIT' if amount >= 0 else 'DEBIT'

    def _extract_reference(self, text: str) -> Optional[str]:
        """Extract transaction reference from text"""
        match = self.mtn_patterns['reference_pattern'].search(text)
        return match.group(0) if match else None

    def _calculate_confidence(self, transactions: List[Transaction], text: str) -> float:
        """Calculate confidence score for text-based parsing"""
        if not transactions:
            return 0.0

        # Base confidence on number of successfully parsed transactions
        base_confidence = min(len(transactions) / 10, 1.0) * 0.6

        # Bonus for finding references
        ref_bonus = sum(1 for t in transactions if t.reference) / len(transactions) * 0.2

        # Bonus for finding balances
        balance_bonus = sum(1 for t in transactions if t.balance) / len(transactions) * 0.2

        return min(base_confidence + ref_bonus + balance_bonus, 1.0)

    def _calculate_confidence_from_dataframe(self, df: pd.DataFrame, transactions: List[Transaction]) -> float:
        """Calculate confidence score for DataFrame-based parsing"""
        if not transactions or df.empty:
            return 0.0

        # Base confidence on successful parsing rate
        parsing_rate = len(transactions) / len(df)
        base_confidence = parsing_rate * 0.7

        # Bonus for having all required columns
        column_mapping = self._identify_columns(df)
        required_columns = ['date', 'amount']
        optional_columns = ['description', 'balance', 'reference']

        required_bonus = sum(1 for col in required_columns if col in column_mapping) / len(required_columns) * 0.2
        optional_bonus = sum(1 for col in optional_columns if col in column_mapping) / len(optional_columns) * 0.1

        return min(base_confidence + required_bonus + optional_bonus, 1.0)

    async def _publish_processing_event(self, upload_id: str, stage: str, progress: int):
        """Publish document processing progress event"""
        try:
            event = MessageEvent(
                event_type="DocumentProcessingProgress",
                data={
                    "uploadId": upload_id,
                    "stage": stage,
                    "progress": progress,
                    "timestamp": datetime.now().isoformat()
                }
            )
            await self.messaging_service.publish_event("document.processing.progress", event)
        except Exception as e:
            logger.error(f"Failed to publish processing event: {e}")

    async def _publish_completion_event(self, upload_id: str, result: DocumentParsingResult):
        """Publish document processing completion event"""
        try:
            event = MessageEvent(
                event_type="DocumentParsingCompleted",
                data={
                    "uploadId": upload_id,
                    "extractedTransactions": len(result.transactions),
                    "confidence": result.confidence_score,
                    "processingTimeMs": result.processing_time_ms,
                    "timestamp": datetime.now().isoformat()
                }
            )
            await self.messaging_service.publish_event("document.processing.completed", event)
        except Exception as e:
            logger.error(f"Failed to publish completion event: {e}")

    async def _publish_failure_event(self, upload_id: str, error: str):
        """Publish document processing failure event"""
        try:
            event = MessageEvent(
                event_type="DocumentProcessingFailed",
                data={
                    "uploadId": upload_id,
                    "error": error,
                    "retryCount": 0,
                    "timestamp": datetime.now().isoformat()
                }
            )
            await self.messaging_service.publish_event("document.processing.failed", event)
        except Exception as e:
            logger.error(f"Failed to publish failure event: {e}")
