"use client"

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { CheckCircle, AlertCircle, HelpCircle } from 'lucide-react';

interface Intent {
  name: string;
  confidence: number;
  description: string;
}

interface IntentDisplayProps {
  intent: Intent;
  showConfidence?: boolean;
  compact?: boolean;
}

export function IntentDisplay({ intent, showConfidence = true, compact = false }: IntentDisplayProps) {
  const getIntentColor = (intentName: string) => {
    switch (intentName) {
      case 'CREATE_INVOICE':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'SHOW_SUMMARY':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'CATEGORIZE_TRANSACTION':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'VIEW_TRANSACTIONS':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'CONNECT_ACCOUNT':
        return 'bg-cyan-100 text-cyan-800 border-cyan-200';
      case 'GET_HELP':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'UNCLEAR':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getIntentIcon = (intentName: string, confidence: number) => {
    if (confidence >= 0.8) {
      return <CheckCircle className="w-4 h-4 text-green-600" />;
    } else if (confidence >= 0.5) {
      return <AlertCircle className="w-4 h-4 text-yellow-600" />;
    } else {
      return <HelpCircle className="w-4 h-4 text-red-600" />;
    }
  };

  const getIntentDisplayName = (intentName: string) => {
    switch (intentName) {
      case 'CREATE_INVOICE':
        return 'Create Invoice';
      case 'SHOW_SUMMARY':
        return 'Show Summary';
      case 'CATEGORIZE_TRANSACTION':
        return 'Categorize Transaction';
      case 'VIEW_TRANSACTIONS':
        return 'View Transactions';
      case 'CONNECT_ACCOUNT':
        return 'Connect Account';
      case 'GET_HELP':
        return 'Get Help';
      case 'UNCLEAR':
        return 'Unclear Intent';
      default:
        return intentName.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.5) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (compact) {
    return (
      <div className="flex items-center gap-2">
        {getIntentIcon(intent.name, intent.confidence)}
        <Badge variant="outline" className={getIntentColor(intent.name)}>
          {getIntentDisplayName(intent.name)}
        </Badge>
        {showConfidence && (
          <span className={`text-sm font-medium ${getConfidenceColor(intent.confidence)}`}>
            {Math.round(intent.confidence * 100)}%
          </span>
        )}
      </div>
    );
  }

  return (
    <Card className="border-l-4 border-l-blue-500">
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            {getIntentIcon(intent.name, intent.confidence)}
            <div>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className={getIntentColor(intent.name)}>
                  {getIntentDisplayName(intent.name)}
                </Badge>
                {showConfidence && (
                  <span className={`text-sm font-medium ${getConfidenceColor(intent.confidence)}`}>
                    {Math.round(intent.confidence * 100)}% confidence
                  </span>
                )}
              </div>
              {intent.description && (
                <p className="text-sm text-gray-600 mt-1">{intent.description}</p>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Confidence indicator component
interface ConfidenceIndicatorProps {
  confidence: number;
  size?: 'sm' | 'md' | 'lg';
}

export function ConfidenceIndicator({ confidence, size = 'md' }: ConfidenceIndicatorProps) {
  const getBarWidth = () => `${Math.round(confidence * 100)}%`;
  
  const getBarColor = () => {
    if (confidence >= 0.8) return 'bg-green-500';
    if (confidence >= 0.5) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const sizeClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3'
  };

  return (
    <div className="w-full">
      <div className={`w-full bg-gray-200 rounded-full ${sizeClasses[size]}`}>
        <div 
          className={`${getBarColor()} ${sizeClasses[size]} rounded-full transition-all duration-300`}
          style={{ width: getBarWidth() }}
        />
      </div>
      <div className="flex justify-between text-xs text-gray-500 mt-1">
        <span>Confidence</span>
        <span>{Math.round(confidence * 100)}%</span>
      </div>
    </div>
  );
}
