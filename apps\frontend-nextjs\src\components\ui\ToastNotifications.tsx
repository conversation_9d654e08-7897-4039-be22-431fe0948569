"use client"

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  CheckCircle,
  AlertCircle,
  Info,
  X,
  TrendingUp,
  Receipt,
  CreditCard,
  Bell
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { journalToastService } from '@/services/journalToastService';

interface ToastNotification {
  id: string;
  type: 'success' | 'error' | 'info' | 'warning';
  title: string;
  message: string;
  icon?: React.ComponentType<{ className?: string }>;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

interface ToastNotificationsProps {
  maxToasts?: number;
}

export function ToastNotifications({ maxToasts = 3 }: ToastNotificationsProps) {
  const [toasts, setToasts] = useState<ToastNotification[]>([]);

  // Real-time notifications from various services
  useEffect(() => {
    // Subscribe to journal toast notifications
    const unsubscribeJournal = journalToastService.onToast((toast) => {
      addToast(toast);
    });

    // Mock other real-time notifications - in real app, this would come from WebSocket or polling
    const mockNotifications: Omit<ToastNotification, 'id'>[] = [
      {
        type: 'success',
        title: 'New Transaction',
        message: 'Payment received from ZESCO - K2,500.00',
        icon: CreditCard,
        duration: 5000,
        action: {
          label: 'View',
          onClick: () => console.log('View transaction')
        }
      },
      {
        type: 'info',
        title: 'Invoice Reminder',
        message: 'Invoice #INV-001 is due in 2 days',
        icon: Receipt,
        duration: 4000,
        action: {
          label: 'Review',
          onClick: () => console.log('Review invoice')
        }
      },
      {
        type: 'warning',
        title: 'Low Balance Alert',
        message: 'MTN Mobile Money balance is below K500',
        icon: AlertCircle,
        duration: 6000
      },
      {
        type: 'success',
        title: 'Monthly Goal',
        message: 'You\'ve reached 85% of your monthly revenue target!',
        icon: TrendingUp,
        duration: 5000
      }
    ];

    // Simulate real-time notifications
    const intervals: NodeJS.Timeout[] = [];

    mockNotifications.forEach((notification, index) => {
      const interval = setTimeout(() => {
        addToast(notification);
      }, (index + 1) * 12000); // Stagger notifications every 12 seconds

      intervals.push(interval);
    });

    return () => {
      intervals.forEach(clearTimeout);
      unsubscribeJournal();
    };
  }, []);

  const addToast = (notification: Omit<ToastNotification, 'id'>) => {
    const id = `toast_${Date.now()}_${Math.random()}`;
    const newToast: ToastNotification = {
      ...notification,
      id,
      duration: notification.duration || 4000
    };

    setToasts(prev => {
      const updated = [newToast, ...prev];
      // Keep only the most recent toasts
      return updated.slice(0, maxToasts);
    });

    // Auto-remove toast after duration
    setTimeout(() => {
      removeToast(id);
    }, newToast.duration);
  };

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  const getToastStyles = (type: ToastNotification['type']) => {
    const styles = {
      success: {
        bg: 'bg-green-50 border-green-200',
        text: 'text-green-800',
        icon: 'text-green-600'
      },
      error: {
        bg: 'bg-red-50 border-red-200',
        text: 'text-red-800',
        icon: 'text-red-600'
      },
      warning: {
        bg: 'bg-yellow-50 border-yellow-200',
        text: 'text-yellow-800',
        icon: 'text-yellow-600'
      },
      info: {
        bg: 'bg-blue-50 border-blue-200',
        text: 'text-blue-800',
        icon: 'text-blue-600'
      }
    };
    return styles[type];
  };

  const getDefaultIcon = (type: ToastNotification['type']) => {
    const icons = {
      success: CheckCircle,
      error: AlertCircle,
      warning: AlertCircle,
      info: Info
    };
    return icons[type];
  };

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      <AnimatePresence>
        {toasts.map((toast) => {
          const styles = getToastStyles(toast.type);
          const Icon = toast.icon || getDefaultIcon(toast.type);
          
          return (
            <motion.div
              key={toast.id}
              initial={{ opacity: 0, x: 300, scale: 0.9 }}
              animate={{ opacity: 1, x: 0, scale: 1 }}
              exit={{ opacity: 0, x: 300, scale: 0.9 }}
              transition={{ duration: 0.3, ease: 'easeOut' }}
              className={`
                max-w-sm w-full rounded-lg border shadow-lg p-4
                ${styles.bg} ${styles.text}
              `}
            >
              <div className="flex items-start space-x-3">
                <div className={`flex-shrink-0 ${styles.icon}`}>
                  <Icon className="w-5 h-5" />
                </div>
                
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-semibold">
                    {toast.title}
                  </h4>
                  <p className="text-sm opacity-90 mt-1">
                    {toast.message}
                  </p>
                  
                  {toast.action && (
                    <div className="mt-3">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={toast.action.onClick}
                        className={`
                          text-xs border-current hover:bg-current hover:text-white
                          ${styles.text}
                        `}
                      >
                        {toast.action.label}
                      </Button>
                    </div>
                  )}
                </div>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeToast(toast.id)}
                  className={`
                    flex-shrink-0 h-6 w-6 p-0 hover:bg-current hover:bg-opacity-20
                    ${styles.text}
                  `}
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </motion.div>
          );
        })}
      </AnimatePresence>
    </div>
  );
}
