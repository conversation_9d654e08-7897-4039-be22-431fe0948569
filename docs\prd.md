# IntelliFin Product Requirements Document (PRD) - V1 Strategic Focus

## 1. Goals and Background Context

#### Goals

These are the desired outcomes that this PRD aims to achieve for IntelliFin V1, reflecting the strategic focus on three non-negotiable pillars.

*   To establish IntelliFin as the trusted mobile money accounting platform for Zambian SMEs through rock-solid accounting foundation.
*   To achieve V1 product-market fit by solving the core "financial blindness" problem with seamless data ingestion.
*   To differentiate through revolutionary conversational experience that makes financial management intuitive.
*   To significantly reduce the time and effort users spend on manual financial reconciliation.
*   To provide users with improved financial visibility and empower data-driven decision-making.
*   To establish a solid foundation for future advanced features and ZRA compliance.

#### Background Context

IntelliFin is being developed to address a critical and widespread problem faced by Zambian Micro, Small, and Medium Enterprises (MSMEs), particularly those operating in the informal and semi-formal sectors that heavily rely on mobile money transactions. Despite the significant volume of mobile money flowing through the economy (K486.3 billion by year-end 2024), there is a distinct lack of accounting solutions tailored to this ecosystem. Existing methods involve time-consuming manual tracking, leading to financial blindness and substantial barriers to accessing formal financial services and growth opportunities.

IntelliFin V1 focuses on three non-negotiable pillars that form the foundation for transformative financial management:

**Pillar 1: Rock-Solid Accounting Engine** - Professional double-entry bookkeeping with Chart of Accounts that accountants can trust and audit.

**Pillar 2: Seamless Data Ingestion** - API-first sync with PDF statement upload fallback, eliminating manual data entry.

**Pillar 3: Conversational Experience** - Revolutionary 2+1 dynamic panel layout inspired by "Oode" that makes financial management feel like natural conversation.

This PRD details the requirements for V1 MVP focused exclusively on these three pillars, deliberately excluding advanced features like ZRA Smart Invoice integration, multi-user access, and automated bill payments to ensure V1 success.

#### Change Log

| Date       | Version | Description        | Author   |
| :--------- | :------ | :----------------- | :------- |
| 2024-07-30 | 1.0     | Initial draft      | John, PM |
| 2024-07-31 | 2.0     | V1 Strategic Realignment - 3 Pillar Focus | John, PM |

---

## 2. Requirements

#### Functional Requirements (FR) - V1 Core Pillars

These describe the specific actions and capabilities IntelliFin V1 *must* perform, organized by the three non-negotiable pillars.

**PILLAR 1: Rock-Solid Accounting Engine**
*   FR1: The system must implement a complete Chart of Accounts with proper hierarchy (Assets, Liabilities, Equity, Income, Expenses) following standard accounting principles.
*   FR2: The system must automatically create balanced double-entry journal entries for all confirmed financial transactions (debits = credits).
*   FR3: The system must map user-friendly transaction categories to formal accounting accounts to maintain accounting integrity.
*   FR4: The system must calculate account balances correctly based on account type (debit/credit normal balance) and provide real-time balance updates.
*   FR5: The system must maintain complete audit trail for all journal entries and account modifications.

**PILLAR 2: Seamless Data Ingestion**
*   FR6: The system must allow a new user to register for an account using their email address and a secure password.
*   FR7: The system must allow a registered user to log in securely using their email and password.
*   FR8: The system must provide a secure and clear interface for a user to authorize the connection to their MTN Mobile Money account during onboarding or from their settings.
*   FR9: The system must automatically fetch new transaction data from the user's connected MTN Mobile Money account in near real-time.
*   FR10: The system must provide PDF statement upload functionality with AI-powered parsing to extract transaction data.
*   FR11: The system must use an AI model to automatically assign a preliminary business category to each transaction from any data source.
*   FR12: The system must allow the user to approve the AI's suggested category for a transaction with a single action.
*   FR13: The system must allow the user to easily override the AI's suggestion and re-categorize a transaction.

**PILLAR 3: Conversational Experience**
*   FR14: The system must implement a 2+1 dynamic panel layout (left navigation, main workspace, right sidebar) inspired by "Oode" conversational interface.
*   FR15: The system must provide a persistent, text-based conversational command bar as the primary method for user interaction.
*   FR16: The system must process natural language commands via WebSocket for real-time conversational experience.
*   FR17: The system must recognize financial intents (view transactions, create summaries, etc.) and extract relevant entities (amounts, dates, categories).
*   FR18: The system must be able to process a natural language command from the user to request a basic financial summary (Total Sales, Total Expenses, Net Profit/Loss) for a specific period.
*   FR19: The system must display the requested financial summary to the user in a clear, conversational format within the main workspace.

**V1 EXPLICITLY EXCLUDED FEATURES (Future Implementation)**
*   ZRA Smart Invoice Integration (FR10-FR12 from original scope)
*   Multi-User/Accountant Access
*   Automated Bill Payments
*   Advanced Reporting & Analytics
*   Performance Optimization Features

#### Non-Functional Requirements (NFR)

These describe how well the system performs, its quality attributes, and its operational aspects.

*   **Performance:**
    *   NFR1: The initial load time for the web application on a standard 3G or better internet connection must be under 4 seconds.
    *   NFR2: Responses to user commands in the conversational interface must feel near-instant. The system must provide visual feedback within 500ms of a command being submitted, and AI-generated text or data displays (not dependent on external APIs) should fully render within 2 seconds.
    *   NFR3: The system must efficiently process and categorize up to 100 new mobile money transactions within 60 seconds of fetching them from the source API.
*   **Security:**
    *   NFR4: All user data, especially Personally Identifiable Information (PII) and financial transaction data, must be encrypted both in transit (using TLS 1.2 or higher) and at rest.
    *   NFR5: User passwords must never be stored in plaintext. They must be securely hashed and salted using a modern, strong algorithm (e.g., bcrypt).
    *   NFR6: The system must implement the "Confirm & Commit" protocol, ensuring that no sensitive external action (e.g., submitting a ZRA invoice) is performed without explicit user review and approval on a clear confirmation screen.
    *   NFR7: User sessions must automatically time out after a reasonable period of inactivity (e.g., 30 minutes), requiring the user to log in again to continue.
*   **Reliability & Availability:**
    *   NFR8: The core IntelliFin application services shall maintain an uptime of 99.5%.
    *   NFR9: The system must handle failures or timeouts from external third-party APIs (MTN, ZRA) gracefully. It must provide clear, user-friendly error messages (e.g., "We're having trouble connecting to MTN right now, please try again in a few minutes") without crashing or losing user data.
    *   NFR10: The system must ensure full data integrity. Once a transaction is fetched from the source and stored in our database, it must not be lost or silently corrupted.
*   **Usability:**
    *   NFR11: A first-time user must be able to complete the entire onboarding process (signup, login, connect MTN account) in under 5 minutes without needing a separate tutorial.
    *   NFR12: The AI's Natural Language Understanding (NLU) must correctly interpret the user's intent for the core MVP commands (e.g., "create invoice," "show profit") with at least a 90% success rate during testing.
*   **Compatibility:**
    *   NFR13: The web application must be fully functional and render correctly on the last two major versions of Google Chrome, Mozilla Firefox, Apple Safari, and Microsoft Edge.
    *   NFR14: The user interface must be fully responsive, providing a high-quality, usable experience on screen sizes ranging from a standard smartphone (360px width) to a standard desktop monitor (1920px width).

---

## 3. User Interface Design Goals

This section captures the high-level vision for IntelliFin's user interface and user experience, guiding the design and development efforts.

*   **Overall UX Vision:**
    The primary UX vision is to create a revolutionary, low-friction, and intuitive financial management experience that feels as natural and empowering as having a conversation. Users should feel relief, clarity, and control, rather than dread or confusion. The interface should disappear, allowing the user to focus on their financial intent and outcomes.

*   **Key Interaction Paradigms:**
    *   **Conversational Command Bar:** The central interaction point will be a persistent text input field, similar to a messaging app, where users type natural language commands.
    *   **"Confirm & Commit" Protocol:** Critical actions (e.g., ZRA invoice submission) will always involve a clear review screen and an explicit user confirmation step, ensuring transparency and control.
    *   **Dynamic Workspace Manipulation:** Instead of navigating through static menus, the UI will dynamically present relevant information, pre-filled forms, or reports directly in the conversational workspace in response to user commands.
    *   **Minimalist & Focused:** The design will be clean, uncluttered, and highly focused on the immediate task or information requested, avoiding unnecessary distractions.

*   **Core Screens and Views - V1 2+1 Dynamic Panel Layout:**
    The V1 interface implements a revolutionary 2+1 dynamic panel layout inspired by "Oode" conversational interface, where all interactions happen within a unified conversational workspace rather than traditional page navigation.

    **2+1 Panel Structure:**
    *   **Left Panel (Navigation):** Persistent navigation for core functions (Transactions, Invoices, Bills, Reporting) with real-time status indicators.
    *   **Main Panel (Conversational Workspace):** Primary interaction area with conversational command bar, dynamic content display, and AI responses.
    *   **Right Panel (+1 Sidebar):** Context-sensitive information (business health vitals, chat history, or relevant insights based on current conversation).

    **Key V1 Views within 2+1 Layout:**
    *   **Login/Signup Screen:** Secure and frictionless onboarding flow (full-screen overlay).
    *   **Main Conversational Workspace:** The primary conversational interface with command bar and dynamic content display.
    *   **Transaction Review Interface:** Embedded within main workspace, showing categorized transactions with AI suggestions.
    *   **Chart of Accounts Management:** Dynamic view for account hierarchy and category mapping within main workspace.
    *   **Basic Financial Summary Display:** Conversational display of financial summaries within main workspace.
    *   **Settings/Account Connection:** Overlay or right panel for profile and MTN account management.

    **V1 EXCLUDED VIEWS:**
    *   ZRA Invoice Draft/Approval Screen (moved to Future Implementation)
    *   Multi-user management interfaces
    *   Advanced reporting dashboards

*   **Accessibility:** WCAG AA (Web Content Accessibility Guidelines Level AA)

*   **Branding:** The branding should convey trustworthiness, intelligence, simplicity, and modernity. It should feel approachable and professional, aligning with the "accounting as easy as a conversation" tagline. Visuals should be clean and intuitive, reinforcing clarity and control.

*   **Target Device and Platforms:** Web Responsive (PWA) - optimized for seamless experience across modern smartphone browsers to standard desktop monitors.

---

## 4. Technical Assumptions

This section documents the technical decisions that will guide the Architect in designing IntelliFin.

*   **Repository Structure:** Monorepo - For a unified codebase managing polyglot microservices (as stated in Project Brief).
*   **Service Architecture:** Microservices Architecture - To prioritize scalability, security, and maintainability, with specialized services developed in their optimal language (Java Spring Boot, Python FastAPI, Containerized Java ZRA app).
*   **Testing Requirements:** Our testing strategy will follow the Testing Pyramid model to ensure a balance of speed, cost, and confidence. Robust, automated testing is a non-negotiable requirement.
    *   **Unit Tests (The Foundation):** All critical business logic in the backend services (Java, Python) and key components/utility functions in the frontend (TypeScript) must be covered by unit tests. We will aim for a minimum of 80% code coverage on these critical modules.
    *   **Integration Tests (The Connective Tissue):** We must have automated tests that verify the "contracts" between our services. This includes testing our Spring Boot API endpoints to ensure they behave as expected when called, and testing the interaction between our services and the PostgreSQL database.
    *   **End-to-End (E2E) Tests (The User Journey):** For the MVP, we will implement a small but vital suite of automated E2E tests covering the most critical user "happy paths":
        *   Successful user registration, login, and connection of an MTN account.
        *   A user command to view a financial summary and the successful display of that summary.
        *   The full lifecycle of creating a ZRA invoice via a command, reviewing the draft, and receiving a success confirmation (mocking the final ZRA submission).
    *   **Manual QA:** A formal QA checklist must be executed before every release. This will cover usability, visual consistency across supported browsers, and exploratory testing to catch issues not covered by automated tests.
*   **Additional Technical Assumptions and Requests:**
    *   **Cloud Provider:** Microsoft Azure
    *   **Frontend Framework/Language:** Next.js 15 (React 19) with TypeScript
    *   **Backend Core Logic:** Java with Spring Boot 3
    *   **Specialized AI/ML:** Python with FastAPI (containerized)
    *   **ZRA Compliance Application:** Dedicated containerized Java application
    *   **Primary Database:** PostgreSQL
    *   **Caching:** Redis
    *   **AI Orchestration:** LangChain4j
    *   **AI Knowledge Base/Vector DB:** Azure AI Search
    *   **Real-time Communication:** Spring WebSockets (STOMP protocol)
    *   **Messaging (Production):** Azure Service Bus for cloud environments
    *   **Messaging (Local Development):** RabbitMQ for zero-cost local development
    *   **Messaging Abstraction:** Spring Cloud Stream (Java), Custom MessagingService wrapper (Python)
    *   **Development Workflow:** Native-first local development with Docker for CI/CD and production parity
    *   **Containerization Tool:** Docker
    *   **Critical Integrations:** MTN Mobile Money API, Stitch aggregator API, ZRA API for VSDC communication.

---

## 5. Epic List (Revised)

Here is the revised list of the Epics planned for the IntelliFin MVP. This structure prioritizes a cost-effective, local-first development workflow and a clear path of delivering incremental value to the user.

*   **Epic 1: Local Development Environment & CI/CD Foundation:**
    *   **Goal:** The primary objective of this foundational epic is to maximize developer velocity and minimize operational costs by creating a native-first local development environment with hybrid messaging strategy. Our AI service will be configured to use Google's Gemini free tier API, ensuring we develop against a production-class model from day one, at zero initial cost. The local environment uses RabbitMQ for messaging (zero cost), while production uses Azure Service Bus. All services use abstraction layers to seamlessly switch between environments. Docker remains critical for CI/CD pipelines and production parity testing.
    *   **Key Deliverables:**
        *   Establish native-first local development with RabbitMQ messaging broker installation and configuration.
        *   Implement messaging abstraction layers (Spring Cloud Stream for Java, MessagingService wrapper for Python).
        *   Create Dockerfiles for all microservices (Java/Spring Boot, Python/FastAPI, Next.js) for CI/CD and production deployment.
        *   Create a docker-compose.yml file for CI environments and production parity testing (including PostgreSQL, Redis, and RabbitMQ containers).
        *   Establish the foundational CI/CD pipeline using GitHub Actions that automates building, testing, and containerizing our applications.
    *   **Outcome:** A developer can clone the repository, install native dependencies (including RabbitMQ), and run services locally with maximum performance while maintaining production parity through abstraction layers.

*   **Epic 2: Cloud Provisioning & User Onboarding:**
    *   **Goal:** To deploy the foundational platform to the cloud and deliver the first piece of user-facing functionality: secure user registration and login.
    *   **Key Deliverables:**
        *   Provision the initial, essential Azure infrastructure (Static Web Apps, App Service, PostgreSQL).
        *   Develop and deploy the user registration and login features.
        *   Develop and deploy the secure connection flow for a user's MTN Mobile Money account.
    *   **Outcome:** The application is live on the internet, and a user can create an account and connect their financial lifeline.

*   **Epic 3: Foundational AI Engine & Transaction Categorization:**
    *   **Goal:** To build and activate our core "Neural Financial Network" to deliver the first "magic moment" of automated transaction categorization.
    *   **Key Deliverables:**
        *   Set up the LangChain4j orchestrator and configure the RAG pipeline to work with both the local Ollama LLM and the cloud-based Azure AI Search.
        *   Implement the full end-to-end user workflow for AI-powered transaction categorization and user review/confirmation.
    *   **Outcome:** A user can see their chaotic transaction data automatically organized and categorized by the AI.

*   **Epic 4: ZRA Smart Invoicing & Compliance Engine:**
    *   **Goal:** To solve the user's acute compliance pain point by enabling AI-driven ZRA invoice creation.
    *   **Key Deliverables:** Develop the functionality for users to initiate, draft, review, approve, and submit ZRA-compliant Smart Invoices via natural language commands.
    *   **Outcome:** A user can create and submit a ZRA-compliant invoice simply by having a conversation with the platform.

*   **Epic 5: Basic Financial Insights & Reporting:**
    *   **Goal:** To deliver on the promise of financial visibility by allowing users to ask questions about their business's performance.
    *   **Key Deliverables:** Enable users to request and view fundamental financial summaries (Sales, Expenses, Profit/Loss) through the conversational interface.
    *   **Outcome:** A user can get an instant, clear answer to the question, "How is my business doing?"

---

## 6. Checklist Results Report

*(This section will be populated after running the `pm-checklist`.)*

---

## 7. Next Steps

*   **Immediate Actions:**
    1.  Final review and approval of this Product Requirements Document (PRD).
    2.  Execute the Product Manager checklist (`pm-checklist`) to validate the PRD.
    3.  Proceed to generate prompts for the UX Expert and Architect based on the finalized PRD.
*   **Handoffs:**
    *   **UX Expert Handoff:** This PRD provides the necessary product requirements for the UX Expert to create the UI/UX Specification.
    *   **Architect Handoff:** This PRD provides the necessary product and technical requirements for the Architect to design the detailed full-stack architecture. 