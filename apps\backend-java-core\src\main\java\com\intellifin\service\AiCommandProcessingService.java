package com.intellifin.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.intellifin.model.Conversation;
import com.intellifin.model.VertexAIResponse;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class AiCommandProcessingService {

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    @Value("${external.ai-service.url:http://localhost:8000}")
    private String aiServiceUrl;

    @Value("${external.ai-service.timeout:30}")
    private int timeoutSeconds;

    public CommandProcessingResult processCommand(String command, Conversation conversation) {
        log.debug("Processing command with AI service: {}", command);
        
        try {
            // Prepare request payload
            Map<String, Object> requestPayload = new HashMap<>();
            requestPayload.put("command", command);
            requestPayload.put("user_id", conversation.getUser().getId().toString());
            requestPayload.put("session_id", conversation.getSessionId());
            
            // Set up headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(List.of(MediaType.APPLICATION_JSON));
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestPayload, headers);
            
            // Call AI service
            String endpoint = aiServiceUrl + "/api/v1/ai/intent";

            ResponseEntity<String> response = restTemplate.exchange(
                endpoint,
                HttpMethod.POST,
                request,
                String.class
            );
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                return parseAiResponse(response.getBody());
            } else {
                log.warn("AI service returned non-success status: {}", response.getStatusCode());
                return createFallbackResponse(command);
            }
            
        } catch (Exception e) {
            log.error("Error calling AI service for command processing", e);
            return createFallbackResponse(command);
        }
    }

    private CommandProcessingResult parseAiResponse(String responseBody) {
        try {
            VertexAIResponse aiResponse = objectMapper.readValue(responseBody, VertexAIResponse.class);

            String responseText = generateResponseText(aiResponse);

            return CommandProcessingResult.builder()
                    .intent(aiResponse.getIntent())
                    .confidence(aiResponse.getConfidence())
                    .response(responseText)
                    .requiresFollowUp(false) // The new AI response doesn't have this field.
                    .followUpPrompt(null)
                    .metadata(aiResponse.getEntities()) // Store entities in metadata
                    .success(true)
                    .build();

        } catch (Exception e) {
            log.error("Error parsing AI service response", e);
            return createFallbackResponse("Error parsing AI response");
        }
    }

    private String generateResponseText(VertexAIResponse aiResponse) {
        String intent = aiResponse.getIntent();
        Map<String, Object> entities = aiResponse.getEntities();

        if (intent == null) {
            return "I'm not sure how to help with that. Can you please rephrase?";
        }

        switch (intent) {
            case "CREATE_INVOICE":
                Object clientName = entities.get("clientName");
                Object amount = entities.get("amount");

                if (clientName != null && amount != null) {
                    return String.format("I'll draft an invoice for %s for K%s. Does that look correct?", clientName, amount);
                } else if (clientName != null) {
                    return String.format("I can create an invoice for %s. What is the amount?", clientName);
                } else if (amount != null) {
                    return String.format("I can create an invoice for K%s. Who is the client?", amount);
                } else {
                    return "I can help create an invoice. Who is it for and for what amount?";
                }
            case "CATEGORIZE_TRANSACTION":
                return "I can help with that. Which transaction would you like to categorize?";
            case "SHOW_SUMMARY":
                return "I'm preparing your financial summary now.";
            default:
                if (aiResponse.getJustification() != null && !aiResponse.getJustification().isBlank()) {
                    return aiResponse.getJustification();
                }
                return "I'm not quite sure how to handle that, but I'm always learning. Please try rephrasing your request.";
        }
    }

    private CommandProcessingResult createFallbackResponse(String originalCommand) {
        String fallbackResponse = "Sorry, I encountered an issue while processing your request. Please try again.";
        
        return CommandProcessingResult.builder()
                .intent("unknown")
                .confidence(0.0)
                .response(fallbackResponse)
                .requiresFollowUp(false)
                .success(false)
                .build();
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CommandProcessingResult {
        private String intent;
        private Double confidence;
        private String response;
        private Boolean requiresFollowUp;
        private String followUpPrompt;
        private Map<String, Object> metadata;
        private Boolean success;
        
        public boolean requiresFollowUp() {
            return Boolean.TRUE.equals(requiresFollowUp);
        }
        
        public boolean isSuccess() {
            return Boolean.TRUE.equals(success);
        }
    }
}
