# Transaction Categorization System

## Overview

The IntelliFin Transaction Categorization System provides AI-powered automatic categorization of financial transactions with real-time updates, user feedback learning, and comprehensive error handling.

## Architecture

### System Components

1. **Backend Java Core** - Main transaction management and API
2. **Backend Python AI** - AI categorization service using LangChain and Gemini
3. **Frontend Next.js** - User interface with real-time updates
4. **Messaging Layer** - Event-driven communication between services
5. **WebSocket Layer** - Real-time updates to frontend

### Data Flow

```
Transaction Created → AI Categorization Request → AI Processing → 
Categorization Result → User Review → Feedback → Learning
```

## Features

### Core Functionality

- **Automatic AI Categorization**: Uses Google Gemini Pro for intelligent transaction categorization
- **Real-time Updates**: WebSocket-based live updates for categorization results
- **User Feedback**: Accept/reject AI suggestions with learning feedback
- **Bulk Operations**: Process multiple transactions simultaneously
- **Custom Categories**: User-defined categories alongside system defaults
- **Zambian Context**: Optimized for Zambian business transactions and vendors

### AI Capabilities

- **High Accuracy**: Confidence scoring with adjustable thresholds
- **Context Awareness**: Understands Zambian business context (Airtel, MTN, ZESCO, etc.)
- **Learning System**: Improves from user feedback (positive/negative)
- **Fallback Handling**: Keyword-based categorization when AI is unavailable
- **Performance Monitoring**: Tracks categorization speed and accuracy

## API Endpoints

### Transaction Management

```http
GET /api/v1/transactions
POST /api/v1/transactions
PUT /api/v1/transactions/{id}
DELETE /api/v1/transactions/{id}
GET /api/v1/transactions/pending-categorization
```

### Categorization

```http
PUT /api/v1/transactions/{id}/categorize
POST /api/v1/transactions/bulk-categorize
```

### Categories

```http
GET /api/v1/categories
POST /api/v1/categories
PUT /api/v1/categories/{id}
DELETE /api/v1/categories/{id}
GET /api/v1/categories/system
GET /api/v1/categories/most-used
```

### AI Service

```http
POST /api/v1/categorization/categorize
POST /api/v1/categorization/bulk-categorize
POST /api/v1/categorization/feedback/positive
POST /api/v1/categorization/feedback/negative
GET /api/v1/categorization/health
```

## Database Schema

### Core Tables

#### transactions
- `id` (UUID, Primary Key)
- `user_id` (UUID, Foreign Key)
- `financial_account_id` (UUID)
- `date` (DATE)
- `description` (VARCHAR)
- `amount` (DECIMAL)
- `type` (ENUM: INCOME, EXPENSE)
- `category_id` (UUID, Foreign Key)
- `ai_category_id` (UUID, Foreign Key)
- `ai_confidence` (DECIMAL)
- `ai_explanation` (TEXT)
- `status` (ENUM: PENDING_CLASSIFICATION, CLASSIFIED, RECONCILED)
- `source` (VARCHAR)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

#### categories
- `id` (UUID, Primary Key)
- `user_id` (UUID, nullable for system categories)
- `name` (VARCHAR)
- `description` (TEXT)
- `type` (ENUM: INCOME, EXPENSE)
- `is_system_defined` (BOOLEAN)
- `is_active` (BOOLEAN)
- `color` (VARCHAR)
- `icon` (VARCHAR)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

## Configuration

### Environment Variables

#### Backend Java Core
```env
# Database
SPRING_DATASOURCE_URL=*******************************************
SPRING_DATASOURCE_USERNAME=intellifin
SPRING_DATASOURCE_PASSWORD=password

# Messaging
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USERNAME=guest
RABBITMQ_PASSWORD=guest

# Production - Azure Service Bus
AZURE_SERVICEBUS_CONNECTION_STRING=Endpoint=sb://...

# Cache
SPRING_CACHE_TYPE=simple
```

#### Backend Python AI
```env
# AI Configuration
GOOGLE_API_KEY=your_gemini_api_key
AI_MODEL_NAME=gemini-pro
AI_TEMPERATURE=0.1

# Messaging
MESSAGING_PROVIDER=rabbitmq
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USERNAME=guest
RABBITMQ_PASSWORD=guest

# Production
MESSAGING_PROVIDER=azure_servicebus
AZURE_SERVICEBUS_CONNECTION_STRING=Endpoint=sb://...
```

#### Frontend Next.js
```env
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080
NEXT_PUBLIC_WS_BASE_URL=ws://localhost:8080
```

## Deployment

### Local Development

1. **Start Infrastructure**
   ```bash
   docker-compose up -d postgres rabbitmq
   ```

2. **Start Backend Services**
   ```bash
   # Java Core Service
   cd apps/backend-java-core
   ./mvnw spring-boot:run

   # Python AI Service
   cd apps/backend-python-ai
   pip install -r requirements.txt
   python -m uvicorn src.main:app --reload --port 8081
   ```

3. **Start Frontend**
   ```bash
   cd apps/frontend-nextjs
   npm install
   npm run dev
   ```

### Production Deployment

#### Docker Deployment
```bash
# Build all services
docker-compose -f docker-compose.prod.yml build

# Deploy with production configuration
docker-compose -f docker-compose.prod.yml up -d
```

#### Kubernetes Deployment
```bash
# Apply Kubernetes manifests
kubectl apply -f k8s/namespace.yaml
kubectl apply -f k8s/configmaps/
kubectl apply -f k8s/secrets/
kubectl apply -f k8s/deployments/
kubectl apply -f k8s/services/
kubectl apply -f k8s/ingress/
```

## Monitoring and Observability

### Performance Metrics

- **Categorization Accuracy**: Percentage of correct AI categorizations
- **Response Time**: Average time for categorization requests
- **User Feedback Rate**: Percentage of AI suggestions reviewed by users
- **Error Rate**: Failed categorization attempts
- **WebSocket Connection Health**: Real-time connection status

### Health Checks

```http
GET /api/v1/health                    # Java Core Service
GET /api/v1/categorization/health     # Python AI Service
GET /api/health                       # Frontend Health
```

### Logging

- **Structured Logging**: JSON format with correlation IDs
- **Log Levels**: DEBUG, INFO, WARN, ERROR
- **Key Events**: Categorization requests, user feedback, errors
- **Performance Logs**: Slow operations, high error rates

## Testing

### Unit Tests
```bash
# Java Backend
./mvnw test

# Python AI Service
pytest apps/backend-python-ai/tests/

# Frontend
npm test
```

### Integration Tests
```bash
# Java Integration Tests
./mvnw test -Dtest=*IntegrationTest

# End-to-End Tests
npm run test:e2e
```

### Performance Tests
```bash
# Load testing with k6
k6 run tests/performance/categorization-load-test.js
```

## Troubleshooting

### Common Issues

1. **AI Service Unavailable**
   - Check Google API key configuration
   - Verify network connectivity
   - Review rate limiting

2. **Messaging Failures**
   - Check RabbitMQ/Azure Service Bus connectivity
   - Verify queue configurations
   - Review dead letter queues

3. **WebSocket Connection Issues**
   - Check CORS configuration
   - Verify authentication tokens
   - Review network firewalls

4. **Performance Issues**
   - Monitor database query performance
   - Check cache hit rates
   - Review AI service response times

### Debug Commands

```bash
# Check service health
curl http://localhost:8080/api/v1/health
curl http://localhost:8081/api/v1/categorization/health

# View logs
docker logs intellifin-java-core
docker logs intellifin-python-ai

# Check message queues
rabbitmqctl list_queues
```

## Security Considerations

- **Authentication**: JWT-based user authentication
- **Authorization**: User-scoped data access
- **API Security**: Rate limiting and input validation
- **Data Privacy**: Encrypted sensitive data
- **Network Security**: HTTPS/WSS in production

## Future Enhancements

- **Advanced ML Models**: Custom model training on user data
- **Multi-language Support**: Support for local languages
- **Mobile App Integration**: React Native mobile app
- **Advanced Analytics**: Spending pattern analysis
- **Bank Integration**: Direct bank API connections
