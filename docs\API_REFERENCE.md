# IntelliFin Transaction Categorization API Reference

## Base URLs

- **Development**: `http://localhost:8080`
- **Production**: `https://api.yourdomain.com`
- **AI Service**: `http://localhost:8081` (dev) / `https://ai.yourdomain.com` (prod)

## Authentication

All API endpoints require JWT authentication via the `Authorization` header:

```http
Authorization: Bearer <jwt_token>
```

## Transaction Management API

### Get Transactions

Retrieve paginated list of transactions with optional filters.

```http
GET /api/v1/transactions
```

**Query Parameters:**
- `page` (integer, default: 0) - Page number
- `limit` (integer, default: 20, max: 100) - Items per page
- `status` (string) - Filter by status: `PENDING_CLASSIFICATION`, `CLASSIFIED`, `RECONCILED`
- `type` (string) - Filter by type: `INCOME`, `EXPENSE`
- `categoryId` (UUID) - Filter by category
- `accountId` (UUID) - Filter by account
- `startDate` (date) - Filter from date (YYYY-MM-DD)
- `endDate` (date) - Filter to date (YYYY-MM-DD)
- `search` (string) - Search in description

**Response:**
```json
{
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "userId": "550e8400-e29b-41d4-a716-************",
      "financialAccountId": "550e8400-e29b-41d4-a716-************",
      "date": "2024-01-15",
      "description": "Fuel purchase at Shell station",
      "amount": 150.00,
      "type": "EXPENSE",
      "categoryId": "550e8400-e29b-41d4-a716-************",
      "aiCategoryId": "550e8400-e29b-41d4-a716-************",
      "aiConfidence": 0.85,
      "aiExplanation": "AI detected fuel purchase keywords",
      "status": "CLASSIFIED",
      "source": "bank_import",
      "createdAt": "2024-01-15T10:30:00Z",
      "updatedAt": "2024-01-15T10:35:00Z"
    }
  ],
  "pagination": {
    "page": 0,
    "totalPages": 5,
    "total": 100,
    "hasNext": true,
    "hasPrevious": false
  }
}
```

### Get Single Transaction

```http
GET /api/v1/transactions/{id}
```

**Response:**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "userId": "550e8400-e29b-41d4-a716-************",
  "description": "Fuel purchase at Shell station",
  "amount": 150.00,
  "type": "EXPENSE",
  "status": "CLASSIFIED",
  "category": {
    "id": "550e8400-e29b-41d4-a716-************",
    "name": "Transport",
    "type": "EXPENSE",
    "color": "#3B82F6"
  }
}
```

### Create Transaction

```http
POST /api/v1/transactions
```

**Request Body:**
```json
{
  "financialAccountId": "550e8400-e29b-41d4-a716-************",
  "date": "2024-01-15",
  "description": "Taxi ride to airport",
  "amount": 75.00,
  "type": "EXPENSE",
  "source": "manual"
}
```

**Response:** `201 Created` with transaction object

### Update Transaction

```http
PUT /api/v1/transactions/{id}
```

**Request Body:**
```json
{
  "description": "Updated description",
  "amount": 200.00,
  "categoryId": "550e8400-e29b-41d4-a716-************"
}
```

### Delete Transaction

```http
DELETE /api/v1/transactions/{id}
```

**Response:** `204 No Content`

### Categorize Transaction

Accept or reject AI categorization suggestion.

```http
PUT /api/v1/transactions/{id}/categorize
```

**Request Body:**
```json
{
  "categoryId": "550e8400-e29b-41d4-a716-************",
  "isAISuggestion": true,
  "explanation": "User accepted AI suggestion"
}
```

**Response:**
```json
{
  "success": true,
  "transaction": {
    "id": "550e8400-e29b-41d4-a716-************",
    "categoryId": "550e8400-e29b-41d4-a716-************",
    "status": "CLASSIFIED"
  },
  "aiLearning": {
    "feedback": "positive",
    "confidence": 0.85
  }
}
```

### Bulk Categorize Transactions

```http
POST /api/v1/transactions/bulk-categorize
```

**Request Body:**
```json
{
  "transactions": [
    {
      "transactionId": "550e8400-e29b-41d4-a716-************",
      "categoryId": "550e8400-e29b-41d4-a716-************",
      "isAISuggestion": false
    }
  ]
}
```

### Get Pending Categorization Transactions

```http
GET /api/v1/transactions/pending-categorization
```

## Category Management API

### Get Categories

```http
GET /api/v1/categories
```

**Query Parameters:**
- `type` (string) - Filter by type: `INCOME`, `EXPENSE`

**Response:**
```json
{
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "name": "Transport",
      "description": "Transportation expenses",
      "type": "EXPENSE",
      "isSystemDefined": true,
      "isActive": true,
      "color": "#3B82F6",
      "icon": "car"
    }
  ],
  "total": 25
}
```

### Create Category

```http
POST /api/v1/categories
```

**Request Body:**
```json
{
  "name": "Business Travel",
  "description": "Business-related travel expenses",
  "type": "EXPENSE",
  "color": "#EC4899",
  "icon": "plane"
}
```

### Update Category

```http
PUT /api/v1/categories/{id}
```

### Delete Category

```http
DELETE /api/v1/categories/{id}
```

### Get System Categories

```http
GET /api/v1/categories/system
```

### Get Most Used Categories

```http
GET /api/v1/categories/most-used?limit=10
```

## AI Categorization API

### Categorize Single Transaction

```http
POST /api/v1/categorization/categorize
```

**Request Body:**
```json
{
  "transaction_id": "550e8400-e29b-41d4-a716-************",
  "description": "Fuel purchase at Shell station",
  "amount": 150.0,
  "transaction_type": "EXPENSE",
  "user_id": "550e8400-e29b-41d4-a716-************",
  "custom_categories": ["Business Travel", "Client Entertainment"]
}
```

**Response:**
```json
{
  "success": true,
  "category_id": "550e8400-e29b-41d4-a716-************",
  "category_name": "Transport",
  "category_type": "EXPENSE",
  "confidence": 0.85,
  "explanation": "AI detected fuel purchase keywords including 'fuel' and 'Shell station'",
  "keywords": ["fuel", "Shell", "station"],
  "processing_time": 250,
  "model_used": "gemini-pro"
}
```

### Bulk Categorize Transactions

```http
POST /api/v1/categorization/bulk-categorize
```

**Request Body:**
```json
{
  "transactions": [
    {
      "id": "txn-1",
      "description": "Fuel purchase",
      "amount": 150.0,
      "type": "EXPENSE"
    }
  ],
  "user_id": "550e8400-e29b-41d4-a716-************"
}
```

### Record Positive Feedback

```http
POST /api/v1/categorization/feedback/positive
```

**Request Body:**
```json
{
  "transaction_id": "550e8400-e29b-41d4-a716-************",
  "category_id": "550e8400-e29b-41d4-a716-************",
  "feedback": "Correct categorization"
}
```

### Record Negative Feedback

```http
POST /api/v1/categorization/feedback/negative
```

**Request Body:**
```json
{
  "transaction_id": "550e8400-e29b-41d4-a716-************",
  "rejected_category_id": "550e8400-e29b-41d4-a716-************",
  "correct_category_id": "550e8400-e29b-41d4-a716-446655440004",
  "reason": "Wrong category selected"
}
```

### Health Check

```http
GET /api/v1/categorization/health
```

**Response:**
```json
{
  "status": "healthy",
  "model_info": {
    "production_model": {
      "name": "gemini-pro",
      "version": "1.0"
    }
  },
  "test_categorization": {
    "category": "Miscellaneous",
    "confidence": 0.3,
    "processing_time": 100
  }
}
```

## WebSocket API

### Connection

```javascript
const ws = new WebSocket('ws://localhost:8080/ws/transaction-updates');
```

### Message Format

```json
{
  "messageId": "550e8400-e29b-41d4-a716-************",
  "userId": "550e8400-e29b-41d4-a716-************",
  "eventType": "TRANSACTION_CATEGORIZED",
  "data": {
    "transactionId": "550e8400-e29b-41d4-a716-************",
    "suggestedCategory": {
      "id": "550e8400-e29b-41d4-a716-************",
      "name": "Transport"
    },
    "confidence": 0.85,
    "explanation": "AI detected transport keywords"
  },
  "timestamp": "2024-01-15T10:35:00Z"
}
```

### Event Types

- `TRANSACTION_CATEGORIZED` - AI has categorized a transaction
- `CATEGORIZATION_ACCEPTED` - User accepted AI suggestion
- `CATEGORIZATION_REJECTED` - User rejected AI suggestion
- `BULK_CATEGORIZATION_COMPLETED` - Bulk operation completed
- `SUBSCRIPTION_CONFIRMED` - WebSocket subscription confirmed
- `PONG` - Response to ping message

## Error Responses

### Standard Error Format

```json
{
  "timestamp": "2024-01-15T10:35:00Z",
  "status": 400,
  "error": "Bad Request",
  "message": "Invalid input data",
  "path": "/api/v1/transactions",
  "validationErrors": {
    "amount": "Amount must be greater than 0",
    "description": "Description is required"
  }
}
```

### HTTP Status Codes

- `200` - Success
- `201` - Created
- `204` - No Content
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Unprocessable Entity
- `500` - Internal Server Error
- `503` - Service Unavailable

### Common Error Scenarios

1. **Transaction Not Found** (404)
2. **Category Not Found** (404)
3. **AI Service Unavailable** (503)
4. **Categorization Failed** (422)
5. **Unauthorized Access** (403)
6. **Validation Error** (400)

## Rate Limiting

- **Standard endpoints**: 100 requests per minute per user
- **AI categorization**: 50 requests per minute per user
- **Bulk operations**: 10 requests per minute per user

Rate limit headers:
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642248000
```
