/**
 * Conversational Preview Components
 * 
 * These components implement Method 1 of the Progressive Disclosure pattern:
 * "Conversational Previews" - Simplified previews embedded in AI chat responses
 * with clear "View Full [Feature]" CTAs that bridge to detailed management pages.
 * 
 * Design Principles:
 * - Read-only, simplified views
 * - Essential information only
 * - Clear visual hierarchy
 * - Prominent "View Full" CTAs
 * - Consistent styling across all previews
 * - Both full and inline variants
 */

// Journal Entry Previews
export { 
  JournalEntryPreview, 
  JournalEntryInlinePreview 
} from './JournalEntryPreview';

// Account Balance Previews  
export { 
  AccountBalancePreview, 
  AccountBalanceInlinePreview 
} from './AccountBalancePreview';

// Transaction-Journal Relationship Previews
export { 
  TransactionJournalPreview, 
  TransactionJournalInlinePreview 
} from './TransactionJournalPreview';

/**
 * Usage Examples:
 * 
 * 1. In AI Chat Responses (Full Previews):
 * ```tsx
 * <JournalEntryPreview 
 *   entry={journalEntry} 
 *   showLines={true} 
 *   maxLines={3} 
 * />
 * ```
 * 
 * 2. In Inline Chat Messages (Compact Previews):
 * ```tsx
 * I've created journal entry <JournalEntryInlinePreview entry={entry} /> 
 * for your transaction.
 * ```
 * 
 * 3. Account Balance Summaries:
 * ```tsx
 * <AccountBalancePreview 
 *   balances={accountBalances}
 *   title="Current Account Balances"
 *   maxAccounts={5}
 * />
 * ```
 * 
 * 4. Transaction-Journal Relationships:
 * ```tsx
 * <TransactionJournalPreview 
 *   transaction={transaction}
 *   journalEntry={journalEntry}
 *   showJournalLines={true}
 * />
 * ```
 */

// Preview component types for TypeScript
export interface PreviewComponentProps {
  className?: string;
}

export interface InlinePreviewProps {
  className?: string;
}

/**
 * Progressive Disclosure Pattern Implementation:
 * 
 * These preview components serve as the "bridge" between:
 * - High-level conversational AI responses (CUI)
 * - Detailed power-user management pages
 * 
 * Key Features:
 * - Simplified, read-only data presentation
 * - Essential information highlighting
 * - Clear visual status indicators
 * - Direct navigation to full features
 * - Consistent design language
 * - Responsive layouts
 * - Accessibility considerations
 */
