# Story 4.1: AI-Powered Transaction Review & Categorization
## DETAILED READY-FOR-DEVELOPMENT SPECIFICATION

**Epic:** 4 - Intelligent Transaction Processing
**Priority:** CRITICAL - Core V1 MVP Feature
**Estimated Effort:** 8-9 developer days
**Dependencies:** Story 2.1 (Chart of Accounts), Story 2.2 (Double-Entry Journal), Story 3.1 (Data Ingestion)

---

## User Story

**As a** user
**I want** my transactions to be intelligently categorized and automatically converted to proper accounting entries
**So that** I can review, approve, and maintain accurate books without manual accounting work

---

## Business Context

This story implements the intelligent processing layer that transforms raw transaction data into meaningful accounting information. It bridges the gap between **Pillar 2 (Seamless Data Ingestion)** and **Pillar 1 (Rock-Solid Accounting Engine)** by using AI to categorize transactions and automatically create proper double-entry journal entries.

**Why This Matters:**
- Transforms raw mobile money transactions into proper accounting entries
- Implements our Command & Control CrewAI crew for intelligent transaction processing
- Provides user-friendly review interface for approving AI decisions
- Ensures all transactions result in balanced journal entries
- C<PERSON>s foundation for accurate financial reporting and business insights
- Handles Zambian business context and mobile money transaction patterns

---

## Technical Implementation Details

### 1. Event-Driven Transaction Processing Flow

#### Transaction Processing Pipeline:

```mermaid
graph TD
    A[RawTransactionReceivedEvent] --> B[Command & Control CrewAI]
    B --> C[Transaction Categorization]
    C --> D[Account Mapping]
    D --> E[Journal Entry Creation]
    E --> F[User Review Interface]
    F --> G[Approval/Rejection]
    G --> H[Final Journal Entry]
    H --> I[TransactionProcessedEvent]
```

#### Event Handler for Raw Transactions:

**transaction_processing_event_handler.py:**
```python
import asyncio
import logging
from typing import Dict, Any
from datetime import datetime

from .command_control_crew import CommandControlCrew
from .messaging_service import MessagingService
from .journal_service import JournalService

class TransactionProcessingEventHandler:
    """
    Handles RawTransactionReceivedEvent and coordinates AI processing.
    """

    def __init__(self, messaging_service: MessagingService, journal_service: JournalService):
        self.messaging_service = messaging_service
        self.journal_service = journal_service
        self.command_control_crew = CommandControlCrew()
        self.logger = logging.getLogger(__name__)

    async def handle_raw_transaction_received(self, event: Dict[str, Any]):
        """
        Process RawTransactionReceivedEvent using Command & Control CrewAI crew.
        """
        try:
            transaction_id = event['transactionId']
            user_id = event['userId']
            amount = event['amount']
            description = event['description']
            transaction_date = event['transactionDate']
            transaction_type = event['transactionType']
            source = event['source']

            self.logger.info(f"Processing raw transaction {transaction_id} for user {user_id}")

            # Update status to processing
            await self.messaging_service.publish_event('transaction.events', {
                'eventType': 'TransactionProcessingStarted',
                'transactionId': transaction_id,
                'userId': user_id,
                'timestamp': datetime.utcnow().isoformat()
            })

            # Process with Command & Control CrewAI
            processing_result = await asyncio.get_event_loop().run_in_executor(
                None,
                self.command_control_crew.process_transaction,
                {
                    'transaction_id': transaction_id,
                    'user_id': user_id,
                    'amount': amount,
                    'description': description,
                    'transaction_date': transaction_date,
                    'transaction_type': transaction_type,
                    'source': source
                }
            )

            if processing_result['status'] == 'SUCCESS':
                # Create pending journal entry
                journal_entry_id = await self.journal_service.create_pending_entry(
                    user_id=user_id,
                    transaction_id=transaction_id,
                    categorization=processing_result['categorization'],
                    account_mapping=processing_result['account_mapping'],
                    amount=amount,
                    description=description,
                    transaction_date=transaction_date
                )

                # Publish success event
                await self.messaging_service.publish_event('transaction.events', {
                    'eventType': 'TransactionProcessingCompleted',
                    'transactionId': transaction_id,
                    'userId': user_id,
                    'journalEntryId': journal_entry_id,
                    'categorization': processing_result['categorization'],
                    'accountMapping': processing_result['account_mapping'],
                    'confidenceScore': processing_result['confidence_score'],
                    'aiExplanation': processing_result['explanation'],
                    'requiresReview': processing_result['requires_review'],
                    'timestamp': datetime.utcnow().isoformat()
                })
            else:
                # Publish failure event
                await self.messaging_service.publish_event('transaction.events', {
                    'eventType': 'TransactionProcessingFailed',
                    'transactionId': transaction_id,
                    'userId': user_id,
                    'error': processing_result.get('error', 'Processing failed'),
                    'timestamp': datetime.utcnow().isoformat()
                })

        except Exception as e:
            self.logger.error(f"Failed to process transaction {transaction_id}: {str(e)}")

            # Publish failure event
            await self.messaging_service.publish_event('transaction.events', {
                'eventType': 'TransactionProcessingFailed',
                'transactionId': transaction_id,
                'userId': user_id,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            })
```

### 2. Command & Control CrewAI Implementation

#### CrewAI Crew for Transaction Processing:

**command_control_crew.py:**
```python
from crewai import Agent, Task, Crew
from crewai.tools import BaseTool
from typing import Dict, Any, List
import json
import logging
from datetime import datetime
from dataclasses import dataclass

@dataclass
class TransactionCategorization:
    category_id: str
    category_name: str
    confidence_score: float
    explanation: str
    requires_review: bool

@dataclass
class AccountMapping:
    debit_account_id: str
    credit_account_id: str
    debit_account_name: str
    credit_account_name: str

class ChartOfAccountsTool(BaseTool):
    """Tool for accessing Chart of Accounts data."""

    name: str = "chart_of_accounts_lookup"
    description: str = "Look up account information from the Chart of Accounts"

    def _run(self, account_type: str, category: str = None) -> str:
        """Look up accounts by type and category."""
        # This would integrate with the actual Chart of Accounts service
        # For now, returning sample Zambian business accounts
        accounts = {
            "ASSET": {
                "CASH": [
                    {"id": "1001", "name": "MTN Mobile Money", "code": "1001"},
                    {"id": "1002", "name": "Airtel Money", "code": "1002"},
                    {"id": "1003", "name": "Bank Account - Stanbic", "code": "1003"}
                ]
            },
            "EXPENSE": {
                "TRANSPORT": [
                    {"id": "5001", "name": "Transport & Fuel", "code": "5001"}
                ],
                "FOOD": [
                    {"id": "5002", "name": "Meals & Entertainment", "code": "5002"}
                ],
                "UTILITIES": [
                    {"id": "5003", "name": "Utilities & Airtime", "code": "5003"}
                ],
                "BUSINESS": [
                    {"id": "5004", "name": "Business Expenses", "code": "5004"}
                ]
            },
            "INCOME": {
                "SALES": [
                    {"id": "4001", "name": "Sales Revenue", "code": "4001"}
                ],
                "SERVICES": [
                    {"id": "4002", "name": "Service Revenue", "code": "4002"}
                ]
            }
        }

        if account_type in accounts:
            if category and category in accounts[account_type]:
                return json.dumps(accounts[account_type][category])
            else:
                return json.dumps(accounts[account_type])

        return json.dumps([])

class CommandControlCrew:
    """
    CrewAI crew for processing transactions and creating accounting entries.
    Implements the Command & Control workflow from the master blueprint.
    """

    def __init__(self):
        self.chart_tool = ChartOfAccountsTool()
        self.logger = logging.getLogger(__name__)

    def create_agents(self):
        """Create the three specialized agents for transaction processing."""

        # Agent 1: TransactionAnalyst
        transaction_analyst = Agent(
            role="Zambian Business Transaction Analyst",
            goal="To analyze mobile money transactions and understand their business context within Zambian commerce patterns",
            backstory="""You are an expert in Zambian business transactions, particularly mobile money payments. You understand local business patterns, common transaction types like airtime purchases, transport payments, market purchases, business-to-business transfers, and salary payments. You can identify transaction patterns and categorize them according to standard business accounting practices used in Zambia.""",
            tools=[self.chart_tool],
            verbose=True,
            allow_delegation=False
        )

        # Agent 2: AccountingSpecialist
        accounting_specialist = Agent(
            role="Double-Entry Bookkeeping Expert",
            goal="To map categorized transactions to the correct Chart of Accounts and create proper double-entry journal entries",
            backstory="""You are a certified accountant specializing in double-entry bookkeeping and Chart of Accounts management. You understand how to map business transactions to the correct debit and credit accounts, ensuring that every transaction maintains the fundamental accounting equation (Assets = Liabilities + Equity). You know Zambian accounting standards and can create proper journal entries for mobile money transactions.""",
            tools=[self.chart_tool],
            verbose=True,
            allow_delegation=False
        )

        # Agent 3: QualityController
        quality_controller = Agent(
            role="Financial Data Quality Auditor",
            goal="To validate the proposed journal entries for accuracy, completeness, and compliance with accounting principles",
            backstory="""You are a meticulous financial auditor who ensures that all journal entries are mathematically correct, properly categorized, and comply with double-entry bookkeeping principles. You verify that debits equal credits, that account mappings are appropriate, and that the transaction makes business sense. You flag any entries that require human review due to complexity or uncertainty.""",
            verbose=True,
            allow_delegation=False
        )

        return transaction_analyst, accounting_specialist, quality_controller

    def create_tasks(self, transaction_data: Dict[str, Any]):
        """Create the sequential tasks for transaction processing."""

        transaction_analyst, accounting_specialist, quality_controller = self.create_agents()

        # Task 1: Analyze and categorize transaction
        analyze_task = Task(
            description=f"""
            Analyze this mobile money transaction and categorize it according to Zambian business patterns:

            Transaction Details:
            - Amount: K{transaction_data['amount']}
            - Description: "{transaction_data['description']}"
            - Type: {transaction_data['transaction_type']}
            - Date: {transaction_data['transaction_date']}
            - Source: {transaction_data['source']}

            Your task is to:
            1. Identify the business nature of this transaction
            2. Categorize it into appropriate business categories (Transport, Food, Utilities, Business Expenses, Sales, etc.)
            3. Determine if this is an expense, income, or transfer
            4. Assess the confidence level of your categorization
            5. Provide a clear explanation of your reasoning
            6. Flag if this transaction requires human review due to complexity or ambiguity

            Consider Zambian context:
            - Mobile money transaction patterns
            - Common business expenses and income sources
            - Local merchant and service provider names
            - Typical transaction amounts and descriptions
            """,
            agent=transaction_analyst,
            expected_output="A detailed categorization with business context, confidence score, and explanation"
        )

        # Task 2: Map to Chart of Accounts
        mapping_task = Task(
            description=f"""
            Based on the transaction analysis, map this transaction to the correct Chart of Accounts and create a proper double-entry journal entry.

            Your task is to:
            1. Use the chart_of_accounts_lookup tool to find appropriate accounts
            2. Determine the correct debit and credit accounts
            3. Ensure the journal entry follows double-entry bookkeeping principles
            4. Verify that the account mapping makes business sense
            5. Create a complete journal entry structure

            For mobile money transactions, consider:
            - Mobile money accounts are typically cash equivalents (Asset accounts)
            - Expenses should debit the appropriate expense account and credit the mobile money account
            - Income should debit the mobile money account and credit the appropriate revenue account
            - Transfers between accounts should debit one asset and credit another

            Provide the complete journal entry with:
            - Debit account ID and name
            - Credit account ID and name
            - Amount (ensuring debits = credits)
            - Clear description
            """,
            agent=accounting_specialist,
            expected_output="A complete double-entry journal entry with proper account mappings",
            context=[analyze_task]
        )

        # Task 3: Quality control and validation
        validate_task = Task(
            description=f"""
            Validate the proposed transaction categorization and journal entry for accuracy and compliance.

            Perform these validations:
            1. Verify that debits equal credits (fundamental accounting equation)
            2. Check that account mappings are appropriate for the transaction type
            3. Ensure the categorization makes business sense
            4. Validate that the journal entry follows double-entry bookkeeping principles
            5. Assess overall confidence in the processing
            6. Determine if human review is required

            Quality checks:
            - Mathematical accuracy (debits = credits)
            - Account classification correctness
            - Business logic validation
            - Compliance with accounting standards
            - Confidence assessment

            Provide a final validation report with:
            - Overall validation status (APPROVED, REQUIRES_REVIEW, REJECTED)
            - Confidence score (0-100)
            - List of any issues or concerns
            - Final recommendation for processing
            """,
            agent=quality_controller,
            expected_output="A comprehensive validation report with final processing recommendation",
            context=[analyze_task, mapping_task]
        )

        return [analyze_task, mapping_task, validate_task]

    def process_transaction(self, transaction_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a transaction using the Command & Control CrewAI crew.

        Args:
            transaction_data: Raw transaction data from RawTransactionReceivedEvent

        Returns:
            Dict containing categorization, account mapping, and validation results
        """
        try:
            self.logger.info(f"Starting transaction processing for {transaction_data['transaction_id']}")

            # Create the crew
            transaction_analyst, accounting_specialist, quality_controller = self.create_agents()
            tasks = self.create_tasks(transaction_data)

            crew = Crew(
                agents=[transaction_analyst, accounting_specialist, quality_controller],
                tasks=tasks,
                verbose=True,
                process="sequential"  # Tasks execute in order: Analyst -> Specialist -> Controller
            )

            # Execute the crew
            result = crew.kickoff()

            # Parse the final validation result
            processing_result = self._parse_crew_result(result, transaction_data)

            self.logger.info(f"Transaction processing completed for {transaction_data['transaction_id']} with confidence {processing_result.get('confidence_score', 0)}")

            return processing_result

        except Exception as e:
            self.logger.error(f"Transaction processing failed for {transaction_data['transaction_id']}: {str(e)}")
            return {
                'status': 'FAILED',
                'error': str(e),
                'confidence_score': 0,
                'requires_review': True
            }

    def _parse_crew_result(self, crew_result: str, transaction_data: Dict[str, Any]) -> Dict[str, Any]:
        """Parse the crew result into a structured format."""
        try:
            # This is a simplified parser - in practice, you'd have more robust parsing
            # The quality_controller should return structured data

            # Extract structured data from the result
            import re
            json_match = re.search(r'\{.*\}', crew_result, re.DOTALL)
            if json_match:
                data = json.loads(json_match.group())
            else:
                # Fallback parsing logic
                data = {
                    "status": "SUCCESS",
                    "confidence_score": 75,
                    "requires_review": True,
                    "categorization": {
                        "category_id": "5004",
                        "category_name": "Business Expenses",
                        "confidence_score": 75,
                        "explanation": "General business expense based on transaction description"
                    },
                    "account_mapping": {
                        "debit_account_id": "5004",
                        "credit_account_id": "1001",
                        "debit_account_name": "Business Expenses",
                        "credit_account_name": "MTN Mobile Money"
                    }
                }

            return {
                'status': 'SUCCESS',
                'categorization': data.get('categorization', {}),
                'account_mapping': data.get('account_mapping', {}),
                'confidence_score': data.get('confidence_score', 75),
                'explanation': data.get('explanation', 'AI-powered categorization'),
                'requires_review': data.get('requires_review', True)
            }

        except Exception as e:
            self.logger.error(f"Failed to parse crew result: {str(e)}")
            return {
                'status': 'FAILED',
                'error': f'Failed to parse crew result: {str(e)}',
                'confidence_score': 0,
                'requires_review': True
            }
```

### 3. Frontend Implementation - Transaction Review Interface

#### Transaction Review Dashboard:

**TransactionReviewDashboard.tsx:**
```typescript
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, AlertTriangle, Eye, DollarSign } from 'lucide-react';
import { transactionService } from '@/services/transactionService';

interface PendingTransaction {
  id: string;
  amount: number;
  description: string;
  date: string;
  source: string;
  categorization: {
    categoryName: string;
    confidenceScore: number;
    explanation: string;
  };
  accountMapping: {
    debitAccountName: string;
    creditAccountName: string;
  };
  requiresReview: boolean;
  journalEntryId: string;
}

export const TransactionReviewDashboard: React.FC = () => {
  const [pendingTransactions, setPendingTransactions] = useState<PendingTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [processingIds, setProcessingIds] = useState<Set<string>>(new Set());

  useEffect(() => {
    loadPendingTransactions();
  }, []);

  const loadPendingTransactions = async () => {
    try {
      setLoading(true);
      const transactions = await transactionService.getPendingReview();
      setPendingTransactions(transactions);
    } catch (error) {
      console.error('Failed to load pending transactions:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async (transactionId: string, journalEntryId: string) => {
    setProcessingIds(prev => new Set(prev).add(transactionId));
    try {
      await transactionService.approveTransaction(transactionId, journalEntryId);
      setPendingTransactions(prev => prev.filter(t => t.id !== transactionId));
    } catch (error) {
      console.error('Failed to approve transaction:', error);
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(transactionId);
        return newSet;
      });
    }
  };

  const handleReject = async (transactionId: string, journalEntryId: string) => {
    setProcessingIds(prev => new Set(prev).add(transactionId));
    try {
      await transactionService.rejectTransaction(transactionId, journalEntryId);
      // Keep in list for manual categorization
      setPendingTransactions(prev =>
        prev.map(t => t.id === transactionId
          ? { ...t, requiresReview: true, categorization: { ...t.categorization, confidenceScore: 0 } }
          : t
        )
      );
    } catch (error) {
      console.error('Failed to reject transaction:', error);
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(transactionId);
        return newSet;
      });
    }
  };

  const getConfidenceBadge = (score: number) => {
    if (score >= 90) return <Badge className="bg-green-100 text-green-800">High Confidence</Badge>;
    if (score >= 70) return <Badge className="bg-yellow-100 text-yellow-800">Medium Confidence</Badge>;
    return <Badge className="bg-red-100 text-red-800">Low Confidence</Badge>;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZM', {
      style: 'currency',
      currency: 'ZMW',
      minimumFractionDigits: 2
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="transaction-review-dashboard max-w-6xl mx-auto p-6">
      <div className="header mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Transaction Review</h1>
        <p className="text-gray-600 mt-2">
          Review AI-categorized transactions and approve journal entries
        </p>
      </div>

      {pendingTransactions.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <CheckCircle className="mx-auto h-12 w-12 text-green-500 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">All Caught Up!</h3>
            <p className="text-gray-600">No transactions pending review at the moment.</p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {pendingTransactions.map((transaction) => (
            <Card key={transaction.id} className="transaction-card">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">
                    {formatCurrency(transaction.amount)}
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    {getConfidenceBadge(transaction.categorization.confidenceScore)}
                    <Badge variant="outline">{transaction.source}</Badge>
                  </div>
                </div>
                <p className="text-sm text-gray-600">{transaction.description}</p>
                <p className="text-xs text-gray-500">{new Date(transaction.date).toLocaleDateString()}</p>
              </CardHeader>

              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* AI Categorization */}
                  <div className="categorization-section">
                    <h4 className="font-medium text-gray-900 mb-3">AI Categorization</h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Category:</span>
                        <span className="font-medium">{transaction.categorization.categoryName}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Confidence:</span>
                        <span className="font-medium">{transaction.categorization.confidenceScore}%</span>
                      </div>
                    </div>
                    <Alert className="mt-3">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription className="text-sm">
                        {transaction.categorization.explanation}
                      </AlertDescription>
                    </Alert>
                  </div>

                  {/* Journal Entry */}
                  <div className="journal-entry-section">
                    <h4 className="font-medium text-gray-900 mb-3">Proposed Journal Entry</h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Debit:</span>
                        <span className="font-medium">{transaction.accountMapping.debitAccountName}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Credit:</span>
                        <span className="font-medium">{transaction.accountMapping.creditAccountName}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Amount:</span>
                        <span className="font-medium">{formatCurrency(transaction.amount)}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center justify-end gap-3 mt-6 pt-4 border-t">
                  <Button
                    variant="outline"
                    onClick={() => handleReject(transaction.id, transaction.journalEntryId)}
                    disabled={processingIds.has(transaction.id)}
                    className="flex items-center gap-2"
                  >
                    <XCircle className="h-4 w-4" />
                    Reject & Recategorize
                  </Button>

                  <Button
                    onClick={() => handleApprove(transaction.id, transaction.journalEntryId)}
                    disabled={processingIds.has(transaction.id)}
                    className="flex items-center gap-2"
                  >
                    <CheckCircle className="h-4 w-4" />
                    Approve & Post
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};
```

### 4. Backend Implementation - Journal Integration

#### Transaction Processing Service:

**TransactionProcessingService.java:**
```java
@Service
@Transactional
public class TransactionProcessingService {

    private final TransactionRepository transactionRepository;
    private final JournalEntryService journalEntryService;
    private final CategoryService categoryService;
    private final ApplicationEventPublisher eventPublisher;

    public TransactionProcessingService(TransactionRepository transactionRepository,
                                     JournalEntryService journalEntryService,
                                     CategoryService categoryService,
                                     ApplicationEventPublisher eventPublisher) {
        this.transactionRepository = transactionRepository;
        this.journalEntryService = journalEntryService;
        this.categoryService = categoryService;
        this.eventPublisher = eventPublisher;
    }

    @EventListener
    @Async
    public void handleTransactionProcessingCompleted(TransactionProcessingCompletedEvent event) {
        try {
            // Create or update transaction record
            Transaction transaction = transactionRepository.findByExternalId(event.getTransactionId())
                .orElse(new Transaction());

            transaction.setExternalId(event.getTransactionId());
            transaction.setUserId(event.getUserId());
            transaction.setJournalEntryId(event.getJournalEntryId());
            transaction.setStatus(TransactionStatus.PENDING_REVIEW);
            transaction.setAiCategoryId(event.getCategorization().getCategoryId());
            transaction.setAiConfidence(event.getConfidenceScore());
            transaction.setAiExplanation(event.getAiExplanation());
            transaction.setRequiresReview(event.getRequiresReview());
            transaction.setProcessedAt(LocalDateTime.now());

            Transaction savedTransaction = transactionRepository.save(transaction);

            // If high confidence and doesn't require review, auto-approve
            if (event.getConfidenceScore() >= 95 && !event.getRequiresReview()) {
                approveTransaction(savedTransaction.getId(), event.getJournalEntryId(), "AUTO_APPROVED");
            }

        } catch (Exception e) {
            log.error("Failed to handle transaction processing completed event: {}", e.getMessage(), e);
        }
    }

    public void approveTransaction(Long transactionId, String journalEntryId, String approvedBy) {
        try {
            Transaction transaction = transactionRepository.findById(transactionId)
                .orElseThrow(() -> new EntityNotFoundException("Transaction not found"));

            // Finalize the journal entry
            JournalEntry finalizedEntry = journalEntryService.finalizeEntry(journalEntryId, approvedBy);

            // Update transaction status
            transaction.setStatus(TransactionStatus.APPROVED);
            transaction.setApprovedAt(LocalDateTime.now());
            transaction.setApprovedBy(approvedBy);
            transaction.setFinalJournalEntryId(finalizedEntry.getId().toString());
            transactionRepository.save(transaction);

            // Publish approval event
            eventPublisher.publishEvent(new TransactionApprovedEvent(
                transaction.getExternalId(),
                transaction.getUserId(),
                finalizedEntry.getId().toString(),
                approvedBy
            ));

        } catch (Exception e) {
            log.error("Failed to approve transaction {}: {}", transactionId, e.getMessage(), e);
            throw new BusinessException("Failed to approve transaction: " + e.getMessage());
        }
    }

    public void rejectTransaction(Long transactionId, String journalEntryId, String rejectedBy, String reason) {
        try {
            Transaction transaction = transactionRepository.findById(transactionId)
                .orElseThrow(() -> new EntityNotFoundException("Transaction not found"));

            // Cancel the journal entry
            journalEntryService.cancelEntry(journalEntryId, rejectedBy, reason);

            // Update transaction status
            transaction.setStatus(TransactionStatus.REJECTED);
            transaction.setRejectedAt(LocalDateTime.now());
            transaction.setRejectedBy(rejectedBy);
            transaction.setRejectionReason(reason);
            transactionRepository.save(transaction);

            // Publish rejection event for reprocessing
            eventPublisher.publishEvent(new TransactionRejectedEvent(
                transaction.getExternalId(),
                transaction.getUserId(),
                reason,
                rejectedBy
            ));

        } catch (Exception e) {
            log.error("Failed to reject transaction {}: {}", transactionId, e.getMessage(), e);
            throw new BusinessException("Failed to reject transaction: " + e.getMessage());
        }
    }

    public List<TransactionReviewDTO> getPendingReviewTransactions(String userId) {
        List<Transaction> pendingTransactions = transactionRepository
            .findByUserIdAndStatusOrderByCreatedAtDesc(userId, TransactionStatus.PENDING_REVIEW);

        return pendingTransactions.stream()
            .map(this::mapToReviewDTO)
            .collect(Collectors.toList());
    }

    private TransactionReviewDTO mapToReviewDTO(Transaction transaction) {
        // Get journal entry details
        JournalEntry journalEntry = journalEntryService.getEntry(transaction.getJournalEntryId());

        // Get category details
        Category category = categoryService.getCategory(transaction.getAiCategoryId());

        return TransactionReviewDTO.builder()
            .id(transaction.getId())
            .externalId(transaction.getExternalId())
            .amount(journalEntry.getTotalAmount())
            .description(journalEntry.getDescription())
            .date(journalEntry.getTransactionDate())
            .source(transaction.getSource())
            .categorization(CategorizationDTO.builder()
                .categoryName(category.getName())
                .confidenceScore(transaction.getAiConfidence())
                .explanation(transaction.getAiExplanation())
                .build())
            .accountMapping(AccountMappingDTO.builder()
                .debitAccountName(journalEntry.getDebitAccountName())
                .creditAccountName(journalEntry.getCreditAccountName())
                .build())
            .requiresReview(transaction.getRequiresReview())
            .journalEntryId(transaction.getJournalEntryId())
            .build();
    }
}
```

#### REST Controller for Transaction Review:

**TransactionReviewController.java:**
```java
@RestController
@RequestMapping("/api/v1/transactions")
@Validated
public class TransactionReviewController {

    private final TransactionProcessingService transactionProcessingService;
    private final SecurityService securityService;

    public TransactionReviewController(TransactionProcessingService transactionProcessingService,
                                     SecurityService securityService) {
        this.transactionProcessingService = transactionProcessingService;
        this.securityService = securityService;
    }

    @GetMapping("/pending-review")
    public ResponseEntity<List<TransactionReviewDTO>> getPendingReview(Authentication authentication) {
        String userId = securityService.getCurrentUserId(authentication);
        List<TransactionReviewDTO> pendingTransactions =
            transactionProcessingService.getPendingReviewTransactions(userId);
        return ResponseEntity.ok(pendingTransactions);
    }

    @PostMapping("/{transactionId}/approve")
    public ResponseEntity<TransactionApprovalResponse> approveTransaction(
            @PathVariable Long transactionId,
            @RequestBody @Valid TransactionApprovalRequest request,
            Authentication authentication) {

        String userId = securityService.getCurrentUserId(authentication);

        try {
            transactionProcessingService.approveTransaction(
                transactionId,
                request.getJournalEntryId(),
                userId
            );

            return ResponseEntity.ok(TransactionApprovalResponse.builder()
                .success(true)
                .message("Transaction approved successfully")
                .build());

        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(TransactionApprovalResponse.builder()
                    .success(false)
                    .message("Failed to approve transaction: " + e.getMessage())
                    .build());
        }
    }

    @PostMapping("/{transactionId}/reject")
    public ResponseEntity<TransactionRejectionResponse> rejectTransaction(
            @PathVariable Long transactionId,
            @RequestBody @Valid TransactionRejectionRequest request,
            Authentication authentication) {

        String userId = securityService.getCurrentUserId(authentication);

        try {
            transactionProcessingService.rejectTransaction(
                transactionId,
                request.getJournalEntryId(),
                userId,
                request.getReason()
            );

            return ResponseEntity.ok(TransactionRejectionResponse.builder()
                .success(true)
                .message("Transaction rejected successfully")
                .build());

        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(TransactionRejectionResponse.builder()
                    .success(false)
                    .message("Failed to reject transaction: " + e.getMessage())
                    .build());
        }
    }

    @GetMapping("/{transactionId}/journal-entry")
    public ResponseEntity<JournalEntryDTO> getJournalEntry(
            @PathVariable Long transactionId,
            Authentication authentication) {

        String userId = securityService.getCurrentUserId(authentication);

        try {
            JournalEntryDTO journalEntry = transactionProcessingService
                .getTransactionJournalEntry(transactionId, userId);
            return ResponseEntity.ok(journalEntry);

        } catch (EntityNotFoundException e) {
            return ResponseEntity.notFound().build();
        }
    }
}
```

---

## Acceptance Criteria

### Core AI Processing:
- [ ] **Event-Driven Processing**: Receives `RawTransactionReceivedEvent` and processes automatically
- [ ] **Command & Control CrewAI**: Three specialized agents (Analyst, Specialist, Controller) work sequentially
- [ ] **Zambian Business Context**: Understands local transaction patterns, merchants, and business types
- [ ] **High Accuracy Categorization**: Achieves >85% accuracy on common Zambian mobile money transactions
- [ ] **Confidence Scoring**: Provides confidence scores (0-100) for all categorizations
- [ ] **Account Mapping**: Maps transactions to correct Chart of Accounts with proper debit/credit logic

### Double-Entry Integration:
- [ ] **Automatic Journal Entries**: Creates proper double-entry journal entries for all transactions
- [ ] **Mathematical Validation**: Ensures debits always equal credits in generated entries
- [ ] **Account Validation**: Verifies account mappings against Chart of Accounts from Story 2.1
- [ ] **Pending Entry Creation**: Creates pending journal entries that require user approval
- [ ] **Entry Finalization**: Converts approved pending entries to final journal entries

### User Review Interface:
- [ ] **Clean Review Dashboard**: Displays pending transactions with AI categorizations and proposed journal entries
- [ ] **Confidence Indicators**: Clear visual indicators for AI confidence levels (High/Medium/Low)
- [ ] **One-Click Approval**: Users can approve transactions and journal entries with single click
- [ ] **Rejection & Recategorization**: Users can reject AI suggestions and trigger manual categorization
- [ ] **Journal Entry Preview**: Shows complete debit/credit details before approval
- [ ] **Bulk Operations**: Support for approving/rejecting multiple transactions

### Event-Driven Architecture:
- [ ] **Reliable Event Processing**: Handles `RawTransactionReceivedEvent` from both API and PDF sources
- [ ] **Status Events**: Publishes `TransactionProcessingStarted`, `TransactionProcessingCompleted`, `TransactionProcessingFailed`
- [ ] **Approval Events**: Publishes `TransactionApprovedEvent` and `TransactionRejectedEvent`
- [ ] **Dead Letter Handling**: Failed processing events routed to dead letter queues
- [ ] **Cross-Environment Compatibility**: Works consistently across RabbitMQ (local) and Azure Service Bus (prod)

### Performance & Quality:
- [ ] **Fast Processing**: <30 seconds for CrewAI transaction processing
- [ ] **Real-Time Updates**: UI updates immediately when transactions are processed
- [ ] **High Availability**: Graceful degradation when AI service is unavailable
- [ ] **Audit Trail**: Complete audit trail for all approvals, rejections, and manual categorizations
- [ ] **Error Recovery**: Automatic retry mechanisms for transient failures

---

## API Contracts

### Transaction Review API:

```typescript
interface TransactionReviewDTO {
  id: string;
  externalId: string;
  amount: number;
  description: string;
  date: string;
  source: string;
  categorization: {
    categoryName: string;
    confidenceScore: number;
    explanation: string;
  };
  accountMapping: {
    debitAccountName: string;
    creditAccountName: string;
  };
  requiresReview: boolean;
  journalEntryId: string;
}

interface TransactionApprovalRequest {
  journalEntryId: string;
  notes?: string;
}

interface TransactionApprovalResponse {
  success: boolean;
  message: string;
  finalJournalEntryId?: string;
}

interface TransactionRejectionRequest {
  journalEntryId: string;
  reason: string;
  newCategoryId?: string;
}

interface TransactionRejectionResponse {
  success: boolean;
  message: string;
}

interface TransactionReviewAPI {
  GET /api/v1/transactions/pending-review: {
    response: TransactionReviewDTO[];
    errors: { 401: "Unauthorized", 500: "Server error" };
  };

  POST /api/v1/transactions/{id}/approve: {
    body: TransactionApprovalRequest;
    response: TransactionApprovalResponse;
    errors: {
      400: "Invalid request",
      404: "Transaction not found",
      409: "Transaction already processed"
    };
  };

  POST /api/v1/transactions/{id}/reject: {
    body: TransactionRejectionRequest;
    response: TransactionRejectionResponse;
    errors: {
      400: "Invalid request",
      404: "Transaction not found",
      409: "Transaction already processed"
    };
  };

  GET /api/v1/transactions/{id}/journal-entry: {
    response: JournalEntryDTO;
    errors: {
      404: "Transaction or journal entry not found",
      403: "Access denied"
    };
  };
}
```

### Event Contracts:

```typescript
// Input event from Story 3.1
interface RawTransactionReceivedEvent {
  eventType: 'RawTransactionReceived';
  transactionId: string;
  userId: string;
  amount: number;
  description: string;
  transactionDate: string;
  transactionType: 'DEBIT' | 'CREDIT' | 'TRANSFER';
  source: 'MTN_API' | 'PDF_UPLOAD';
  sourceReference: string;
  timestamp: string;
}

// Output events
interface TransactionProcessingStartedEvent {
  eventType: 'TransactionProcessingStarted';
  transactionId: string;
  userId: string;
  timestamp: string;
}

interface TransactionProcessingCompletedEvent {
  eventType: 'TransactionProcessingCompleted';
  transactionId: string;
  userId: string;
  journalEntryId: string;
  categorization: {
    categoryId: string;
    categoryName: string;
    confidenceScore: number;
    explanation: string;
  };
  accountMapping: {
    debitAccountId: string;
    creditAccountId: string;
    debitAccountName: string;
    creditAccountName: string;
  };
  confidenceScore: number;
  aiExplanation: string;
  requiresReview: boolean;
  timestamp: string;
}

interface TransactionApprovedEvent {
  eventType: 'TransactionApproved';
  transactionId: string;
  userId: string;
  finalJournalEntryId: string;
  approvedBy: string;
  timestamp: string;
}

interface TransactionRejectedEvent {
  eventType: 'TransactionRejected';
  transactionId: string;
  userId: string;
  reason: string;
  rejectedBy: string;
  timestamp: string;
}
```

### CrewAI Integration Contracts:

```typescript
interface TransactionProcessingRequest {
  transaction_id: string;
  user_id: string;
  amount: number;
  description: string;
  transaction_date: string;
  transaction_type: string;
  source: string;
}

interface TransactionProcessingResult {
  status: 'SUCCESS' | 'FAILED';
  categorization?: {
    category_id: string;
    category_name: string;
    confidence_score: number;
    explanation: string;
  };
  account_mapping?: {
    debit_account_id: string;
    credit_account_id: string;
    debit_account_name: string;
    credit_account_name: string;
  };
  confidence_score: number;
  explanation: string;
  requires_review: boolean;
  error?: string;
}
```

---

## Error Handling & Edge Cases

### AI Processing Failures:
- **CrewAI Service Unavailable**: Queue transactions for retry with exponential backoff
- **Low Confidence Categorization**: Automatically flag for manual review (confidence < 70%)
- **Account Mapping Failures**: Fallback to default expense/cash accounts with manual review flag
- **Parsing Errors**: Route to dead letter queue with detailed error logging

### Journal Entry Validation:
- **Unbalanced Entries**: Reject and retry with error correction
- **Invalid Account References**: Validate against Chart of Accounts before creation
- **Duplicate Transaction Processing**: Idempotency checks to prevent duplicate journal entries
- **Concurrent Approval/Rejection**: Optimistic locking to prevent race conditions

### User Interface Errors:
- **Network Failures**: Retry mechanisms with user feedback
- **Stale Data**: Real-time updates via WebSocket to prevent outdated information
- **Bulk Operation Failures**: Partial success reporting with detailed error messages
- **Session Timeouts**: Graceful session handling with auto-save of pending actions

---

## Performance Requirements

### Processing Performance:
- **CrewAI Processing**: <30 seconds per transaction (95th percentile)
- **Journal Entry Creation**: <2 seconds per entry
- **UI Response Time**: <500ms for approval/rejection actions
- **Bulk Operations**: Process up to 100 transactions in <5 minutes

### Scalability:
- **Concurrent Processing**: Handle 50+ simultaneous transaction processing requests
- **Queue Throughput**: Process 1000+ transactions per hour
- **Database Performance**: <100ms query response times for transaction lists
- **Memory Usage**: <512MB per CrewAI crew instance

### Availability:
- **Service Uptime**: 99.9% availability during business hours
- **Graceful Degradation**: Continue basic operations when AI service is down
- **Recovery Time**: <5 minutes for service restart after failures
- **Data Consistency**: Zero data loss during system failures

---

## Definition of Done

### Core Functionality:
- [ ] **Event Processing**: Successfully processes `RawTransactionReceivedEvent` from both MTN API and PDF sources
- [ ] **CrewAI Integration**: Command & Control crew processes transactions with three specialized agents
- [ ] **Journal Entry Creation**: Creates proper double-entry journal entries with correct account mappings
- [ ] **User Review Interface**: Clean, intuitive interface for reviewing and approving transactions
- [ ] **Approval Workflow**: Complete approval/rejection workflow with proper state management

### Technical Implementation:
- [ ] **Event-Driven Architecture**: Proper event publishing and consumption with reliable messaging
- [ ] **Database Integration**: Seamless integration with Chart of Accounts and Journal Entry systems
- [ ] **API Implementation**: Complete REST API for transaction review operations
- [ ] **Error Handling**: Comprehensive error handling with proper user feedback
- [ ] **Performance Optimization**: Meets all performance requirements under load

### Quality Assurance:
- [ ] **Unit Tests**: >90% code coverage for all business logic
- [ ] **Integration Tests**: End-to-end tests for complete transaction processing flow
- [ ] **AI Accuracy Tests**: Validation of >85% categorization accuracy on test dataset
- [ ] **Performance Tests**: Load testing confirms performance requirements
- [ ] **Security Tests**: Proper authorization and data protection validation

### Documentation & Deployment:
- [ ] **API Documentation**: Complete OpenAPI specification with examples
- [ ] **Deployment Scripts**: Docker containers and deployment configurations
- [ ] **Monitoring Setup**: Application metrics, logging, and alerting
- [ ] **User Documentation**: User guide for transaction review interface
- [ ] **Runbook**: Operational procedures for troubleshooting and maintenance

### Business Validation:
- [ ] **Zambian Context Testing**: Validation with real Zambian mobile money transaction patterns
- [ ] **Accounting Accuracy**: Verification that all journal entries are mathematically correct
- [ ] **User Acceptance**: Stakeholder approval of the review interface and workflow
- [ ] **Performance Validation**: Confirmation that processing times meet business requirements
- [ ] **Integration Validation**: Successful integration with Stories 2.1, 2.2, and 3.1

---

## Dependencies

### Required Stories (Must be completed first):
- **Story 2.1**: Chart of Accounts & Category Management - Required for account mapping
- **Story 2.2**: Double-Entry Journal System - Required for journal entry creation
- **Story 3.1**: Dual-Track Data Ingestion - Required for `RawTransactionReceivedEvent`

### External Dependencies:
- **Google Gemini 2.5 Pro/Flash**: For CrewAI agent LLM capabilities
- **CrewAI Framework**: For multi-agent transaction processing
- **Event Bus**: RabbitMQ (local) or Azure Service Bus (production)
- **Database**: PostgreSQL for transaction and journal entry storage

---

## Implementation Notes

### CrewAI Best Practices:
- Use sequential processing for transaction analysis → account mapping → validation
- Implement proper tool integration for Chart of Accounts lookup
- Ensure agents have clear, specific roles and responsibilities
- Include Zambian business context in agent backstories and prompts

### Event-Driven Considerations:
- Implement idempotency for all event handlers
- Use correlation IDs for tracking transaction processing across services
- Implement proper dead letter queue handling for failed processing
- Ensure event ordering for transaction state changes

### Accounting Integration:
- Always validate journal entries for mathematical correctness
- Implement proper audit trails for all accounting changes
- Ensure integration with existing Chart of Accounts structure
- Maintain referential integrity between transactions and journal entries

### User Experience:
- Provide clear confidence indicators for AI decisions
- Implement real-time updates for processing status
- Ensure intuitive approval/rejection workflows
- Include helpful explanations for AI categorization decisions

---

**Status**: 🔄 **READY FOR DEVELOPMENT**
**Next Story**: [Story 11.1+11.2: The 'Oode' Conversational Controller](../epic-11/story-11.1-conversational-interface.md)
```

