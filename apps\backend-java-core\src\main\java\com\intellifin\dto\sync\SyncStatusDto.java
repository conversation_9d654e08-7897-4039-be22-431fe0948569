package com.intellifin.dto.sync;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * DTO for sync status information
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SyncStatusDto {

    private UUID accountId;
    private OffsetDateTime lastSync;
    private Integer pendingTransactions;
    private SyncStatus syncStatus;
    private String errorMessage;
    private OffsetDateTime nextSync;
    private Integer totalSyncedTransactions;
    private Integer failedTransactions;
    private SyncType syncType;

    public enum SyncStatus {
        ACTIVE,
        FAILED,
        PAUSED,
        DISABLED
    }

    public enum SyncType {
        WEBHOOK,
        POLLING,
        MANUAL
    }
}
