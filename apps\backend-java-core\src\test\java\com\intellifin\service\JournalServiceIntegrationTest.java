package com.intellifin.service;

import com.intellifin.model.*;
import com.intellifin.repository.AccountRepository;
import com.intellifin.repository.CategoryRepository;
import com.intellifin.repository.JournalEntryRepository;
import com.intellifin.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class JournalServiceIntegrationTest {

    @Autowired
    private JournalService journalService;

    @Autowired
    private JournalEntryRepository journalEntryRepository;

    @Autowired
    private AccountRepository accountRepository;

    @Autowired
    private CategoryRepository categoryRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AuthService authService;

    private User testUser;
    private Account cashAccount;
    private Account expenseAccount;
    private Account incomeAccount;
    private Category expenseCategory;
    private Category incomeCategory;

    @BeforeEach
    void setUp() {
        // Create test user
        testUser = User.builder()
                .email("<EMAIL>")
                .firstName("Test")
                .lastName("User")
                .emailVerified(true)
                .accountLocked(false)
                .build();
        testUser = userRepository.save(testUser);

        // Create test accounts
        cashAccount = Account.builder()
                .code("1100")
                .name("Cash")
                .type(Account.AccountType.ASSET)
                .normalBalance(Account.NormalBalance.DEBIT)
                .isActive(true)
                .build();
        cashAccount = accountRepository.save(cashAccount);

        expenseAccount = Account.builder()
                .code("5100")
                .name("Office Expenses")
                .type(Account.AccountType.EXPENSE)
                .normalBalance(Account.NormalBalance.DEBIT)
                .isActive(true)
                .build();
        expenseAccount = accountRepository.save(expenseAccount);

        incomeAccount = Account.builder()
                .code("4100")
                .name("Service Revenue")
                .type(Account.AccountType.INCOME)
                .normalBalance(Account.NormalBalance.CREDIT)
                .isActive(true)
                .build();
        incomeAccount = accountRepository.save(incomeAccount);

        // Create test categories
        expenseCategory = Category.builder()
                .name("Office Supplies")
                .type(Category.CategoryType.EXPENSE)
                .account(expenseAccount)
                .user(testUser)
                .isActive(true)
                .build();
        expenseCategory = categoryRepository.save(expenseCategory);

        incomeCategory = Category.builder()
                .name("Consulting Services")
                .type(Category.CategoryType.INCOME)
                .account(incomeAccount)
                .user(testUser)
                .isActive(true)
                .build();
        incomeCategory = categoryRepository.save(incomeCategory);
    }

    @Test
    void testCreateBalancedJournalEntry() {
        // Create a balanced journal entry
        JournalEntry journalEntry = JournalEntry.builder()
                .user(testUser)
                .description("Test expense transaction")
                .totalAmount(new BigDecimal("100.00"))
                .entryDate(OffsetDateTime.now())
                .sourceType("TEST")
                .sourceId("test-001")
                .build();

        // Add journal entry lines
        JournalEntryLine debitLine = JournalEntryLine.builder()
                .account(expenseAccount)
                .debitAmount(new BigDecimal("100.00"))
                .creditAmount(BigDecimal.ZERO)
                .description("Office supplies purchase")
                .lineNumber(1)
                .build();

        JournalEntryLine creditLine = JournalEntryLine.builder()
                .account(cashAccount)
                .debitAmount(BigDecimal.ZERO)
                .creditAmount(new BigDecimal("100.00"))
                .description("Cash payment")
                .lineNumber(2)
                .build();

        journalEntry.addLine(debitLine);
        journalEntry.addLine(creditLine);

        // Create the journal entry
        JournalEntry savedEntry = journalService.createJournalEntry(journalEntry);

        // Verify the entry was created correctly
        assertNotNull(savedEntry.getId());
        assertNotNull(savedEntry.getEntryNumber());
        assertEquals(JournalEntry.JournalEntryStatus.DRAFT, savedEntry.getStatus());
        assertEquals(testUser.getId(), savedEntry.getUser().getId());
        assertEquals(new BigDecimal("100.00"), savedEntry.getTotalAmount());
        assertEquals(2, savedEntry.getLines().size());
        assertTrue(savedEntry.isBalanced());

        // Verify the entry can be posted
        JournalEntry postedEntry = journalService.postJournalEntry(savedEntry.getId());
        assertEquals(JournalEntry.JournalEntryStatus.POSTED, postedEntry.getStatus());
        assertNotNull(postedEntry.getPostedAt());
    }

    @Test
    void testCreateUnbalancedJournalEntryFails() {
        // Create an unbalanced journal entry
        JournalEntry journalEntry = JournalEntry.builder()
                .user(testUser)
                .description("Unbalanced test entry")
                .totalAmount(new BigDecimal("100.00"))
                .entryDate(OffsetDateTime.now())
                .sourceType("TEST")
                .sourceId("test-002")
                .build();

        // Add unbalanced lines
        JournalEntryLine debitLine = JournalEntryLine.builder()
                .account(expenseAccount)
                .debitAmount(new BigDecimal("100.00"))
                .creditAmount(BigDecimal.ZERO)
                .description("Office supplies purchase")
                .lineNumber(1)
                .build();

        JournalEntryLine creditLine = JournalEntryLine.builder()
                .account(cashAccount)
                .debitAmount(BigDecimal.ZERO)
                .creditAmount(new BigDecimal("50.00")) // Unbalanced!
                .description("Partial cash payment")
                .lineNumber(2)
                .build();

        journalEntry.addLine(debitLine);
        journalEntry.addLine(creditLine);

        // Attempt to create the journal entry should fail
        assertThrows(IllegalArgumentException.class, () -> {
            journalService.createJournalEntry(journalEntry);
        });
    }

    @Test
    void testJournalEntryLifecycle() {
        // Create a journal entry
        JournalEntry journalEntry = createTestJournalEntry();
        JournalEntry savedEntry = journalService.createJournalEntry(journalEntry);

        // Verify initial state
        assertEquals(JournalEntry.JournalEntryStatus.DRAFT, savedEntry.getStatus());

        // Post the entry
        JournalEntry postedEntry = journalService.postJournalEntry(savedEntry.getId());
        assertEquals(JournalEntry.JournalEntryStatus.POSTED, postedEntry.getStatus());

        // Reverse the entry
        JournalEntry reversalEntry = journalService.reverseJournalEntry(postedEntry.getId(), "Test reversal");
        assertEquals(JournalEntry.JournalEntryStatus.POSTED, reversalEntry.getStatus());

        // Verify original entry is marked as reversed
        Optional<JournalEntry> originalEntry = journalService.getJournalEntryById(postedEntry.getId());
        assertTrue(originalEntry.isPresent());
        assertEquals(JournalEntry.JournalEntryStatus.REVERSED, originalEntry.get().getStatus());
    }

    @Test
    void testAccountBalanceCalculation() {
        // Create multiple journal entries affecting the cash account
        
        // Entry 1: Cash receipt (debit cash, credit income)
        JournalEntry incomeEntry = createIncomeJournalEntry(new BigDecimal("500.00"));
        JournalEntry savedIncomeEntry = journalService.createJournalEntry(incomeEntry);
        journalService.postJournalEntry(savedIncomeEntry.getId());

        // Entry 2: Cash payment (debit expense, credit cash)
        JournalEntry expenseEntry = createExpenseJournalEntry(new BigDecimal("200.00"));
        JournalEntry savedExpenseEntry = journalService.createJournalEntry(expenseEntry);
        journalService.postJournalEntry(savedExpenseEntry.getId());

        // Calculate cash account balance
        BigDecimal cashBalance = journalService.calculateAccountBalance(cashAccount.getId());
        
        // Cash account should have a debit balance of 300.00 (500.00 - 200.00)
        assertEquals(new BigDecimal("300.00"), cashBalance);
    }

    private JournalEntry createTestJournalEntry() {
        return createExpenseJournalEntry(new BigDecimal("100.00"));
    }

    private JournalEntry createExpenseJournalEntry(BigDecimal amount) {
        JournalEntry journalEntry = JournalEntry.builder()
                .user(testUser)
                .description("Test expense - " + amount)
                .totalAmount(amount)
                .entryDate(OffsetDateTime.now())
                .sourceType("TEST")
                .sourceId("test-expense-" + System.currentTimeMillis())
                .build();

        JournalEntryLine debitLine = JournalEntryLine.builder()
                .account(expenseAccount)
                .debitAmount(amount)
                .creditAmount(BigDecimal.ZERO)
                .description("Expense transaction")
                .lineNumber(1)
                .build();

        JournalEntryLine creditLine = JournalEntryLine.builder()
                .account(cashAccount)
                .debitAmount(BigDecimal.ZERO)
                .creditAmount(amount)
                .description("Cash payment")
                .lineNumber(2)
                .build();

        journalEntry.addLine(debitLine);
        journalEntry.addLine(creditLine);

        return journalEntry;
    }

    private JournalEntry createIncomeJournalEntry(BigDecimal amount) {
        JournalEntry journalEntry = JournalEntry.builder()
                .user(testUser)
                .description("Test income - " + amount)
                .totalAmount(amount)
                .entryDate(OffsetDateTime.now())
                .sourceType("TEST")
                .sourceId("test-income-" + System.currentTimeMillis())
                .build();

        JournalEntryLine debitLine = JournalEntryLine.builder()
                .account(cashAccount)
                .debitAmount(amount)
                .creditAmount(BigDecimal.ZERO)
                .description("Cash received")
                .lineNumber(1)
                .build();

        JournalEntryLine creditLine = JournalEntryLine.builder()
                .account(incomeAccount)
                .debitAmount(BigDecimal.ZERO)
                .creditAmount(amount)
                .description("Service revenue")
                .lineNumber(2)
                .build();

        journalEntry.addLine(debitLine);
        journalEntry.addLine(creditLine);

        return journalEntry;
    }
}
