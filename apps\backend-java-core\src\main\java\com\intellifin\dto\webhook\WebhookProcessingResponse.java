package com.intellifin.dto.webhook;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * DTO for webhook processing responses
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WebhookProcessingResponse {

    private String status; // 'PROCESSED', 'FAILED', 'DUPLICATE'
    private String message;
    private UUID webhookEventId;
    private UUID transactionId;
    private String externalTransactionId;
    private OffsetDateTime processedAt;
    private Long processingTimeMs;
    private String errorCode;
    private boolean requiresRetry;
}
