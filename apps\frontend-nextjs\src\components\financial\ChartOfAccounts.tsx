"use client";

import React from 'react';
import { useAccounts } from '../../hooks/useAccounts';
import { Account } from '@intellifin/data-models';

const AccountTree = ({ accounts, parentId = null }: { accounts: Account[], parentId?: string | null }) => {
  const childAccounts = accounts.filter(a => a.parentAccountId === parentId);

  if (childAccounts.length === 0) {
    return null;
  }

  return (
    <ul className="pl-6">
      {childAccounts.map(account => (
        <li key={account.id} className="py-1">
          <span>{account.code} - {account.name}</span>
          <AccountTree accounts={accounts} parentId={account.id} />
        </li>
      ))}
    </ul>
  );
};

const ChartOfAccounts = () => {
  const { accounts, loading, error } = useAccounts();

  if (loading) return <div>Loading Chart of Accounts...</div>;
  if (error) return <div className="text-red-500">{error}</div>;

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Chart of Accounts</h1>
      <AccountTree accounts={accounts} />
    </div>
  );
};

export default ChartOfAccounts;
