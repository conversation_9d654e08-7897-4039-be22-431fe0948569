import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { jest } from '@jest/globals';
import { TransactionRow } from '../TransactionRow';
import { Transaction, Category } from '@/types/api';
import * as transactionCategorizationHook from '@/hooks/useTransactionCategorization';

// Mock the hook
const mockUseTransactionCategorization = {
  acceptAISuggestion: jest.fn(),
  rejectAISuggestion: jest.fn(),
  manualCategorize: jest.fn(),
  isTransactionBeingCategorized: jest.fn(() => false)
};

jest.spyOn(transactionCategorizationHook, 'useTransactionCategorization').mockReturnValue(mockUseTransactionCategorization as any);

// Mock date formatting
jest.mock('date-fns', () => ({
  format: jest.fn((date, formatStr) => {
    if (formatStr === 'MMM dd, yyyy') return 'Jan 15, 2024';
    if (formatStr === 'MMM dd, HH:mm') return 'Jan 15, 10:30';
    return 'Jan 15, 2024';
  })
}));

describe('TransactionRow', () => {
  const mockCategories: Category[] = [
    {
      id: 'cat-1',
      name: 'Transport',
      type: 'EXPENSE',
      isSystemDefined: true,
      isActive: true,
      color: '#3B82F6',
      icon: 'car',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 'cat-2',
      name: 'Food & Dining',
      type: 'EXPENSE',
      isSystemDefined: true,
      isActive: true,
      color: '#EF4444',
      icon: 'utensils',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    }
  ];

  const mockTransaction: Transaction = {
    id: 'txn-1',
    userId: 'user-1',
    financialAccountId: 'acc-1',
    date: '2024-01-15',
    description: 'Fuel purchase at Shell station',
    amount: 150.00,
    type: 'EXPENSE',
    status: 'PENDING_CLASSIFICATION',
    source: 'manual',
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-15T10:30:00Z'
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders transaction details correctly', () => {
    render(
      <TransactionRow
        transaction={mockTransaction}
        categories={mockCategories}
      />
    );

    expect(screen.getByText('Fuel purchase at Shell station')).toBeInTheDocument();
    expect(screen.getByText('Jan 15, 2024')).toBeInTheDocument();
    expect(screen.getByText('manual')).toBeInTheDocument();
    expect(screen.getByText('EXPENSE')).toBeInTheDocument();
    expect(screen.getByText('-K150.00')).toBeInTheDocument();
  });

  it('displays AI categorization suggestion', () => {
    const transactionWithAI: Transaction = {
      ...mockTransaction,
      aiCategoryId: 'cat-1',
      aiConfidence: 0.85,
      aiExplanation: 'AI detected fuel purchase keywords'
    };

    render(
      <TransactionRow
        transaction={transactionWithAI}
        categories={mockCategories}
      />
    );

    expect(screen.getByText('AI: Transport')).toBeInTheDocument();
    expect(screen.getByText('(85%)')).toBeInTheDocument();
    expect(screen.getByText('AI detected fuel purchase keywords')).toBeInTheDocument();
  });

  it('shows accept and reject buttons for AI suggestions', () => {
    const transactionWithAI: Transaction = {
      ...mockTransaction,
      aiCategoryId: 'cat-1',
      aiConfidence: 0.85,
      aiExplanation: 'AI detected fuel purchase keywords'
    };

    render(
      <TransactionRow
        transaction={transactionWithAI}
        categories={mockCategories}
      />
    );

    expect(screen.getByTitle('Accept AI suggestion')).toBeInTheDocument();
    expect(screen.getByTitle('Reject AI suggestion')).toBeInTheDocument();
  });

  it('calls acceptAISuggestion when accept button is clicked', async () => {
    const transactionWithAI: Transaction = {
      ...mockTransaction,
      aiCategoryId: 'cat-1',
      aiConfidence: 0.85,
      aiExplanation: 'AI detected fuel purchase keywords'
    };

    mockUseTransactionCategorization.acceptAISuggestion.mockResolvedValue({
      success: true,
      transaction: { ...transactionWithAI, categoryId: 'cat-1', status: 'CLASSIFIED' }
    });

    render(
      <TransactionRow
        transaction={transactionWithAI}
        categories={mockCategories}
      />
    );

    const acceptButton = screen.getByTitle('Accept AI suggestion');
    fireEvent.click(acceptButton);

    await waitFor(() => {
      expect(mockUseTransactionCategorization.acceptAISuggestion).toHaveBeenCalledWith(
        'txn-1',
        'cat-1',
        'User accepted AI suggestion'
      );
    });
  });

  it('shows category selector when reject button is clicked', async () => {
    const transactionWithAI: Transaction = {
      ...mockTransaction,
      aiCategoryId: 'cat-1',
      aiConfidence: 0.85,
      aiExplanation: 'AI detected fuel purchase keywords'
    };

    render(
      <TransactionRow
        transaction={transactionWithAI}
        categories={mockCategories}
      />
    );

    const rejectButton = screen.getByTitle('Reject AI suggestion');
    fireEvent.click(rejectButton);

    await waitFor(() => {
      expect(screen.getByText('Choose category:')).toBeInTheDocument();
      expect(screen.getByText('Select category')).toBeInTheDocument();
      expect(screen.getByText('Cancel')).toBeInTheDocument();
    });
  });

  it('displays current category when transaction is categorized', () => {
    const categorizedTransaction: Transaction = {
      ...mockTransaction,
      categoryId: 'cat-1',
      status: 'CLASSIFIED'
    };

    render(
      <TransactionRow
        transaction={categorizedTransaction}
        categories={mockCategories}
      />
    );

    expect(screen.getByText('Category:')).toBeInTheDocument();
    expect(screen.getByText('Transport')).toBeInTheDocument();
  });

  it('shows loading state when transaction is being processed', () => {
    mockUseTransactionCategorization.isTransactionBeingCategorized.mockReturnValue(true);

    render(
      <TransactionRow
        transaction={mockTransaction}
        categories={mockCategories}
      />
    );

    expect(screen.getByText('Categorizing...')).toBeInTheDocument();
  });

  it('renders in compact mode', () => {
    render(
      <TransactionRow
        transaction={mockTransaction}
        categories={mockCategories}
        compact={true}
      />
    );

    // In compact mode, should not show detailed layout
    expect(screen.queryByText('Category:')).not.toBeInTheDocument();
    expect(screen.getByText('Fuel purchase at Shell station')).toBeInTheDocument();
    expect(screen.getByText('-K150.00')).toBeInTheDocument();
  });

  it('shows action menu when showActions is true', () => {
    const mockOnEdit = jest.fn();
    const mockOnDelete = jest.fn();

    render(
      <TransactionRow
        transaction={mockTransaction}
        categories={mockCategories}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        showActions={true}
      />
    );

    // Click the more options button
    const moreButton = screen.getByRole('button', { name: /more/i });
    fireEvent.click(moreButton);

    expect(screen.getByText('Edit')).toBeInTheDocument();
    expect(screen.getByText('Delete')).toBeInTheDocument();
  });

  it('calls onEdit when edit is clicked', async () => {
    const mockOnEdit = jest.fn();

    render(
      <TransactionRow
        transaction={mockTransaction}
        categories={mockCategories}
        onEdit={mockOnEdit}
        showActions={true}
      />
    );

    // Click the more options button
    const moreButton = screen.getByRole('button', { name: /more/i });
    fireEvent.click(moreButton);

    // Click edit
    const editButton = screen.getByText('Edit');
    fireEvent.click(editButton);

    expect(mockOnEdit).toHaveBeenCalledWith(mockTransaction);
  });

  it('calls onDelete when delete is clicked', async () => {
    const mockOnDelete = jest.fn();

    render(
      <TransactionRow
        transaction={mockTransaction}
        categories={mockCategories}
        onDelete={mockOnDelete}
        showActions={true}
      />
    );

    // Click the more options button
    const moreButton = screen.getByRole('button', { name: /more/i });
    fireEvent.click(moreButton);

    // Click delete
    const deleteButton = screen.getByText('Delete');
    fireEvent.click(deleteButton);

    expect(mockOnDelete).toHaveBeenCalledWith('txn-1');
  });

  it('displays confidence badge with correct color for high confidence', () => {
    const transactionWithHighConfidence: Transaction = {
      ...mockTransaction,
      aiCategoryId: 'cat-1',
      aiConfidence: 0.92,
      aiExplanation: 'High confidence categorization'
    };

    render(
      <TransactionRow
        transaction={transactionWithHighConfidence}
        categories={mockCategories}
      />
    );

    const badge = screen.getByText('AI: Transport');
    expect(badge).toHaveClass('text-green-600');
  });

  it('displays confidence badge with correct color for low confidence', () => {
    const transactionWithLowConfidence: Transaction = {
      ...mockTransaction,
      aiCategoryId: 'cat-1',
      aiConfidence: 0.45,
      aiExplanation: 'Low confidence categorization'
    };

    render(
      <TransactionRow
        transaction={transactionWithLowConfidence}
        categories={mockCategories}
      />
    );

    const badge = screen.getByText('AI: Transport');
    expect(badge).toHaveClass('text-red-600');
  });

  it('handles manual categorization correctly', async () => {
    mockUseTransactionCategorization.manualCategorize.mockResolvedValue({
      success: true,
      transaction: { ...mockTransaction, categoryId: 'cat-2', status: 'CLASSIFIED' }
    });

    render(
      <TransactionRow
        transaction={mockTransaction}
        categories={mockCategories}
      />
    );

    // Since there's no AI suggestion, clicking should show category selector immediately
    // This would need to be implemented in the component logic
  });

  it('formats income amounts correctly', () => {
    const incomeTransaction: Transaction = {
      ...mockTransaction,
      type: 'INCOME',
      amount: 1000.00
    };

    render(
      <TransactionRow
        transaction={incomeTransaction}
        categories={mockCategories}
      />
    );

    expect(screen.getByText('+K1,000.00')).toBeInTheDocument();
    const amountElement = screen.getByText('+K1,000.00');
    expect(amountElement).toHaveClass('text-green-600');
  });

  it('formats expense amounts correctly', () => {
    render(
      <TransactionRow
        transaction={mockTransaction}
        categories={mockCategories}
      />
    );

    expect(screen.getByText('-K150.00')).toBeInTheDocument();
    const amountElement = screen.getByText('-K150.00');
    expect(amountElement).toHaveClass('text-red-600');
  });
});
