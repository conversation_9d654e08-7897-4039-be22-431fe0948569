import { useState, useEffect, useCallback } from 'react';
import { useTransactionWebSocket } from './useTransactionWebSocket';

interface SyncStatus {
  accountId: string;
  lastSync: string | null;
  pendingTransactions: number;
  syncStatus: 'ACTIVE' | 'FAILED' | 'PAUSED' | 'DISABLED';
  errorMessage?: string;
  nextSync?: string;
  totalSyncedTransactions: number;
  failedTransactions: number;
  syncType: 'WEBHOOK' | 'POLLING' | 'MANUAL';
}

interface TransactionNotification {
  id: string;
  transactionId: string;
  type: 'NEW_TRANSACTION' | 'CATEGORIZED' | 'SYNC_FAILED';
  title: string;
  message: string;
  amount?: number;
  currency?: string;
  source?: string;
  timestamp: string;
  isRead: boolean;
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
}

interface UseTransactionSyncReturn {
  // Sync status
  syncStatuses: Record<string, SyncStatus>;
  loading: boolean;
  error: string | null;
  
  // Notifications
  notifications: TransactionNotification[];
  unreadCount: number;
  
  // Actions
  refreshSyncStatus: (accountId: string) => Promise<void>;
  triggerSync: (accountId: string) => Promise<void>;
  pauseSync: (accountId: string) => Promise<void>;
  resumeSync: (accountId: string) => Promise<void>;
  
  // Notification actions
  markNotificationRead: (notificationId: string) => void;
  dismissNotification: (notificationId: string) => void;
  markAllNotificationsRead: () => void;
  clearAllNotifications: () => void;
}

export const useTransactionSync = (accountIds: string[] = []): UseTransactionSyncReturn => {
  const [syncStatuses, setSyncStatuses] = useState<Record<string, SyncStatus>>({});
  const [notifications, setNotifications] = useState<TransactionNotification[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Use WebSocket for real-time updates
  const { isConnected, lastMessage } = useTransactionWebSocket();

  // Fetch sync status for a specific account
  const refreshSyncStatus = useCallback(async (accountId: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/v1/sync/status?accountId=${accountId}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch sync status for account ${accountId}`);
      }
      
      const syncStatus = await response.json();
      
      setSyncStatuses(prev => ({
        ...prev,
        [accountId]: syncStatus
      }));
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('Error fetching sync status:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Trigger manual sync
  const triggerSync = useCallback(async (accountId: string) => {
    try {
      const response = await fetch(`/api/v1/sync/trigger?accountId=${accountId}`, {
        method: 'POST'
      });
      
      if (!response.ok) {
        throw new Error(`Failed to trigger sync for account ${accountId}`);
      }
      
      // Refresh status after triggering sync
      await refreshSyncStatus(accountId);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to trigger sync';
      setError(errorMessage);
      console.error('Error triggering sync:', err);
    }
  }, [refreshSyncStatus]);

  // Pause sync
  const pauseSync = useCallback(async (accountId: string) => {
    try {
      const response = await fetch(`/api/v1/sync/pause?accountId=${accountId}`, {
        method: 'POST'
      });
      
      if (!response.ok) {
        throw new Error(`Failed to pause sync for account ${accountId}`);
      }
      
      // Refresh status after pausing
      await refreshSyncStatus(accountId);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to pause sync';
      setError(errorMessage);
      console.error('Error pausing sync:', err);
    }
  }, [refreshSyncStatus]);

  // Resume sync
  const resumeSync = useCallback(async (accountId: string) => {
    try {
      const response = await fetch(`/api/v1/sync/resume?accountId=${accountId}`, {
        method: 'POST'
      });
      
      if (!response.ok) {
        throw new Error(`Failed to resume sync for account ${accountId}`);
      }
      
      // Refresh status after resuming
      await refreshSyncStatus(accountId);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to resume sync';
      setError(errorMessage);
      console.error('Error resuming sync:', err);
    }
  }, [refreshSyncStatus]);

  // Handle WebSocket messages for real-time updates
  useEffect(() => {
    if (!lastMessage) return;

    try {
      const message = JSON.parse(lastMessage.data);
      
      switch (message.eventType) {
        case 'TRANSACTION_RECEIVED':
          // Add new transaction notification
          const newTransactionNotification: TransactionNotification = {
            id: `tx-${message.data.transactionId}-${Date.now()}`,
            transactionId: message.data.transactionId,
            type: 'NEW_TRANSACTION',
            title: 'New Transaction Received',
            message: `${message.data.description || 'Transaction received'}`,
            amount: message.data.amount,
            currency: message.data.currency || 'ZMW',
            source: message.data.source,
            timestamp: new Date().toISOString(),
            isRead: false,
            priority: 'MEDIUM'
          };
          
          setNotifications(prev => [newTransactionNotification, ...prev]);
          break;

        case 'TRANSACTION_CATEGORIZED':
          // Add categorization notification
          const categorizedNotification: TransactionNotification = {
            id: `cat-${message.data.transactionId}-${Date.now()}`,
            transactionId: message.data.transactionId,
            type: 'CATEGORIZED',
            title: 'Transaction Categorized',
            message: `Transaction has been automatically categorized`,
            timestamp: new Date().toISOString(),
            isRead: false,
            priority: 'LOW'
          };
          
          setNotifications(prev => [categorizedNotification, ...prev]);
          break;

        case 'SYNC_FAILED':
          // Add sync failure notification
          const syncFailedNotification: TransactionNotification = {
            id: `sync-fail-${Date.now()}`,
            transactionId: message.data.transactionId || '',
            type: 'SYNC_FAILED',
            title: 'Sync Failed',
            message: `Transaction sync failed: ${message.data.errorMessage || 'Unknown error'}`,
            timestamp: new Date().toISOString(),
            isRead: false,
            priority: 'HIGH'
          };
          
          setNotifications(prev => [syncFailedNotification, ...prev]);
          break;
      }
    } catch (err) {
      console.error('Error processing WebSocket message:', err);
    }
  }, [lastMessage]);

  // Notification management
  const markNotificationRead = useCallback((notificationId: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, isRead: true }
          : notification
      )
    );
  }, []);

  const dismissNotification = useCallback((notificationId: string) => {
    setNotifications(prev => 
      prev.filter(notification => notification.id !== notificationId)
    );
  }, []);

  const markAllNotificationsRead = useCallback(() => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, isRead: true }))
    );
  }, []);

  const clearAllNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  // Initial load and periodic refresh
  useEffect(() => {
    if (accountIds.length === 0) return;

    // Load initial sync statuses
    const loadInitialStatuses = async () => {
      for (const accountId of accountIds) {
        await refreshSyncStatus(accountId);
      }
    };

    loadInitialStatuses();

    // Set up periodic refresh (every 30 seconds)
    const interval = setInterval(() => {
      accountIds.forEach(accountId => {
        refreshSyncStatus(accountId);
      });
    }, 30000);

    return () => clearInterval(interval);
  }, [accountIds, refreshSyncStatus]);

  // Calculate unread count
  const unreadCount = notifications.filter(n => !n.isRead).length;

  return {
    // Sync status
    syncStatuses,
    loading,
    error,
    
    // Notifications
    notifications,
    unreadCount,
    
    // Actions
    refreshSyncStatus,
    triggerSync,
    pauseSync,
    resumeSync,
    
    // Notification actions
    markNotificationRead,
    dismissNotification,
    markAllNotificationsRead,
    clearAllNotifications
  };
};
