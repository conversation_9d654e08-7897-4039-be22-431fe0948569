spring:
  application:
    name: intellifin-backend-core
  
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:development}
  
  datasource:
    url: ${DATABASE_URL:***********************************************}
    username: ${DATABASE_USERNAME:postgres}
    password: ${DATABASE_PASSWORD:postgres}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 2
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
  
  data:
    redis:
      url: ${REDIS_URL:redis://localhost:6379}
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
  
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true

  # Spring Cloud Stream Configuration
  cloud:
    stream:
      default-binder: rabbit
      bindings:
        # Transaction categorization events
        transactionCategorizationRequested-out-0:
          destination: transaction.categorization.requested
          content-type: application/json
        transactionCategorized-in-0:
          destination: transaction.categorized
          content-type: application/json
          group: core-backend
        categorizationAccepted-out-0:
          destination: categorization.accepted
          content-type: application/json
        categorizationRejected-out-0:
          destination: categorization.rejected
          content-type: application/json
        bulkCategorizationCompleted-in-0:
          destination: bulk.categorization.completed
          content-type: application/json
          group: core-backend
        # Document processing events
        documentUploadStarted-out-0:
          destination: document.upload.started
          content-type: application/json
        documentProcessingRequested-out-0:
          destination: document.processing.requested
          content-type: application/json
        documentParsingCompleted-in-0:
          destination: document.parsing.completed
          content-type: application/json
          group: core-backend
        documentProcessingFailed-in-0:
          destination: document.processing.failed
          content-type: application/json
          group: core-backend
        documentProcessingProgress-in-0:
          destination: document.processing.progress
          content-type: application/json
          group: core-backend
        # Webhook and sync events
        transactionReceived-out-0:
          destination: transaction.received
          content-type: application/json
        webhookProcessed-out-0:
          destination: webhook.processed
          content-type: application/json
        webhookFailed-out-0:
          destination: webhook.failed
          content-type: application/json
        transactionSyncFailed-out-0:
          destination: transaction.sync.failed
          content-type: application/json
        journalEntryCreated-out-0:
          destination: journal.entry.created
          content-type: application/json
        # Journal entry events
        transactionConfirmed-in-0:
          destination: transaction.confirmed
          content-type: application/json
          group: core-backend
        journalEntryCreated-out-0:
          destination: journal.entry.created
          content-type: application/json
        transactionConfirmed-out-0:
          destination: transaction.confirmed
          content-type: application/json
      binders:
        rabbit:
          type: rabbit
          environment:
            spring:
              rabbitmq:
                host: ${RABBITMQ_HOST:localhost}
                port: ${RABBITMQ_PORT:5672}
                username: ${RABBITMQ_USERNAME:guest}
                password: ${RABBITMQ_PASSWORD:guest}
                virtual-host: ${RABBITMQ_VHOST:/}
      rabbit:
        bindings:
          transactionCategorizationRequested-out-0:
            producer:
              routing-key-expression: "'transaction.categorization.requested'"
              exchange-type: topic
              declare-exchange: true
          transactionCategorized-in-0:
            consumer:
              exchange-type: topic
              declare-exchange: true
              auto-bind-dlq: true
              republish-to-dlq: true
              max-attempts: 3
          categorizationAccepted-out-0:
            producer:
              routing-key-expression: "'categorization.accepted'"
              exchange-type: topic
              declare-exchange: true
          categorizationRejected-out-0:
            producer:
              routing-key-expression: "'categorization.rejected'"
              exchange-type: topic
              declare-exchange: true
          bulkCategorizationCompleted-in-0:
            consumer:
              exchange-type: topic
              declare-exchange: true
              auto-bind-dlq: true
              republish-to-dlq: true
              max-attempts: 3
          transactionConfirmed-in-0:
            consumer:
              exchange-type: topic
              declare-exchange: true
              auto-bind-dlq: true
              republish-to-dlq: true
              max-attempts: 3
          journalEntryCreated-out-0:
            producer:
              routing-key-expression: "'journal.entry.created'"
              exchange-type: topic
              declare-exchange: true
          transactionConfirmed-out-0:
            producer:
              routing-key-expression: "'transaction.confirmed'"
              exchange-type: topic
              declare-exchange: true

server:
  port: 8080
  servlet:
    context-path: /
  error:
    include-message: always
    include-binding-errors: always

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  health:
    redis:
      enabled: true
    db:
      enabled: true

logging:
  level:
    com.intellifin: ${LOG_LEVEL:INFO}
    org.springframework.web: ${LOG_LEVEL:INFO}
    org.hibernate.SQL: ${LOG_LEVEL:INFO}
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:intellifin-super-secure-jwt-secret-key-for-development-environment-2024}
  expiration: ${JWT_EXPIRATION:86400}

# Application Configuration
app:
  frontend:
    url: ${FRONTEND_URL:http://localhost:3000}
  email:
    enabled: ${EMAIL_ENABLED:false}
    from: ${EMAIL_FROM:<EMAIL>}
    smtp:
      host: ${SMTP_HOST:localhost}
      port: ${SMTP_PORT:587}
      username: ${SMTP_USERNAME:}
      password: ${SMTP_PASSWORD:}
      auth: ${SMTP_AUTH:true}
      starttls: ${SMTP_STARTTLS:true}

# OAuth Configuration
oauth:
  google:
    client-id: ${GOOGLE_CLIENT_ID:}
    client-secret: ${GOOGLE_CLIENT_SECRET:}
    redirect-uri: ${GOOGLE_REDIRECT_URI:http://localhost:8080/api/v1/auth/oauth/google/callback}
  apple:
    client-id: ${APPLE_CLIENT_ID:}
    client-secret: ${APPLE_CLIENT_SECRET:}
    redirect-uri: ${APPLE_REDIRECT_URI:http://localhost:8080/api/v1/auth/oauth/apple/callback}

# External Services
external:
  ai-service:
    url: ${AI_SERVICE_URL:http://localhost:8000}
    timeout: 30
  zra-service:
    url: ${ZRA_SERVICE_URL:http://localhost:8081}
    timeout: 30

# MTN Integration Configuration
mtn:
  api:
    enabled: ${MTN_API_ENABLED:false}
    base-url: ${MTN_API_BASE_URL:https://sandbox.momodeveloper.mtn.com}
  oauth:
    client-id: ${MTN_OAUTH_CLIENT_ID:mock-client-id}
    client-secret: ${MTN_OAUTH_CLIENT_SECRET:mock-client-secret}
    redirect-uri: ${MTN_OAUTH_REDIRECT_URI:http://localhost:3000/settings/accounts/mtn/callback}
    scopes: ${MTN_OAUTH_SCOPES:account:read,transactions:read}

# File Storage Configuration
file:
  storage:
    base-path: ${FILE_STORAGE_BASE_PATH:./uploads}
    use-cloud: ${FILE_STORAGE_USE_CLOUD:false}
    cloud:
      bucket: ${FILE_STORAGE_CLOUD_BUCKET:intellifin-documents}
      region: ${FILE_STORAGE_CLOUD_REGION:us-east-1}

# Document Upload Configuration
document:
  upload:
    max-size-mb: ${DOCUMENT_UPLOAD_MAX_SIZE_MB:10}
    url-expiry-minutes: ${DOCUMENT_UPLOAD_URL_EXPIRY_MINUTES:60}
    supported-types: ${DOCUMENT_UPLOAD_SUPPORTED_TYPES:PDF,CSV,EXCEL}

# Encryption Configuration
encryption:
  key: ${ENCRYPTION_KEY:} # Base64 encoded encryption key

---
# Development Profile
spring:
  config:
    activate:
      on-profile: development
  
  devtools:
    restart:
      enabled: true
    livereload:
      enabled: true
  
  jpa:
    show-sql: true
    properties:
      hibernate:
        format_sql: true

logging:
  level:
    com.intellifin: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG

---
# Production Profile
spring:
  config:
    activate:
      on-profile: production

  jpa:
    show-sql: false

  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5

  # Production messaging with Azure Service Bus
  cloud:
    stream:
      default-binder: servicebus
      binders:
        servicebus:
          type: servicebus
          environment:
            spring:
              cloud:
                azure:
                  servicebus:
                    connection-string: ${AZURE_SERVICEBUS_CONNECTION_STRING}
      servicebus:
        bindings:
          transactionCategorizationRequested-out-0:
            producer:
              entity-type: topic
          transactionCategorized-in-0:
            consumer:
              entity-type: topic
              max-delivery-count: 3
          categorizationAccepted-out-0:
            producer:
              entity-type: topic
          categorizationRejected-out-0:
            producer:
              entity-type: topic
          bulkCategorizationCompleted-in-0:
            consumer:
              entity-type: topic
              max-delivery-count: 3
          transactionConfirmed-in-0:
            consumer:
              entity-type: topic
              max-delivery-count: 3
          journalEntryCreated-out-0:
            producer:
              entity-type: topic
          transactionConfirmed-out-0:
            producer:
              entity-type: topic

logging:
  level:
    com.intellifin: INFO
    org.springframework.web: WARN
    org.hibernate.SQL: WARN
