"use client"

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Search, 
  Filter, 
  RefreshCw, 
  Plus,
  Download,
  CheckSquare,
  Square,
  Brain,
  AlertCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Transaction, Category } from '@/types/api';
import { TransactionRow } from './TransactionRow';
import { useFinancialStore } from '@/stores';
import { useTransactionCategorization } from '@/hooks/useTransactionCategorization';

interface TransactionListProps {
  showFilters?: boolean;
  showBulkActions?: boolean;
  showCreateButton?: boolean;
  compact?: boolean;
  maxHeight?: string;
  onCreateTransaction?: () => void;
  onEditTransaction?: (transaction: Transaction) => void;
  className?: string;
}

/**
 * Main transaction list component with filtering, search, and bulk operations
 */
export const TransactionList: React.FC<TransactionListProps> = ({
  showFilters = true,
  showBulkActions = true,
  showCreateButton = true,
  compact = false,
  maxHeight = "600px",
  onCreateTransaction,
  onEditTransaction,
  className
}) => {
  const { 
    transactions, 
    categories, 
    isLoading, 
    errors, 
    fetchTransactions, 
    fetchCategories,
    deleteTransaction 
  } = useFinancialStore();

  const {
    bulkCategorize,
    getCategorizationStats,
    bulkCategorizationLoading
  } = useTransactionCategorization();

  // Local state
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [selectedTransactions, setSelectedTransactions] = useState<Set<string>>(new Set());
  const [showPendingOnly, setShowPendingOnly] = useState(false);

  // Load data on mount
  useEffect(() => {
    fetchTransactions();
    fetchCategories();
  }, [fetchTransactions, fetchCategories]);

  // Filter transactions
  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch = searchTerm === '' || 
      transaction.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || transaction.status === statusFilter;
    const matchesType = typeFilter === 'all' || transaction.type === typeFilter;
    const matchesCategory = categoryFilter === 'all' || transaction.categoryId === categoryFilter;
    const matchesPending = !showPendingOnly || transaction.status === 'PENDING_CLASSIFICATION';

    return matchesSearch && matchesStatus && matchesType && matchesCategory && matchesPending;
  });

  // Categorization statistics
  const stats = getCategorizationStats();

  // Bulk selection handlers
  const handleSelectAll = () => {
    if (selectedTransactions.size === filteredTransactions.length) {
      setSelectedTransactions(new Set());
    } else {
      setSelectedTransactions(new Set(filteredTransactions.map(t => t.id)));
    }
  };

  const handleSelectTransaction = (transactionId: string) => {
    const newSelected = new Set(selectedTransactions);
    if (newSelected.has(transactionId)) {
      newSelected.delete(transactionId);
    } else {
      newSelected.add(transactionId);
    }
    setSelectedTransactions(newSelected);
  };

  // Bulk categorization
  const handleBulkCategorize = async (categoryId: string) => {
    const selectedTransactionsList = filteredTransactions.filter(t => 
      selectedTransactions.has(t.id)
    );

    const categorizationRequests = selectedTransactionsList.map(transaction => ({
      transactionId: transaction.id,
      categoryId,
      isAISuggestion: false,
      explanation: 'Bulk categorization'
    }));

    try {
      await bulkCategorize(categorizationRequests);
      setSelectedTransactions(new Set());
    } catch (error) {
      console.error('Bulk categorization failed:', error);
    }
  };

  const handleDeleteTransaction = async (transactionId: string) => {
    try {
      await deleteTransaction(transactionId);
    } catch (error) {
      console.error('Failed to delete transaction:', error);
    }
  };

  const handleRefresh = () => {
    fetchTransactions();
  };

  if (isLoading.transactions && transactions.length === 0) {
    return (
      <Card className={cn("p-8 text-center", className)}>
        <RefreshCw className="w-8 h-8 mx-auto mb-4 animate-spin text-muted-foreground" />
        <p className="text-muted-foreground">Loading transactions...</p>
      </Card>
    );
  }

  if (errors.transactions) {
    return (
      <Card className={cn("p-8 text-center", className)}>
        <AlertCircle className="w-8 h-8 mx-auto mb-4 text-red-500" />
        <p className="text-red-600 mb-4">Failed to load transactions</p>
        <Button onClick={handleRefresh} variant="outline">
          <RefreshCw className="w-4 h-4 mr-2" />
          Try Again
        </Button>
      </Card>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header with stats */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h2 className="text-xl font-semibold">Transactions</h2>
          <div className="flex items-center gap-2">
            <Badge variant="outline">
              {filteredTransactions.length} total
            </Badge>
            {stats.pending > 0 && (
              <Badge variant="outline" className="text-orange-600 bg-orange-50">
                <Brain className="w-3 h-3 mr-1" />
                {stats.pending} pending
              </Badge>
            )}
            {stats.withAISuggestions > 0 && (
              <Badge variant="outline" className="text-blue-600 bg-blue-50">
                {stats.withAISuggestions} AI suggestions
              </Badge>
            )}
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowPendingOnly(!showPendingOnly)}
            className={showPendingOnly ? "bg-orange-50 text-orange-600" : ""}
          >
            <AlertCircle className="w-4 h-4 mr-2" />
            Pending Only
          </Button>
          
          <Button variant="outline" size="sm" onClick={handleRefresh}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          
          {showCreateButton && onCreateTransaction && (
            <Button onClick={onCreateTransaction}>
              <Plus className="w-4 h-4 mr-2" />
              Add Transaction
            </Button>
          )}
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <Card className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search transactions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9"
              />
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="PENDING_CLASSIFICATION">Pending</SelectItem>
                <SelectItem value="CLASSIFIED">Classified</SelectItem>
                <SelectItem value="RECONCILED">Reconciled</SelectItem>
              </SelectContent>
            </Select>

            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="INCOME">Income</SelectItem>
                <SelectItem value="EXPENSE">Expense</SelectItem>
              </SelectContent>
            </Select>

            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button variant="outline" onClick={() => {
              setSearchTerm('');
              setStatusFilter('all');
              setTypeFilter('all');
              setCategoryFilter('all');
              setShowPendingOnly(false);
            }}>
              Clear Filters
            </Button>
          </div>
        </Card>
      )}

      {/* Bulk actions */}
      {showBulkActions && selectedTransactions.size > 0 && (
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                {selectedTransactions.size} selected
              </span>
              <Button variant="outline" size="sm" onClick={() => setSelectedTransactions(new Set())}>
                Clear Selection
              </Button>
            </div>

            <div className="flex items-center gap-2">
              <Select onValueChange={handleBulkCategorize}>
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="Bulk categorize..." />
                </SelectTrigger>
                <SelectContent>
                  {categories.map(category => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </Card>
      )}

      {/* Transaction list */}
      <div 
        className="space-y-2 overflow-y-auto"
        style={{ maxHeight }}
      >
        {showBulkActions && (
          <div className="flex items-center gap-2 p-2 border-b">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSelectAll}
              className="h-8 w-8 p-0"
            >
              {selectedTransactions.size === filteredTransactions.length ? (
                <CheckSquare className="h-4 w-4" />
              ) : (
                <Square className="h-4 w-4" />
              )}
            </Button>
            <span className="text-sm text-muted-foreground">
              Select all
            </span>
          </div>
        )}

        {filteredTransactions.length === 0 ? (
          <Card className="p-8 text-center">
            <AlertCircle className="w-8 h-8 mx-auto mb-4 text-muted-foreground" />
            <p className="text-muted-foreground">No transactions found</p>
            {searchTerm && (
              <p className="text-sm text-muted-foreground mt-2">
                Try adjusting your search or filters
              </p>
            )}
          </Card>
        ) : (
          filteredTransactions.map((transaction) => (
            <div key={transaction.id} className="flex items-center gap-2">
              {showBulkActions && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleSelectTransaction(transaction.id)}
                  className="h-8 w-8 p-0 flex-shrink-0"
                >
                  {selectedTransactions.has(transaction.id) ? (
                    <CheckSquare className="h-4 w-4" />
                  ) : (
                    <Square className="h-4 w-4" />
                  )}
                </Button>
              )}
              
              <TransactionRow
                transaction={transaction}
                categories={categories}
                onEdit={onEditTransaction}
                onDelete={handleDeleteTransaction}
                compact={compact}
                className="flex-1"
              />
            </div>
          ))
        )}
      </div>

      {/* Loading indicator for bulk operations */}
      {bulkCategorizationLoading && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <Card className="p-6">
            <div className="flex items-center gap-3">
              <RefreshCw className="w-5 h-5 animate-spin" />
              <span>Processing bulk categorization...</span>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};
