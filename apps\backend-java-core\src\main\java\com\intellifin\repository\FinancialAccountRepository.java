package com.intellifin.repository;

import com.intellifin.model.FinancialAccount;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface FinancialAccountRepository extends JpaRepository<FinancialAccount, UUID> {

    /**
     * Find all financial accounts for a specific user
     */
    List<FinancialAccount> findByUserIdOrderByCreatedAtDesc(UUID userId);

    /**
     * Find financial accounts by user and account type
     */
    List<FinancialAccount> findByUserIdAndAccountTypeOrderByCreatedAtDesc(
            UUID userId, 
            FinancialAccount.AccountType accountType
    );

    /**
     * Find financial accounts by user and connection status
     */
    List<FinancialAccount> findByUserIdAndConnectionStatusOrderByCreatedAtDesc(
            UUID userId, 
            FinancialAccount.ConnectionStatus connectionStatus
    );

    /**
     * Find connected financial accounts for a user
     */
    @Query("SELECT fa FROM FinancialAccount fa WHERE fa.user.id = :userId AND fa.connectionStatus = 'CONNECTED' ORDER BY fa.createdAt DESC")
    List<FinancialAccount> findConnectedAccountsByUserId(@Param("userId") UUID userId);

    /**
     * Find MTN Mobile Money accounts for a user
     */
    @Query("SELECT fa FROM FinancialAccount fa WHERE fa.user.id = :userId AND fa.accountType = 'MTN_MOBILE_MONEY' ORDER BY fa.createdAt DESC")
    List<FinancialAccount> findMTNAccountsByUserId(@Param("userId") UUID userId);

    /**
     * Find financial account by user and phone number (for MTN accounts)
     */
    Optional<FinancialAccount> findByUserIdAndPhoneNumber(UUID userId, String phoneNumber);

    /**
     * Find financial account by user, provider, and account name
     */
    Optional<FinancialAccount> findByUserIdAndProviderAndAccountName(
            UUID userId, 
            String provider, 
            String accountName
    );

    /**
     * Check if user has any connected accounts
     */
    @Query("SELECT COUNT(fa) > 0 FROM FinancialAccount fa WHERE fa.user.id = :userId AND fa.connectionStatus = 'CONNECTED'")
    boolean hasConnectedAccounts(@Param("userId") UUID userId);

    /**
     * Count financial accounts by user and type
     */
    long countByUserIdAndAccountType(UUID userId, FinancialAccount.AccountType accountType);

    /**
     * Find accounts that need sync (connected but haven't synced recently)
     */
    @Query("SELECT fa FROM FinancialAccount fa WHERE fa.connectionStatus = 'CONNECTED' AND (fa.lastSyncAt IS NULL OR fa.lastSyncAt < :cutoffTime)")
    List<FinancialAccount> findAccountsNeedingSync(@Param("cutoffTime") java.time.OffsetDateTime cutoffTime);

    /**
     * Find accounts with errors
     */
    @Query("SELECT fa FROM FinancialAccount fa WHERE fa.connectionStatus = 'ERROR' ORDER BY fa.updatedAt DESC")
    List<FinancialAccount> findAccountsWithErrors();
}
