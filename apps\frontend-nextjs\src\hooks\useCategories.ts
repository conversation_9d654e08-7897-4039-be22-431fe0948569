import { useState, useEffect, useCallback } from 'react';
import { Category } from '@intellifin/data-models';
import * as categoryService from '../services/categoryService';

export const useCategories = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCategories = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await categoryService.getCategories();
      setCategories(data);
    } catch (err) {
      setError('Failed to fetch categories');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  const mapCategory = async (categoryId: string, accountId: string) => {
    try {
      const updatedCategory = await categoryService.mapCategoryToAccount(categoryId, accountId);
      setCategories(prev => prev.map(c => c.id === categoryId ? updatedCategory : c));
    } catch (err) {
      // Handle error
    }
  };

  return {
    categories,
    loading,
    error,
    refetch: fetchCategories,
    mapCategory,
  };
};
