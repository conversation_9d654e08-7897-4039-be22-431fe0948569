package com.intellifin.dto.webhook;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.time.OffsetDateTime;

/**
 * DTO for MTN Mobile Money transaction webhook requests
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MTNTransactionWebhookRequest {

    @NotBlank(message = "Transaction ID is required")
    private String transactionId;

    @NotNull(message = "Amount is required")
    @DecimalMin(value = "0.01", message = "Amount must be greater than 0")
    private BigDecimal amount;

    @NotBlank(message = "Description is required")
    private String description;

    @NotNull(message = "Timestamp is required")
    private OffsetDateTime timestamp;

    @NotBlank(message = "Account ID is required")
    private String accountId;

    private String currency;
    private String transactionType; // 'DEBIT', 'CREDIT'
    private String status; // 'COMPLETED', 'PENDING', 'FAILED'
    private String reference;
    private String counterpartyName;
    private String counterpartyPhone;
    private BigDecimal balance;
    private String category;
    private String location;
    private String channel; // 'USSD', 'APP', 'WEB'
}
