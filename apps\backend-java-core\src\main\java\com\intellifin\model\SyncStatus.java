package com.intellifin.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.*;
import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * Entity representing synchronization status for financial accounts
 */
@Entity
@Table(name = "sync_status", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"financial_account_id", "sync_type"})
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SyncStatus {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "financial_account_id", nullable = false)
    private FinancialAccount financialAccount;

    @Enumerated(EnumType.STRING)
    @Column(name = "sync_type", nullable = false)
    private SyncType syncType;

    @Enumerated(EnumType.STRING)
    @Column(name = "sync_status", nullable = false)
    private Status syncStatus = Status.ACTIVE;

    @Column(name = "last_sync_at")
    private OffsetDateTime lastSyncAt;

    @Column(name = "last_successful_sync_at")
    private OffsetDateTime lastSuccessfulSyncAt;

    @Column(name = "next_sync_at")
    private OffsetDateTime nextSyncAt;

    @Column(name = "pending_transactions")
    private Integer pendingTransactions = 0;

    @Column(name = "failed_transactions")
    private Integer failedTransactions = 0;

    @Column(name = "total_synced_transactions")
    private Integer totalSyncedTransactions = 0;

    @Column(name = "error_message", columnDefinition = "text")
    private String errorMessage;

    @Column(name = "sync_metadata", columnDefinition = "jsonb")
    private String syncMetadata;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false)
    private OffsetDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private OffsetDateTime updatedAt;

    public enum SyncType {
        WEBHOOK,
        POLLING,
        MANUAL
    }

    public enum Status {
        ACTIVE,
        FAILED,
        PAUSED,
        DISABLED
    }

    /**
     * Mark sync as successful
     */
    public void markSyncSuccessful() {
        this.syncStatus = Status.ACTIVE;
        this.lastSyncAt = OffsetDateTime.now();
        this.lastSuccessfulSyncAt = OffsetDateTime.now();
        this.errorMessage = null;
    }

    /**
     * Mark sync as failed
     */
    public void markSyncFailed(String errorMessage) {
        this.syncStatus = Status.FAILED;
        this.lastSyncAt = OffsetDateTime.now();
        this.errorMessage = errorMessage;
        this.failedTransactions = this.failedTransactions != null ? this.failedTransactions + 1 : 1;
    }

    /**
     * Increment successful transaction count
     */
    public void incrementSuccessfulTransactions() {
        this.totalSyncedTransactions = this.totalSyncedTransactions != null ? 
                this.totalSyncedTransactions + 1 : 1;
    }

    /**
     * Check if sync is healthy
     */
    public boolean isHealthy() {
        return this.syncStatus == Status.ACTIVE && 
               (this.lastSuccessfulSyncAt == null || 
                this.lastSuccessfulSyncAt.isAfter(OffsetDateTime.now().minusHours(24)));
    }
}
