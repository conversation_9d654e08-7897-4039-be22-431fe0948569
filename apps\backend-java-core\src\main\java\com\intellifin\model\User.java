package com.intellifin.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "users")
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    @Column(unique = true, nullable = false)
    private String email;

    @Column(nullable = false)
    private String passwordHash;

    private String firstName;
    private String lastName;
    private String organizationName;
    private String tpin;

    @Enumerated(EnumType.STRING)
    private Role role;

    private boolean emailVerified;
    private boolean accountLocked;
    private int failedLoginAttempts;
    private LocalDateTime lockedUntil;
    private LocalDateTime lastLoginAt;

    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<Conversation> conversations;


    @Embedded
    private UserPreferences preferences;
    
    @Enumerated(EnumType.STRING)
    private OnboardingStatus onboardingStatus;


    @CreationTimestamp
    private LocalDateTime createdAt;

    @UpdateTimestamp
    private LocalDateTime updatedAt;

    public boolean getEmailVerified() {
        return emailVerified;
    }

    public boolean getAccountLocked() {
        return accountLocked;
    }

    public String getFullName() {

        return firstName + " " + lastName;
    }

    public boolean isAccountNonLocked() {
        return !accountLocked || (lockedUntil != null && lockedUntil.isBefore(LocalDateTime.now()));
    }

    public void incrementFailedLoginAttempts() {
        this.failedLoginAttempts++;
    }

    public void resetFailedLoginAttempts() {
        this.failedLoginAttempts = 0;
        this.accountLocked = false;
        this.lockedUntil = null;
    }

    public enum OnboardingStatus {
        STARTED,
        COMPLETED
    }
}
