package com.intellifin.messaging.events;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Event published when user rejects AI categorization suggestion
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CategorizationRejectedEvent {
    
    private UUID transactionId;
    private UUID rejectedCategoryId;
    private UUID newCategoryId;
    private String reason;
    private LocalDateTime timestamp;
}
