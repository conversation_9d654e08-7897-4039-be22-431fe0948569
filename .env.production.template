# IntelliFin Production Environment Configuration
# Copy this file to .env.production and fill in the actual values

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
POSTGRES_PASSWORD=your_secure_postgres_password_here
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=intellifin_prod
POSTGRES_USER=intellifin_prod

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================
REDIS_PASSWORD=your_secure_redis_password_here
REDIS_HOST=redis
REDIS_PORT=6379

# =============================================================================
# MESSAGING CONFIGURATION
# =============================================================================
# For local development with RabbitMQ
MESSAGING_PROVIDER=rabbitmq
RABBITMQ_USERNAME=intellifin
RABBITMQ_PASSWORD=your_secure_rabbitmq_password_here

# For production with Azure Service Bus
# MESSAGING_PROVIDER=azure_servicebus
# AZURE_SERVICEBUS_CONNECTION_STRING=Endpoint=sb://your-namespace.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=your-key

# =============================================================================
# AI SERVICE CONFIGURATION
# =============================================================================
GOOGLE_API_KEY=your_google_gemini_api_key_here
AI_MODEL_NAME=gemini-pro
AI_TEMPERATURE=0.1

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
JWT_SECRET=your_very_long_and_secure_jwt_secret_key_here_at_least_256_bits
NEXTAUTH_SECRET=your_nextauth_secret_key_here

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
NEXT_PUBLIC_API_BASE_URL=https://api.yourdomain.com
NEXT_PUBLIC_WS_BASE_URL=wss://api.yourdomain.com
NEXTAUTH_URL=https://yourdomain.com

# =============================================================================
# SSL/TLS CONFIGURATION
# =============================================================================
SSL_CERTIFICATE_PATH=/etc/nginx/ssl/certificate.crt
SSL_PRIVATE_KEY_PATH=/etc/nginx/ssl/private.key

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
GRAFANA_PASSWORD=your_secure_grafana_password_here

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
# AWS S3 for backups (optional)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_S3_BACKUP_BUCKET=intellifin-backups
AWS_REGION=us-east-1

# =============================================================================
# EMAIL CONFIGURATION (for notifications)
# =============================================================================
SMTP_HOST=smtp.yourdomain.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_smtp_password
SMTP_FROM_EMAIL=<EMAIL>

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=INFO
LOG_FORMAT=json

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
# Java JVM settings
JAVA_OPTS=-Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200

# Python worker settings
PYTHON_WORKERS=4
PYTHON_MAX_REQUESTS=1000

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_ANALYTICS=true
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_ERROR_TRACKING=true
ENABLE_RATE_LIMITING=true

# =============================================================================
# RATE LIMITING
# =============================================================================
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_AI_REQUESTS_PER_MINUTE=50
RATE_LIMIT_BULK_REQUESTS_PER_MINUTE=10

# =============================================================================
# HEALTH CHECK CONFIGURATION
# =============================================================================
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3

# =============================================================================
# DOMAIN CONFIGURATION
# =============================================================================
DOMAIN_NAME=yourdomain.com
API_DOMAIN=api.yourdomain.com
WS_DOMAIN=ws.yourdomain.com

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================
DEPLOYMENT_ENVIRONMENT=production
DEPLOYMENT_VERSION=1.0.0
DEPLOYMENT_DATE=2024-01-15

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
# Sentry for error tracking (optional)
SENTRY_DSN=https://<EMAIL>/project-id

# New Relic for APM (optional)
NEW_RELIC_LICENSE_KEY=your_new_relic_license_key
NEW_RELIC_APP_NAME=IntelliFin-Production

# =============================================================================
# BACKUP SCHEDULE
# =============================================================================
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_ENCRYPTION_KEY=your_backup_encryption_key

# =============================================================================
# SECURITY HEADERS
# =============================================================================
SECURITY_HEADERS_ENABLED=true
HSTS_MAX_AGE=31536000
CSP_POLICY="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"

# =============================================================================
# DATABASE PERFORMANCE
# =============================================================================
DB_MAX_CONNECTIONS=20
DB_CONNECTION_TIMEOUT=30
DB_IDLE_TIMEOUT=600

# =============================================================================
# CACHE PERFORMANCE
# =============================================================================
CACHE_TTL=3600
CACHE_MAX_MEMORY=256mb
CACHE_EVICTION_POLICY=allkeys-lru
