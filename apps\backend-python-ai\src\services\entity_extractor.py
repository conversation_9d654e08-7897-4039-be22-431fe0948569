"""
Service for extracting structured data from text using Vertex AI
"""

import json
from json import JSONDecodeError
from typing import Dict, Any
from pydantic import ValidationError

import google.generativeai as genai
from google.generativeai import types


from ..config.settings import get_settings
from ..models.vertex_schemas import VertexAIResponse
from ..models.exceptions import InvalidAIResponseError

class EntityExtractor:
    """Service for extracting structured data from text using Vertex AI"""

    def __init__(self):
        self.settings = get_settings()
        if not self.settings.google_api_key:
            raise ValueError("GOOGLE_API_KEY is not set in the environment.")
        genai.configure(api_key=self.settings.google_api_key)

        system_instruction = self._load_system_prompt()
        self.model = genai.GenerativeModel(
            model_name=self.settings.vertex_ai_model,
            system_instruction=system_instruction
        )


    def _load_system_prompt(self) -> str:
        """Load system prompt from file"""
        with open("apps/backend-python-ai/src/prompts/system_prompt.txt", "r") as f:
            return f.read()

    def get_structured_intent_from_text(self, user_command: str) -> VertexAIResponse:
        """
        Get structured intent from user command using Vertex AI

        Args:
            user_command: The user command text

        Returns:
            A validated Pydantic model of the Vertex AI response

        Raises:
            InvalidAIResponseError: If the response is not valid JSON or does not match the Pydantic model
            Exception: For any other API errors
        """
        try:
            response = self.model.generate_content(
                contents=[user_command],
                generation_config=types.GenerationConfig(
                    temperature=1,
                    top_p=0.95,
                    max_output_tokens=8192,
                ),
                safety_settings=[
                    {
                        "category": "HARM_CATEGORY_HATE_SPEECH",
                        "threshold": "BLOCK_NONE",
                    },
                    {
                        "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                        "threshold": "BLOCK_NONE",
                    },
                    {
                        "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                        "threshold": "BLOCK_NONE",
                    },
                    {
                        "category": "HARM_CATEGORY_HARASSMENT",
                        "threshold": "BLOCK_NONE",
                    },
                ],

            )


            response_text = response.text.strip()
            
            # Clean the response to extract only the JSON part
            if "```json" in response_text:
                response_text = response_text.split("```json")[1].split("```")[0]

            try:
                response_json = json.loads(response_text)
                validated_response = VertexAIResponse(**response_json)
                return validated_response
            except (JSONDecodeError, ValidationError) as e:
                raise InvalidAIResponseError(f"Failed to parse or validate AI response: {e} | Response: {response_text}")

        except Exception as e:
            # Catch any other exceptions from the API call
            raise Exception(f"Failed to get response from Vertex AI: {e}")
