import { useState, useCallback, useRef } from 'react';

interface AISuggestionRequest {
  description: string;
  amount?: number;
  type?: 'INCOME' | 'EXPENSE';
}

interface AISuggestion {
  categoryId: string;
  categoryName: string;
  confidence: number;
  explanation: string;
  keywords?: string[];
  alternativeCategories?: Array<{
    categoryId: string;
    categoryName: string;
    confidence: number;
  }>;
}

interface UseAISuggestionsReturn {
  suggestion: AISuggestion | null;
  isLoadingSuggestion: boolean;
  error: string | null;
  getSuggestion: (request: AISuggestionRequest) => Promise<void>;
  clearSuggestion: () => void;
  retryLastRequest: () => Promise<void>;
}

export const useAISuggestions = (): UseAISuggestionsReturn => {
  const [suggestion, setSuggestion] = useState<AISuggestion | null>(null);
  const [isLoadingSuggestion, setIsLoadingSuggestion] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const lastRequestRef = useRef<AISuggestionRequest | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const getSuggestion = useCallback(async (request: AISuggestionRequest) => {
    // Don't make request if description is too short
    if (!request.description || request.description.trim().length < 3) {
      return;
    }

    // Cancel any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();
    
    setIsLoadingSuggestion(true);
    setError(null);
    lastRequestRef.current = request;

    try {
      const response = await fetch('/api/v1/ai/suggest-category-realtime', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          description: request.description,
          amount: request.amount,
          transaction_type: request.type,
          user_id: 'current-user', // This would come from auth context
        }),
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        const aiSuggestion: AISuggestion = {
          categoryId: data.category_id,
          categoryName: data.category_name,
          confidence: data.confidence,
          explanation: data.explanation,
          keywords: data.keywords || [],
          alternativeCategories: data.alternative_categories || []
        };

        setSuggestion(aiSuggestion);
      } else {
        throw new Error(data.message || 'Failed to get AI suggestion');
      }

    } catch (err) {
      if (err instanceof Error) {
        if (err.name === 'AbortError') {
          // Request was cancelled, don't set error
          return;
        }
        setError(err.message);
      } else {
        setError('An unexpected error occurred');
      }
      setSuggestion(null);
    } finally {
      setIsLoadingSuggestion(false);
      abortControllerRef.current = null;
    }
  }, []);

  const clearSuggestion = useCallback(() => {
    setSuggestion(null);
    setError(null);
    
    // Cancel any pending request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    
    setIsLoadingSuggestion(false);
  }, []);

  const retryLastRequest = useCallback(async () => {
    if (lastRequestRef.current) {
      await getSuggestion(lastRequestRef.current);
    }
  }, [getSuggestion]);

  return {
    suggestion,
    isLoadingSuggestion,
    error,
    getSuggestion,
    clearSuggestion,
    retryLastRequest
  };
};
