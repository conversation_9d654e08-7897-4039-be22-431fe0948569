"use client"

import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { useTransactionWebSocket } from '@/hooks/useTransactionWebSocket';
import { useAuthStore } from '@/stores';
import { Badge } from '@/components/ui/badge';
import { Wifi, WifiOff, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TransactionWebSocketContextType {
  isConnected: boolean;
  connectionError: string | null;
  isConnecting: boolean;
}

const TransactionWebSocketContext = createContext<TransactionWebSocketContextType | undefined>(undefined);

interface TransactionWebSocketProviderProps {
  children: ReactNode;
  showConnectionStatus?: boolean;
  autoSubscribe?: boolean;
}

/**
 * Provider component for transaction WebSocket connection
 */
export const TransactionWebSocketProvider: React.FC<TransactionWebSocketProviderProps> = ({
  children,
  showConnectionStatus = true,
  autoSubscribe = true
}) => {
  const { isAuthenticated } = useAuthStore();
  const {
    isConnected,
    connectionError,
    isConnecting,
    subscribeToTransactionUpdates,
    unsubscribeFromTransactionUpdates
  } = useTransactionWebSocket();

  // Auto-subscribe to transaction updates when connected
  useEffect(() => {
    if (autoSubscribe && isConnected && isAuthenticated) {
      subscribeToTransactionUpdates();
      
      return () => {
        unsubscribeFromTransactionUpdates();
      };
    }
  }, [isConnected, isAuthenticated, autoSubscribe, subscribeToTransactionUpdates, unsubscribeFromTransactionUpdates]);

  const contextValue: TransactionWebSocketContextType = {
    isConnected,
    connectionError,
    isConnecting
  };

  return (
    <TransactionWebSocketContext.Provider value={contextValue}>
      {children}
      
      {/* Connection status indicator */}
      {showConnectionStatus && isAuthenticated && (
        <TransactionWebSocketStatus />
      )}
    </TransactionWebSocketContext.Provider>
  );
};

/**
 * Hook to use the transaction WebSocket context
 */
export const useTransactionWebSocketContext = () => {
  const context = useContext(TransactionWebSocketContext);
  if (context === undefined) {
    throw new Error('useTransactionWebSocketContext must be used within a TransactionWebSocketProvider');
  }
  return context;
};

/**
 * Connection status indicator component
 */
const TransactionWebSocketStatus: React.FC = () => {
  const { isConnected, connectionError, isConnecting } = useTransactionWebSocketContext();

  if (!isConnecting && !isConnected && !connectionError) {
    return null; // Don't show anything if not attempting to connect
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <Badge
        variant="outline"
        className={cn(
          "flex items-center gap-2 px-3 py-2 shadow-lg",
          isConnected && "bg-green-50 text-green-700 border-green-200",
          isConnecting && "bg-blue-50 text-blue-700 border-blue-200",
          connectionError && "bg-red-50 text-red-700 border-red-200"
        )}
      >
        {isConnecting && (
          <>
            <div className="w-3 h-3 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
            <span className="text-xs">Connecting...</span>
          </>
        )}
        
        {isConnected && (
          <>
            <Wifi className="w-3 h-3" />
            <span className="text-xs">Real-time updates active</span>
          </>
        )}
        
        {connectionError && (
          <>
            <AlertCircle className="w-3 h-3" />
            <span className="text-xs">Connection error</span>
          </>
        )}
      </Badge>
    </div>
  );
};

/**
 * Component to display detailed connection information
 */
export const TransactionWebSocketInfo: React.FC<{
  className?: string;
}> = ({ className }) => {
  const { isConnected, connectionError, isConnecting } = useTransactionWebSocketContext();

  return (
    <div className={cn("flex items-center gap-2", className)}>
      {isConnecting && (
        <div className="flex items-center gap-2 text-blue-600">
          <div className="w-3 h-3 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
          <span className="text-sm">Connecting to real-time updates...</span>
        </div>
      )}
      
      {isConnected && (
        <div className="flex items-center gap-2 text-green-600">
          <Wifi className="w-4 h-4" />
          <span className="text-sm">Real-time updates active</span>
        </div>
      )}
      
      {connectionError && (
        <div className="flex items-center gap-2 text-red-600">
          <WifiOff className="w-4 h-4" />
          <span className="text-sm">Real-time updates unavailable</span>
          <span className="text-xs text-muted-foreground">({connectionError})</span>
        </div>
      )}
    </div>
  );
};

/**
 * Hook to check if real-time updates are available
 */
export const useRealTimeUpdates = () => {
  const { isConnected } = useTransactionWebSocketContext();
  return { isRealTimeEnabled: isConnected };
};
