# Known Issue: Document Upload Fails with 403 Forbidden

## Description

When attempting to upload a document, the request fails with a 403 Forbidden error. This issue is intermittent and appears to be related to the security configuration of the backend.

## Details

The `JwtAuthenticationFilter` is likely blocking the `PUT` request to the pre-signed URL because it's not configured to bypass authentication for this endpoint.

## Workaround

There is no known workaround at this time.

## Next Steps

This issue requires further investigation and careful debugging of the security configuration.
