package com.intellifin.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "document_uploads")
public class DocumentUpload {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    @Column(name = "upload_id", nullable = false, unique = true)
    private String uploadId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "financial_account_id")
    private FinancialAccount financialAccount;

    @Column(name = "file_name", nullable = false)
    private String fileName;

    @Enumerated(EnumType.STRING)
    @Column(name = "file_type", nullable = false)
    private FileType fileType;

    @Column(name = "file_size_bytes", nullable = false)
    private Long fileSizeBytes;

    @Column(name = "file_path", columnDefinition = "TEXT")
    private String filePath;

    @Column(name = "upload_url", columnDefinition = "TEXT")
    private String uploadUrl;

    @Column(name = "upload_url_expires_at")
    private OffsetDateTime uploadUrlExpiresAt;

    @Enumerated(EnumType.STRING)
    @Column(name = "processing_status", nullable = false)
    @Builder.Default
    private ProcessingStatus processingStatus = ProcessingStatus.UPLOADING;

    @Column(name = "progress_percentage")
    @Builder.Default
    private Integer progressPercentage = 0;

    @Column(name = "extracted_transactions_count")
    @Builder.Default
    private Integer extractedTransactionsCount = 0;

    @Column(name = "confidence_score", precision = 3, scale = 2)
    private BigDecimal confidenceScore;

    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    @Column(name = "processing_started_at")
    private OffsetDateTime processingStartedAt;

    @Column(name = "processing_completed_at")
    private OffsetDateTime processingCompletedAt;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private OffsetDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private OffsetDateTime updatedAt;

    public enum FileType {
        PDF,
        CSV,
        EXCEL
    }

    public enum ProcessingStatus {
        UPLOADING,
        PROCESSING,
        COMPLETED,
        FAILED
    }

    // Helper methods
    public boolean isUploading() {
        return ProcessingStatus.UPLOADING.equals(processingStatus);
    }

    public boolean isProcessing() {
        return ProcessingStatus.PROCESSING.equals(processingStatus);
    }

    public boolean isCompleted() {
        return ProcessingStatus.COMPLETED.equals(processingStatus);
    }

    public boolean isFailed() {
        return ProcessingStatus.FAILED.equals(processingStatus);
    }

    public boolean isPDF() {
        return FileType.PDF.equals(fileType);
    }

    public boolean isCSV() {
        return FileType.CSV.equals(fileType);
    }

    public boolean isExcel() {
        return FileType.EXCEL.equals(fileType);
    }

    public void markAsProcessing() {
        this.processingStatus = ProcessingStatus.PROCESSING;
        this.processingStartedAt = OffsetDateTime.now();
        this.progressPercentage = 0;
    }

    public void markAsCompleted(Integer transactionCount, BigDecimal confidence) {
        this.processingStatus = ProcessingStatus.COMPLETED;
        this.processingCompletedAt = OffsetDateTime.now();
        this.progressPercentage = 100;
        this.extractedTransactionsCount = transactionCount;
        this.confidenceScore = confidence;
        this.errorMessage = null;
    }

    public void markAsFailed(String errorMessage) {
        this.processingStatus = ProcessingStatus.FAILED;
        this.processingCompletedAt = OffsetDateTime.now();
        this.errorMessage = errorMessage;
    }

    public void updateProgress(Integer percentage) {
        this.progressPercentage = Math.max(0, Math.min(100, percentage));
    }

    public String getDisplayFileName() {
        return fileName != null ? fileName : "Unknown file";
    }

    public String getFileSizeDisplay() {
        if (fileSizeBytes == null) return "Unknown size";
        
        if (fileSizeBytes < 1024) {
            return fileSizeBytes + " B";
        } else if (fileSizeBytes < 1024 * 1024) {
            return String.format("%.1f KB", fileSizeBytes / 1024.0);
        } else {
            return String.format("%.1f MB", fileSizeBytes / (1024.0 * 1024.0));
        }
    }
}
