package com.intellifin.messaging.events;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Event received when AI service has categorized a transaction
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionCategorizedEvent {
    
    private UUID transactionId;
    private UUID suggestedCategoryId;
    private String suggestedCategoryName;
    private BigDecimal confidence;
    private String explanation;
    private Long processingTime; // in milliseconds
    private LocalDateTime timestamp;
}
