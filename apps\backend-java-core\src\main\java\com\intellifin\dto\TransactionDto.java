package com.intellifin.dto;

import com.intellifin.model.Transaction;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO for Transaction entity
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionDto {
    
    private UUID id;
    private UUID userId;
    private UUID financialAccountId;
    private LocalDate date;
    private String description;
    private BigDecimal amount;
    private Transaction.TransactionType type;
    private UUID categoryId;
    private UUID aiCategoryId;
    private BigDecimal aiConfidence;
    private String aiExplanation;
    private Transaction.TransactionStatus status;
    private String source;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Related entity information
    private CategoryDto category;
    private CategoryDto aiCategory;
    
    /**
     * Convert Transaction entity to DTO
     */
    public static TransactionDto fromEntity(Transaction transaction) {
        if (transaction == null) {
            return null;
        }
        
        return TransactionDto.builder()
                .id(transaction.getId())
                .userId(transaction.getUserId())
                .financialAccountId(transaction.getFinancialAccountId())
                .date(transaction.getDate())
                .description(transaction.getDescription())
                .amount(transaction.getAmount())
                .type(transaction.getType())
                .categoryId(transaction.getCategoryId())
                .aiCategoryId(transaction.getAiCategoryId())
                .aiConfidence(transaction.getAiConfidence())
                .aiExplanation(transaction.getAiExplanation())
                .status(transaction.getStatus())
                .source(transaction.getSource())
                .createdAt(transaction.getCreatedAt())
                .updatedAt(transaction.getUpdatedAt())
                .category(CategoryDto.fromEntity(transaction.getCategory()))
                .aiCategory(CategoryDto.fromEntity(transaction.getAiCategory()))
                .build();
    }
    
    /**
     * Convert DTO to Transaction entity (for creation/updates)
     */
    public Transaction toEntity() {
        return Transaction.builder()
                .id(this.id)
                .userId(this.userId)
                .financialAccountId(this.financialAccountId)
                .date(this.date)
                .description(this.description)
                .amount(this.amount)
                .type(this.type)
                .categoryId(this.categoryId)
                .aiCategoryId(this.aiCategoryId)
                .aiConfidence(this.aiConfidence)
                .aiExplanation(this.aiExplanation)
                .status(this.status)
                .source(this.source)
                .build();
    }
}
