-- V3__Create_Accounts_And_Categories.sql

-- Create the accounts table with hierarchy support
CREATE TABLE accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(20) NOT NULL UNIQUE,
    name <PERSON><PERSON>HAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL,
    sub_type VARCHAR(100),
    parent_account_id UUID,
    is_system_defined BOOLEAN NOT NULL DEFAULT false,
    is_active BOOLEAN NOT NULL DEFAULT true,
    normal_balance VARCHAR(10) NOT NULL,
    user_id UUID,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT fk_parent_account FOREIGN KEY (parent_account_id) REFERENCES accounts(id),
    CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES users(id)
);

-- <PERSON>reate indexes for performance
CREATE INDEX idx_accounts_code ON accounts(code);
CREATE INDEX idx_accounts_user_id ON accounts(user_id);
CREATE INDEX idx_accounts_parent_account_id ON accounts(parent_account_id);

-- Create the categories table with account mapping
CREATE TABLE categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL,
    account_id UUID NOT NULL,
    is_system_defined BOOLEAN NOT NULL DEFAULT false,
    is_active BOOLEAN NOT NULL DEFAULT true,
    color VARCHAR(20),
    icon VARCHAR(50),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT fk_account FOREIGN KEY (account_id) REFERENCES accounts(id),
    CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create indexes for performance
CREATE INDEX idx_categories_user_id ON categories(user_id);
CREATE INDEX idx_categories_account_id ON categories(account_id);

-- Seed the Standard Chart of Accounts (Zambian Context)
-- System-defined accounts have user_id = NULL

-- Assets (1000-1999)
INSERT INTO accounts (code, name, type, sub_type, normal_balance, is_system_defined) VALUES
('1000', 'Assets', 'ASSET', 'ROOT', 'DEBIT', true);

-- Get the ID of the root asset account
DO $$
DECLARE root_asset_id UUID;
BEGIN
  SELECT id INTO root_asset_id FROM accounts WHERE code = '1000';

  -- Current Assets
  INSERT INTO accounts (code, name, type, sub_type, parent_account_id, normal_balance, is_system_defined) VALUES
  ('1100', 'Current Assets', 'ASSET', 'CURRENT_ASSET', root_asset_id, 'DEBIT', true);
END $$;

DO $$
DECLARE current_assets_id UUID;
BEGIN
  SELECT id INTO current_assets_id FROM accounts WHERE code = '1100';
  INSERT INTO accounts (code, name, type, sub_type, parent_account_id, normal_balance, is_system_defined) VALUES
  ('1110', 'Cash and Cash Equivalents', 'ASSET', 'CASH', current_assets_id, 'DEBIT', true),
  ('1111', 'Bank Accounts - ZMW', 'ASSET', 'BANK', (SELECT id FROM accounts WHERE code = '1110'), 'DEBIT', true),
  ('1112', 'Bank Accounts - USD', 'ASSET', 'BANK', (SELECT id FROM accounts WHERE code = '1110'), 'DEBIT', true),
  ('1120', 'Petty Cash', 'ASSET', 'CASH', current_assets_id, 'DEBIT', true),
  ('1130', 'Accounts Receivable', 'ASSET', 'ACCOUNTS_RECEIVABLE', current_assets_id, 'DEBIT', true),
  ('1140', 'Inventory', 'ASSET', 'INVENTORY', current_assets_id, 'DEBIT', true),
  ('1150', 'Prepaid Expenses', 'ASSET', 'PREPAID_EXPENSE', current_assets_id, 'DEBIT', true);
END $$;

-- Fixed Assets
DO $$
DECLARE root_asset_id UUID;
BEGIN
  SELECT id INTO root_asset_id FROM accounts WHERE code = '1000';
  INSERT INTO accounts (code, name, type, sub_type, parent_account_id, normal_balance, is_system_defined) VALUES
  ('1200', 'Fixed Assets', 'ASSET', 'FIXED_ASSET', root_asset_id, 'DEBIT', true);
END $$;

DO $$
DECLARE fixed_assets_id UUID;
BEGIN
  SELECT id INTO fixed_assets_id FROM accounts WHERE code = '1200';
  INSERT INTO accounts (code, name, type, sub_type, parent_account_id, normal_balance, is_system_defined) VALUES
  ('1210', 'Property, Plant & Equipment', 'ASSET', 'PLANT_PROPERTY_EQUIPMENT', fixed_assets_id, 'DEBIT', true),
  ('1211', 'Land and Buildings', 'ASSET', 'BUILDING', (SELECT id FROM accounts WHERE code = '1210'), 'DEBIT', true),
  ('1212', 'Vehicles', 'ASSET', 'VEHICLE', (SELECT id FROM accounts WHERE code = '1210'), 'DEBIT', true),
  ('1213', 'Equipment and Machinery', 'ASSET', 'EQUIPMENT', (SELECT id FROM accounts WHERE code = '1210'), 'DEBIT', true),
  ('1214', 'Furniture and Fixtures', 'ASSET', 'FURNITURE', (SELECT id FROM accounts WHERE code = '1210'), 'DEBIT', true),
  ('1250', 'Accumulated Depreciation', 'ASSET', 'ACCUMULATED_DEPRECIATION', fixed_assets_id, 'CREDIT', true);
END $$;

-- Liabilities (2000-2999)
INSERT INTO accounts (code, name, type, sub_type, normal_balance, is_system_defined) VALUES
('2000', 'Liabilities', 'LIABILITY', 'ROOT', 'CREDIT', true);

DO $$
DECLARE root_liability_id UUID;
BEGIN
  SELECT id INTO root_liability_id FROM accounts WHERE code = '2000';
  INSERT INTO accounts (code, name, type, sub_type, parent_account_id, normal_balance, is_system_defined) VALUES
  ('2100', 'Current Liabilities', 'LIABILITY', 'CURRENT_LIABILITY', root_liability_id, 'CREDIT', true);
END $$;

DO $$
DECLARE current_liabilities_id UUID;
BEGIN
  SELECT id INTO current_liabilities_id FROM accounts WHERE code = '2100';
  INSERT INTO accounts (code, name, type, sub_type, parent_account_id, normal_balance, is_system_defined) VALUES
  ('2110', 'Accounts Payable', 'LIABILITY', 'ACCOUNTS_PAYABLE', current_liabilities_id, 'CREDIT', true),
  ('2120', 'Accrued Expenses', 'LIABILITY', 'ACCRUED_EXPENSE', current_liabilities_id, 'CREDIT', true),
  ('2130', 'Short-term Loans', 'LIABILITY', 'SHORT_TERM_LOAN', current_liabilities_id, 'CREDIT', true),
  ('2140', 'VAT Payable', 'LIABILITY', 'TAX_PAYABLE', current_liabilities_id, 'CREDIT', true),
  ('2150', 'PAYE Payable', 'LIABILITY', 'TAX_PAYABLE', current_liabilities_id, 'CREDIT', true),
  ('2160', 'NAPSA Contributions Payable', 'LIABILITY', 'PENSION_PAYABLE', current_liabilities_id, 'CREDIT', true);
END $$;

DO $$
DECLARE root_liability_id UUID;
BEGIN
  SELECT id INTO root_liability_id FROM accounts WHERE code = '2000';
  INSERT INTO accounts (code, name, type, sub_type, parent_account_id, normal_balance, is_system_defined) VALUES
  ('2200', 'Long-term Liabilities', 'LIABILITY', 'LONG_TERM_LIABILITY', root_liability_id, 'CREDIT', true);
END $$;

DO $$
DECLARE long_term_liabilities_id UUID;
BEGIN
  SELECT id INTO long_term_liabilities_id FROM accounts WHERE code = '2200';
  INSERT INTO accounts (code, name, type, sub_type, parent_account_id, normal_balance, is_system_defined) VALUES
  ('2210', 'Long-term Loans', 'LIABILITY', 'LONG_TERM_LOAN', long_term_liabilities_id, 'CREDIT', true),
  ('2220', 'Mortgages Payable', 'LIABILITY', 'MORTGAGE', long_term_liabilities_id, 'CREDIT', true);
END $$;

-- Equity (3000-3999)
INSERT INTO accounts (code, name, type, sub_type, normal_balance, is_system_defined) VALUES
('3000', 'Equity', 'EQUITY', 'ROOT', 'CREDIT', true);

DO $$
DECLARE root_equity_id UUID;
BEGIN
  SELECT id INTO root_equity_id FROM accounts WHERE code = '3000';
  INSERT INTO accounts (code, name, type, sub_type, parent_account_id, normal_balance, is_system_defined) VALUES
  ('3100', 'Owner''s Equity', 'EQUITY', 'OWNERS_EQUITY', root_equity_id, 'CREDIT', true),
  ('3200', 'Retained Earnings', 'EQUITY', 'RETAINED_EARNINGS', root_equity_id, 'CREDIT', true),
  ('3300', 'Current Year Earnings', 'EQUITY', 'CURRENT_EARNINGS', root_equity_id, 'CREDIT', true);
END $$;

-- Income (4000-4999)
INSERT INTO accounts (code, name, type, sub_type, normal_balance, is_system_defined) VALUES
('4000', 'Income', 'INCOME', 'ROOT', 'CREDIT', true);

DO $$
DECLARE root_income_id UUID;
BEGIN
  SELECT id INTO root_income_id FROM accounts WHERE code = '4000';
  INSERT INTO accounts (code, name, type, sub_type, parent_account_id, normal_balance, is_system_defined) VALUES
  ('4100', 'Sales Revenue', 'INCOME', 'SALES_REVENUE', root_income_id, 'CREDIT', true),
  ('4200', 'Service Revenue', 'INCOME', 'SERVICE_REVENUE', root_income_id, 'CREDIT', true),
  ('4300', 'Interest Income', 'INCOME', 'INTEREST_INCOME', root_income_id, 'CREDIT', true),
  ('4400', 'Other Income', 'INCOME', 'OTHER_INCOME', root_income_id, 'CREDIT', true);
END $$;

-- Expenses (5000-5999)
INSERT INTO accounts (code, name, type, sub_type, normal_balance, is_system_defined) VALUES
('5000', 'Expenses', 'EXPENSE', 'ROOT', 'DEBIT', true);

DO $$
DECLARE root_expense_id UUID;
BEGIN
  SELECT id INTO root_expense_id FROM accounts WHERE code = '5000';
  INSERT INTO accounts (code, name, type, sub_type, parent_account_id, normal_balance, is_system_defined) VALUES
  ('5100', 'Cost of Goods Sold', 'EXPENSE', 'COST_OF_GOODS_SOLD', root_expense_id, 'DEBIT', true),
  ('5200', 'Operating Expenses', 'EXPENSE', 'OPERATING_EXPENSE', root_expense_id, 'DEBIT', true);
END $$;

DO $$
DECLARE operating_expenses_id UUID;
BEGIN
  SELECT id INTO operating_expenses_id FROM accounts WHERE code = '5200';
  INSERT INTO accounts (code, name, type, sub_type, parent_account_id, normal_balance, is_system_defined) VALUES
  ('5205', 'Salaries and Wages', 'EXPENSE', 'SALARIES_WAGES', operating_expenses_id, 'DEBIT', true),
  ('5210', 'Employee Benefits', 'EXPENSE', 'EMPLOYEE_BENEFITS', operating_expenses_id, 'DEBIT', true),
  ('5220', 'Rent Expense', 'EXPENSE', 'RENT_EXPENSE', operating_expenses_id, 'DEBIT', true),
  ('5230', 'Utilities', 'EXPENSE', 'UTILITIES', operating_expenses_id, 'DEBIT', true),
  ('5231', 'ZESCO', 'EXPENSE', 'UTILITIES_ELECTRICITY', (SELECT id FROM accounts WHERE code = '5230'), 'DEBIT', true),
  ('5232', 'Water', 'EXPENSE', 'UTILITIES_WATER', (SELECT id FROM accounts WHERE code = '5230'), 'DEBIT', true),
  ('5240', 'Telecommunications', 'EXPENSE', 'TELECOMMUNICATIONS', operating_expenses_id, 'DEBIT', true),
  ('5241', 'Airtel', 'EXPENSE', 'TELECOM_PROVIDER', (SELECT id FROM accounts WHERE code = '5240'), 'DEBIT', true),
  ('5242', 'MTN', 'EXPENSE', 'TELECOM_PROVIDER', (SELECT id FROM accounts WHERE code = '5240'), 'DEBIT', true),
  ('5250', 'Fuel and Transportation', 'EXPENSE', 'TRANSPORTATION', operating_expenses_id, 'DEBIT', true),
  ('5260', 'Office Supplies', 'EXPENSE', 'OFFICE_SUPPLIES', operating_expenses_id, 'DEBIT', true),
  ('5270', 'Professional Services', 'EXPENSE', 'PROFESSIONAL_SERVICES', operating_expenses_id, 'DEBIT', true),
  ('5280', 'Insurance', 'EXPENSE', 'INSURANCE', operating_expenses_id, 'DEBIT', true),
  ('5290', 'Depreciation Expense', 'EXPENSE', 'DEPRECIATION_EXPENSE', operating_expenses_id, 'DEBIT', true);
END $$;
