package com.intellifin.controller;

import com.intellifin.dto.webhook.WebhookProcessingResponse;
import com.intellifin.dto.webhook.MTNTransactionWebhookRequest;
import com.intellifin.service.WebhookProcessingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.Map;

/**
 * Controller for handling incoming webhooks from financial service providers
 */
@RestController
@RequestMapping("/api/v1/webhooks")
@RequiredArgsConstructor
@Slf4j
public class WebhookController {

    private final WebhookProcessingService webhookProcessingService;

    /**
     * Handle MTN Mobile Money transaction webhooks
     */
    @PostMapping("/mtn/transactions")
    public ResponseEntity<WebhookProcessingResponse> handleMTNTransactionWebhook(
            @Valid @RequestBody MTNTransactionWebhookRequest request,
            @RequestHeader(value = "X-MTN-Signature", required = false) String signature,
            @RequestHeader(value = "X-MTN-Timestamp", required = false) String timestamp,
            HttpServletRequest httpRequest) {
        
        try {
            log.info("Received MTN transaction webhook for transaction: {}", request.getTransactionId());
            
            // Extract client IP for security logging
            String clientIp = getClientIpAddress(httpRequest);
            
            // Process the webhook
            WebhookProcessingResponse response = webhookProcessingService.processMTNTransactionWebhook(
                request, signature, timestamp, clientIp);
            
            log.info("Successfully processed MTN webhook for transaction: {} with status: {}", 
                    request.getTransactionId(), response.getStatus());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error processing MTN transaction webhook for transaction: {}", 
                    request.getTransactionId(), e);
            
            WebhookProcessingResponse errorResponse = WebhookProcessingResponse.builder()
                    .status("FAILED")
                    .message("Webhook processing failed: " + e.getMessage())
                    .build();
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Generic webhook endpoint for other providers
     */
    @PostMapping("/{provider}/transactions")
    public ResponseEntity<WebhookProcessingResponse> handleGenericTransactionWebhook(
            @PathVariable String provider,
            @RequestBody Map<String, Object> payload,
            @RequestHeader Map<String, String> headers,
            HttpServletRequest httpRequest) {
        
        try {
            log.info("Received {} transaction webhook", provider.toUpperCase());
            
            String clientIp = getClientIpAddress(httpRequest);
            
            WebhookProcessingResponse response = webhookProcessingService.processGenericTransactionWebhook(
                provider, payload, headers, clientIp);
            
            log.info("Successfully processed {} webhook with status: {}", 
                    provider.toUpperCase(), response.getStatus());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error processing {} transaction webhook", provider.toUpperCase(), e);
            
            WebhookProcessingResponse errorResponse = WebhookProcessingResponse.builder()
                    .status("FAILED")
                    .message("Webhook processing failed: " + e.getMessage())
                    .build();
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Webhook health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> webhookHealthCheck() {
        return ResponseEntity.ok(Map.of(
            "status", "healthy",
            "timestamp", System.currentTimeMillis(),
            "service", "webhook-processor"
        ));
    }

    /**
     * Extract client IP address from request
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
