import { useState, useCallback, useEffect } from 'react';
import { Transaction } from '@/types/api';

interface FormData {
  description?: string;
  amount?: number;
  type?: 'INCOME' | 'EXPENSE';
  date?: string;
  categoryId?: string;
  notes?: string;
}

interface FormErrors {
  [key: string]: string;
}

interface FormWarnings {
  [key: string]: string;
}

interface UseTransactionFormReturn {
  formData: FormData;
  errors: FormErrors;
  warnings: FormWarnings;
  isValid: boolean;
  isDirty: boolean;
  updateField: (field: keyof FormData, value: any) => void;
  validateField: (field: keyof FormData) => boolean;
  validateForm: () => boolean;
  resetForm: () => void;
  setFormData: (data: FormData) => void;
}

export const useTransactionForm = (initialData?: Partial<Transaction>): UseTransactionFormReturn => {
  const [formData, setFormDataState] = useState<FormData>(() => ({
    description: initialData?.description || '',
    amount: initialData?.amount || undefined,
    type: initialData?.type || undefined,
    date: initialData?.date ? new Date(initialData.date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
    categoryId: initialData?.categoryId || '',
    notes: initialData?.notes || ''
  }));

  const [errors, setErrors] = useState<FormErrors>({});
  const [warnings, setWarnings] = useState<FormWarnings>({});
  const [isDirty, setIsDirty] = useState(false);

  // Validation rules
  const validateField = useCallback((field: keyof FormData): boolean => {
    const value = formData[field];
    const newErrors = { ...errors };
    const newWarnings = { ...warnings };

    // Clear existing error/warning for this field
    delete newErrors[field];
    delete newWarnings[field];

    switch (field) {
      case 'description':
        if (!value || (typeof value === 'string' && value.trim().length === 0)) {
          newErrors[field] = 'Description is required';
        } else if (typeof value === 'string' && value.length < 3) {
          newErrors[field] = 'Description must be at least 3 characters';
        } else if (typeof value === 'string' && value.length > 255) {
          newErrors[field] = 'Description must not exceed 255 characters';
        } else if (typeof value === 'string' && !/^[a-zA-Z0-9\s\-_.,()]+$/.test(value)) {
          newErrors[field] = 'Description contains invalid characters';
        } else if (typeof value === 'string' && isGenericDescription(value)) {
          newWarnings[field] = 'Consider adding more specific details';
        }
        break;

      case 'amount':
        if (!value) {
          newErrors[field] = 'Amount is required';
        } else if (typeof value === 'number' && value <= 0) {
          newErrors[field] = 'Amount must be greater than 0';
        } else if (typeof value === 'number' && value > 999999999.99) {
          newErrors[field] = 'Amount exceeds maximum limit';
        } else if (typeof value === 'number' && !isValidDecimalPlaces(value)) {
          newErrors[field] = 'Amount can have at most 2 decimal places';
        } else if (typeof value === 'number' && value > 10000) {
          newWarnings[field] = 'Large amount detected - please verify';
        }
        break;

      case 'type':
        if (!value) {
          newErrors[field] = 'Transaction type is required';
        } else if (value !== 'INCOME' && value !== 'EXPENSE') {
          newErrors[field] = 'Invalid transaction type';
        }
        break;

      case 'date':
        if (!value) {
          newErrors[field] = 'Date is required';
        } else if (typeof value === 'string') {
          const date = new Date(value);
          const today = new Date();
          
          if (isNaN(date.getTime())) {
            newErrors[field] = 'Invalid date format';
          } else if (date > today) {
            newErrors[field] = 'Date cannot be in the future';
          } else if (date < new Date(today.getFullYear() - 2, today.getMonth(), today.getDate())) {
            newWarnings[field] = 'Date is more than 2 years ago - please verify';
          }
        }
        break;

      case 'notes':
        if (typeof value === 'string' && value.length > 1000) {
          newErrors[field] = 'Notes must not exceed 1000 characters';
        }
        break;

      case 'categoryId':
        // Category is optional, but if provided should be valid
        // Validation would typically check against available categories
        break;

      default:
        break;
    }

    setErrors(newErrors);
    setWarnings(newWarnings);

    return !newErrors[field];
  }, [formData, errors, warnings]);

  const validateForm = useCallback((): boolean => {
    const fieldsToValidate: (keyof FormData)[] = ['description', 'amount', 'type', 'date', 'notes'];
    let isFormValid = true;

    fieldsToValidate.forEach(field => {
      const isFieldValid = validateField(field);
      if (!isFieldValid) {
        isFormValid = false;
      }
    });

    return isFormValid;
  }, [validateField]);

  const updateField = useCallback((field: keyof FormData, value: any) => {
    setFormDataState(prev => ({
      ...prev,
      [field]: value
    }));
    setIsDirty(true);

    // Validate field after a short delay to avoid excessive validation
    setTimeout(() => {
      validateField(field);
    }, 100);
  }, [validateField]);

  const resetForm = useCallback(() => {
    setFormDataState({
      description: initialData?.description || '',
      amount: initialData?.amount || undefined,
      type: initialData?.type || undefined,
      date: initialData?.date ? new Date(initialData.date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
      categoryId: initialData?.categoryId || '',
      notes: initialData?.notes || ''
    });
    setErrors({});
    setWarnings({});
    setIsDirty(false);
  }, [initialData]);

  const setFormData = useCallback((data: FormData) => {
    setFormDataState(data);
    setIsDirty(true);
  }, []);

  // Calculate if form is valid
  const isValid = Object.keys(errors).length === 0 && 
                  formData.description && 
                  formData.amount && 
                  formData.type && 
                  formData.date;

  // Helper functions
  const isGenericDescription = (description: string): boolean => {
    const generic = ['payment', 'purchase', 'transaction', 'expense', 'income'];
    const lower = description.toLowerCase().trim();
    return generic.includes(lower) || lower.length < 5;
  };

  const isValidDecimalPlaces = (amount: number): boolean => {
    const decimalPlaces = (amount.toString().split('.')[1] || '').length;
    return decimalPlaces <= 2;
  };

  return {
    formData,
    errors,
    warnings,
    isValid,
    isDirty,
    updateField,
    validateField,
    validateForm,
    resetForm,
    setFormData
  };
};
