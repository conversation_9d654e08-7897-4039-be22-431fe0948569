# MTN Mobile Money Integration with Document Upload Fallback

This document describes the implementation of MTN Mobile Money account connection with document upload fallback functionality as specified in Story 3.1.

## Overview

The implementation provides two ways for users to connect their MTN Mobile Money accounts:

1. **Live API Integration**: OAuth-based connection to MTN Mobile Money API (when available)
2. **Document Upload Fallback**: Manual upload of MTN statements (PDF, CSV, Excel) with AI-powered parsing

## Architecture

### Backend Components

#### Database Schema
- **financial_accounts**: Stores external account connections
- **account_connections**: OAuth tokens and connection details
- **document_uploads**: Document upload tracking and processing status
- **connection_audit_trail**: Security monitoring
- **document_processing_audit**: Compliance audit trail

#### Java Services
- **FinancialAccountService**: Main service for account management
- **MTNIntegrationService**: MTN-specific OAuth integration
- **DocumentUploadService**: Document upload and processing coordination
- **EncryptionService**: Secure credential storage
- **FileStorageService**: File storage abstraction (local/cloud)
- **DocumentProcessingMessagingService**: Event publishing for document processing

#### Controllers
- **FinancialAccountController**: REST API endpoints for account management

### Frontend Components

#### React Components
- **AccountConnection**: Main account connection interface
- **MTNConnectionFlow**: MTN-specific OAuth flow
- **DocumentUpload**: Document upload interface with drag-and-drop
- **ConnectionStatus**: Real-time connection status display

#### Hooks
- **useAccountConnection**: Account management state
- **useMTNConnection**: MTN OAuth flow management
- **useDocumentUpload**: Document upload with progress tracking

### AI Service Enhancement

#### Document Parser
- **DocumentParser**: Python service for parsing MTN statements
- Supports PDF, CSV, and Excel formats
- AI-powered transaction extraction
- Confidence scoring for parsing accuracy

## Features Implemented

### ✅ Primary Integration (API-based)
- [x] User can initiate MTN Mobile Money connection from settings
- [x] Secure OAuth flow with MTN (mock implementation ready for live API)
- [x] Connection status clearly displayed (connected, disconnected, error)
- [x] User can disconnect and reconnect MTN account
- [x] Connection details securely stored and encrypted
- [x] Error handling for connection failures and timeouts
- [x] Clear feedback during connection process
- [x] Support for multiple MTN accounts per user
- [x] Connection health monitoring

### ✅ Fallback Integration (Document Upload)
- [x] User can upload MTN Mobile Money statements (PDF, CSV, Excel)
- [x] Document parsing extracts transaction data automatically
- [x] Document upload events processed via messaging abstraction layer
- [x] Uploaded documents securely stored and encrypted
- [x] Document processing status tracked and displayed
- [x] Error handling for unsupported file formats and parsing failures
- [x] API contract defined and documented

### ✅ Technical Implementation
- [x] Database schema with proper indexing and constraints
- [x] Encrypted credential storage
- [x] Messaging abstraction layer (RabbitMQ/Azure Service Bus)
- [x] Comprehensive error handling
- [x] Security audit trail
- [x] File upload with progress tracking
- [x] AI-powered document parsing

## API Endpoints

### MTN Integration
```
POST /api/v1/financial-accounts/connect/mtn
POST /api/v1/financial-accounts/mtn/callback
GET  /api/v1/financial-accounts/{accountId}/status
DELETE /api/v1/financial-accounts/{accountId}/disconnect
```

### Document Upload
```
POST /api/v1/financial-accounts/upload/initiate
GET  /api/v1/financial-accounts/upload/{uploadId}/status
POST /api/v1/financial-accounts/upload/{uploadId}/confirm
```

### Account Management
```
GET  /api/v1/financial-accounts
GET  /api/v1/financial-accounts/mtn
GET  /api/v1/financial-accounts/connected/status
```

## Configuration

### Environment Variables

#### MTN Integration
```bash
MTN_API_ENABLED=false                    # Enable live MTN API
MTN_API_BASE_URL=https://sandbox.momodeveloper.mtn.com
MTN_OAUTH_CLIENT_ID=your-client-id
MTN_OAUTH_CLIENT_SECRET=your-client-secret
MTN_OAUTH_REDIRECT_URI=http://localhost:3000/settings/accounts/mtn/callback
```

#### File Storage
```bash
FILE_STORAGE_BASE_PATH=./uploads         # Local storage path
FILE_STORAGE_USE_CLOUD=false            # Enable cloud storage
FILE_STORAGE_CLOUD_BUCKET=intellifin-documents
```

#### Document Upload
```bash
DOCUMENT_UPLOAD_MAX_SIZE_MB=10           # Max file size
DOCUMENT_UPLOAD_URL_EXPIRY_MINUTES=60    # Upload URL expiry
```

#### Security
```bash
ENCRYPTION_KEY=base64-encoded-key        # For credential encryption
```

## Usage

### 1. Live Connection Flow

1. User navigates to Settings > Account Connections
2. Clicks "Connect MTN Account" in Live Connection tab
3. Enters account name and phone number
4. Redirected to MTN OAuth authorization
5. After authorization, account is connected and syncing begins

### 2. Document Upload Flow

1. User navigates to Settings > Account Connections
2. Switches to "Upload Statements" tab
3. Drags and drops or selects MTN statement file
4. Optionally enters account name
5. File is uploaded and processed by AI
6. Transactions are extracted and imported

## Security Features

### Data Protection
- All OAuth tokens encrypted using AES-256-GCM
- File uploads stored in encrypted format
- Secure audit trails for all operations
- No storage of user credentials

### Access Control
- JWT-based authentication
- User-scoped data access
- Read-only API permissions
- Secure OAuth state validation

## Error Handling

### Connection Errors
- Network timeouts with retry mechanisms
- Invalid credentials with secure error messages
- Service unavailable with graceful degradation
- OAuth failures with clear user guidance

### Upload Errors
- File size validation with helpful messages
- Unsupported format detection
- Parsing failures with manual mapping option
- Processing timeouts with retry mechanisms

## Testing

### Manual Testing
1. Start the backend services
2. Navigate to `/settings/accounts` in the frontend
3. Test both connection flows:
   - Live connection (mock OAuth)
   - Document upload with sample files

### Sample Files
Create test files in these formats:
- **PDF**: MTN statement with transaction history
- **CSV**: Columns: Date, Description, Amount, Balance
- **Excel**: Same structure as CSV

## Deployment

### Database Migration
```bash
# Run the migration to create new tables
./mvnw flyway:migrate
```

### Service Dependencies
- PostgreSQL database
- RabbitMQ (local) or Azure Service Bus (production)
- File storage (local filesystem or cloud)

## Future Enhancements

### Live API Integration
When MTN API becomes available:
1. Update `MTN_API_ENABLED=true`
2. Configure real OAuth credentials
3. Implement actual API calls in `MTNIntegrationService`
4. Add webhook endpoints for real-time updates

### Enhanced Document Processing
- OCR for scanned documents
- Machine learning model training
- Support for additional file formats
- Batch processing capabilities

## Troubleshooting

### Common Issues

#### Connection Fails
- Check MTN API configuration
- Verify OAuth redirect URI
- Check network connectivity
- Review audit logs

#### Upload Processing Fails
- Verify file format and size
- Check AI service connectivity
- Review document processing logs
- Ensure messaging service is running

#### Performance Issues
- Monitor database query performance
- Check file storage capacity
- Review messaging queue health
- Optimize document parsing algorithms

## Support

For technical support or questions about this implementation:
1. Check the application logs
2. Review the audit trails
3. Consult the API documentation
4. Contact the development team

---

**Implementation Status**: ✅ Complete and Ready for Testing
**Last Updated**: 2024-07-30
**Version**: 1.0.0
