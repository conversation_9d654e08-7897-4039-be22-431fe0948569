# Story 6.1: Conversational Financial Summary Generation

**Epic:** Financial Reporting & Analytics  
**Status:** Ready for Development  
**Priority:** High  
**Story Points:** 10

## User Story

**As a** user  
**I want** to ask for financial summaries in natural language and receive instant insights  
**So that** I can quickly understand my business performance without navigating complex reports

## Acceptance Criteria

- [ ] User can request financial summaries via conversational commands
- [ ] System generates real-time financial summaries based on user queries
- [ ] AI provides insights and explanations for financial data
- [ ] Summaries include key metrics (profit, expenses, cash flow)
- [ ] Data is presented in an easy-to-understand format
- [ ] Users can specify time periods (this month, last quarter, etc.)
- [ ] System handles ambiguous queries with clarification questions
- [ ] Error handling for insufficient data or calculation errors
- [ ] Graceful degradation when AI service is unavailable
- [ ] API contract is defined and documented
- [ ] Report generation events are published via messaging abstraction layer
- [ ] Real-time data updates are handled through messaging events
- [ ] Report caching and invalidation work via messaging patterns
- [ ] Failed report generation events are routed to dead-letter queues

## Technical Implementation

### Frontend Changes
- `src/components/financial/FinancialSummary.tsx` - Financial summary display
- `src/components/financial/MetricCard.tsx` - Individual metric display
- `src/components/financial/ChartDisplay.tsx` - Chart and visualization components
- `src/components/financial/InsightBubble.tsx` - AI-generated insights display
- `src/hooks/useFinancialSummary.ts` - Financial summary logic
- `src/stores/financialStore.ts` - Financial data state management

### API Gateway Changes
- `src/main/java/com/intellifin/gateway/routes/FinancialReportController.java` - Financial reporting routing
- `src/main/java/com/intellifin/gateway/middleware/FinancialDataCache.java` - Financial data caching

### Service Changes
- **Core Backend**: `src/main/java/com/intellifin/reports/` - Financial reporting engine with Spring Cloud Stream
- **Core Backend**: `src/main/java/com/intellifin/messaging/` - Report generation event handling
- **AI Service**: `src/financial_analysis.py` - AI-powered financial analysis with MessagingService integration
- **AI Service**: `src/services/messaging_service.py` - Python messaging abstraction implementation
- **Database**: Financial calculations and reporting data models

### Database Changes
- Financial summary cache table for performance optimization
- Report generation audit trail
- User preference table for report customization

## API Contracts

```typescript
// Financial summary contracts
interface FinancialSummaryRequest {
  query: string;
  userId: string;
  timePeriod?: {
    startDate: string;
    endDate: string;
  };
  metrics?: string[];
}

interface FinancialMetric {
  name: string;
  value: number;
  currency: string;
  change?: {
    amount: number;
    percentage: number;
    direction: 'increase' | 'decrease' | 'stable';
  };
  trend?: 'up' | 'down' | 'stable';
}

interface FinancialInsight {
  type: 'observation' | 'recommendation' | 'warning';
  message: string;
  confidence: number;
  relatedMetrics?: string[];
}

interface FinancialSummary {
  period: {
    startDate: string;
    endDate: string;
    label: string;
  };
  metrics: {
    [metricName: string]: FinancialMetric;
  };
  insights: FinancialInsight[];
  charts?: {
    type: 'line' | 'bar' | 'pie';
    data: any;
    title: string;
  }[];
  generatedAt: string;
}

interface FinancialReportAPI {
  POST /api/v1/reports/conversational: {
    body: FinancialSummaryRequest;
    response: FinancialSummary;
    errors: { 400: "Invalid query", 404: "No data found" };
  };
  
  GET /api/v1/reports/summary: {
    query: { period?: string; metrics?: string; };
    response: FinancialSummary;
    errors: { 400: "Invalid parameters", 500: "Calculation error" };
  };
  
  GET /api/v1/reports/metrics/{metricName}: {
    query: { startDate: string; endDate: string; };
    response: FinancialMetric;
    errors: { 400: "Invalid date range", 404: "Metric not found" };
  };
}

// Internal messaging events for financial reporting (abstracted across RabbitMQ/Azure Service Bus)
interface ReportingEvents {
  ReportGenerationRequested: {
    reportId: string;
    userId: string;
    reportType: string;
    period: string;
    metrics?: string[];
    timestamp: string;
  };
  ReportGenerated: {
    reportId: string;
    summary: FinancialSummary;
    insights: AIInsight[];
    generationTime: number;
    cacheKey?: string;
  };
  ReportGenerationFailed: {
    reportId: string;
    error: string;
    retryCount: number;
    timestamp: string;
  };
  ReportCacheInvalidated: {
    userId: string;
    affectedReports: string[];
    reason: string;
  };
}
```

## Error Handling

- **Insufficient Data:** Clear messages about data availability and suggestions
- **Calculation Errors:** Graceful fallback with error explanations via messaging events
- **Invalid Time Periods:** Helpful suggestions for valid date ranges
- **AI Service Unavailable:** Fallback to basic calculations without insights via messaging dead-letter queues
- **Data Synchronization Issues:** Clear indicators for data freshness
- **Messaging Broker Connectivity:** Issues handled by abstraction layer with automatic failover
- **Failed Report Generation:** Events routed to dead-letter queues (RabbitMQ DLX or Azure Service Bus DLQ)
- **Event Processing Failures:** Retry mechanisms with exponential backoff and manual intervention queues

## Definition of Done

- [ ] Users can request financial summaries through conversational commands
- [ ] AI generates meaningful insights and explanations
- [ ] Financial data is calculated accurately and displayed clearly
- [ ] Time period specifications work correctly
- [ ] Error scenarios are handled gracefully with helpful guidance
- [ ] Performance meets requirements (< 3 seconds for summary generation)
- [ ] Insights are relevant and actionable
- [ ] Data visualization is clear and informative
- [ ] Tests cover summary generation and error scenarios
- [ ] Service can be deployed independently
- [ ] No breaking changes to other services
- [ ] Report generation events work consistently on both RabbitMQ (local) and Azure Service Bus (production)
- [ ] Messaging abstraction layer handles report generation failures gracefully
- [ ] Dead-letter queue processing is implemented for failed report events
- [ ] Event-driven report caching and invalidation maintain business logic environment independence

## Dependencies

- [Story 2.1: Basic Conversational Command Processing](../epic-02/story-2.1-conversational-commands.md)
- [Story 2.2: Intent Recognition and Entity Extraction](../epic-02/story-2.2-intent-recognition.md)
- [Story 3.1: Transaction List Display with AI Categorization](../epic-03/story-3.1-transaction-categorization.md)

## Notes

This story delivers the AI-powered financial insights that make IntelliFin valuable for business decision-making. The summaries must be accurate, insightful, and presented in an easily digestible format.

---

**Related Stories:**
- [Story 6.2: Advanced Financial Reports and Export](story-6.2-advanced-reports.md)

**Epic:** [Financial Reporting & Analytics](../../epics-and-stories.md#epic-6-financial-reporting--analytics) 