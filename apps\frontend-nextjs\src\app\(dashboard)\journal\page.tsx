'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, BookOpen, TrendingUp, AlertCircle, CheckCircle } from 'lucide-react';
import { JournalEntryList } from '@/components/journal/JournalEntryList';
import { JournalEntryDetails } from '@/components/journal/JournalEntryDetails';
import { useJournalStore } from '@/stores/journalStore';
import { JournalEntry } from '@intellifin/data-models';

export default function JournalPage() {
  const searchParams = useSearchParams();
  const [selectedEntry, setSelectedEntry] = useState<JournalEntry | null>(null);
  const [activeTab, setActiveTab] = useState('all');

  const {
    entries,
    loading,
    error,
    getDraftEntries,
    getPostedEntries,
    getUnbalancedEntries,
    fetchEntries,
    postEntry,
    reverseEntry,
    deleteEntry
  } = useJournalStore();

  // Handle URL query parameters for Progressive Disclosure navigation
  useEffect(() => {
    const status = searchParams.get('status');
    if (status && ['draft', 'posted', 'unbalanced'].includes(status)) {
      setActiveTab(status);
    }
  }, [searchParams]);

  const handleViewEntry = (entry: JournalEntry) => {
    setSelectedEntry(entry);
  };

  const handleEditEntry = (entry: JournalEntry) => {
    // TODO: Implement edit functionality
    console.log('Edit entry:', entry.id);
  };

  const handleDeleteEntry = async (entryId: string) => {
    try {
      await deleteEntry(entryId);
      if (selectedEntry?.id === entryId) {
        setSelectedEntry(null);
      }
    } catch (error) {
      console.error('Failed to delete entry:', error);
    }
  };

  const handlePostEntry = async (entryId: string) => {
    try {
      await postEntry(entryId);
    } catch (error) {
      console.error('Failed to post entry:', error);
    }
  };

  const handleReverseEntry = async (entryId: string, reason: string) => {
    try {
      await reverseEntry(entryId, reason);
    } catch (error) {
      console.error('Failed to reverse entry:', error);
    }
  };

  const handleCreateNew = () => {
    // TODO: Implement create new journal entry
    console.log('Create new journal entry');
  };

  const draftEntries = getDraftEntries();
  const postedEntries = getPostedEntries();
  const unbalancedEntries = getUnbalancedEntries();

  const stats = [
    {
      title: 'Total Entries',
      value: entries.length,
      icon: BookOpen,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: 'Draft Entries',
      value: draftEntries.length,
      icon: AlertCircle,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100'
    },
    {
      title: 'Posted Entries',
      value: postedEntries.length,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      title: 'Unbalanced',
      value: unbalancedEntries.length,
      icon: TrendingUp,
      color: 'text-red-600',
      bgColor: 'bg-red-100'
    }
  ];

  return (
    <div className="h-full flex flex-col space-y-6">
      {/* Page Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Journal Entries</h1>
          <p className="text-gray-600 mt-1">
            Manage your double-entry journal entries and account balances
          </p>
        </div>
        <Button onClick={handleCreateNew} className="flex items-center space-x-2">
          <Plus className="w-4 h-4" />
          <span>New Entry</span>
        </Button>
      </motion.div>

      {/* Statistics Cards */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
      >
        {stats.map((stat, index) => (
          <Card key={stat.title}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-full ${stat.bgColor}`}>
                  <stat.icon className={`w-6 h-6 ${stat.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </motion.div>

      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
        className="flex-1 flex gap-6 min-h-0"
      >
        {/* Left Panel - Journal Entry List */}
        <div className="flex-1 min-w-0">
          <Card className="h-full">
            <CardHeader>
              <CardTitle>Journal Entries</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
                <div className="px-6 pt-2">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="all">
                      All ({entries.length})
                    </TabsTrigger>
                    <TabsTrigger value="draft">
                      Draft ({draftEntries.length})
                    </TabsTrigger>
                    <TabsTrigger value="posted">
                      Posted ({postedEntries.length})
                    </TabsTrigger>
                    <TabsTrigger value="unbalanced">
                      <div className="flex items-center space-x-1">
                        <span>Unbalanced</span>
                        {unbalancedEntries.length > 0 && (
                          <Badge variant="destructive" className="text-xs">
                            {unbalancedEntries.length}
                          </Badge>
                        )}
                      </div>
                    </TabsTrigger>
                  </TabsList>
                </div>
                
                <div className="flex-1 overflow-hidden">
                  <TabsContent value="all" className="h-full m-0 p-6">
                    <JournalEntryList
                      onViewEntry={handleViewEntry}
                      onEditEntry={handleEditEntry}
                      onDeleteEntry={handleDeleteEntry}
                      onPostEntry={handlePostEntry}
                      onReverseEntry={handleReverseEntry}
                    />
                  </TabsContent>
                  
                  <TabsContent value="draft" className="h-full m-0 p-6">
                    <JournalEntryList
                      entries={draftEntries}
                      onViewEntry={handleViewEntry}
                      onEditEntry={handleEditEntry}
                      onDeleteEntry={handleDeleteEntry}
                      onPostEntry={handlePostEntry}
                      onReverseEntry={handleReverseEntry}
                    />
                  </TabsContent>
                  
                  <TabsContent value="posted" className="h-full m-0 p-6">
                    <JournalEntryList
                      entries={postedEntries}
                      onViewEntry={handleViewEntry}
                      onEditEntry={handleEditEntry}
                      onDeleteEntry={handleDeleteEntry}
                      onPostEntry={handlePostEntry}
                      onReverseEntry={handleReverseEntry}
                    />
                  </TabsContent>
                  
                  <TabsContent value="unbalanced" className="h-full m-0 p-6">
                    <JournalEntryList
                      entries={unbalancedEntries}
                      onViewEntry={handleViewEntry}
                      onEditEntry={handleEditEntry}
                      onDeleteEntry={handleDeleteEntry}
                      onPostEntry={handlePostEntry}
                      onReverseEntry={handleReverseEntry}
                    />
                  </TabsContent>
                </div>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        {/* Right Panel - Journal Entry Details */}
        {selectedEntry && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
            className="w-96 flex-shrink-0"
          >
            <Card className="h-full">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Entry Details</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedEntry(null)}
                  >
                    ×
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="overflow-auto">
                <JournalEntryDetails entry={selectedEntry} />
              </CardContent>
            </Card>
          </motion.div>
        )}
      </motion.div>
    </div>
  );
}
