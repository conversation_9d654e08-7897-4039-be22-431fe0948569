package com.intellifin.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * JournalEntryLine entity representing individual debit/credit lines in a journal entry
 * Each line must have either a debit amount OR a credit amount (not both)
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "journal_entry_lines")
public class JournalEntryLine {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "journal_entry_id", nullable = false)
    private JournalEntry journalEntry;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "account_id", nullable = false)
    private Account account;

    @NotNull(message = "Debit amount cannot be null")
    @DecimalMin(value = "0.00", message = "Debit amount must be non-negative")
    @Column(name = "debit_amount", nullable = false, precision = 15, scale = 2)
    @Builder.Default
    private BigDecimal debitAmount = BigDecimal.ZERO;

    @NotNull(message = "Credit amount cannot be null")
    @DecimalMin(value = "0.00", message = "Credit amount must be non-negative")
    @Column(name = "credit_amount", nullable = false, precision = 15, scale = 2)
    @Builder.Default
    private BigDecimal creditAmount = BigDecimal.ZERO;

    @Column(nullable = false)
    private String description;

    @Column(name = "reference_number")
    private String referenceNumber;

    @Column(name = "line_number")
    private Integer lineNumber;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private OffsetDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private OffsetDateTime updatedAt;

    /**
     * Get the effective amount for this line (debit or credit)
     */
    public BigDecimal getAmount() {
        if (debitAmount.compareTo(BigDecimal.ZERO) > 0) {
            return debitAmount;
        }
        return creditAmount;
    }

    /**
     * Check if this is a debit line
     */
    public boolean isDebit() {
        return debitAmount.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * Check if this is a credit line
     */
    public boolean isCredit() {
        return creditAmount.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * Validate that the line has either debit OR credit amount (not both, not neither)
     */
    public boolean isValid() {
        boolean hasDebit = debitAmount.compareTo(BigDecimal.ZERO) > 0;
        boolean hasCredit = creditAmount.compareTo(BigDecimal.ZERO) > 0;
        
        // Must have exactly one of debit or credit
        return hasDebit ^ hasCredit;
    }

    /**
     * Set this line as a debit
     */
    public void setAsDebit(BigDecimal amount) {
        this.debitAmount = amount;
        this.creditAmount = BigDecimal.ZERO;
    }

    /**
     * Set this line as a credit
     */
    public void setAsCredit(BigDecimal amount) {
        this.creditAmount = amount;
        this.debitAmount = BigDecimal.ZERO;
    }

    /**
     * Get the impact on account balance based on account type and normal balance
     * Returns positive for increases, negative for decreases
     */
    public BigDecimal getBalanceImpact() {
        if (account == null) {
            return BigDecimal.ZERO;
        }

        Account.NormalBalance normalBalance = account.getNormalBalance();
        
        if (isDebit()) {
            // Debit increases accounts with normal debit balance, decreases credit balance accounts
            return normalBalance == Account.NormalBalance.DEBIT ? debitAmount : debitAmount.negate();
        } else {
            // Credit increases accounts with normal credit balance, decreases debit balance accounts
            return normalBalance == Account.NormalBalance.CREDIT ? creditAmount : creditAmount.negate();
        }
    }
}
