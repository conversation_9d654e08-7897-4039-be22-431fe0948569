"use client"

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Send, 
  Mic, 
  Sparkles, 
  Loader2, 
  AlertCircle, 
  User,
  MessageSquare,
  ArrowUp
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAuthStore, useConversationStore } from '@/stores';
import { formatRelativeTime } from '@/utils';
import { WorkspaceResults } from './WorkspaceResults';
import { IntentDisplay } from './IntentDisplay';
import { EntityHighlight } from './EntityHighlight';
import { conversationalCommandService } from '@/services/conversational-commands';

export function ConversationalWorkspace() {
  const { user } = useAuthStore();
  const {
    messages,
    isConnected,
    isConnecting,
    connectionError,
    isTyping,
    awaitingResponse,
    addUserMessage,
    currentSessionId
  } = useConversationStore();
  
  const [input, setInput] = useState('');
  const [showResults, setShowResults] = useState(false);
  const [currentTask, setCurrentTask] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Focus input on mount
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!input.trim() || awaitingResponse || !isConnected) {
      return;
    }

    const command = input.trim();

    // Add user message to conversation
    const userMessage = addUserMessage(command);

    // Show results area when user sends a message
    setShowResults(true);
    setCurrentTask(command);

    setInput('');
  };

  const handleSuggestionClick = async (suggestion: string) => {
    if (!awaitingResponse && isConnected) {
      // Add user message to conversation (this handles the API call)
      addUserMessage(suggestion);
      setShowResults(true);
      setCurrentTask(suggestion);
    }
  };

  const suggestions = conversationalCommandService.getCommandSuggestions();
  const firstName = user?.firstName || 'there';

  return (
    <div className="h-full flex flex-col bg-gray-50 rounded-lg shadow-sm relative">

      {/* Main Content Area */}
      <div className="flex-1 min-h-0 overflow-hidden">
        {/* Connection Error Toast */}
        <AnimatePresence>
          {connectionError && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="absolute top-4 left-1/2 transform -translate-x-1/2 z-50"
            >
              <div className="bg-white border border-red-200 rounded-lg shadow-lg p-3 flex items-center space-x-2">
                <AlertCircle className="w-4 h-4 text-red-500" />
                <span className="text-sm text-red-700">Reconnecting...</span>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Welcome State - When no active task */}
        {!showResults && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex-1 flex flex-col items-center justify-center p-8"
          >
            {/* Minimal branding */}
            <div className="mb-auto" />
            
            {/* Suggestion Cards - Smaller and more subtle */}
            <div className="w-full max-w-3xl mb-8">
              <div className="grid grid-cols-2 gap-3">
                {suggestions.slice(0, 4).map((suggestion, index) => (
                  <motion.button
                    key={suggestion.text}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                    onClick={() => handleSuggestionClick(suggestion.text)}
                    disabled={!isConnected || awaitingResponse}
                    className="text-left p-3 bg-white hover:bg-gray-50 rounded-lg border border-gray-200 transition-all hover:shadow-sm disabled:opacity-50 disabled:cursor-not-allowed group"
                  >
                    <p className="text-sm font-medium text-gray-700 group-hover:text-gray-900">
                      {suggestion.text}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">{suggestion.description}</p>
                  </motion.button>
                ))}
              </div>
            </div>
          </motion.div>
        )}

        {/* Active Task Results Area */}
        <AnimatePresence>
          {showResults && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="flex-1 p-6 overflow-y-auto"
            >
              {/* Current Task Header */}
              <div className="mb-6">
                <div className="flex items-center space-x-3 mb-2">
                  <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                    <Sparkles className="w-4 h-4 text-gray-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Working on your request</h3>
                    <p className="text-sm text-gray-600">{currentTask}</p>
                  </div>
                </div>

                {(isTyping || awaitingResponse) && (
                  <div className="flex items-center space-x-2 text-sm text-gray-500 ml-11">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                    </div>
                    <span>AI is processing your request...</span>
                  </div>
                )}
              </div>

              {/* Conversation Messages */}
              <div className="space-y-4 mb-4">
                {messages.map((message, index) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className={`flex ${message.role === 'USER' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div className={`max-w-[70%] ${message.role === 'USER' ? 'order-2' : ''}`}>
                      <div className={`rounded-lg px-4 py-2 ${
                        message.role === 'USER' 
                          ? 'bg-primary text-white' 
                          : 'bg-gray-100 text-gray-900'
                      }`}>
                        <p className="text-sm">{message.content}</p>
                      </div>
                      {message.intentRecognized && (
                        <p className="text-xs text-gray-500 mt-1">
                          Intent: {message.intentRecognized}
                        </p>
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Results Content */}
              <WorkspaceResults currentTask={currentTask} />

              {/* Error Display */}
              {(() => {
                const lastMessage = messages[messages.length - 1];
                if (lastMessage?.error || lastMessage?.type === 'ERROR') {
                  return (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="mt-4 bg-red-50 border border-red-200 rounded-lg p-4"
                    >
                      <div className="flex items-start space-x-3">
                        <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-red-900">Unable to process your request</h4>
                          <p className="text-sm text-red-700 mt-1">
                            {lastMessage.error || lastMessage.content || 'An unexpected error occurred. Please try again.'}
                          </p>
                          <button
                            onClick={() => {
                              setShowResults(false);
                              setCurrentTask(null);
                            }}
                            className="text-sm text-red-600 hover:text-red-800 underline mt-2"
                          >
                            Try a different request
                          </button>
                        </div>
                      </div>
                    </motion.div>
                  );
                }
                return null;
              })()}
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Command Bar - Primary focal point at bottom */}
      <div className="p-4 bg-white border-t border-gray-200">
        <form onSubmit={handleSubmit} className="flex items-center space-x-3">
          <div className="flex-1 relative">
            <Input
              ref={inputRef}
              type="text"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder={`Hi ${firstName}, how can I help you with your business finances today?`}
              disabled={!isConnected || awaitingResponse}
              className="pr-12 h-12 text-base border-gray-300 focus:border-primary focus:ring-primary"
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 text-gray-400 hover:text-gray-600"
              disabled={!isConnected}
            >
              <Mic className="h-4 w-4" />
            </Button>
          </div>
          <Button
            type="submit"
            disabled={!input.trim() || !isConnected || awaitingResponse}
            className="bg-primary hover:bg-primary/90 h-12 px-6"
          >
            {awaitingResponse ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <ArrowUp className="h-4 w-4" />
            )}
          </Button>
        </form>
      </div>
    </div>
  );
}
