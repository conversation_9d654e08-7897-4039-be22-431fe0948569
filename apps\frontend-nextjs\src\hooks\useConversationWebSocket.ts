"use client"

import { useEffect } from 'react';
import { useWebSocket } from './useWebSocket';
import { useConversationStore } from '@/stores';

/**
 * Custom hook that integrates WebSocket functionality with the conversation store
 * This eliminates the state synchronization issues between WebSocket provider and store
 */
export function useConversationWebSocket() {
  const {
    isConnected,
    isConnecting,
    connectionError,
    messages,
    sendCommand,
    sendTyping,
  } = useWebSocket();

  const {
    setConnectionState,
    setConnectionError,
    processWebSocketMessage,
    setAwaitingResponse,
  } = useConversationStore();

  // Sync WebSocket connection state directly with conversation store
  useEffect(() => {
    console.log('🔄 useConversationWebSocket - State sync:', {
      isConnected,
      isConnecting,
      hasConnectionError: !!connectionError,
      timestamp: new Date().toISOString()
    });

    // Update conversation store state directly
    useConversationStore.setState({
      isConnected,
      isConnecting,
      connectionError,
      connectionState: isConnected ? 'CONNECTED' : isConnecting ? 'CONNECTING' : 'DISCONNECTED'
    });

    // Also update via store actions for consistency
    if (isConnected) {
      setConnectionState('CONNECTED');
      setConnectionError(null);
    } else if (isConnecting) {
      setConnectionState('CONNECTING');
    } else {
      setConnectionState('DISCONNECTED');
    }

    if (connectionError) {
      setConnectionError(connectionError);
    }
  }, [isConnected, isConnecting, connectionError, setConnectionState, setConnectionError]);

  // Process incoming WebSocket messages
  useEffect(() => {
    console.log('🔄 useConversationWebSocket - Processing messages:', {
      messageCount: messages.length,
      latestMessage: messages[messages.length - 1]?.content?.substring(0, 50)
    });

    messages.forEach(message => {
      processWebSocketMessage(message);
      
      // If message is complete, stop awaiting response
      if (message.isComplete) {
        setAwaitingResponse(false);
      }
    });
  }, [messages, processWebSocketMessage, setAwaitingResponse]);

  // Return WebSocket methods for direct use
  return {
    isConnected,
    isConnecting,
    connectionError,
    sendCommand,
    sendTyping,
    messageCount: messages.length
  };
}
