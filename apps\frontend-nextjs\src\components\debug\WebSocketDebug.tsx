"use client"

import React, { useEffect, useState } from 'react';
import { useAuthStore } from '@/stores';
import { useWebSocket } from '@/hooks/useWebSocket';

export function WebSocketDebug() {
  const { token, isAuthenticated, user } = useAuthStore();
  const {
    isConnected,
    isConnecting,
    connectionError,
    messages,
    sendCommand,
  } = useWebSocket();

  const [testMessage, setTestMessage] = useState('');

  const handleSendTest = () => {
    if (testMessage.trim()) {
      sendCommand(testMessage);
      setTestMessage('');
    }
  };

  return (
    <div className="p-4 bg-gray-100 rounded-lg">
      <h3 className="text-lg font-semibold mb-4">WebSocket Debug Panel</h3>
      
      {/* Authentication Status */}
      <div className="mb-4">
        <h4 className="font-medium">Authentication:</h4>
        <p>Authenticated: {isAuthenticated ? 'Yes' : 'No'}</p>
        <p>User: {user?.email || 'None'}</p>
        <p>Token: {token ? `${token.substring(0, 20)}...` : 'None'}</p>
      </div>

      {/* WebSocket Status */}
      <div className="mb-4">
        <h4 className="font-medium">WebSocket Status:</h4>
        <p>Connected: {isConnected ? 'Yes' : 'No'}</p>
        <p>Connecting: {isConnecting ? 'Yes' : 'No'}</p>
        <p>Error: {connectionError || 'None'}</p>
        <p>Messages Received: {messages.length}</p>
      </div>

      {/* Test Controls */}
      <div className="mb-4">
        <h4 className="font-medium">Test Controls:</h4>
        <div className="flex gap-2">
          <input
            type="text"
            value={testMessage}
            onChange={(e) => setTestMessage(e.target.value)}
            placeholder="Enter test message"
            className="flex-1 px-3 py-2 border rounded"
          />
          <button
            onClick={handleSendTest}
            disabled={!isConnected || !testMessage.trim()}
            className="px-4 py-2 bg-blue-500 text-white rounded disabled:bg-gray-300"
          >
            Send
          </button>
        </div>
      </div>

      {/* Messages */}
      <div>
        <h4 className="font-medium">Recent Messages:</h4>
        <div className="max-h-40 overflow-y-auto bg-white p-2 rounded border">
          {messages.length === 0 ? (
            <p className="text-gray-500">No messages yet</p>
          ) : (
            messages.slice(-5).map((msg, index) => (
              <div key={index} className="mb-2 text-sm">
                <strong>{msg.role}:</strong> {msg.content}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
