package com.intellifin.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.intellifin.events.CommandProcessedEvent;
import com.intellifin.events.EntityExtractedEvent;
import com.intellifin.events.IntentFailedEvent;
import com.intellifin.events.IntentRecognizedEvent;
import com.intellifin.model.ConversationMessage;
import com.intellifin.model.User;
import com.intellifin.service.MessagingService;
import com.intellifin.service.MessageHandler;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.PostConstruct;

/**
 * Intent Event Processor for Story 2.2
 * Processes intent recognition and entity extraction events from messaging layer
 */
@Service
public class IntentEventProcessor {

    private final MessagingService messagingService;
    
    @Autowired
    private ConversationService conversationService;
    
    @Autowired
    private AiCommandProcessingService aiCommandProcessingService;

    @Autowired
    private ObjectMapper objectMapper;

    // Topic names for event processing
    private static final String INTENT_RECOGNIZED_TOPIC = "intent.recognized";
    private static final String ENTITY_EXTRACTED_TOPIC = "entity.extracted";
    private static final String COMMAND_PROCESSED_TOPIC = "command.processed";
    private static final String INTENT_FAILED_TOPIC = "intent.failed";

    public IntentEventProcessor(MessagingService messagingService) {
        this.messagingService = messagingService;
    }

    /**
     * Initialize event subscriptions
     */
    @PostConstruct
    public void initializeSubscriptions() {
        // This is a simplified approach. In a real application, you'd have a more robust way to handle this.
    }

    /**
     * Handler for intent recognition events
     */
    private class IntentRecognizedHandler implements MessageHandler {
        @Override
        public void handle(ConversationMessage message) {
            try {
                IntentRecognizedEvent event = objectMapper.convertValue(message.getPayload(), IntentRecognizedEvent.class);
                
                // Log intent recognition for analytics
                System.out.println("Intent recognized: " + event.getIntentName() + 
                                 " (confidence: " + event.getConfidence() + ")");
                
                // Update conversation with intent information
                conversationService.updateConversationWithIntent(
                    event.getSessionId(),
                    event.getIntentName(),
                    event.getConfidence(),
                    event.getDescription()
                );
                
                // Trigger next steps based on intent
                processIntentAction(event);
                
            } catch (Exception e) {
                System.err.println("Error processing intent recognition event: " + e.getMessage());
            }
        }
    }

    /**
     * Handler for entity extraction events
     */
    private class EntityExtractedHandler implements MessageHandler {
        @Override
        public void handle(ConversationMessage message) {
            try {
                EntityExtractedEvent event = objectMapper.convertValue(message.getPayload(), EntityExtractedEvent.class);
                
                // Log entity extraction for analytics
                System.out.println("Entities extracted: " + event.getEntities().size() + " entities");
                
                // Update conversation with entity information
                conversationService.updateConversationWithEntities(
                    event.getSessionId(),
                    event.getEntities()
                );
                
                // Process entities for business logic
                processExtractedEntities(event);
                
            } catch (Exception e) {
                System.err.println("Error processing entity extraction event: " + e.getMessage());
            }
        }
    }

    /**
     * Handler for command processing completion events
     */
    private class CommandProcessedHandler implements MessageHandler {
        @Override
        public void handle(ConversationMessage message) {
            try {
                CommandProcessedEvent event = objectMapper.convertValue(message.getPayload(), CommandProcessedEvent.class);
                
                // Log command processing completion
                System.out.println("Command processed: " + event.getCommand() + 
                                 " (success: " + event.isSuccess() + 
                                 ", time: " + event.getProcessingTimeMs() + "ms)");
                
                // Update conversation with final response
                conversationService.updateConversationWithResponse(
                    event.getSessionId(),
                    event.getResponse(),
                    event.isSuccess(),
                    event.getProcessingTimeMs()
                );
                
                // Analytics and monitoring
                recordCommandProcessingMetrics(event);
                
            } catch (Exception e) {
                System.err.println("Error processing command completion event: " + e.getMessage());
            }
        }
    }

    /**
     * Handler for intent processing failure events
     */
    private class IntentFailedHandler implements MessageHandler {
        @Override
        public void handle(ConversationMessage message) {
            try {
                IntentFailedEvent event = objectMapper.convertValue(message.getPayload(), IntentFailedEvent.class);
                
                // Log intent processing failure
                System.err.println("Intent processing failed: " + event.getErrorMessage() + 
                                 " (type: " + event.getErrorType() + ")");
                
                // Update conversation with error information
                conversationService.updateConversationWithError(
                    event.getSessionId(),
                    event.getErrorMessage(),
                    event.getErrorType()
                );
                
                // Trigger fallback processing
                triggerFallbackProcessing(event);
                
            } catch (Exception e) {
                System.err.println("Error processing intent failure event: " + e.getMessage());
            }
        }
    }

    /**
     * Process intent-specific actions
     */
    private void processIntentAction(IntentRecognizedEvent event) {
        switch (event.getIntentName()) {
            case "CREATE_INVOICE":
                // Trigger invoice creation workflow
                System.out.println("Triggering invoice creation workflow");
                break;
                
            case "SHOW_SUMMARY":
                // Trigger financial summary generation
                System.out.println("Triggering financial summary generation");
                break;
                
            case "CATEGORIZE_TRANSACTION":
                // Trigger transaction categorization
                System.out.println("Triggering transaction categorization");
                break;
                
            case "VIEW_TRANSACTIONS":
                // Trigger transaction view
                System.out.println("Triggering transaction view");
                break;
                
            case "UNCLEAR":
                // Trigger clarification workflow
                System.out.println("Triggering clarification workflow");
                break;
                
            default:
                System.out.println("No specific action for intent: " + event.getIntentName());
        }
    }

    /**
     * Process extracted entities for business logic
     */
    private void processExtractedEntities(EntityExtractedEvent event) {
        // Process different entity types
        event.getEntities().forEach((entityType, entityValue) -> {
            switch (entityType.toLowerCase()) {
                case "amount":
                    System.out.println("Processing amount entity: " + entityValue);
                    break;
                    
                case "client_name":
                    System.out.println("Processing client entity: " + entityValue);
                    break;
                    
                case "date_range":
                    System.out.println("Processing date range entity: " + entityValue);
                    break;
                    
                case "category":
                    System.out.println("Processing category entity: " + entityValue);
                    break;
                    
                default:
                    System.out.println("Processing entity " + entityType + ": " + entityValue);
            }
        });
    }

    /**
     * Record command processing metrics for analytics
     */
    private void recordCommandProcessingMetrics(CommandProcessedEvent event) {
        // TODO: Implement metrics recording
        System.out.println("Recording metrics for command: " + event.getCommand());
    }

    /**
     * Trigger fallback processing for failed intents
     */
    private void triggerFallbackProcessing(IntentFailedEvent event) {
        // TODO: Implement fallback processing
        System.out.println("Triggering fallback for failed intent: " + event.getCommand());
    }
}
