package com.intellifin.service;

import com.intellifin.dto.webhook.MTNTransactionWebhookRequest;
import com.intellifin.dto.webhook.WebhookProcessingResponse;
import com.intellifin.model.WebhookEvent;
import com.intellifin.model.FinancialAccount;
import com.intellifin.repository.WebhookEventRepository;
import com.intellifin.repository.FinancialAccountRepository;
import com.intellifin.messaging.WebhookMessagingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * Service for processing incoming webhooks from financial service providers
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WebhookProcessingService {

    private final WebhookEventRepository webhookEventRepository;
    private final FinancialAccountRepository financialAccountRepository;
    private final WebhookMessagingService webhookMessagingService;
    private final WebhookSecurityService webhookSecurityService;
    private final TransactionSyncService transactionSyncService;

    /**
     * Process MTN Mobile Money transaction webhook
     */
    @Transactional
    public WebhookProcessingResponse processMTNTransactionWebhook(
            MTNTransactionWebhookRequest request,
            String signature,
            String timestamp,
            String clientIp) {
        
        long startTime = System.currentTimeMillis();
        
        try {
            log.info("Processing MTN webhook for transaction: {}", request.getTransactionId());
            
            // Check for duplicate webhook
            Optional<WebhookEvent> existingEvent = webhookEventRepository
                    .findByWebhookIdAndSource(request.getTransactionId(), "MTN");
            
            if (existingEvent.isPresent()) {
                log.warn("Duplicate MTN webhook received for transaction: {}", request.getTransactionId());
                return WebhookProcessingResponse.builder()
                        .status("DUPLICATE")
                        .message("Webhook already processed")
                        .webhookEventId(existingEvent.get().getId())
                        .externalTransactionId(request.getTransactionId())
                        .processedAt(OffsetDateTime.now())
                        .processingTimeMs(System.currentTimeMillis() - startTime)
                        .build();
            }
            
            // Find the financial account
            Optional<FinancialAccount> accountOpt = financialAccountRepository
                    .findByProviderAndAccountInfo(request.getAccountId());
            
            if (accountOpt.isEmpty()) {
                log.error("Financial account not found for MTN account: {}", request.getAccountId());
                return createFailedResponse(request.getTransactionId(), 
                        "Financial account not found", startTime);
            }
            
            FinancialAccount account = accountOpt.get();
            
            // Verify webhook signature
            if (!webhookSecurityService.verifyMTNSignature(request, signature, timestamp, account)) {
                log.error("Invalid webhook signature for MTN transaction: {}", request.getTransactionId());
                return createFailedResponse(request.getTransactionId(), 
                        "Invalid webhook signature", startTime);
            }
            
            // Create webhook event record
            WebhookEvent webhookEvent = createWebhookEvent(request, account, clientIp);
            webhookEvent = webhookEventRepository.save(webhookEvent);
            
            // Process the transaction synchronously
            UUID transactionId = transactionSyncService.processWebhookTransaction(webhookEvent, request);
            
            // Update webhook event status
            webhookEvent.setProcessingStatus(WebhookEvent.ProcessingStatus.PROCESSED);
            webhookEvent.setProcessedAt(OffsetDateTime.now());
            webhookEventRepository.save(webhookEvent);
            
            // Publish webhook processed event
            webhookMessagingService.publishTransactionReceived(webhookEvent, transactionId);
            
            log.info("Successfully processed MTN webhook for transaction: {}", request.getTransactionId());
            
            return WebhookProcessingResponse.builder()
                    .status("PROCESSED")
                    .message("Webhook processed successfully")
                    .webhookEventId(webhookEvent.getId())
                    .transactionId(transactionId)
                    .externalTransactionId(request.getTransactionId())
                    .processedAt(OffsetDateTime.now())
                    .processingTimeMs(System.currentTimeMillis() - startTime)
                    .build();
            
        } catch (Exception e) {
            log.error("Error processing MTN webhook for transaction: {}", request.getTransactionId(), e);
            return createFailedResponse(request.getTransactionId(), e.getMessage(), startTime);
        }
    }

    /**
     * Process generic transaction webhook from other providers
     */
    @Transactional
    public WebhookProcessingResponse processGenericTransactionWebhook(
            String provider,
            Map<String, Object> payload,
            Map<String, String> headers,
            String clientIp) {
        
        long startTime = System.currentTimeMillis();
        String transactionId = extractTransactionId(payload);
        
        try {
            log.info("Processing {} webhook for transaction: {}", provider.toUpperCase(), transactionId);
            
            // Check for duplicate webhook
            Optional<WebhookEvent> existingEvent = webhookEventRepository
                    .findByWebhookIdAndSource(transactionId, provider.toUpperCase());
            
            if (existingEvent.isPresent()) {
                log.warn("Duplicate {} webhook received for transaction: {}", provider.toUpperCase(), transactionId);
                return WebhookProcessingResponse.builder()
                        .status("DUPLICATE")
                        .message("Webhook already processed")
                        .webhookEventId(existingEvent.get().getId())
                        .externalTransactionId(transactionId)
                        .processedAt(OffsetDateTime.now())
                        .processingTimeMs(System.currentTimeMillis() - startTime)
                        .build();
            }
            
            // Create webhook event record
            WebhookEvent webhookEvent = createGenericWebhookEvent(provider, payload, headers, clientIp);
            webhookEvent = webhookEventRepository.save(webhookEvent);
            
            // Process the transaction
            UUID internalTransactionId = transactionSyncService.processGenericWebhookTransaction(webhookEvent, payload);
            
            // Update webhook event status
            webhookEvent.setProcessingStatus(WebhookEvent.ProcessingStatus.PROCESSED);
            webhookEvent.setProcessedAt(OffsetDateTime.now());
            webhookEventRepository.save(webhookEvent);
            
            // Publish webhook processed event
            webhookMessagingService.publishTransactionReceived(webhookEvent, internalTransactionId);
            
            log.info("Successfully processed {} webhook for transaction: {}", provider.toUpperCase(), transactionId);
            
            return WebhookProcessingResponse.builder()
                    .status("PROCESSED")
                    .message("Webhook processed successfully")
                    .webhookEventId(webhookEvent.getId())
                    .transactionId(internalTransactionId)
                    .externalTransactionId(transactionId)
                    .processedAt(OffsetDateTime.now())
                    .processingTimeMs(System.currentTimeMillis() - startTime)
                    .build();
            
        } catch (Exception e) {
            log.error("Error processing {} webhook for transaction: {}", provider.toUpperCase(), transactionId, e);
            return createFailedResponse(transactionId, e.getMessage(), startTime);
        }
    }

    private WebhookEvent createWebhookEvent(MTNTransactionWebhookRequest request, 
                                          FinancialAccount account, String clientIp) {
        return WebhookEvent.builder()
                .webhookId(request.getTransactionId())
                .source("MTN")
                .eventType("TRANSACTION_CREATED")
                .financialAccount(account)
                .transactionId(request.getTransactionId())
                .payload(convertToJsonb(request))
                .processingStatus(WebhookEvent.ProcessingStatus.PROCESSING)
                .retryCount(0)
                .build();
    }

    private WebhookEvent createGenericWebhookEvent(String provider, Map<String, Object> payload, 
                                                 Map<String, String> headers, String clientIp) {
        String transactionId = extractTransactionId(payload);
        
        return WebhookEvent.builder()
                .webhookId(transactionId)
                .source(provider.toUpperCase())
                .eventType("TRANSACTION_CREATED")
                .transactionId(transactionId)
                .payload(convertToJsonb(payload))
                .processingStatus(WebhookEvent.ProcessingStatus.PROCESSING)
                .retryCount(0)
                .build();
    }

    private WebhookProcessingResponse createFailedResponse(String transactionId, String errorMessage, long startTime) {
        return WebhookProcessingResponse.builder()
                .status("FAILED")
                .message(errorMessage)
                .externalTransactionId(transactionId)
                .processedAt(OffsetDateTime.now())
                .processingTimeMs(System.currentTimeMillis() - startTime)
                .requiresRetry(true)
                .build();
    }

    private String extractTransactionId(Map<String, Object> payload) {
        // Try common field names for transaction ID
        Object id = payload.get("transactionId");
        if (id == null) id = payload.get("transaction_id");
        if (id == null) id = payload.get("id");
        if (id == null) id = payload.get("reference");
        
        return id != null ? id.toString() : "unknown";
    }

    private String convertToJsonb(Object obj) {
        // Convert object to JSON string for JSONB storage
        // Implementation would use Jackson ObjectMapper
        return "{}"; // Placeholder
    }
}
