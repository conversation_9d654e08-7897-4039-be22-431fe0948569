package com.intellifin.service;

import com.intellifin.dto.webhook.MTNTransactionWebhookRequest;
import com.intellifin.dto.sync.SyncStatusDto;
import com.intellifin.model.*;
import com.intellifin.repository.*;
import com.intellifin.messaging.TransactionSyncMessagingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * Service for synchronizing transactions from webhooks and external sources
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TransactionSyncService {

    private final TransactionRepository transactionRepository;
    private final SyncStatusRepository syncStatusRepository;
    private final TransactionSyncEventRepository transactionSyncEventRepository;
    private final FinancialAccountRepository financialAccountRepository;
    private final TransactionService transactionService;
    private final JournalService journalService;
    private final TransactionSyncMessagingService syncMessagingService;

    /**
     * Process transaction from MTN webhook
     */
    @Transactional
    public UUID processWebhookTransaction(WebhookEvent webhookEvent, MTNTransactionWebhookRequest request) {
        log.info("Processing webhook transaction for external ID: {}", request.getTransactionId());
        
        try {
            // Check if transaction already exists
            Optional<Transaction> existingTransaction = transactionRepository
                    .findByExternalTransactionId(request.getTransactionId());
            
            if (existingTransaction.isPresent()) {
                log.warn("Transaction already exists for external ID: {}", request.getTransactionId());
                return existingTransaction.get().getId();
            }
            
            // Create new transaction from webhook data
            Transaction transaction = createTransactionFromMTNWebhook(request, webhookEvent.getFinancialAccount());
            transaction = transactionRepository.save(transaction);
            
            // Create sync event record
            createSyncEvent(webhookEvent, transaction, "CREATE", "SUCCESS");
            
            // Update sync status
            updateSyncStatus(webhookEvent.getFinancialAccount(), true);
            
            // Trigger AI categorization
            transactionService.triggerAICategorization(transaction);
            
            // Publish transaction received event
            syncMessagingService.publishTransactionReceived(transaction, webhookEvent);
            
            log.info("Successfully created transaction {} from webhook for external ID: {}", 
                    transaction.getId(), request.getTransactionId());
            
            return transaction.getId();
            
        } catch (Exception e) {
            log.error("Error processing webhook transaction for external ID: {}", request.getTransactionId(), e);
            
            // Create failed sync event
            createFailedSyncEvent(webhookEvent, "CREATE", e.getMessage());
            
            // Update sync status with error
            updateSyncStatus(webhookEvent.getFinancialAccount(), false);
            
            // Publish sync failed event
            syncMessagingService.publishTransactionSyncFailed(request.getTransactionId(), e.getMessage());
            
            throw e;
        }
    }

    /**
     * Process transaction from generic webhook
     */
    @Transactional
    public UUID processGenericWebhookTransaction(WebhookEvent webhookEvent, Map<String, Object> payload) {
        String externalTransactionId = extractTransactionId(payload);
        log.info("Processing generic webhook transaction for external ID: {}", externalTransactionId);
        
        try {
            // Check if transaction already exists
            Optional<Transaction> existingTransaction = transactionRepository
                    .findByExternalTransactionId(externalTransactionId);
            
            if (existingTransaction.isPresent()) {
                log.warn("Transaction already exists for external ID: {}", externalTransactionId);
                return existingTransaction.get().getId();
            }
            
            // Create new transaction from generic webhook data
            Transaction transaction = createTransactionFromGenericWebhook(payload, webhookEvent.getFinancialAccount());
            transaction = transactionRepository.save(transaction);
            
            // Create sync event record
            createSyncEvent(webhookEvent, transaction, "CREATE", "SUCCESS");
            
            // Update sync status
            updateSyncStatus(webhookEvent.getFinancialAccount(), true);
            
            // Trigger AI categorization
            transactionService.triggerAICategorization(transaction);
            
            // Publish transaction received event
            syncMessagingService.publishTransactionReceived(transaction, webhookEvent);
            
            log.info("Successfully created transaction {} from generic webhook for external ID: {}", 
                    transaction.getId(), externalTransactionId);
            
            return transaction.getId();
            
        } catch (Exception e) {
            log.error("Error processing generic webhook transaction for external ID: {}", externalTransactionId, e);
            
            // Create failed sync event
            createFailedSyncEvent(webhookEvent, "CREATE", e.getMessage());
            
            // Update sync status with error
            updateSyncStatus(webhookEvent.getFinancialAccount(), false);
            
            // Publish sync failed event
            syncMessagingService.publishTransactionSyncFailed(externalTransactionId, e.getMessage());
            
            throw e;
        }
    }

    /**
     * Handle transaction categorization completion and create journal entries
     */
    @Transactional
    public void handleTransactionCategorized(UUID transactionId, UUID categoryId) {
        log.info("Handling categorization completion for transaction: {}", transactionId);
        
        try {
            Optional<Transaction> transactionOpt = transactionRepository.findById(transactionId);
            if (transactionOpt.isEmpty()) {
                log.error("Transaction not found: {}", transactionId);
                return;
            }
            
            Transaction transaction = transactionOpt.get();
            
            // Create balanced journal entries
            journalService.createJournalEntriesForTransaction(transaction, categoryId);
            
            // Update transaction status
            transaction.setStatus(Transaction.TransactionStatus.CATEGORIZED);
            transactionRepository.save(transaction);
            
            // Publish categorization completed event
            syncMessagingService.publishTransactionCategorized(transaction, categoryId);
            
            log.info("Successfully created journal entries for transaction: {}", transactionId);
            
        } catch (Exception e) {
            log.error("Error creating journal entries for transaction: {}", transactionId, e);
            
            // Publish categorization failed event
            syncMessagingService.publishTransactionSyncFailed(transactionId.toString(), e.getMessage());
        }
    }

    /**
     * Get sync status for a financial account
     */
    public SyncStatusDto getSyncStatus(UUID accountId) {
        Optional<SyncStatus> syncStatusOpt = syncStatusRepository
                .findByFinancialAccountIdAndSyncType(accountId, SyncStatus.SyncType.WEBHOOK);
        
        if (syncStatusOpt.isEmpty()) {
            return SyncStatusDto.builder()
                    .accountId(accountId)
                    .syncStatus(SyncStatusDto.SyncStatus.DISABLED)
                    .syncType(SyncStatusDto.SyncType.WEBHOOK)
                    .pendingTransactions(0)
                    .totalSyncedTransactions(0)
                    .failedTransactions(0)
                    .build();
        }
        
        SyncStatus syncStatus = syncStatusOpt.get();
        return SyncStatusDto.builder()
                .accountId(accountId)
                .lastSync(syncStatus.getLastSyncAt())
                .pendingTransactions(syncStatus.getPendingTransactions())
                .syncStatus(mapSyncStatus(syncStatus.getSyncStatus()))
                .errorMessage(syncStatus.getErrorMessage())
                .nextSync(syncStatus.getNextSyncAt())
                .totalSyncedTransactions(syncStatus.getTotalSyncedTransactions())
                .failedTransactions(syncStatus.getFailedTransactions())
                .syncType(SyncStatusDto.SyncType.WEBHOOK)
                .build();
    }

    private Transaction createTransactionFromMTNWebhook(MTNTransactionWebhookRequest request, FinancialAccount account) {
        return Transaction.builder()
                .user(account.getUser())
                .externalTransactionId(request.getTransactionId())
                .amount(request.getAmount())
                .description(request.getDescription())
                .transactionDate(request.getTimestamp().toLocalDate())
                .type(determineTransactionType(request.getTransactionType()))
                .status(Transaction.TransactionStatus.PENDING_CLASSIFICATION)
                .currency(request.getCurrency() != null ? request.getCurrency() : "ZMW")
                .reference(request.getReference())
                .counterpartyName(request.getCounterpartyName())
                .source("MTN_WEBHOOK")
                .build();
    }

    private Transaction createTransactionFromGenericWebhook(Map<String, Object> payload, FinancialAccount account) {
        return Transaction.builder()
                .user(account.getUser())
                .externalTransactionId(extractTransactionId(payload))
                .amount(extractAmount(payload))
                .description(extractDescription(payload))
                .transactionDate(extractDate(payload))
                .type(determineTransactionTypeFromPayload(payload))
                .status(Transaction.TransactionStatus.PENDING_CLASSIFICATION)
                .currency(extractCurrency(payload))
                .reference(extractReference(payload))
                .source(account.getProvider() + "_WEBHOOK")
                .build();
    }

    private void createSyncEvent(WebhookEvent webhookEvent, Transaction transaction, String operation, String status) {
        Optional<SyncStatus> syncStatusOpt = syncStatusRepository
                .findByFinancialAccountIdAndSyncType(webhookEvent.getFinancialAccount().getId(), SyncStatus.SyncType.WEBHOOK);
        
        if (syncStatusOpt.isPresent()) {
            TransactionSyncEvent syncEvent = TransactionSyncEvent.builder()
                    .syncStatus(syncStatusOpt.get())
                    .webhookEvent(webhookEvent)
                    .transaction(transaction)
                    .externalTransactionId(webhookEvent.getTransactionId())
                    .syncOperation(TransactionSyncEvent.SyncOperation.valueOf(operation))
                    .operationStatus(TransactionSyncEvent.OperationStatus.valueOf(status))
                    .processingTimeMs(System.currentTimeMillis())
                    .retryCount(0)
                    .build();
            
            transactionSyncEventRepository.save(syncEvent);
        }
    }

    private void createFailedSyncEvent(WebhookEvent webhookEvent, String operation, String errorMessage) {
        Optional<SyncStatus> syncStatusOpt = syncStatusRepository
                .findByFinancialAccountIdAndSyncType(webhookEvent.getFinancialAccount().getId(), SyncStatus.SyncType.WEBHOOK);
        
        if (syncStatusOpt.isPresent()) {
            TransactionSyncEvent syncEvent = TransactionSyncEvent.builder()
                    .syncStatus(syncStatusOpt.get())
                    .webhookEvent(webhookEvent)
                    .externalTransactionId(webhookEvent.getTransactionId())
                    .syncOperation(TransactionSyncEvent.SyncOperation.valueOf(operation))
                    .operationStatus(TransactionSyncEvent.OperationStatus.FAILED)
                    .errorDetails(errorMessage)
                    .processingTimeMs(System.currentTimeMillis())
                    .retryCount(0)
                    .build();
            
            transactionSyncEventRepository.save(syncEvent);
        }
    }

    private void updateSyncStatus(FinancialAccount account, boolean success) {
        Optional<SyncStatus> syncStatusOpt = syncStatusRepository
                .findByFinancialAccountIdAndSyncType(account.getId(), SyncStatus.SyncType.WEBHOOK);
        
        SyncStatus syncStatus;
        if (syncStatusOpt.isPresent()) {
            syncStatus = syncStatusOpt.get();
        } else {
            syncStatus = SyncStatus.builder()
                    .financialAccount(account)
                    .syncType(SyncStatus.SyncType.WEBHOOK)
                    .syncStatus(SyncStatus.Status.ACTIVE)
                    .pendingTransactions(0)
                    .failedTransactions(0)
                    .totalSyncedTransactions(0)
                    .build();
        }
        
        syncStatus.setLastSyncAt(OffsetDateTime.now());
        
        if (success) {
            syncStatus.setLastSuccessfulSyncAt(OffsetDateTime.now());
            syncStatus.setSyncStatus(SyncStatus.Status.ACTIVE);
            syncStatus.setTotalSyncedTransactions(syncStatus.getTotalSyncedTransactions() + 1);
            syncStatus.setErrorMessage(null);
        } else {
            syncStatus.setSyncStatus(SyncStatus.Status.FAILED);
            syncStatus.setFailedTransactions(syncStatus.getFailedTransactions() + 1);
        }
        
        syncStatusRepository.save(syncStatus);
    }

    // Helper methods for data extraction and mapping
    private Transaction.TransactionType determineTransactionType(String type) {
        if ("DEBIT".equalsIgnoreCase(type)) {
            return Transaction.TransactionType.EXPENSE;
        } else if ("CREDIT".equalsIgnoreCase(type)) {
            return Transaction.TransactionType.INCOME;
        }
        return Transaction.TransactionType.EXPENSE; // Default
    }

    private Transaction.TransactionType determineTransactionTypeFromPayload(Map<String, Object> payload) {
        Object type = payload.get("type");
        if (type != null) {
            return determineTransactionType(type.toString());
        }
        
        Object amount = payload.get("amount");
        if (amount instanceof Number) {
            return ((Number) amount).doubleValue() < 0 ? 
                    Transaction.TransactionType.EXPENSE : Transaction.TransactionType.INCOME;
        }
        
        return Transaction.TransactionType.EXPENSE; // Default
    }

    private SyncStatusDto.SyncStatus mapSyncStatus(SyncStatus.Status status) {
        return switch (status) {
            case ACTIVE -> SyncStatusDto.SyncStatus.ACTIVE;
            case FAILED -> SyncStatusDto.SyncStatus.FAILED;
            case PAUSED -> SyncStatusDto.SyncStatus.PAUSED;
            case DISABLED -> SyncStatusDto.SyncStatus.DISABLED;
        };
    }

    // Additional helper methods for extracting data from generic payloads
    private String extractTransactionId(Map<String, Object> payload) {
        Object id = payload.get("transactionId");
        if (id == null) id = payload.get("transaction_id");
        if (id == null) id = payload.get("id");
        if (id == null) id = payload.get("reference");
        return id != null ? id.toString() : UUID.randomUUID().toString();
    }

    private BigDecimal extractAmount(Map<String, Object> payload) {
        Object amount = payload.get("amount");
        if (amount instanceof Number) {
            return BigDecimal.valueOf(((Number) amount).doubleValue());
        }
        if (amount instanceof String) {
            try {
                return new BigDecimal((String) amount);
            } catch (NumberFormatException e) {
                return BigDecimal.ZERO;
            }
        }
        return BigDecimal.ZERO;
    }

    private String extractDescription(Map<String, Object> payload) {
        Object desc = payload.get("description");
        if (desc == null) desc = payload.get("memo");
        if (desc == null) desc = payload.get("narrative");
        return desc != null ? desc.toString() : "Webhook transaction";
    }

    private java.time.LocalDate extractDate(Map<String, Object> payload) {
        Object timestamp = payload.get("timestamp");
        if (timestamp != null) {
            try {
                return OffsetDateTime.parse(timestamp.toString()).toLocalDate();
            } catch (Exception e) {
                // Fall back to current date
            }
        }
        return java.time.LocalDate.now();
    }

    private String extractCurrency(Map<String, Object> payload) {
        Object currency = payload.get("currency");
        return currency != null ? currency.toString() : "ZMW";
    }

    private String extractReference(Map<String, Object> payload) {
        Object ref = payload.get("reference");
        if (ref == null) ref = payload.get("ref");
        if (ref == null) ref = payload.get("referenceNumber");
        return ref != null ? ref.toString() : null;
    }
}
