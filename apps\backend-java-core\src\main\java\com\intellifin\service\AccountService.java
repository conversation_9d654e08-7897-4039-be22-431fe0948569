package com.intellifin.service;

import com.intellifin.model.Account;
import com.intellifin.repository.AccountRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional
public class AccountService {

    private final AccountRepository accountRepository;

    public List<Account> getAllAccounts() {
        return accountRepository.findAll();
    }

    public Optional<Account> getAccountById(UUID id) {
        return accountRepository.findById(id);
    }

    public Optional<Account> getAccountByCode(String code) {
        return accountRepository.findByCode(code);
    }

    public Account createAccount(Account account) {
        // Add validation logic here, e.g., check for duplicate codes
        return accountRepository.save(account);
    }

    public Account updateAccount(UUID id, Account accountDetails) {
        Account account = accountRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Account not found with id: " + id));

        // Update fields
        account.setName(accountDetails.getName());
        account.setDescription(accountDetails.getDescription());
        account.setType(accountDetails.getType());
        account.setSubType(accountDetails.getSubType());
        account.setParentAccount(accountDetails.getParentAccount());
        account.setActive(accountDetails.isActive());

        return accountRepository.save(account);
    }

    public void deleteAccount(UUID id) {
        // Add validation logic here, e.g., prevent deletion if transactions are associated
        accountRepository.deleteById(id);
    }
}
