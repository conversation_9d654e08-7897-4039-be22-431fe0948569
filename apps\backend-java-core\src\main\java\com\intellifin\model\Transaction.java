package com.intellifin.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Transaction entity representing financial transactions with AI categorization support
 */
@Entity
@Table(name = "transactions", indexes = {
    @Index(name = "idx_transaction_user_id", columnList = "user_id"),
    @Index(name = "idx_transaction_account_id", columnList = "financial_account_id"),
    @Index(name = "idx_transaction_date", columnList = "date"),
    @Index(name = "idx_transaction_status", columnList = "status"),
    @Index(name = "idx_transaction_category", columnList = "category_id")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Transaction {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Column(name = "user_id", nullable = false)
    private UUID userId;

    @Column(name = "financial_account_id", nullable = false)
    private UUID financialAccountId;

    @Column(nullable = false)
    private LocalDate date;

    @Column(nullable = false, length = 1000)
    private String description;

    @Column(nullable = false, precision = 15, scale = 2)
    private BigDecimal amount;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 10)
    private TransactionType type;

    @Column(name = "category_id")
    private UUID categoryId;

    @Column(name = "ai_category_id")
    private UUID aiCategoryId;

    @Column(name = "ai_confidence", precision = 3, scale = 2)
    private BigDecimal aiConfidence;

    @Column(name = "ai_explanation", length = 2000)
    private String aiExplanation;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 25)
    @Builder.Default
    private TransactionStatus status = TransactionStatus.PENDING_CLASSIFICATION;

    @Column(nullable = false, length = 100)
    private String source;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "category_id", insertable = false, updatable = false)
    private Category category;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ai_category_id", insertable = false, updatable = false)
    private Category aiCategory;

    // Enums
    public enum TransactionType {
        INCOME,
        EXPENSE
    }

    public enum TransactionStatus {
        PENDING_CLASSIFICATION,
        CLASSIFIED,
        RECONCILED
    }

    // Helper methods
    public boolean hasAIClassification() {
        return aiCategoryId != null && aiConfidence != null;
    }

    public boolean isAIConfidenceHigh() {
        return aiConfidence != null && aiConfidence.compareTo(BigDecimal.valueOf(0.8)) >= 0;
    }

    public boolean isPendingClassification() {
        return status == TransactionStatus.PENDING_CLASSIFICATION;
    }

    public boolean isClassified() {
        return status == TransactionStatus.CLASSIFIED;
    }

    public boolean isReconciled() {
        return status == TransactionStatus.RECONCILED;
    }

    public void acceptAIClassification() {
        if (hasAIClassification()) {
            this.categoryId = this.aiCategoryId;
            this.status = TransactionStatus.CLASSIFIED;
        }
    }

    public void rejectAIClassification() {
        this.aiCategoryId = null;
        this.aiConfidence = null;
        this.aiExplanation = null;
    }

    public void setManualCategory(UUID categoryId) {
        this.categoryId = categoryId;
        this.status = TransactionStatus.CLASSIFIED;
        // Keep AI suggestion for learning purposes
    }
}
