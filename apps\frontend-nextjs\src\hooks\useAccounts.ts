import { useEffect } from 'react';
import { useAccountStore } from '../stores/accountStore';

export const useAccounts = () => {
  const { accounts, loading, error, fetchAccounts, createAccount, updateAccount, deleteAccount } = useAccountStore();

  useEffect(() => {
    fetchAccounts();
  }, [fetchAccounts]);

  return {
    accounts,
    loading,
    error,
    createAccount,
    updateAccount,
    deleteAccount,
  };
};
