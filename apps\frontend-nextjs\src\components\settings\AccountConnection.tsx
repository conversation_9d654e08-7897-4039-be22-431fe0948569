'use client';

import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert } from '@/components/ui/alert';
import { MTNConnectionFlow } from './MTNConnectionFlow';
import { DocumentUpload } from './DocumentUpload';
import { ConnectionStatus } from './ConnectionStatus';
import { useAccountConnection } from '@/hooks/useAccountConnection';
import { Smartphone, Upload, AlertCircle, CheckCircle, Clock } from 'lucide-react';

interface AccountConnectionProps {
  userId: string;
}

export const AccountConnection: React.FC<AccountConnectionProps> = ({ userId }) => {
  const {
    accounts,
    loading,
    error,
    hasConnectedAccounts,
    refreshAccounts,
    disconnectAccount
  } = useAccountConnection(userId);

  const [activeTab, setActiveTab] = useState<'api' | 'upload'>('api');
  const [showMTNFlow, setShowMTNFlow] = useState(false);

  useEffect(() => {
    refreshAccounts();
  }, [refreshAccounts]);

  const handleConnectionSuccess = () => {
    setShowMTNFlow(false);
    refreshAccounts();
  };

  const handleDisconnect = async (accountId: string) => {
    if (window.confirm('Are you sure you want to disconnect this account?')) {
      await disconnectAccount(accountId);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'CONNECTED':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'CONNECTING':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'ERROR':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'CONNECTED':
        return 'bg-green-100 text-green-800';
      case 'CONNECTING':
        return 'bg-yellow-100 text-yellow-800';
      case 'ERROR':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Account Connections</h2>
        <p className="text-gray-600 mt-1">
          Connect your financial accounts to automatically import transactions
        </p>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <div className="text-red-800">{error}</div>
        </Alert>
      )}

      {/* Connected Accounts */}
      {accounts.length > 0 && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Connected Accounts</h3>
          <div className="space-y-3">
            {accounts.map((account) => (
              <div
                key={account.accountId}
                className="flex items-center justify-between p-4 border rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <Smartphone className="h-5 w-5 text-gray-500" />
                  <div>
                    <div className="font-medium">
                      {account.accountInfo?.accountName || 'MTN Mobile Money'}
                    </div>
                    {account.accountInfo?.phoneNumber && (
                      <div className="text-sm text-gray-500">
                        {account.accountInfo.phoneNumber}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(account.status)}
                    <Badge className={getStatusColor(account.status)}>
                      {account.status}
                    </Badge>
                  </div>
                  <ConnectionStatus account={account} />
                  {account.status === 'CONNECTED' && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDisconnect(account.accountId)}
                    >
                      Disconnect
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Connection Options */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Add New Account</h3>
        
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'api' | 'upload')}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="api" className="flex items-center space-x-2">
              <Smartphone className="h-4 w-4" />
              <span>Live Connection</span>
            </TabsTrigger>
            <TabsTrigger value="upload" className="flex items-center space-x-2">
              <Upload className="h-4 w-4" />
              <span>Upload Statements</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="api" className="mt-6">
            <div className="space-y-4">
              <div className="text-center py-8">
                <Smartphone className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h4 className="text-lg font-medium mb-2">Connect MTN Mobile Money</h4>
                <p className="text-gray-600 mb-6">
                  Securely connect your MTN Mobile Money account to automatically import transactions
                </p>
                
                {!showMTNFlow ? (
                  <Button onClick={() => setShowMTNFlow(true)} className="px-8">
                    Connect MTN Account
                  </Button>
                ) : (
                  <MTNConnectionFlow
                    userId={userId}
                    onSuccess={handleConnectionSuccess}
                    onCancel={() => setShowMTNFlow(false)}
                  />
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="upload" className="mt-6">
            <div className="space-y-4">
              <div className="text-center py-4">
                <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h4 className="text-lg font-medium mb-2">Upload Bank Statements</h4>
                <p className="text-gray-600 mb-6">
                  Upload your MTN Mobile Money statements (PDF, CSV, or Excel) to import transactions
                </p>
              </div>
              
              <DocumentUpload
                userId={userId}
                onUploadComplete={refreshAccounts}
              />
            </div>
          </TabsContent>
        </Tabs>
      </Card>

      {/* Help Section */}
      <Card className="p-6 bg-blue-50 border-blue-200">
        <h4 className="font-medium text-blue-900 mb-2">Need Help?</h4>
        <p className="text-blue-800 text-sm">
          Having trouble connecting your account? Contact our support team for assistance.
        </p>
      </Card>
    </div>
  );
};
