# IntelliFin AI Service

AI service for the IntelliFin platform providing natural language processing, intent recognition, and entity extraction for conversational financial commands.

## 🚀 Features

### Story 2.2: Intent Recognition and Entity Extraction
- **Intent Recognition**: Understands user commands like "Show me recent transactions", "Create invoice for ZESCO"
- **Entity Extraction**: Extracts amounts (K2500), dates (last week), client names (ZESCO), categories (utilities)
- **Zambian Business Context**: Optimized for Zambian businesses with local terminology and currency
- **Confidence Scoring**: Provides confidence levels for all recognized intents and entities
- **Fallback Handling**: Graceful handling of unclear commands with clarification prompts

### Supported Intents
- `VIEW_TRANSACTIONS` - View transaction history and financial data
- `CREATE_INVOICE` - Create invoices for clients  
- `VIEW_FINANCIAL_SUMMARY` - View profit/loss and financial summaries
- `CATEGORIZE_TRANSACTION` - Categorize transactions and expenses
- `CONNECT_ACCOUNT` - Connect financial accounts (MTN Mobile Money, banks)
- `GET_HELP` - Get help and assistance

### Entity Types
- **Amounts**: Zambian Kwacha (K500, ZMW 1000, 500 kwacha)
- **Dates**: Relative (yesterday, last week) and specific dates
- **Client Names**: Business names (ZESCO, MTN) and person names
- **Categories**: Business expense categories (utilities, transport, etc.)
- **Account Types**: MTN Mobile Money, Airtel Money, bank accounts
- **Transaction Types**: Payment, expense, income, transfer

## 🛠️ Setup

### Prerequisites
- Python 3.10+
- Poetry for dependency management
- Ollama (for local development) OR Google API key (for production)

### Installation

1. **Install dependencies:**
```bash
cd apps/backend-python-ai
poetry install
```

2. **Set up environment variables:**
```bash
# For local development with Ollama
export OLLAMA_BASE_URL=http://localhost:11434
export ENVIRONMENT=development

# For production with Google Gemini
export GOOGLE_API_KEY=your_google_api_key
export ENVIRONMENT=production
```

3. **Start the service:**
```bash
# Quick start (recommended for development)
python start_dev.py

# Or manually with uvicorn
poetry run uvicorn src.main:app --reload --port 8002
```

## 📡 API Endpoints

### Intent Recognition
```http
POST /api/v1/ai/intent
Content-Type: application/json

{
  "command": "Show me recent transactions",
  "user_id": "user123",
  "session_id": "session456",
  "context": {}
}
```

### Complete Command Processing
```http
POST /api/v1/process-command
Content-Type: application/json

{
  "command": "Create invoice for ZESCO for K2500",
  "user_id": "user123", 
  "session_id": "session456",
  "context": {}
}
```

## 🧪 Testing

### Simple Test
```bash
python test_intent_simple.py
```

### Test Commands
- "Show me recent transactions"
- "Create invoice for ZESCO"
- "What's my profit this month?"
- "Connect my MTN Mobile Money account"

## 🌍 Zambian Business Context

Optimized for Zambian small businesses with:
- ZESCO (electricity), MTN/Airtel (mobile money)
- Zambian Kwacha currency handling
- Local business categories and terminology

## 🚀 Quick Start

```bash
# 1. Install dependencies
poetry install

# 2. Start development server
python start_dev.py

# 3. Test the API
curl -X POST http://localhost:8002/api/v1/ai/intent \
  -H "Content-Type: application/json" \
  -d '{"command": "Show me recent transactions", "user_id": "test", "session_id": "test"}'
```

The service will be available at http://localhost:8002 with API docs at http://localhost:8002/docs
