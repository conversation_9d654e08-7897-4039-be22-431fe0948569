"use client"

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Calendar,
  DollarSign,
  Building,
  Clock
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Transaction, Category } from '@/types/api';
import { AICategorizationBadge, CategorizationLoadingBadge } from './AICategorizationBadge';
import { CategorySelector, CategoryDisplay } from './CategorySelector';
import { useTransactionCategorization } from '@/hooks/useTransactionCategorization';
import { format } from 'date-fns';

interface TransactionRowProps {
  transaction: Transaction;
  categories: Category[];
  onEdit?: (transaction: Transaction) => void;
  onDelete?: (transactionId: string) => void;
  showActions?: boolean;
  compact?: boolean;
  className?: string;
}

/**
 * Individual transaction row component with categorization features
 */
export const TransactionRow: React.FC<TransactionRowProps> = ({
  transaction,
  categories,
  onEdit,
  onDelete,
  showActions = true,
  compact = false,
  className
}) => {
  const {
    acceptAISuggestion,
    rejectAISuggestion,
    manualCategorize,
    isTransactionBeingCategorized
  } = useTransactionCategorization();

  const [showCategorySelector, setShowCategorySelector] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  // Find category objects
  const currentCategory = categories.find(cat => cat.id === transaction.categoryId);
  const aiSuggestedCategory = categories.find(cat => cat.id === transaction.aiCategoryId);

  const isLoading = isTransactionBeingCategorized(transaction.id) || isProcessing;

  const handleAcceptAI = async () => {
    if (!transaction.aiCategoryId) return;
    
    setIsProcessing(true);
    try {
      await acceptAISuggestion(
        transaction.id,
        transaction.aiCategoryId,
        "User accepted AI suggestion"
      );
    } catch (error) {
      console.error('Failed to accept AI suggestion:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRejectAI = () => {
    setShowCategorySelector(true);
  };

  const handleManualCategorize = async (categoryId: string, categoryName: string) => {
    setIsProcessing(true);
    try {
      if (transaction.aiCategoryId) {
        // This is a rejection of AI suggestion
        await rejectAISuggestion(
          transaction.id,
          categoryId,
          `User chose ${categoryName} instead of AI suggestion`
        );
      } else {
        // This is manual categorization
        await manualCategorize(
          transaction.id,
          categoryId,
          `User manually categorized as ${categoryName}`
        );
      }
      setShowCategorySelector(false);
    } catch (error) {
      console.error('Failed to categorize transaction:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const getCategorizationStatus = () => {
    if (isLoading) return 'loading';
    if (transaction.status === 'PENDING_CLASSIFICATION') {
      if (transaction.aiCategoryId) return 'suggested';
      return 'pending';
    }
    if (transaction.categoryId) {
      if (transaction.aiCategoryId === transaction.categoryId) return 'accepted';
      if (transaction.aiCategoryId) return 'rejected';
      return 'manual';
    }
    return 'pending';
  };

  const formatAmount = (amount: number, type: 'INCOME' | 'EXPENSE') => {
    const formatted = new Intl.NumberFormat('en-ZM', {
      style: 'currency',
      currency: 'ZMW',
      minimumFractionDigits: 2
    }).format(Math.abs(amount));
    
    return {
      amount: formatted,
      color: type === 'INCOME' ? 'text-green-600' : 'text-red-600',
      sign: type === 'INCOME' ? '+' : '-'
    };
  };

  const { amount: formattedAmount, color: amountColor, sign } = formatAmount(transaction.amount, transaction.type);

  if (compact) {
    return (
      <div className={cn("flex items-center justify-between p-3 border-b", className)}>
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <span className="font-medium truncate">{transaction.description}</span>
            <Badge variant="outline" className="text-xs">
              {transaction.type}
            </Badge>
          </div>
          <div className="flex items-center gap-2 mt-1 text-sm text-muted-foreground">
            <Calendar className="w-3 h-3" />
            {format(new Date(transaction.date), 'MMM dd, yyyy')}
            {currentCategory && (
              <>
                <span>•</span>
                <CategoryDisplay category={currentCategory} size="sm" />
              </>
            )}
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <span className={cn("font-semibold", amountColor)}>
            {sign}{formattedAmount}
          </span>
        </div>
      </div>
    );
  }

  return (
    <Card className={cn("p-4", className)}>
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          {/* Transaction details */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-lg truncate">
                {transaction.description}
              </h3>
              
              <div className="flex items-center gap-4 mt-1 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  {format(new Date(transaction.date), 'MMM dd, yyyy')}
                </div>
                
                <div className="flex items-center gap-1">
                  <Building className="w-4 h-4" />
                  {transaction.source}
                </div>
                
                <Badge variant={transaction.type === 'INCOME' ? 'default' : 'secondary'}>
                  {transaction.type}
                </Badge>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <span className={cn("text-xl font-bold", amountColor)}>
                {sign}{formattedAmount}
              </span>
              
              {showActions && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {onEdit && (
                      <DropdownMenuItem onClick={() => onEdit(transaction)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                    )}
                    {onDelete && (
                      <DropdownMenuItem 
                        onClick={() => onDelete(transaction.id)}
                        className="text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          </div>

          {/* Categorization section */}
          <div className="space-y-2">
            {/* Current category */}
            {currentCategory && (
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Category:</span>
                <CategoryDisplay category={currentCategory} />
              </div>
            )}

            {/* AI categorization status */}
            {isLoading ? (
              <CategorizationLoadingBadge />
            ) : (
              <AICategorizationBadge
                confidence={transaction.aiConfidence}
                categoryName={aiSuggestedCategory?.name}
                explanation={transaction.aiExplanation}
                status={getCategorizationStatus()}
                onAccept={handleAcceptAI}
                onReject={handleRejectAI}
                isLoading={isLoading}
              />
            )}

            {/* Category selector for manual categorization */}
            {showCategorySelector && (
              <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-md">
                <span className="text-sm text-muted-foreground">Choose category:</span>
                <CategorySelector
                  selectedCategoryId={transaction.categoryId}
                  transactionType={transaction.type}
                  onCategorySelect={handleManualCategorize}
                  disabled={isLoading}
                  placeholder="Select category"
                  showCreateNew={false}
                />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowCategorySelector(false)}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
              </div>
            )}
          </div>

          {/* Status and timestamps */}
          <div className="flex items-center gap-4 mt-3 text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <Clock className="w-3 h-3" />
              Created {format(new Date(transaction.createdAt), 'MMM dd, HH:mm')}
            </div>
            
            <Badge variant="outline" className="text-xs">
              {transaction.status.replace('_', ' ').toLowerCase()}
            </Badge>
          </div>
        </div>
      </div>
    </Card>
  );
};
