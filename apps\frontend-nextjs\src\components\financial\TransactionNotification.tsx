'use client';

import React, { useEffect, useState } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert } from '@/components/ui/alert';
import { 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  DollarSign, 
  X,
  Bell,
  BellOff 
} from 'lucide-react';

interface TransactionNotificationProps {
  notification: TransactionNotification;
  onDismiss: (id: string) => void;
  onView: (transactionId: string) => void;
}

interface TransactionNotification {
  id: string;
  transactionId: string;
  type: 'NEW_TRANSACTION' | 'CATEGORIZED' | 'SYNC_FAILED';
  title: string;
  message: string;
  amount?: number;
  currency?: string;
  source?: string;
  timestamp: string;
  isRead: boolean;
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
}

export const TransactionNotification: React.FC<TransactionNotificationProps> = ({
  notification,
  onDismiss,
  onView
}) => {
  const [isVisible, setIsVisible] = useState(true);

  const getIcon = () => {
    switch (notification.type) {
      case 'NEW_TRANSACTION':
        return <DollarSign className="h-5 w-5 text-green-500" />;
      case 'CATEGORIZED':
        return <CheckCircle className="h-5 w-5 text-blue-500" />;
      case 'SYNC_FAILED':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Bell className="h-5 w-5 text-gray-500" />;
    }
  };

  const getPriorityColor = () => {
    switch (notification.priority) {
      case 'HIGH':
        return 'border-red-200 bg-red-50';
      case 'MEDIUM':
        return 'border-yellow-200 bg-yellow-50';
      case 'LOW':
        return 'border-blue-200 bg-blue-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const formatAmount = (amount: number, currency: string = 'ZMW') => {
    return new Intl.NumberFormat('en-ZM', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(amount);
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    
    return date.toLocaleDateString();
  };

  const handleDismiss = () => {
    setIsVisible(false);
    setTimeout(() => onDismiss(notification.id), 300);
  };

  const handleView = () => {
    onView(notification.transactionId);
  };

  if (!isVisible) return null;

  return (
    <Card className={`p-4 mb-3 transition-all duration-300 ${getPriorityColor()} ${
      !notification.isRead ? 'border-l-4 border-l-blue-500' : ''
    }`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3 flex-1">
          <div className="flex-shrink-0 mt-1">
            {getIcon()}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-1">
              <h4 className="text-sm font-medium text-gray-900 truncate">
                {notification.title}
              </h4>
              <div className="flex items-center space-x-2">
                {notification.source && (
                  <Badge variant="outline" className="text-xs">
                    {notification.source}
                  </Badge>
                )}
                <span className="text-xs text-gray-500">
                  {formatTimestamp(notification.timestamp)}
                </span>
              </div>
            </div>
            
            <p className="text-sm text-gray-600 mb-2">
              {notification.message}
            </p>
            
            {notification.amount && (
              <div className="flex items-center space-x-2 mb-2">
                <span className="text-sm font-medium text-gray-900">
                  Amount: {formatAmount(notification.amount, notification.currency)}
                </span>
              </div>
            )}
            
            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={handleView}
                className="text-xs"
              >
                View Transaction
              </Button>
              
              {notification.type === 'SYNC_FAILED' && (
                <Button
                  size="sm"
                  variant="outline"
                  className="text-xs text-orange-600 border-orange-200 hover:bg-orange-50"
                >
                  Retry Sync
                </Button>
              )}
            </div>
          </div>
        </div>
        
        <Button
          size="sm"
          variant="ghost"
          onClick={handleDismiss}
          className="flex-shrink-0 ml-2 h-6 w-6 p-0"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </Card>
  );
};

interface TransactionNotificationListProps {
  notifications: TransactionNotification[];
  onDismiss: (id: string) => void;
  onView: (transactionId: string) => void;
  onMarkAllRead: () => void;
  onClearAll: () => void;
}

export const TransactionNotificationList: React.FC<TransactionNotificationListProps> = ({
  notifications,
  onDismiss,
  onView,
  onMarkAllRead,
  onClearAll
}) => {
  const unreadCount = notifications.filter(n => !n.isRead).length;

  if (notifications.length === 0) {
    return (
      <div className="text-center py-8">
        <BellOff className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No notifications</h3>
        <p className="text-gray-600">
          You'll see transaction notifications here when they arrive.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">
          Transaction Notifications
          {unreadCount > 0 && (
            <Badge variant="default" className="ml-2">
              {unreadCount} new
            </Badge>
          )}
        </h3>
        
        <div className="flex items-center space-x-2">
          {unreadCount > 0 && (
            <Button
              size="sm"
              variant="outline"
              onClick={onMarkAllRead}
              className="text-xs"
            >
              Mark all read
            </Button>
          )}
          
          <Button
            size="sm"
            variant="outline"
            onClick={onClearAll}
            className="text-xs text-red-600 border-red-200 hover:bg-red-50"
          >
            Clear all
          </Button>
        </div>
      </div>
      
      <div className="max-h-96 overflow-y-auto space-y-2">
        {notifications.map((notification) => (
          <TransactionNotification
            key={notification.id}
            notification={notification}
            onDismiss={onDismiss}
            onView={onView}
          />
        ))}
      </div>
    </div>
  );
};
