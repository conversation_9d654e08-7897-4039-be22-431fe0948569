package com.intellifin.service;

import com.intellifin.dto.*;
import com.intellifin.messaging.TransactionCategorizationMessagingService;
import com.intellifin.messaging.events.TransactionCategorizedEvent;
import com.intellifin.model.Category;
import com.intellifin.model.Transaction;
import com.intellifin.repository.CategoryRepository;
import com.intellifin.repository.TransactionRepository;
import com.intellifin.websocket.TransactionWebSocketController;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
class TransactionServiceIntegrationTest {

    @Autowired
    private TransactionService transactionService;

    @Autowired
    private TransactionRepository transactionRepository;

    @Autowired
    private CategoryRepository categoryRepository;

    @MockBean
    private TransactionCategorizationMessagingService messagingService;

    @MockBean
    private TransactionWebSocketController webSocketController;

    private UUID testUserId;
    private Category testCategory;
    private Transaction testTransaction;

    @BeforeEach
    void setUp() {
        testUserId = UUID.randomUUID();
        
        // Create test category
        testCategory = Category.createSystemCategory(
            "Test Transport",
            true,
            "#3B82F6",
            "car"
        );
        testCategory = categoryRepository.save(testCategory);

        // Create test transaction
        testTransaction = Transaction.builder()
            .userId(testUserId)
            .financialAccountId(UUID.randomUUID())
            .date(LocalDate.now())
            .description("Test fuel purchase")
            .amount(BigDecimal.valueOf(150.00))
            .type(Transaction.TransactionType.EXPENSE)
            .status(Transaction.TransactionStatus.PENDING_CLASSIFICATION)
            .source("test")
            .build();
        testTransaction = transactionRepository.save(testTransaction);
    }

    @Test
    void shouldCreateTransactionAndTriggerAICategorization() {
        // Given
        TransactionDto newTransactionDto = TransactionDto.builder()
            .userId(testUserId)
            .financialAccountId(UUID.randomUUID())
            .date(LocalDate.now())
            .description("New taxi ride")
            .amount(BigDecimal.valueOf(50.00))
            .type(Transaction.TransactionType.EXPENSE)
            .source("manual")
            .build();

        // When
        TransactionDto result = transactionService.createTransaction(newTransactionDto);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isNotNull();
        assertThat(result.getStatus()).isEqualTo(Transaction.TransactionStatus.PENDING_CLASSIFICATION);
        assertThat(result.getDescription()).isEqualTo("New taxi ride");

        // Verify AI categorization was triggered
        verify(messagingService, times(1)).publishCategorizationRequest(any());
    }

    @Test
    void shouldGetTransactionsWithFilters() {
        // When
        Page<TransactionDto> result = transactionService.getTransactions(
            testUserId,
            Transaction.TransactionStatus.PENDING_CLASSIFICATION,
            Transaction.TransactionType.EXPENSE,
            null,
            null,
            null,
            null,
            null,
            PageRequest.of(0, 10)
        );

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContent()).hasSize(1);
        assertThat(result.getContent().get(0).getDescription()).isEqualTo("Test fuel purchase");
    }

    @Test
    void shouldCategorizeTransactionWithAIAcceptance() {
        // Given - Set up AI suggestion
        testTransaction.setAiCategoryId(testCategory.getId());
        testTransaction.setAiConfidence(BigDecimal.valueOf(0.85));
        testTransaction.setAiExplanation("AI suggested transport category");
        testTransaction = transactionRepository.save(testTransaction);

        CategorizationRequestDto request = CategorizationRequestDto.builder()
            .transactionId(testTransaction.getId())
            .categoryId(testCategory.getId())
            .isAISuggestion(true)
            .explanation("User accepted AI suggestion")
            .build();

        // When
        CategorizationResponseDto result = transactionService.categorizeTransaction(
            testTransaction.getId(),
            testUserId,
            request
        );

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getSuccess()).isTrue();
        assertThat(result.getTransaction().getCategoryId()).isEqualTo(testCategory.getId());
        assertThat(result.getTransaction().getStatus()).isEqualTo(Transaction.TransactionStatus.CLASSIFIED);
        assertThat(result.getAiLearning()).isNotNull();
        assertThat(result.getAiLearning().getFeedback()).isEqualTo("positive");

        // Verify messaging and WebSocket notifications
        verify(messagingService, times(1)).publishCategorizationAccepted(any());
        verify(webSocketController, times(1)).sendCategorizationAccepted(anyString(), any());
    }

    @Test
    void shouldCategorizeTransactionWithAIRejection() {
        // Given - Set up AI suggestion
        Category alternativeCategory = Category.createSystemCategory(
            "Food & Dining",
            true,
            "#EF4444",
            "utensils"
        );
        alternativeCategory = categoryRepository.save(alternativeCategory);

        testTransaction.setAiCategoryId(testCategory.getId());
        testTransaction.setAiConfidence(BigDecimal.valueOf(0.60));
        testTransaction.setAiExplanation("AI suggested transport category");
        testTransaction = transactionRepository.save(testTransaction);

        CategorizationRequestDto request = CategorizationRequestDto.builder()
            .transactionId(testTransaction.getId())
            .categoryId(alternativeCategory.getId())
            .isAISuggestion(false)
            .explanation("User chose different category")
            .build();

        // When
        CategorizationResponseDto result = transactionService.categorizeTransaction(
            testTransaction.getId(),
            testUserId,
            request
        );

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getSuccess()).isTrue();
        assertThat(result.getTransaction().getCategoryId()).isEqualTo(alternativeCategory.getId());
        assertThat(result.getTransaction().getStatus()).isEqualTo(Transaction.TransactionStatus.CLASSIFIED);
        assertThat(result.getAiLearning()).isNotNull();
        assertThat(result.getAiLearning().getFeedback()).isEqualTo("negative");

        // Verify messaging and WebSocket notifications
        verify(messagingService, times(1)).publishCategorizationRejected(any());
        verify(webSocketController, times(1)).sendCategorizationRejected(anyString(), any());
    }

    @Test
    void shouldHandleAICategorizationResult() {
        // Given
        TransactionCategorizedEvent event = TransactionCategorizedEvent.builder()
            .transactionId(testTransaction.getId())
            .suggestedCategoryId(testCategory.getId())
            .suggestedCategoryName(testCategory.getName())
            .confidence(BigDecimal.valueOf(0.88))
            .explanation("AI categorized as transport based on keywords")
            .processingTime(250L)
            .timestamp(LocalDateTime.now())
            .build();

        // When
        transactionService.handleAICategorizationResult(event);

        // Then
        Transaction updatedTransaction = transactionRepository.findById(testTransaction.getId()).orElseThrow();
        assertThat(updatedTransaction.getAiCategoryId()).isEqualTo(testCategory.getId());
        assertThat(updatedTransaction.getAiConfidence()).isEqualTo(BigDecimal.valueOf(0.88));
        assertThat(updatedTransaction.getAiExplanation()).isEqualTo("AI categorized as transport based on keywords");

        // Verify WebSocket notification
        verify(webSocketController, times(1)).sendTransactionCategorized(anyString(), any());
    }

    @Test
    void shouldBulkCategorizeTransactions() {
        // Given - Create additional test transactions
        Transaction transaction2 = Transaction.builder()
            .userId(testUserId)
            .financialAccountId(UUID.randomUUID())
            .date(LocalDate.now())
            .description("Restaurant bill")
            .amount(BigDecimal.valueOf(75.00))
            .type(Transaction.TransactionType.EXPENSE)
            .status(Transaction.TransactionStatus.PENDING_CLASSIFICATION)
            .source("test")
            .build();
        transaction2 = transactionRepository.save(transaction2);

        Category foodCategory = Category.createSystemCategory(
            "Food & Dining",
            true,
            "#EF4444",
            "utensils"
        );
        foodCategory = categoryRepository.save(foodCategory);

        BulkCategorizationRequestDto request = BulkCategorizationRequestDto.builder()
            .transactions(List.of(
                CategorizationRequestDto.builder()
                    .transactionId(testTransaction.getId())
                    .categoryId(testCategory.getId())
                    .isAISuggestion(false)
                    .build(),
                CategorizationRequestDto.builder()
                    .transactionId(transaction2.getId())
                    .categoryId(foodCategory.getId())
                    .isAISuggestion(false)
                    .build()
            ))
            .build();

        // When
        BulkCategorizationResponseDto result = transactionService.bulkCategorizeTransactions(testUserId, request);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getResults()).hasSize(2);
        assertThat(result.getResults().stream().allMatch(CategorizationResponseDto::getSuccess)).isTrue();

        // Verify transactions were updated
        Transaction updatedTransaction1 = transactionRepository.findById(testTransaction.getId()).orElseThrow();
        Transaction updatedTransaction2 = transactionRepository.findById(transaction2.getId()).orElseThrow();
        
        assertThat(updatedTransaction1.getCategoryId()).isEqualTo(testCategory.getId());
        assertThat(updatedTransaction1.getStatus()).isEqualTo(Transaction.TransactionStatus.CLASSIFIED);
        assertThat(updatedTransaction2.getCategoryId()).isEqualTo(foodCategory.getId());
        assertThat(updatedTransaction2.getStatus()).isEqualTo(Transaction.TransactionStatus.CLASSIFIED);
    }

    @Test
    void shouldUpdateTransaction() {
        // Given
        TransactionDto updates = TransactionDto.builder()
            .description("Updated description")
            .amount(BigDecimal.valueOf(200.00))
            .categoryId(testCategory.getId())
            .build();

        // When
        TransactionDto result = transactionService.updateTransaction(testTransaction.getId(), testUserId, updates);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getDescription()).isEqualTo("Updated description");
        assertThat(result.getAmount()).isEqualTo(BigDecimal.valueOf(200.00));
        assertThat(result.getCategoryId()).isEqualTo(testCategory.getId());
        assertThat(result.getStatus()).isEqualTo(Transaction.TransactionStatus.CLASSIFIED);
    }

    @Test
    void shouldDeleteTransaction() {
        // When
        transactionService.deleteTransaction(testTransaction.getId(), testUserId);

        // Then
        Optional<Transaction> deletedTransaction = transactionRepository.findById(testTransaction.getId());
        assertThat(deletedTransaction).isEmpty();
    }

    @Test
    void shouldThrowExceptionForUnauthorizedAccess() {
        // Given
        UUID unauthorizedUserId = UUID.randomUUID();

        // When & Then
        assertThatThrownBy(() -> 
            transactionService.getTransaction(testTransaction.getId(), unauthorizedUserId)
        ).isInstanceOf(RuntimeException.class);

        assertThatThrownBy(() -> 
            transactionService.deleteTransaction(testTransaction.getId(), unauthorizedUserId)
        ).isInstanceOf(RuntimeException.class);
    }

    @Test
    void shouldHandleNonExistentTransactionInAIResult() {
        // Given
        UUID nonExistentTransactionId = UUID.randomUUID();
        TransactionCategorizedEvent event = TransactionCategorizedEvent.builder()
            .transactionId(nonExistentTransactionId)
            .suggestedCategoryId(testCategory.getId())
            .suggestedCategoryName(testCategory.getName())
            .confidence(BigDecimal.valueOf(0.80))
            .explanation("AI categorization for non-existent transaction")
            .processingTime(200L)
            .timestamp(LocalDateTime.now())
            .build();

        // When & Then - Should not throw exception, just log warning
        transactionService.handleAICategorizationResult(event);

        // Verify no WebSocket notification was sent
        verify(webSocketController, never()).sendTransactionCategorized(anyString(), any());
    }

    @Test
    void shouldCreateCategoryIfNotExistsInAIResult() {
        // Given
        String newCategoryName = "New AI Category";
        TransactionCategorizedEvent event = TransactionCategorizedEvent.builder()
            .transactionId(testTransaction.getId())
            .suggestedCategoryId(UUID.randomUUID()) // Non-existent category ID
            .suggestedCategoryName(newCategoryName)
            .confidence(BigDecimal.valueOf(0.75))
            .explanation("AI suggested new category")
            .processingTime(300L)
            .timestamp(LocalDateTime.now())
            .build();

        // When
        transactionService.handleAICategorizationResult(event);

        // Then
        Transaction updatedTransaction = transactionRepository.findById(testTransaction.getId()).orElseThrow();
        assertThat(updatedTransaction.getAiCategoryId()).isNotNull();
        assertThat(updatedTransaction.getAiConfidence()).isEqualTo(BigDecimal.valueOf(0.75));

        // Verify new category was created
        Optional<Category> newCategory = categoryRepository.findByIsSystemDefinedTrueAndNameAndIsActiveTrue(newCategoryName);
        assertThat(newCategory).isPresent();
        assertThat(newCategory.get().getName()).isEqualTo(newCategoryName);
        assertThat(newCategory.get().getType()).isEqualTo(Category.CategoryType.EXPENSE);
    }
}
