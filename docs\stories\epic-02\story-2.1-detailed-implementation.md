# Story 2.1: Chart of Accounts & Category Mapping Implementation
## DETAILED READY-FOR-DEVELOPMENT SPECIFICATION

**Epic:** 2 - Core Financial Engine & Accounting Integrity  
**Priority:** CRITICAL - Must complete before any other V1 development  
**Estimated Effort:** 3-4 developer days  
**Dependencies:** None (foundational story)

---

## User Story

**As a** business owner  
**I want** a proper Chart of Accounts with category mapping  
**So that** my financial transactions follow standard accounting principles and maintain audit integrity

---

## Business Context

This story establishes the foundational accounting engine that all other financial features depend on. Without a proper Chart of Accounts and category mapping system, IntelliFin cannot provide accurate financial reporting or maintain accounting integrity. This is the cornerstone of the entire V1 platform.

**Why This Matters:**
- Enables professional-grade accounting that accountants can trust and audit
- Provides the foundation for all future financial features
- Ensures compliance with standard accounting principles
- Bridges user-friendly categories with formal accounting structure

---

## Technical Implementation Details

### 1. Database Schema Changes

#### New Tables to Create:

**accounts table:**
```sql
CREATE TABLE accounts (
    id BIGSERIAL PRIMARY KEY,
    account_code VARCHAR(10) NOT NULL UNIQUE,
    account_name VARCHAR(255) NOT NULL,
    account_type VARCHAR(50) NOT NULL, -- ASSET, LIABILITY, EQUITY, REVENUE, EXPENSE
    parent_account_id BIGINT REFERENCES accounts(id),
    normal_balance VARCHAR(10) NOT NULL, -- DEBIT or CREDIT
    is_active BOOLEAN DEFAULT true,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT REFERENCES users(id),
    updated_by BIGINT REFERENCES users(id)
);

CREATE INDEX idx_accounts_code ON accounts(account_code);
CREATE INDEX idx_accounts_type ON accounts(account_type);
CREATE INDEX idx_accounts_parent ON accounts(parent_account_id);
```

**account_categories table (mapping table):**
```sql
CREATE TABLE account_categories (
    id BIGSERIAL PRIMARY KEY,
    account_id BIGINT NOT NULL REFERENCES accounts(id),
    category_id BIGINT NOT NULL REFERENCES categories(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT REFERENCES users(id),
    UNIQUE(account_id, category_id)
);

CREATE INDEX idx_account_categories_account ON account_categories(account_id);
CREATE INDEX idx_account_categories_category ON account_categories(category_id);
```

#### Modify Existing Tables:

**categories table - add account mapping:**
```sql
ALTER TABLE categories ADD COLUMN default_account_id BIGINT REFERENCES accounts(id);
ALTER TABLE categories ADD COLUMN is_mapped_to_account BOOLEAN DEFAULT false;
```

### 2. Java Backend Implementation

#### New Entity Classes:

**Account.java:**
```java
@Entity
@Table(name = "accounts")
public class Account {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "account_code", unique = true, nullable = false)
    private String accountCode;
    
    @Column(name = "account_name", nullable = false)
    private String accountName;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "account_type", nullable = false)
    private AccountType accountType;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_account_id")
    private Account parentAccount;
    
    @OneToMany(mappedBy = "parentAccount", cascade = CascadeType.ALL)
    private List<Account> subAccounts = new ArrayList<>();
    
    @Enumerated(EnumType.STRING)
    @Column(name = "normal_balance", nullable = false)
    private BalanceType normalBalance;
    
    @Column(name = "is_active")
    private Boolean isActive = true;
    
    private String description;
    
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "created_by")
    private User createdBy;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "updated_by")
    private User updatedBy;
    
    // Constructors, getters, setters, equals, hashCode
}

public enum AccountType {
    ASSET, LIABILITY, EQUITY, REVENUE, EXPENSE
}

public enum BalanceType {
    DEBIT, CREDIT
}
```

**AccountCategory.java:**
```java
@Entity
@Table(name = "account_categories")
public class AccountCategory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "account_id", nullable = false)
    private Account account;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "category_id", nullable = false)
    private Category category;
    
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "created_by")
    private User createdBy;
    
    // Constructors, getters, setters
}
```

#### Repository Interfaces:

**AccountRepository.java:**
```java
@Repository
public interface AccountRepository extends JpaRepository<Account, Long> {
    Optional<Account> findByAccountCode(String accountCode);
    List<Account> findByAccountTypeOrderByAccountCode(AccountType accountType);
    List<Account> findByParentAccountIsNullOrderByAccountCode();
    List<Account> findByParentAccountOrderByAccountCode(Account parentAccount);
    List<Account> findByAccountNameContainingIgnoreCase(String accountName);
    boolean existsByAccountCode(String accountCode);
    
    @Query("SELECT a FROM Account a WHERE a.isActive = true ORDER BY a.accountCode")
    List<Account> findAllActiveAccounts();
    
    @Query("SELECT a FROM Account a LEFT JOIN FETCH a.subAccounts WHERE a.parentAccount IS NULL ORDER BY a.accountCode")
    List<Account> findRootAccountsWithSubAccounts();
}
```

**AccountCategoryRepository.java:**
```java
@Repository
public interface AccountCategoryRepository extends JpaRepository<AccountCategory, Long> {
    List<AccountCategory> findByAccount(Account account);
    List<AccountCategory> findByCategory(Category category);
    Optional<AccountCategory> findByAccountAndCategory(Account account, Category category);
    boolean existsByAccountAndCategory(Account account, Category category);
}
```

#### Service Layer:

**AccountService.java:**
```java
@Service
@Transactional
public class AccountService {
    
    private final AccountRepository accountRepository;
    private final AccountCategoryRepository accountCategoryRepository;
    private final CategoryRepository categoryRepository;
    
    public AccountService(AccountRepository accountRepository, 
                         AccountCategoryRepository accountCategoryRepository,
                         CategoryRepository categoryRepository) {
        this.accountRepository = accountRepository;
        this.accountCategoryRepository = accountCategoryRepository;
        this.categoryRepository = categoryRepository;
    }
    
    public Account createAccount(CreateAccountRequest request, User user) {
        validateAccountCode(request.getAccountCode());
        validateAccountHierarchy(request.getParentAccountId());
        
        Account account = new Account();
        account.setAccountCode(request.getAccountCode());
        account.setAccountName(request.getAccountName());
        account.setAccountType(request.getAccountType());
        account.setNormalBalance(determineNormalBalance(request.getAccountType()));
        account.setDescription(request.getDescription());
        account.setCreatedBy(user);
        account.setUpdatedBy(user);
        
        if (request.getParentAccountId() != null) {
            Account parentAccount = accountRepository.findById(request.getParentAccountId())
                .orElseThrow(() -> new EntityNotFoundException("Parent account not found"));
            account.setParentAccount(parentAccount);
        }
        
        return accountRepository.save(account);
    }
    
    public void mapCategoryToAccount(Long categoryId, Long accountId, User user) {
        Category category = categoryRepository.findById(categoryId)
            .orElseThrow(() -> new EntityNotFoundException("Category not found"));
        Account account = accountRepository.findById(accountId)
            .orElseThrow(() -> new EntityNotFoundException("Account not found"));
            
        validateCategoryAccountMapping(category, account);
        
        if (!accountCategoryRepository.existsByAccountAndCategory(account, category)) {
            AccountCategory mapping = new AccountCategory();
            mapping.setAccount(account);
            mapping.setCategory(category);
            mapping.setCreatedBy(user);
            accountCategoryRepository.save(mapping);
            
            // Update category default account
            category.setDefaultAccountId(accountId);
            category.setIsMappedToAccount(true);
            categoryRepository.save(category);
        }
    }
    
    public List<AccountHierarchyDto> getAccountHierarchy() {
        List<Account> rootAccounts = accountRepository.findRootAccountsWithSubAccounts();
        return rootAccounts.stream()
            .map(this::buildAccountHierarchy)
            .collect(Collectors.toList());
    }
    
    public BigDecimal calculateAccountBalance(Long accountId) {
        // Implementation will integrate with journal entries in Story 2.2
        // For now, return zero as placeholder
        return BigDecimal.ZERO;
    }
    
    private BalanceType determineNormalBalance(AccountType accountType) {
        switch (accountType) {
            case ASSET:
            case EXPENSE:
                return BalanceType.DEBIT;
            case LIABILITY:
            case EQUITY:
            case REVENUE:
                return BalanceType.CREDIT;
            default:
                throw new IllegalArgumentException("Unknown account type: " + accountType);
        }
    }
    
    private void validateAccountCode(String accountCode) {
        if (accountRepository.existsByAccountCode(accountCode)) {
            throw new BusinessException("Account code already exists: " + accountCode);
        }
        
        if (!isValidAccountCode(accountCode)) {
            throw new BusinessException("Invalid account code format: " + accountCode);
        }
    }
    
    private boolean isValidAccountCode(String accountCode) {
        // Validate standard numbering: 1000-1999 Assets, 2000-2999 Liabilities, etc.
        return accountCode.matches("^[1-5]\\d{3}$");
    }
    
    private AccountHierarchyDto buildAccountHierarchy(Account account) {
        AccountHierarchyDto dto = new AccountHierarchyDto();
        dto.setId(account.getId());
        dto.setAccountCode(account.getAccountCode());
        dto.setAccountName(account.getAccountName());
        dto.setAccountType(account.getAccountType());
        dto.setBalance(calculateAccountBalance(account.getId()));
        
        List<AccountHierarchyDto> subAccounts = account.getSubAccounts().stream()
            .map(this::buildAccountHierarchy)
            .collect(Collectors.toList());
        dto.setSubAccounts(subAccounts);
        
        return dto;
    }
}
```

#### REST Controller:

**AccountController.java:**
```java
@RestController
@RequestMapping("/api/accounts")
@Validated
public class AccountController {

    private final AccountService accountService;

    public AccountController(AccountService accountService) {
        this.accountService = accountService;
    }

    @PostMapping
    public ResponseEntity<AccountDto> createAccount(@Valid @RequestBody CreateAccountRequest request,
                                                   Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        Account account = accountService.createAccount(request, user);
        return ResponseEntity.ok(AccountMapper.toDto(account));
    }

    @GetMapping("/hierarchy")
    public ResponseEntity<List<AccountHierarchyDto>> getAccountHierarchy() {
        List<AccountHierarchyDto> hierarchy = accountService.getAccountHierarchy();
        return ResponseEntity.ok(hierarchy);
    }

    @GetMapping("/{id}")
    public ResponseEntity<AccountDto> getAccount(@PathVariable Long id) {
        Account account = accountService.findById(id);
        return ResponseEntity.ok(AccountMapper.toDto(account));
    }

    @GetMapping("/{id}/balance")
    public ResponseEntity<AccountBalanceDto> getAccountBalance(@PathVariable Long id) {
        BigDecimal balance = accountService.calculateAccountBalance(id);
        AccountBalanceDto dto = new AccountBalanceDto(id, balance);
        return ResponseEntity.ok(dto);
    }

    @PostMapping("/categories/map")
    public ResponseEntity<Void> mapCategoryToAccount(@Valid @RequestBody MapCategoryRequest request,
                                                    Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        accountService.mapCategoryToAccount(request.getCategoryId(), request.getAccountId(), user);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/search")
    public ResponseEntity<List<AccountDto>> searchAccounts(@RequestParam String query) {
        List<Account> accounts = accountService.searchAccounts(query);
        List<AccountDto> dtos = accounts.stream()
            .map(AccountMapper::toDto)
            .collect(Collectors.toList());
        return ResponseEntity.ok(dtos);
    }
}
```

### 3. Frontend Implementation

#### React Components:

**ChartOfAccountsPage.tsx:**
```typescript
import React, { useState, useEffect } from 'react';
import { AccountHierarchy, Account } from '@/types/accounting';
import { accountService } from '@/services/accountService';
import { AccountTree } from '@/components/accounts/AccountTree';
import { CreateAccountModal } from '@/components/accounts/CreateAccountModal';
import { CategoryMappingPanel } from '@/components/accounts/CategoryMappingPanel';

export const ChartOfAccountsPage: React.FC = () => {
  const [accounts, setAccounts] = useState<AccountHierarchy[]>([]);
  const [selectedAccount, setSelectedAccount] = useState<Account | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadAccountHierarchy();
  }, []);

  const loadAccountHierarchy = async () => {
    try {
      setLoading(true);
      const hierarchy = await accountService.getAccountHierarchy();
      setAccounts(hierarchy);
    } catch (error) {
      console.error('Failed to load account hierarchy:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAccountCreated = () => {
    setShowCreateModal(false);
    loadAccountHierarchy();
  };

  return (
    <div className="chart-of-accounts-page">
      <div className="page-header">
        <h1>Chart of Accounts</h1>
        <button
          onClick={() => setShowCreateModal(true)}
          className="btn btn-primary"
        >
          Add Account
        </button>
      </div>

      <div className="accounts-layout">
        <div className="accounts-tree">
          {loading ? (
            <div>Loading accounts...</div>
          ) : (
            <AccountTree
              accounts={accounts}
              selectedAccount={selectedAccount}
              onAccountSelect={setSelectedAccount}
            />
          )}
        </div>

        <div className="account-details">
          {selectedAccount && (
            <CategoryMappingPanel
              account={selectedAccount}
              onMappingUpdated={loadAccountHierarchy}
            />
          )}
        </div>
      </div>

      {showCreateModal && (
        <CreateAccountModal
          onClose={() => setShowCreateModal(false)}
          onAccountCreated={handleAccountCreated}
          parentAccounts={accounts}
        />
      )}
    </div>
  );
};
```

#### API Service:

**accountService.ts:**
```typescript
import { api } from '@/lib/api';
import { AccountHierarchy, Account, CreateAccountRequest, MapCategoryRequest } from '@/types/accounting';

export const accountService = {
  async getAccountHierarchy(): Promise<AccountHierarchy[]> {
    const response = await api.get('/accounts/hierarchy');
    return response.data;
  },

  async createAccount(request: CreateAccountRequest): Promise<Account> {
    const response = await api.post('/accounts', request);
    return response.data;
  },

  async getAccount(id: number): Promise<Account> {
    const response = await api.get(`/accounts/${id}`);
    return response.data;
  },

  async getAccountBalance(id: number): Promise<{ id: number; balance: number }> {
    const response = await api.get(`/accounts/${id}/balance`);
    return response.data;
  },

  async mapCategoryToAccount(request: MapCategoryRequest): Promise<void> {
    await api.post('/accounts/categories/map', request);
  },

  async searchAccounts(query: string): Promise<Account[]> {
    const response = await api.get(`/accounts/search?query=${encodeURIComponent(query)}`);
    return response.data;
  }
};
```

### 4. Data Migration Script

**V1_001__Create_Chart_Of_Accounts.sql:**
```sql
-- Create accounts table
-- (Schema creation from above)

-- Seed standard Zambian Chart of Accounts
INSERT INTO accounts (account_code, account_name, account_type, normal_balance, description) VALUES
-- Assets (1000-1999)
('1000', 'Current Assets', 'ASSET', 'DEBIT', 'Short-term assets'),
('1100', 'Cash and Cash Equivalents', 'ASSET', 'DEBIT', 'Cash, bank accounts, mobile money'),
('1110', 'Cash on Hand', 'ASSET', 'DEBIT', 'Physical cash'),
('1120', 'Bank Accounts', 'ASSET', 'DEBIT', 'Traditional bank accounts'),
('1130', 'Mobile Money Accounts', 'ASSET', 'DEBIT', 'MTN, Airtel mobile money'),
('1200', 'Accounts Receivable', 'ASSET', 'DEBIT', 'Money owed by customers'),
('1300', 'Inventory', 'ASSET', 'DEBIT', 'Goods for sale'),

-- Liabilities (2000-2999)
('2000', 'Current Liabilities', 'LIABILITY', 'CREDIT', 'Short-term obligations'),
('2100', 'Accounts Payable', 'LIABILITY', 'CREDIT', 'Money owed to suppliers'),
('2200', 'Accrued Expenses', 'LIABILITY', 'CREDIT', 'Expenses incurred but not paid'),

-- Equity (3000-3999)
('3000', 'Owner Equity', 'EQUITY', 'CREDIT', 'Owner investment and retained earnings'),
('3100', 'Capital', 'EQUITY', 'CREDIT', 'Initial investment'),
('3200', 'Retained Earnings', 'EQUITY', 'CREDIT', 'Accumulated profits'),

-- Revenue (4000-4999)
('4000', 'Revenue', 'REVENUE', 'CREDIT', 'Income from business operations'),
('4100', 'Sales Revenue', 'REVENUE', 'CREDIT', 'Revenue from sales'),
('4200', 'Service Revenue', 'REVENUE', 'CREDIT', 'Revenue from services'),

-- Expenses (5000-5999)
('5000', 'Operating Expenses', 'EXPENSE', 'DEBIT', 'Business operating costs'),
('5100', 'Cost of Goods Sold', 'EXPENSE', 'DEBIT', 'Direct costs of products sold'),
('5200', 'Administrative Expenses', 'EXPENSE', 'DEBIT', 'General admin costs'),
('5300', 'Transport Expenses', 'EXPENSE', 'DEBIT', 'Transportation costs'),
('5400', 'Communication Expenses', 'EXPENSE', 'DEBIT', 'Phone, internet costs'),
('5500', 'Utilities', 'EXPENSE', 'DEBIT', 'Electricity, water, etc.');

-- Set up parent-child relationships
UPDATE accounts SET parent_account_id = (SELECT id FROM accounts WHERE account_code = '1000') WHERE account_code IN ('1100', '1200', '1300');
UPDATE accounts SET parent_account_id = (SELECT id FROM accounts WHERE account_code = '1100') WHERE account_code IN ('1110', '1120', '1130');
UPDATE accounts SET parent_account_id = (SELECT id FROM accounts WHERE account_code = '2000') WHERE account_code IN ('2100', '2200');
UPDATE accounts SET parent_account_id = (SELECT id FROM accounts WHERE account_code = '3000') WHERE account_code IN ('3100', '3200');
UPDATE accounts SET parent_account_id = (SELECT id FROM accounts WHERE account_code = '4000') WHERE account_code IN ('4100', '4200');
UPDATE accounts SET parent_account_id = (SELECT id FROM accounts WHERE account_code = '5000') WHERE account_code IN ('5100', '5200', '5300', '5400', '5500');
```

---

## Acceptance Criteria (Detailed)

### Functional Requirements:
- [ ] **AC1:** Account data model supports full hierarchy with parent-child relationships
- [ ] **AC2:** Standard Zambian Chart of Accounts is seeded with proper account codes (1000-5999)
- [ ] **AC3:** Category management UI allows users to create categories and map to accounts
- [ ] **AC4:** Account hierarchy displays correctly with expandable tree structure
- [ ] **AC5:** Account codes follow standard numbering (Assets 1000-1999, Liabilities 2000-2999, etc.)
- [ ] **AC6:** Category-to-Account mapping validates accounting integrity (expense categories → expense accounts)
- [ ] **AC7:** System prevents deletion of accounts with associated transactions
- [ ] **AC8:** Account balance calculations work correctly based on normal balance type
- [ ] **AC9:** API endpoints support full CRUD operations with proper validation
- [ ] **AC10:** Account search and filtering performs efficiently (< 500ms response)
- [ ] **AC11:** Audit trail captures all Chart of Accounts modifications
- [ ] **AC12:** Data migration preserves existing categories during account implementation

### Technical Requirements:
- [ ] **TR1:** Database schema supports account hierarchy and category mapping
- [ ] **TR2:** JPA entities properly model account relationships
- [ ] **TR3:** REST API follows OpenAPI specification
- [ ] **TR4:** Frontend components are responsive and accessible
- [ ] **TR5:** Error handling provides clear user feedback
- [ ] **TR6:** Performance meets requirements (Chart of Accounts loads < 1 second)
- [ ] **TR7:** Unit tests cover account hierarchy and balance calculations
- [ ] **TR8:** Integration tests verify API endpoints
- [ ] **TR9:** No breaking changes to existing transaction system

---

## Definition of Done

### Development Complete:
- [ ] All database migrations executed successfully
- [ ] Java entities, repositories, and services implemented
- [ ] REST API endpoints implemented with proper validation
- [ ] Frontend components implemented and integrated
- [ ] Account hierarchy displays correctly in UI
- [ ] Category mapping functionality works end-to-end

### Quality Assurance:
- [ ] Unit tests achieve 90%+ code coverage
- [ ] Integration tests verify API contracts
- [ ] Frontend tests verify user interactions
- [ ] Performance tests meet response time requirements
- [ ] Security tests verify access controls

### Documentation:
- [ ] API documentation updated in OpenAPI spec
- [ ] Database schema documented
- [ ] Chart of Accounts setup guide created
- [ ] Developer documentation updated

### Deployment Ready:
- [ ] Code reviewed and approved
- [ ] Database migrations tested in staging
- [ ] No breaking changes to existing functionality
- [ ] Monitoring and logging configured
- [ ] Ready for production deployment

---

## Risk Mitigation

**Risk 1:** Data migration breaks existing categories
- **Mitigation:** Comprehensive backup and rollback plan, staged migration

**Risk 2:** Performance issues with large account hierarchies
- **Mitigation:** Database indexing, lazy loading, pagination

**Risk 3:** User confusion with accounting terminology
- **Mitigation:** Clear UI labels, help text, guided setup wizard

---

## Success Metrics

- Chart of Accounts loads in < 1 second
- Category mapping completion rate > 95%
- Zero data loss during migration
- User can successfully create and map categories within 2 minutes
- Account balance calculations are 100% accurate

---

This story is now ready for immediate development pickup by any developer familiar with the IntelliFin stack.
```
