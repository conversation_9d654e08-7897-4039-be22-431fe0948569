'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Separator } from '@/components/ui/separator';
import { CheckCircle, XCircle, Calendar, User, Hash, FileText } from 'lucide-react';
import { JournalEntry } from '@intellifin/data-models';
import { formatCurrency, formatDate, formatDateTime } from '@/utils/formatting';

interface JournalEntryDetailsProps {
  entry: JournalEntry;
}

export function JournalEntryDetails({ entry }: JournalEntryDetailsProps) {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return <Badge variant="secondary">Draft</Badge>;
      case 'POSTED':
        return <Badge variant="default">Posted</Badge>;
      case 'REVERSED':
        return <Badge variant="destructive">Reversed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getBalanceBadge = (isBalanced: boolean) => {
    return isBalanced ? (
      <Badge variant="default" className="bg-green-100 text-green-800">
        <CheckCircle className="w-3 h-3 mr-1" />
        Balanced
      </Badge>
    ) : (
      <Badge variant="destructive">
        <XCircle className="w-3 h-3 mr-1" />
        Unbalanced
      </Badge>
    );
  };

  const totalDebits = entry.lines?.reduce((sum, line) => sum + (line.debitAmount || 0), 0) || 0;
  const totalCredits = entry.lines?.reduce((sum, line) => sum + (line.creditAmount || 0), 0) || 0;

  return (
    <div className="space-y-6">
      {/* Header Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center space-x-2">
              <Hash className="w-5 h-5" />
              <span>Journal Entry {entry.entryNumber}</span>
            </span>
            <div className="flex space-x-2">
              {getStatusBadge(entry.status)}
              {getBalanceBadge(totalDebits === totalCredits)}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            <div className="space-y-2">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Calendar className="w-4 h-4" />
                <span>Entry Date:</span>
                <span className="font-medium">{formatDate(entry.entryDate)}</span>
              </div>
              
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <FileText className="w-4 h-4" />
                <span>Description:</span>
                <span className="font-medium">{entry.description}</span>
              </div>
              
              {entry.referenceNumber && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Hash className="w-4 h-4" />
                  <span>Reference:</span>
                  <span className="font-medium">{entry.referenceNumber}</span>
                </div>
              )}
              
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <span>Total Amount:</span>
                <span className="font-mono font-medium">{formatCurrency(entry.totalAmount)}</span>
              </div>
              
              {entry.sourceType && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <span>Source:</span>
                  <span className="font-medium">{entry.sourceType}</span>
                </div>
              )}
              
              {entry.transactionId && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <span>Transaction ID:</span>
                  <span className="font-mono text-xs">{entry.transactionId}</span>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Journal Entry Lines */}
      <Card>
        <CardHeader>
          <CardTitle>Journal Entry Lines</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Line #</TableHead>
                <TableHead>Account</TableHead>
                <TableHead>Description</TableHead>
                <TableHead className="text-right">Debit</TableHead>
                <TableHead className="text-right">Credit</TableHead>
                <TableHead>Reference</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {entry.lines?.map((line, index) => (
                <TableRow key={line.id || index}>
                  <TableCell className="font-mono text-sm">
                    {line.lineNumber || index + 1}
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{line.account?.name}</div>
                      <div className="text-sm text-gray-500">{line.account?.code}</div>
                    </div>
                  </TableCell>
                  <TableCell className="max-w-xs">
                    {line.description}
                  </TableCell>
                  <TableCell className="text-right font-mono">
                    {line.debitAmount && line.debitAmount > 0 ? formatCurrency(line.debitAmount) : '—'}
                  </TableCell>
                  <TableCell className="text-right font-mono">
                    {line.creditAmount && line.creditAmount > 0 ? formatCurrency(line.creditAmount) : '—'}
                  </TableCell>
                  <TableCell className="text-sm text-gray-500">
                    {line.referenceNumber || '—'}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          <Separator className="my-4" />
          
          {/* Totals */}
          <div className="flex justify-end space-x-8">
            <div className="text-right">
              <div className="text-sm text-gray-600">Total Debits</div>
              <div className="font-mono font-medium">{formatCurrency(totalDebits)}</div>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-600">Total Credits</div>
              <div className="font-mono font-medium">{formatCurrency(totalCredits)}</div>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-600">Difference</div>
              <div className={`font-mono font-medium ${
                totalDebits === totalCredits ? 'text-green-600' : 'text-red-600'
              }`}>
                {formatCurrency(Math.abs(totalDebits - totalCredits))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Audit Information */}
      <Card>
        <CardHeader>
          <CardTitle>Audit Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 text-sm">
            <div className="space-y-2">
              <div className="flex items-center space-x-2 text-gray-600">
                <User className="w-4 h-4" />
                <span>Created by:</span>
                <span className="font-medium">{entry.createdBy?.email || 'System'}</span>
              </div>
              <div className="flex items-center space-x-2 text-gray-600">
                <Calendar className="w-4 h-4" />
                <span>Created at:</span>
                <span className="font-medium">{formatDateTime(entry.createdAt)}</span>
              </div>
              
              {entry.postedBy && (
                <>
                  <div className="flex items-center space-x-2 text-gray-600">
                    <User className="w-4 h-4" />
                    <span>Posted by:</span>
                    <span className="font-medium">{entry.postedBy.email}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-600">
                    <Calendar className="w-4 h-4" />
                    <span>Posted at:</span>
                    <span className="font-medium">{formatDateTime(entry.postedAt)}</span>
                  </div>
                </>
              )}
              
              {entry.reversedBy && (
                <>
                  <div className="flex items-center space-x-2 text-gray-600">
                    <User className="w-4 h-4" />
                    <span>Reversed by:</span>
                    <span className="font-medium">{entry.reversedBy.email}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-600">
                    <Calendar className="w-4 h-4" />
                    <span>Reversed at:</span>
                    <span className="font-medium">{formatDateTime(entry.reversedAt)}</span>
                  </div>
                  {entry.reversalReason && (
                    <div className="flex items-center space-x-2 text-gray-600">
                      <FileText className="w-4 h-4" />
                      <span>Reason:</span>
                      <span className="font-medium">{entry.reversalReason}</span>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
