# Story 5.1: Conversational Invoice Draft Creation

**Epic:** Invoice Generation & ZRA Compliance  
**Status:** Ready for Development  
**Priority:** High  
**Story Points:** 13

## User Story

**As a** user  
**I want** to create invoice drafts through natural language commands  
**So that** I can quickly generate professional invoices without navigating complex forms

## Acceptance Criteria

- [ ] User can create invoice drafts via conversational commands
- [ ] System extracts client, amount, and service details from natural language
- [ ] AI suggests missing information and asks for clarification when needed
- [ ] Invoice draft is displayed for user review and approval
- [ ] Users can edit invoice details before finalizing
- [ ] System validates invoice data for ZRA compliance
- [ ] Invoice numbering is automatically generated
- [ ] Client information is retrieved from existing client database
- [ ] Error handling for invalid or incomplete invoice data
- [ ] API contract is defined and documented
- [ ] Invoice creation events are published via messaging abstraction layer
- [ ] AI processing events work consistently across RabbitMQ (local) and Azure Service Bus (production)
- [ ] Failed invoice processing events are routed to dead-letter queues
- [ ] Manual ZRA submission tracking for invoices when API is unavailable

## Technical Implementation

### Frontend Changes
- `src/components/invoice/InvoiceDraft.tsx` - Invoice draft display and editing
- `src/components/invoice/InvoiceForm.tsx` - Invoice form with validation
- `src/components/invoice/ClientSelector.tsx` - Client selection component
- `src/components/invoice/LineItemEditor.tsx` - Invoice line item editing
- `src/hooks/useInvoiceCreation.ts` - Invoice creation logic
- `src/stores/invoiceStore.ts` - Invoice state management

### API Gateway Changes
- `src/main/java/com/intellifin/gateway/routes/InvoiceController.java` - Invoice API routing
- `src/main/java/com/intellifin/gateway/middleware/InvoiceValidation.java` - Invoice validation middleware

### Service Changes
- **Core Backend**: `src/main/java/com/intellifin/invoices/` - Invoice management with Spring Cloud Stream
- **Core Backend**: `src/main/java/com/intellifin/messaging/` - Invoice creation event handling
- **AI Service**: `src/invoice_creation.py` - AI-powered invoice creation with MessagingService integration
- **AI Service**: `src/services/messaging_service.py` - Python messaging abstraction implementation
- **Database**: Invoice and client data models

### Database Changes
- Invoice table with draft status and ZRA compliance fields
- InvoiceLineItem table for individual line items
- Client table for client information retrieval

## API Contracts

```typescript
// Invoice creation contracts
interface InvoiceDraftRequest {
  clientId: string;
  issueDate: string;
  dueDate: string;
  lineItems: {
    description: string;
    quantity: number;
    unitPrice: number;
  }[];
  notes?: string;
}

interface InvoiceDraft {
  id: string;
  userId: string;
  clientId: string;
  invoiceNumber: string;
  issueDate: string;
  dueDate: string;
  subtotal: number;
  taxAmount: number;
  totalAmount: number;
  status: 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE' | 'CANCELLED';
  lineItems: InvoiceLineItem[];
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

interface ConversationalInvoiceRequest {
  command: string;
  userId: string;
  context?: {
    previousCommands: string[];
    clientSuggestions?: string[];
  };
}

interface ConversationalInvoiceResponse {
  invoiceDraft?: InvoiceDraft;
  missingInfo?: {
    field: string;
    message: string;
    suggestions?: string[];
  }[];
  clarifications?: {
    question: string;
    options?: string[];
  }[];
}

interface InvoiceAPI {
  POST /api/v1/invoices/conversational: {
    body: ConversationalInvoiceRequest;
    response: ConversationalInvoiceResponse;
    errors: { 400: "Invalid request", 404: "Client not found" };
  };
  
  POST /api/v1/invoices: {
    body: InvoiceDraftRequest;
    response: InvoiceDraft;
    errors: { 400: "Validation error", 500: "Creation failed" };
  };
  
  PUT /api/v1/invoices/{id}: {
    body: Partial<InvoiceDraftRequest>;
    response: InvoiceDraft;
    errors: { 400: "Validation error", 404: "Invoice not found" };
  };
  
  GET /api/v1/invoices/{id}: {
    response: InvoiceDraft;
    errors: { 404: "Invoice not found" };
  };

  POST /api/v1/invoices/{id}/manual-submission: {
    body: { zraSubmissionDetails: string; submissionDate: string; };
    response: { success: boolean; };
    errors: { 404: "Invoice not found", 400: "Invalid submission details" };
  };
}

// Internal messaging events for invoice creation (abstracted across RabbitMQ/Azure Service Bus)
interface InvoiceCreationEvents {
  InvoiceCreationRequested: {
    invoiceId: string;
    userId: string;
    command: string;
    timestamp: string;
  };
  InvoiceDraftCreated: {
    invoiceId: string;
    clientId: string;
    totalAmount: number;
    aiConfidence: number;
    processingTime: number;
  };
  InvoiceValidationCompleted: {
    invoiceId: string;
    isValid: boolean;
    validationErrors?: string[];
    zraCompliant: boolean;
  };
  InvoiceCreationFailed: {
    invoiceId: string;
    error: string;
    retryCount: number;
    timestamp: string;
  };
  ManualZRASubmissionRecorded: {
    invoiceId: string;
    submissionDetails: string;
    submissionDate: string;
    recordedBy: string;
  };
}
```

## Error Handling

- **Missing Client Information:** Clear prompts for client selection or creation
- **Invalid Amounts:** Validation with helpful error messages
- **ZRA Compliance Issues:** Specific guidance on compliance requirements
- **Network Failures:** Retry mechanisms with draft preservation
- **Validation Errors:** Field-specific error messages with suggestions
- **Messaging Broker Connectivity:** Issues handled by abstraction layer with automatic failover
- **Failed Invoice Processing:** Events routed to dead-letter queues (RabbitMQ DLX or Azure Service Bus DLQ)
- **AI Service Unavailable:** Fallback to manual invoice creation with messaging dead-letter queues

## Definition of Done

- [ ] Users can create invoice drafts through conversational commands
- [ ] AI extracts and validates invoice information accurately
- [ ] Invoice drafts are displayed for review and editing
- [ ] System validates data for ZRA compliance
- [ ] Error scenarios are handled gracefully with helpful guidance
- [ ] Performance meets requirements (< 3 seconds for draft creation)
- [ ] Invoice numbering is unique and properly formatted
- [ ] Client information is correctly retrieved and validated
- [ ] Tests cover invoice creation workflows and validation
- [ ] Service can be deployed independently
- [ ] No breaking changes to other services
- [ ] Invoice creation events work consistently on both RabbitMQ (local) and Azure Service Bus (production)
- [ ] Messaging abstraction layer handles AI service communication failures gracefully
- [ ] Dead-letter queue processing is implemented for failed invoice events
- [ ] Manual ZRA submission tracking works when API integration is unavailable

## Dependencies

- [Story 2.1: Basic Conversational Command Processing](../epic-02/story-2.1-conversational-commands.md)
- [Story 2.2: Intent Recognition and Entity Extraction](../epic-02/story-2.2-intent-recognition.md)

## Notes

This story delivers the conversational invoice creation experience that differentiates IntelliFin from traditional accounting software. The AI must accurately extract invoice details while providing a smooth user experience.

---

**Related Stories:**
- [Story 5.2: ZRA Invoice Submission and Compliance](story-5.2-zra-submission.md)

**Epic:** [Invoice Generation & ZRA Compliance](../../epics-and-stories.md#epic-5-invoice-generation--zra-compliance) 