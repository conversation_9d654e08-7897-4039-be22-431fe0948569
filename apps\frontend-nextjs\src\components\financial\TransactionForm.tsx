'use client';

import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Alert } from '@/components/ui/alert';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Save, 
  Plus, 
  AlertCircle, 
  CheckCircle, 
  Loader2,
  Calendar,
  DollarSign,
  FileText,
  Brain
} from 'lucide-react';
import { CategorySelector } from './CategorySelector';
import { AICategoryHelper } from './AICategoryHelper';
import { useTransactionForm } from '@/hooks/useTransactionForm';
import { useAISuggestions } from '@/hooks/useAISuggestions';
import { Transaction, Category } from '@/types/api';

interface TransactionFormProps {
  onSubmit: (transaction: Partial<Transaction>) => Promise<void>;
  onSaveDraft?: (draft: any) => Promise<void>;
  initialData?: Partial<Transaction>;
  draftId?: string;
  categories: Category[];
  isLoading?: boolean;
  className?: string;
}

export const TransactionForm: React.FC<TransactionFormProps> = ({
  onSubmit,
  onSaveDraft,
  initialData,
  draftId,
  categories,
  isLoading = false,
  className
}) => {
  const {
    formData,
    errors,
    warnings,
    isValid,
    isDirty,
    updateField,
    validateField,
    validateForm,
    resetForm,
    setFormData
  } = useTransactionForm(initialData);

  const {
    suggestion,
    isLoadingSuggestion,
    getSuggestion,
    clearSuggestion
  } = useAISuggestions();

  const [showCategorySelector, setShowCategorySelector] = useState(false);
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);
  const [lastAutoSave, setLastAutoSave] = useState<Date | null>(null);

  // Auto-save functionality
  useEffect(() => {
    if (autoSaveEnabled && isDirty && onSaveDraft) {
      const timer = setTimeout(() => {
        handleAutoSave();
      }, 2000); // Auto-save after 2 seconds of inactivity

      return () => clearTimeout(timer);
    }
  }, [formData, isDirty, autoSaveEnabled]);

  // Get AI suggestion when description changes
  useEffect(() => {
    if (formData.description && formData.description.length >= 3) {
      const timer = setTimeout(() => {
        getSuggestion({
          description: formData.description,
          amount: formData.amount,
          type: formData.type
        });
      }, 500); // Debounce for 500ms

      return () => clearTimeout(timer);
    }
  }, [formData.description, formData.amount, formData.type]);

  const handleAutoSave = async () => {
    if (!onSaveDraft || !isDirty) return;

    try {
      await onSaveDraft({
        ...formData,
        isDraft: true,
        isAutoSaved: true,
        draftId
      });
      setLastAutoSave(new Date());
    } catch (error) {
      console.error('Auto-save failed:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit({
        ...formData,
        categoryId: formData.categoryId || suggestion?.categoryId
      });
      resetForm();
      clearSuggestion();
    } catch (error) {
      console.error('Failed to submit transaction:', error);
    }
  };

  const handleSaveDraft = async () => {
    if (!onSaveDraft) return;

    try {
      await onSaveDraft({
        ...formData,
        isDraft: true,
        isAutoSaved: false,
        draftId
      });
      setLastAutoSave(new Date());
    } catch (error) {
      console.error('Failed to save draft:', error);
    }
  };

  const handleCategorySelect = (categoryId: string, categoryName: string) => {
    updateField('categoryId', categoryId);
    setShowCategorySelector(false);
    clearSuggestion();
  };

  const handleAcceptAISuggestion = () => {
    if (suggestion) {
      updateField('categoryId', suggestion.categoryId);
      clearSuggestion();
    }
  };

  const handleRejectAISuggestion = () => {
    setShowCategorySelector(true);
    clearSuggestion();
  };

  const getSelectedCategory = () => {
    if (formData.categoryId) {
      return categories.find(c => c.id === formData.categoryId);
    }
    return null;
  };

  return (
    <Card className={`p-6 ${className}`}>
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">
            {initialData?.id ? 'Edit Transaction' : 'Add New Transaction'}
          </h3>
          
          {lastAutoSave && (
            <span className="text-xs text-gray-500">
              Auto-saved {lastAutoSave.toLocaleTimeString()}
            </span>
          )}
        </div>

        {/* Description Field */}
        <div className="space-y-2">
          <Label htmlFor="description" className="flex items-center space-x-2">
            <FileText className="h-4 w-4" />
            <span>Description *</span>
          </Label>
          <Input
            id="description"
            value={formData.description || ''}
            onChange={(e) => updateField('description', e.target.value)}
            onBlur={() => validateField('description')}
            placeholder="Enter transaction description..."
            className={errors.description ? 'border-red-300' : ''}
          />
          {errors.description && (
            <p className="text-sm text-red-600">{errors.description}</p>
          )}
          {warnings.description && (
            <p className="text-sm text-yellow-600">{warnings.description}</p>
          )}
        </div>

        {/* Amount and Type Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Amount Field */}
          <div className="space-y-2">
            <Label htmlFor="amount" className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4" />
              <span>Amount *</span>
            </Label>
            <Input
              id="amount"
              type="number"
              step="0.01"
              min="0.01"
              value={formData.amount || ''}
              onChange={(e) => updateField('amount', parseFloat(e.target.value))}
              onBlur={() => validateField('amount')}
              placeholder="0.00"
              className={errors.amount ? 'border-red-300' : ''}
            />
            {errors.amount && (
              <p className="text-sm text-red-600">{errors.amount}</p>
            )}
          </div>

          {/* Transaction Type */}
          <div className="space-y-2">
            <Label htmlFor="type">Type *</Label>
            <Select
              value={formData.type || ''}
              onValueChange={(value) => updateField('type', value as 'INCOME' | 'EXPENSE')}
            >
              <SelectTrigger className={errors.type ? 'border-red-300' : ''}>
                <SelectValue placeholder="Select type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="EXPENSE">Expense</SelectItem>
                <SelectItem value="INCOME">Income</SelectItem>
              </SelectContent>
            </Select>
            {errors.type && (
              <p className="text-sm text-red-600">{errors.type}</p>
            )}
          </div>
        </div>

        {/* Date Field */}
        <div className="space-y-2">
          <Label htmlFor="date" className="flex items-center space-x-2">
            <Calendar className="h-4 w-4" />
            <span>Date *</span>
          </Label>
          <Input
            id="date"
            type="date"
            value={formData.date || ''}
            onChange={(e) => updateField('date', e.target.value)}
            onBlur={() => validateField('date')}
            max={new Date().toISOString().split('T')[0]}
            className={errors.date ? 'border-red-300' : ''}
          />
          {errors.date && (
            <p className="text-sm text-red-600">{errors.date}</p>
          )}
        </div>

        {/* AI Category Helper */}
        {(suggestion || isLoadingSuggestion) && (
          <AICategoryHelper
            suggestion={suggestion}
            isLoading={isLoadingSuggestion}
            onAccept={handleAcceptAISuggestion}
            onReject={handleRejectAISuggestion}
            onShowSelector={() => setShowCategorySelector(true)}
          />
        )}

        {/* Category Selection */}
        {!suggestion && (
          <div className="space-y-2">
            <Label>Category</Label>
            {getSelectedCategory() ? (
              <div className="flex items-center justify-between p-3 border rounded-md bg-gray-50">
                <span className="font-medium">{getSelectedCategory()?.name}</span>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setShowCategorySelector(true)}
                >
                  Change
                </Button>
              </div>
            ) : (
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowCategorySelector(true)}
                className="w-full justify-start"
              >
                <Brain className="h-4 w-4 mr-2" />
                Select Category
              </Button>
            )}
          </div>
        )}

        {/* Category Selector Modal */}
        {showCategorySelector && (
          <CategorySelector
            categories={categories}
            selectedCategoryId={formData.categoryId}
            onSelect={handleCategorySelect}
            onClose={() => setShowCategorySelector(false)}
            transactionType={formData.type}
          />
        )}

        {/* Notes Field */}
        <div className="space-y-2">
          <Label htmlFor="notes">Notes</Label>
          <Textarea
            id="notes"
            value={formData.notes || ''}
            onChange={(e) => updateField('notes', e.target.value)}
            placeholder="Additional notes (optional)..."
            rows={3}
            maxLength={1000}
          />
          <p className="text-xs text-gray-500">
            {(formData.notes || '').length}/1000 characters
          </p>
        </div>

        {/* Validation Errors */}
        {Object.keys(errors).length > 0 && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <div>
              <h4 className="font-medium">Please fix the following errors:</h4>
              <ul className="mt-1 text-sm list-disc list-inside">
                {Object.values(errors).map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          </Alert>
        )}

        {/* Action Buttons */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex items-center space-x-2">
            {onSaveDraft && (
              <Button
                type="button"
                variant="outline"
                onClick={handleSaveDraft}
                disabled={!isDirty}
                className="flex items-center space-x-2"
              >
                <Save className="h-4 w-4" />
                <span>Save Draft</span>
              </Button>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={resetForm}
              disabled={isLoading}
            >
              Reset
            </Button>
            
            <Button
              type="submit"
              disabled={!isValid || isLoading}
              className="flex items-center space-x-2"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Plus className="h-4 w-4" />
              )}
              <span>{initialData?.id ? 'Update' : 'Create'} Transaction</span>
            </Button>
          </div>
        </div>
      </form>
    </Card>
  );
};
