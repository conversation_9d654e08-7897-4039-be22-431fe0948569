package com.intellifin.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO for bulk transaction categorization requests
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BulkCategorizationRequestDto {
    
    @NotEmpty(message = "Transactions list cannot be empty")
    @Valid
    private List<CategorizationRequestDto> transactions;
}
