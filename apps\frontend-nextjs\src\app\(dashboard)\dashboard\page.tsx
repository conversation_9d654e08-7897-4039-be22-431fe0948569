"use client"

import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuthStore, useFinancialStore, useConversationStore } from '@/stores';
import { ConversationalWorkspace } from '@/components/conversation/ConversationalWorkspace';
import { BusinessVitalsBar } from '@/components/dashboard/BusinessVitalsBar';
import { ConversationHistory } from '@/components/conversation/ConversationHistory';
import { ToastNotifications } from '@/components/ui/ToastNotifications';
import { WebSocketProvider } from '@/components/providers/WebSocketProvider';



export default function DashboardPage() {
  const { user } = useAuthStore();
  const { fetchDashboardData, dashboardData, isLoading } = useFinancialStore();
  const { isConnected } = useConversationStore();

  useEffect(() => {
    // Fetch dashboard data when component mounts
    fetchDashboardData();
  }, [fetchDashboardData]);

  const firstName = user?.firstName || 'there';

  return (
    <WebSocketProvider>
      <div className="h-full flex flex-col">
        {/* Toast Notifications - Non-intrusive real-time updates */}
        <ToastNotifications />

        {/* Business Vitals Bar - Below command bar area */}
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="flex-shrink-0 mb-4"
        >
          <BusinessVitalsBar
            data={dashboardData}
            isLoading={isLoading.dashboard}
          />
        </motion.div>

        {/* Main Content Area */}
        <div className="flex-1 flex gap-6 min-h-0 overflow-hidden">
          {/* Main Workspace - Where AI interactions happen */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="flex-1 min-w-0 flex flex-col"
          >
            <ConversationalWorkspace />
          </motion.div>

          {/* Right Sidebar - Conversation History */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="w-80 flex-shrink-0"
          >
            <ConversationHistory />
          </motion.div>
        </div>
      </div>
    </WebSocketProvider>
  );
}
