package com.intellifin.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.type.SqlTypes;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "financial_accounts")
public class FinancialAccount {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Enumerated(EnumType.STRING)
    @Column(name = "account_type", nullable = false)
    private AccountType accountType;

    @Column(name = "account_name", nullable = false)
    private String accountName;

    @Column(name = "phone_number")
    private String phoneNumber;

    @Column(name = "account_number_last4")
    private String accountNumberLast4;

    @Column(nullable = false)
    private String provider;

    @Column(precision = 15, scale = 2)
    @Builder.Default
    private BigDecimal balance = BigDecimal.ZERO;

    @Builder.Default
    private String currency = "ZMW";

    @Enumerated(EnumType.STRING)
    @Column(name = "connection_status", nullable = false)
    @Builder.Default
    private ConnectionStatus connectionStatus = ConnectionStatus.DISCONNECTED;

    @Column(name = "last_sync_at")
    private OffsetDateTime lastSyncAt;

    @Column(name = "error_message")
    private String errorMessage;

    @Column(name = "account_info", columnDefinition = "jsonb")
    @JdbcTypeCode(SqlTypes.JSON)
    private String accountInfo;

    @OneToMany(mappedBy = "financialAccount", cascade = CascadeType.ALL, orphanRemoval = true)
    @Builder.Default
    private Set<AccountConnection> connections = new HashSet<>();

    @OneToMany(mappedBy = "financialAccount", cascade = CascadeType.ALL, orphanRemoval = true)
    @Builder.Default
    private Set<DocumentUpload> documentUploads = new HashSet<>();

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private OffsetDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private OffsetDateTime updatedAt;

    public enum AccountType {
        MTN_MOBILE_MONEY,
        BANK_ACCOUNT,
        OTHER
    }

    public enum ConnectionStatus {
        CONNECTING,
        CONNECTED,
        DISCONNECTED,
        ERROR
    }

    // Helper methods
    public boolean isConnected() {
        return ConnectionStatus.CONNECTED.equals(connectionStatus);
    }

    public boolean isConnecting() {
        return ConnectionStatus.CONNECTING.equals(connectionStatus);
    }

    public boolean hasError() {
        return ConnectionStatus.ERROR.equals(connectionStatus);
    }

    public boolean isMTNMobileMoney() {
        return AccountType.MTN_MOBILE_MONEY.equals(accountType);
    }

    public void markAsConnecting() {
        this.connectionStatus = ConnectionStatus.CONNECTING;
        this.errorMessage = null;
    }

    public void markAsConnected() {
        this.connectionStatus = ConnectionStatus.CONNECTED;
        this.errorMessage = null;
        this.lastSyncAt = OffsetDateTime.now();
    }

    public void markAsDisconnected() {
        this.connectionStatus = ConnectionStatus.DISCONNECTED;
        this.errorMessage = null;
    }

    public void markAsError(String errorMessage) {
        this.connectionStatus = ConnectionStatus.ERROR;
        this.errorMessage = errorMessage;
    }

    public void updateBalance(BigDecimal newBalance) {
        this.balance = newBalance;
        this.lastSyncAt = OffsetDateTime.now();
    }

    public String getDisplayName() {
        if (phoneNumber != null && !phoneNumber.isEmpty()) {
            return accountName + " (" + phoneNumber + ")";
        }
        return accountName;
    }
}
