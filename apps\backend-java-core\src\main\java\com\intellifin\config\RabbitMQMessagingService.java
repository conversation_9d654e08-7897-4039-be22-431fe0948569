package com.intellifin.config;

import com.intellifin.model.ConversationMessage;
import com.intellifin.service.MessageHandler;

public class RabbitMQMessagingService implements MessageHandler {
    private final String host;
    private final int port;
    private final String username;
    private final String password;

    public RabbitMQMessagingService(String host, int port, String username, String password) {
        this.host = host;
        this.port = port;
        this.username = username;
        this.password = password;
    }

    @Override
    public void handle(ConversationMessage message) {
        // TODO: Implement RabbitMQ publishing
        System.out.println("RabbitMQ: Publishing to " + message.getConversation().getId() + ": " + message.getContent());
    }
}
