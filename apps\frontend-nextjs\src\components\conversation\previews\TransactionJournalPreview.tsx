'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowRightLeft, 
  ExternalLink, 
  CheckCircle, 
  AlertCircle,
  Receipt,
  BookOpen,
  Link
} from 'lucide-react';
import { JournalEntry } from '@intellifin/data-models';
import { Transaction } from '@/types/api';
import { formatCurrency, formatDate } from '@/utils/formatting';
import { ROUTES } from '@/utils/constants';

interface TransactionJournalPreviewProps {
  transaction: Transaction;
  journalEntry?: JournalEntry;
  showJournalLines?: boolean;
  className?: string;
}

export function TransactionJournalPreview({ 
  transaction, 
  journalEntry,
  showJournalLines = true,
  className = '' 
}: TransactionJournalPreviewProps) {
  const hasJournalEntry = !!journalEntry;
  const isJournalBalanced = (journalEntry as any)?.isBalanced ?? false;

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={className}
    >
      <Card className="border-l-4 border-l-purple-500 hover:shadow-md transition-shadow">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <ArrowRightLeft className="w-5 h-5 text-purple-600" />
              <CardTitle className="text-lg">
                Transaction → Journal Entry
              </CardTitle>
            </div>
            <div className="flex items-center space-x-2">
              {hasJournalEntry ? (
                <Badge variant="outline" className="bg-green-100 text-green-700 border-green-200">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Journal Created
                </Badge>
              ) : (
                <Badge variant="outline" className="bg-yellow-100 text-yellow-700 border-yellow-200">
                  <AlertCircle className="w-3 h-3 mr-1" />
                  Pending Journal
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          {/* Transaction Details */}
          <div className="mb-4 p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <Receipt className="w-4 h-4 text-gray-600" />
              <h4 className="text-sm font-medium text-gray-700">Original Transaction</h4>
            </div>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p><strong>Amount:</strong> {formatCurrency(transaction.amount)}</p>
                <p><strong>Date:</strong> {formatDate(transaction.transactionDate)}</p>
                <p><strong>Type:</strong> {transaction.type}</p>
              </div>
              <div>
                <p><strong>Description:</strong> {transaction.description}</p>
                <p><strong>Category:</strong> {transaction.categoryName || 'Uncategorized'}</p>
                <p><strong>Status:</strong> 
                  <Badge variant="outline" className="ml-1 text-xs">
                    {transaction.status}
                  </Badge>
                </p>
              </div>
            </div>
          </div>

          {/* Journal Entry Details */}
          {hasJournalEntry && journalEntry ? (
            <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center space-x-2 mb-2">
                <BookOpen className="w-4 h-4 text-blue-600" />
                <h4 className="text-sm font-medium text-blue-700">Generated Journal Entry</h4>
                <Link className="w-3 h-3 text-blue-500" />
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm mb-3">
                <div>
                  <p><strong>Entry Number:</strong> {journalEntry.entryNumber}</p>
                  <p><strong>Status:</strong> 
                    <Badge 
                      variant="outline" 
                      className={`ml-1 text-xs ${
                        journalEntry.status === 'POSTED' 
                          ? 'bg-green-100 text-green-700' 
                          : 'bg-yellow-100 text-yellow-700'
                      }`}
                    >
                      {journalEntry.status}
                    </Badge>
                  </p>
                </div>
                <div>
                  <p><strong>Entry Date:</strong> {formatDate(journalEntry.entryDate)}</p>
                  <p><strong>Balance Status:</strong> 
                    {isJournalBalanced ? (
                      <Badge variant="outline" className="ml-1 text-xs bg-green-100 text-green-700">
                        ✓ Balanced
                      </Badge>
                    ) : (
                      <Badge variant="destructive" className="ml-1 text-xs">
                        Unbalanced
                      </Badge>
                    )}
                  </p>
                </div>
              </div>

              {/* Journal Lines Preview */}
              {showJournalLines && journalEntry.lines && journalEntry.lines.length > 0 && (
                <div className="space-y-1">
                  <h5 className="text-xs font-medium text-blue-700 uppercase tracking-wide">
                    Double-Entry Lines:
                  </h5>
                  {journalEntry.lines.slice(0, 3).map((line, index) => (
                    <div 
                      key={line.id || index} 
                      className="flex justify-between items-center text-xs p-2 bg-white border border-blue-100 rounded"
                    >
                      <span className="font-medium text-gray-700">
                        {(line as any).accountName || `Account ${line.accountId}`}
                      </span>
                      <div className="flex space-x-3">
                        <span className={`w-16 text-right ${line.debitAmount ? 'text-green-600 font-medium' : 'text-gray-300'}`}>
                          {line.debitAmount ? formatCurrency(line.debitAmount) : '—'}
                        </span>
                        <span className={`w-16 text-right ${line.creditAmount ? 'text-red-600 font-medium' : 'text-gray-300'}`}>
                          {line.creditAmount ? formatCurrency(line.creditAmount) : '—'}
                        </span>
                      </div>
                    </div>
                  ))}
                  
                  {journalEntry.lines.length > 3 && (
                    <div className="text-center py-1">
                      <span className="text-xs text-blue-600">
                        +{journalEntry.lines.length - 3} more lines
                      </span>
                    </div>
                  )}
                </div>
              )}
            </div>
          ) : (
            <div className="mb-4 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
              <div className="flex items-center space-x-2 mb-2">
                <AlertCircle className="w-4 h-4 text-yellow-600" />
                <h4 className="text-sm font-medium text-yellow-700">Journal Entry Pending</h4>
              </div>
              <p className="text-sm text-yellow-700">
                This transaction has been categorized but the corresponding journal entry 
                has not been created yet. Journal entries are typically created when 
                transactions are confirmed and posted.
              </p>
            </div>
          )}

          {/* Accounting Impact Summary */}
          <div className="mb-4 p-3 bg-green-50 rounded-lg border border-green-200">
            <h4 className="text-sm font-medium text-green-700 mb-2">Accounting Impact</h4>
            <div className="text-sm text-green-700">
              {transaction.type === 'CREDIT' ? (
                <p>💰 <strong>Money In:</strong> Increases cash/bank account (Asset ↑) and records income or reduces liability</p>
              ) : (
                <p>💸 <strong>Money Out:</strong> Decreases cash/bank account (Asset ↓) and records expense or increases liability</p>
              )}
              <p className="text-xs text-green-600 mt-1">
                Double-entry ensures: <strong>Debits = Credits</strong> for balanced books
              </p>
            </div>
          </div>

          {/* Progressive Disclosure CTAs */}
          <div className="flex justify-between items-center pt-3 border-t">
            <div className="text-xs text-gray-500">
              {hasJournalEntry ? 'Journal entry linked' : 'Awaiting journal creation'}
            </div>
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                size="sm"
                className="flex items-center space-x-2 hover:bg-gray-50"
                onClick={() => {
                  window.location.href = `/transactions?id=${transaction.id}`;
                }}
              >
                <Receipt className="w-3 h-3" />
                <span>View Transaction</span>
              </Button>
              
              {hasJournalEntry && (
                <Button 
                  variant="outline" 
                  size="sm"
                  className="flex items-center space-x-2 hover:bg-blue-50 hover:border-blue-300"
                  onClick={() => {
                    window.location.href = `${ROUTES.JOURNAL_ENTRIES}?entry=${journalEntry.id}`;
                  }}
                >
                  <BookOpen className="w-3 h-3" />
                  <span>View Journal Entry</span>
                  <ExternalLink className="w-3 h-3" />
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

/**
 * Compact inline version for chat responses
 */
export function TransactionJournalInlinePreview({ 
  transaction, 
  journalEntry 
}: { 
  transaction: Transaction; 
  journalEntry?: JournalEntry; 
}) {
  const hasJournal = !!journalEntry;
  
  return (
    <div className="inline-flex items-center space-x-2 bg-purple-50 border border-purple-200 rounded-lg px-3 py-2 text-sm">
      <ArrowRightLeft className="w-4 h-4 text-purple-600" />
      <span className="font-medium">{formatCurrency(transaction.amount)}</span>
      <span>→</span>
      {hasJournal ? (
        <>
          <Badge variant="outline" className="bg-green-100 text-green-700 text-xs">
            Entry {journalEntry.entryNumber}
          </Badge>
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-6 px-2 text-purple-600 hover:bg-purple-100"
            onClick={() => {
              window.location.href = `${ROUTES.JOURNAL_ENTRIES}?entry=${journalEntry.id}`;
            }}
          >
            View <ExternalLink className="w-3 h-3 ml-1" />
          </Button>
        </>
      ) : (
        <Badge variant="outline" className="bg-yellow-100 text-yellow-700 text-xs">
          Pending Journal
        </Badge>
      )}
    </div>
  );
}
