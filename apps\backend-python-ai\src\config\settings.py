"""
Application settings and configuration
"""

from functools import lru_cache
from typing import Optional

from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings"""
    
    # Environment
    environment: str = Field(default="development", alias="ENVIRONMENT")
    log_level: str = Field(default="INFO", alias="LOG_LEVEL")
    
    # AI Configuration
    ollama_base_url: str = Field(default="http://localhost:11434", alias="OLLAMA_BASE_URL")
    google_api_key: Optional[str] = Field(default=None, alias="GOOGLE_API_KEY")
    openai_api_key: Optional[str] = Field(default=None, alias="OPENAI_API_KEY")
    vertex_ai_project_id: str = Field(default="intellifin-mvp", alias="VERTEX_AI_PROJECT_ID")
    vertex_ai_location: str = Field(default="global", alias="VERTEX_AI_LOCATION")
    vertex_ai_model: str = Field(default="gemini-2.5-pro", alias="VERTEX_AI_MODEL")
    
    # Redis Configuration
    redis_url: str = Field(default="redis://localhost:6379", alias="REDIS_URL")

    # Messaging Service Configuration
    messaging_service_provider: str = Field(default="rabbitmq", alias="MESSAGING_SERVICE_PROVIDER")
    rabbitmq_url: str = Field(default="amqp://intellifin:intellifin123@localhost:5672/", alias="RABBITMQ_URL")
    azure_service_bus_connection_string: Optional[str] = Field(default=None, alias="AZURE_SERVICE_BUS_CONNECTION_STRING")

    
    # Model Configuration
    default_model: str = Field(default="llama3.1:8b", alias="DEFAULT_MODEL")
    model_temperature: float = Field(default=0.1, alias="MODEL_TEMPERATURE")
    max_tokens: int = Field(default=512, alias="MAX_TOKENS")
    
    # Performance Configuration
    request_timeout: int = Field(default=30, alias="REQUEST_TIMEOUT")
    max_concurrent_requests: int = Field(default=10, alias="MAX_CONCURRENT_REQUESTS")
    
    # Cache Configuration
    cache_ttl: int = Field(default=3600, alias="CACHE_TTL")  # 1 hour
    enable_caching: bool = Field(default=True, alias="ENABLE_CACHING")
    
    # Confidence Thresholds
    high_confidence_threshold: float = Field(default=0.85, alias="HIGH_CONFIDENCE_THRESHOLD")
    medium_confidence_threshold: float = Field(default=0.60, alias="MEDIUM_CONFIDENCE_THRESHOLD")
    low_confidence_threshold: float = Field(default=0.40, alias="LOW_CONFIDENCE_THRESHOLD")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        extra = "ignore"



@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance"""
    return Settings()
