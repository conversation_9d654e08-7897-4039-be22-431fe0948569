import { NextRequest, NextResponse } from 'next/server';

const AI_SERVICE_URL = process.env.AI_SERVICE_URL || 'http://localhost:8080';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.command || !body.user_id || !body.session_id) {
      return NextResponse.json(
        { error: 'Missing required fields: command, user_id, session_id' },
        { status: 400 }
      );
    }

    // Forward request to AI service
    const aiResponse = await fetch(`${AI_SERVICE_URL}/api/v1/ai/intent`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!aiResponse.ok) {
      const errorText = await aiResponse.text();
      console.error('AI service error:', errorText);
      
      // Return fallback response
      return NextResponse.json({
        intent: {
          name: 'UNCLEAR',
          confidence: 0.1,
          description: 'AI service temporarily unavailable'
        },
        entities: {},
        suggestions: [
          "Show me recent transactions",
          "Create a new invoice",
          "Help me with something else"
        ],
        fallback: {
          type: 'ERROR',
          message: 'AI service is temporarily unavailable. Please try again.',
          options: [
            "Show me recent transactions",
            "Create a new invoice",
            "Get help"
          ]
        },
        processing_time_ms: 0
      });
    }

    const aiResult = await aiResponse.json();
    return NextResponse.json(aiResult);

  } catch (error) {
    console.error('Error in intent recognition proxy:', error);
    
    // Return fallback response on error
    return NextResponse.json({
      intent: {
        name: 'UNCLEAR',
        confidence: 0.0,
        description: 'Error processing request'
      },
      entities: {},
      suggestions: [],
      fallback: {
        type: 'ERROR',
        message: 'I\'m experiencing technical difficulties. Please try again later.',
        options: [
          "Show me recent transactions",
          "Create a new invoice",
          "Get help"
        ]
      },
      processing_time_ms: 0
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'AI Intent Recognition API',
    endpoints: {
      'POST /api/ai/intent': 'Recognize intent from user commands'
    }
  });
}
