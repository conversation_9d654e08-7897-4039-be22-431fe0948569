package com.intellifin.messaging.events;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Event published when user accepts AI categorization suggestion
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CategorizationAcceptedEvent {
    
    private UUID transactionId;
    private UUID categoryId;
    private Boolean userAccepted;
    private String feedback;
    private LocalDateTime timestamp;
}
