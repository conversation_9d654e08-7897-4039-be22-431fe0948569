"use client"

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DollarSign, Calendar, User, Tag, CreditCard, ArrowUpDown } from 'lucide-react';

interface Entity {
  value: string;
  confidence: number;
  startIndex: number;
  endIndex: number;
  entityType: string;
}

interface EntityHighlightProps {
  text: string;
  entities: Record<string, Entity[]>;
  showConfidence?: boolean;
}

export function EntityHighlight({ text, entities, showConfidence = true }: EntityHighlightProps) {
  const getEntityIcon = (entityType: string) => {
    switch (entityType.toLowerCase()) {
      case 'amount':
      case 'money':
        return <DollarSign className="w-3 h-3" />;
      case 'date':
      case 'date_range':
        return <Calendar className="w-3 h-3" />;
      case 'client_name':
      case 'person':
        return <User className="w-3 h-3" />;
      case 'category':
        return <Tag className="w-3 h-3" />;
      case 'account_type':
        return <CreditCard className="w-3 h-3" />;
      case 'transaction_type':
        return <ArrowUpDown className="w-3 h-3" />;
      default:
        return <Tag className="w-3 h-3" />;
    }
  };

  const getEntityColor = (entityType: string) => {
    switch (entityType.toLowerCase()) {
      case 'amount':
      case 'money':
        return 'bg-green-100 text-green-800 border-green-300';
      case 'date':
      case 'date_range':
        return 'bg-blue-100 text-blue-800 border-blue-300';
      case 'client_name':
      case 'person':
        return 'bg-purple-100 text-purple-800 border-purple-300';
      case 'category':
        return 'bg-orange-100 text-orange-800 border-orange-300';
      case 'account_type':
        return 'bg-cyan-100 text-cyan-800 border-cyan-300';
      case 'transaction_type':
        return 'bg-pink-100 text-pink-800 border-pink-300';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  const getEntityDisplayName = (entityType: string) => {
    switch (entityType.toLowerCase()) {
      case 'amount':
        return 'Amount';
      case 'date_range':
        return 'Date Range';
      case 'client_name':
        return 'Client';
      case 'category':
        return 'Category';
      case 'account_type':
        return 'Account';
      case 'transaction_type':
        return 'Transaction Type';
      default:
        return entityType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  };

  // Create highlighted text with entity markers
  const createHighlightedText = () => {
    // Flatten all entities with their positions
    const allEntities: (Entity & { type: string })[] = [];
    
    Object.entries(entities).forEach(([type, entityList]) => {
      entityList.forEach(entity => {
        allEntities.push({ ...entity, type });
      });
    });

    // Sort by start index to process in order
    allEntities.sort((a, b) => a.startIndex - b.startIndex);

    if (allEntities.length === 0) {
      return <span>{text}</span>;
    }

    const parts = [];
    let lastIndex = 0;

    allEntities.forEach((entity, index) => {
      // Add text before entity
      if (entity.startIndex > lastIndex) {
        parts.push(
          <span key={`text-${index}`}>
            {text.slice(lastIndex, entity.startIndex)}
          </span>
        );
      }

      // Add highlighted entity
      parts.push(
        <span
          key={`entity-${index}`}
          className={`inline-flex items-center gap-1 px-2 py-1 rounded-md text-sm font-medium ${getEntityColor(entity.type)}`}
          title={`${getEntityDisplayName(entity.type)}: ${entity.value} (${Math.round(entity.confidence * 100)}% confidence)`}
        >
          {getEntityIcon(entity.type)}
          {entity.value}
          {showConfidence && (
            <span className="text-xs opacity-75">
              {Math.round(entity.confidence * 100)}%
            </span>
          )}
        </span>
      );

      lastIndex = entity.endIndex;
    });

    // Add remaining text
    if (lastIndex < text.length) {
      parts.push(
        <span key="text-end">
          {text.slice(lastIndex)}
        </span>
      );
    }

    return <>{parts}</>;
  };

  // Get entity summary for display
  const getEntitySummary = () => {
    const summary: { type: string; count: number; entities: Entity[] }[] = [];
    
    Object.entries(entities).forEach(([type, entityList]) => {
      if (entityList.length > 0) {
        summary.push({
          type,
          count: entityList.length,
          entities: entityList
        });
      }
    });

    return summary;
  };

  const entitySummary = getEntitySummary();

  if (entitySummary.length === 0) {
    return (
      <div className="text-gray-500 text-sm">
        No entities detected in this message.
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Highlighted text */}
      <Card>
        <CardContent className="p-4">
          <div className="text-sm leading-relaxed">
            {createHighlightedText()}
          </div>
        </CardContent>
      </Card>

      {/* Entity summary */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">Extracted Entities</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {entitySummary.map(({ type, entities: entityList }) => (
              <div key={type} className="space-y-2">
                <div className="flex items-center gap-2">
                  {getEntityIcon(type)}
                  <span className="text-sm font-medium text-gray-700">
                    {getEntityDisplayName(type)}
                  </span>
                  <Badge variant="secondary" className="text-xs">
                    {entityList.length}
                  </Badge>
                </div>
                <div className="space-y-1">
                  {entityList.map((entity, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <Badge variant="outline" className={getEntityColor(type)}>
                        {entity.value}
                      </Badge>
                      {showConfidence && (
                        <span className="text-xs text-gray-500">
                          {Math.round(entity.confidence * 100)}%
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Simple entity badge component for inline use
interface EntityBadgeProps {
  entity: Entity;
  type: string;
  showConfidence?: boolean;
}

export function EntityBadge({ entity, type, showConfidence = false }: EntityBadgeProps) {
  const getEntityIcon = (entityType: string) => {
    switch (entityType.toLowerCase()) {
      case 'amount':
        return <DollarSign className="w-3 h-3" />;
      case 'date_range':
        return <Calendar className="w-3 h-3" />;
      case 'client_name':
        return <User className="w-3 h-3" />;
      case 'category':
        return <Tag className="w-3 h-3" />;
      default:
        return <Tag className="w-3 h-3" />;
    }
  };

  const getEntityColor = (entityType: string) => {
    switch (entityType.toLowerCase()) {
      case 'amount':
        return 'bg-green-100 text-green-800 border-green-300';
      case 'date_range':
        return 'bg-blue-100 text-blue-800 border-blue-300';
      case 'client_name':
        return 'bg-purple-100 text-purple-800 border-purple-300';
      case 'category':
        return 'bg-orange-100 text-orange-800 border-orange-300';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  return (
    <Badge variant="outline" className={`inline-flex items-center gap-1 ${getEntityColor(type)}`}>
      {getEntityIcon(type)}
      {entity.value}
      {showConfidence && (
        <span className="text-xs opacity-75">
          {Math.round(entity.confidence * 100)}%
        </span>
      )}
    </Badge>
  );
}
