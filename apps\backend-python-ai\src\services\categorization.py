"""
AI-powered transaction categorization service
"""
import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from uuid import uuid4
from functools import wraps

from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import <PERSON>sonOutputParser
from pydantic import BaseModel, Field

from ..config.ai_config import get_ai_config
from ..models.schemas import TransactionCategorizationRequest, TransactionCategorizationResponse

logger = logging.getLogger(__name__)


def monitor_performance(func):
    """Decorator to monitor function performance"""
    @wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            execution_time = (time.time() - start_time) * 1000
            logger.info(f"{func.__name__} completed in {execution_time:.2f}ms")
            return result
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            logger.error(f"{func.__name__} failed after {execution_time:.2f}ms: {str(e)}")
            raise

    @wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = (time.time() - start_time) * 1000
            logger.info(f"{func.__name__} completed in {execution_time:.2f}ms")
            return result
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            logger.error(f"{func.__name__} failed after {execution_time:.2f}ms: {str(e)}")
            raise

    return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper


class CategorySuggestion(BaseModel):
    """Pydantic model for category suggestion output"""
    category_name: str = Field(description="The suggested category name")
    category_type: str = Field(description="Either 'INCOME' or 'EXPENSE'")
    confidence: float = Field(description="Confidence score between 0.0 and 1.0")
    explanation: str = Field(description="Brief explanation for the categorization")
    keywords: List[str] = Field(description="Key words that influenced the decision")


class TransactionCategorizationService:
    """Service for AI-powered transaction categorization"""
    
    def __init__(self):
        self.ai_config = get_ai_config()
        self.model = self.ai_config.get_model()
        self.parser = JsonOutputParser(pydantic_object=CategorySuggestion)
        
        # Zambian business context categories
        self.default_categories = {
            "EXPENSE": [
                "Transport", "Fuel", "Food & Dining", "Groceries", "Utilities", 
                "Rent", "Office Supplies", "Marketing", "Professional Services",
                "Insurance", "Telecommunications", "Banking Fees", "Taxes",
                "Equipment", "Maintenance", "Travel", "Entertainment",
                "Medical", "Education", "Clothing", "Miscellaneous"
            ],
            "INCOME": [
                "Sales Revenue", "Service Revenue", "Consulting", "Investment Income",
                "Interest", "Rental Income", "Commission", "Grants", "Refunds",
                "Other Income"
            ]
        }
        
        self.categorization_prompt = PromptTemplate(
            input_variables=["description", "amount", "transaction_type", "categories"],
            template="""
You are an AI assistant specialized in categorizing financial transactions for Zambian businesses.

Transaction Details:
- Description: {description}
- Amount: K{amount} (Zambian Kwacha)
- Type: {transaction_type}

Available Categories for {transaction_type}:
{categories}

Context: This is a Zambian business transaction. Consider local business practices, common vendors, and typical expense patterns in Zambia.

Instructions:
1. Analyze the transaction description carefully
2. Consider the amount and transaction type
3. Select the most appropriate category from the available list
4. If no exact match exists, choose the closest category
5. Provide a confidence score (0.0 to 1.0) based on how certain you are
6. Explain your reasoning briefly
7. Extract key words that influenced your decision

{format_instructions}

Response:
"""
        )
    
    @monitor_performance
    async def categorize_transaction(
        self,
        description: str,
        amount: float,
        transaction_type: str,
        user_id: str,
        custom_categories: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Categorize a transaction using AI
        
        Args:
            description: Transaction description
            amount: Transaction amount
            transaction_type: 'INCOME' or 'EXPENSE'
            user_id: User ID for personalization
            custom_categories: Optional user-defined categories
            
        Returns:
            Dictionary with categorization results
        """
        start_time = time.time()
        
        try:
            # Get available categories
            categories = custom_categories or self.default_categories.get(transaction_type, [])
            categories_text = "\n".join([f"- {cat}" for cat in categories])
            
            # Format the prompt
            formatted_prompt = self.categorization_prompt.format(
                description=description,
                amount=amount,
                transaction_type=transaction_type,
                categories=categories_text,
                format_instructions=self.parser.get_format_instructions()
            )
            
            logger.info(f"Categorizing transaction for user {user_id}: {description[:50]}...")
            
            # Get AI response
            response = await self._invoke_model_with_retry(formatted_prompt)
            
            # Parse the response
            try:
                parsed_result = self.parser.parse(response)
                
                # Validate and adjust confidence based on heuristics
                adjusted_confidence = self._adjust_confidence(
                    parsed_result.confidence,
                    description,
                    parsed_result.category_name,
                    amount
                )
                
                processing_time = int((time.time() - start_time) * 1000)
                
                result = {
                    "category_id": str(uuid4()),  # Generate temporary ID
                    "category_name": parsed_result.category_name,
                    "category_type": parsed_result.category_type,
                    "confidence": adjusted_confidence,
                    "explanation": parsed_result.explanation,
                    "keywords": parsed_result.keywords,
                    "processing_time": processing_time,
                    "model_used": self.ai_config.get_model_info()["production_model"]["name"] if self.ai_config.has_google_api_key else "local"
                }
                
                logger.info(f"Successfully categorized transaction: {parsed_result.category_name} (confidence: {adjusted_confidence:.2f})")
                return result
                
            except Exception as parse_error:
                logger.error(f"Failed to parse AI response: {str(parse_error)}")
                return self._fallback_categorization(description, transaction_type, amount)
                
        except Exception as e:
            logger.error(f"Error in transaction categorization: {str(e)}")
            return self._fallback_categorization(description, transaction_type, amount)
    
    async def _invoke_model_with_retry(self, prompt: str, max_retries: int = 3) -> str:
        """Invoke the AI model with retry logic"""
        for attempt in range(max_retries):
            try:
                if hasattr(self.model, 'ainvoke'):
                    response = await self.model.ainvoke(prompt)
                else:
                    response = self.model.invoke(prompt)
                
                # Handle different response types
                if hasattr(response, 'content'):
                    return response.content
                else:
                    return str(response)
                    
            except Exception as e:
                logger.warning(f"AI model invocation attempt {attempt + 1} failed: {str(e)}")
                if attempt == max_retries - 1:
                    raise
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
    
    def _adjust_confidence(
        self, 
        base_confidence: float, 
        description: str, 
        category: str, 
        amount: float
    ) -> float:
        """Adjust confidence based on heuristics"""
        adjusted = base_confidence
        
        # Boost confidence for clear keywords
        high_confidence_keywords = {
            "fuel": ["fuel", "petrol", "diesel", "gas station"],
            "food": ["restaurant", "food", "meal", "lunch", "dinner"],
            "transport": ["taxi", "bus", "transport", "uber", "bolt"],
            "utilities": ["electricity", "water", "zesco", "lwsc"],
            "rent": ["rent", "rental", "lease"],
            "telecommunications": ["airtel", "mtn", "zamtel", "internet"]
        }
        
        description_lower = description.lower()
        category_lower = category.lower()
        
        for cat_key, keywords in high_confidence_keywords.items():
            if cat_key in category_lower:
                for keyword in keywords:
                    if keyword in description_lower:
                        adjusted = min(0.95, adjusted + 0.1)
                        break
        
        # Reduce confidence for very generic descriptions
        generic_terms = ["payment", "transfer", "transaction", "amount"]
        if any(term in description_lower for term in generic_terms) and len(description) < 20:
            adjusted = max(0.3, adjusted - 0.2)
        
        # Adjust based on amount patterns (Zambian context)
        if amount > 10000:  # Large amounts might be rent, equipment
            if "rent" in category_lower or "equipment" in category_lower:
                adjusted = min(0.9, adjusted + 0.05)
        elif amount < 50:  # Small amounts might be transport, snacks
            if "transport" in category_lower or "food" in category_lower:
                adjusted = min(0.9, adjusted + 0.05)
        
        return round(adjusted, 2)
    
    def _fallback_categorization(self, description: str, transaction_type: str, amount: float) -> Dict[str, Any]:
        """Provide fallback categorization when AI fails"""
        logger.info("Using fallback categorization")
        
        # Simple keyword-based fallback
        fallback_rules = {
            "EXPENSE": {
                "Miscellaneous": 0.3,  # Default
                "Transport": ["taxi", "bus", "fuel", "petrol", "transport"],
                "Food & Dining": ["food", "restaurant", "meal", "lunch"],
                "Utilities": ["electricity", "water", "zesco", "lwsc"],
                "Telecommunications": ["airtel", "mtn", "internet", "phone"]
            },
            "INCOME": {
                "Other Income": 0.3,  # Default
                "Sales Revenue": ["sale", "payment", "invoice"],
                "Service Revenue": ["service", "consulting", "professional"]
            }
        }
        
        description_lower = description.lower()
        rules = fallback_rules.get(transaction_type, {})
        
        best_category = "Miscellaneous" if transaction_type == "EXPENSE" else "Other Income"
        best_confidence = 0.3
        
        for category, keywords in rules.items():
            if isinstance(keywords, list):
                for keyword in keywords:
                    if keyword in description_lower:
                        best_category = category
                        best_confidence = 0.6
                        break
        
        return {
            "category_id": str(uuid4()),
            "category_name": best_category,
            "category_type": transaction_type,
            "confidence": best_confidence,
            "explanation": f"Fallback categorization based on keyword matching",
            "keywords": [description.split()[0]] if description else [],
            "processing_time": 100,
            "model_used": "fallback"
        }
    
    async def record_positive_feedback(
        self, 
        transaction_id: str, 
        category_id: str, 
        feedback: Optional[str] = None
    ) -> None:
        """Record positive feedback for AI learning"""
        logger.info(f"Recording positive feedback for transaction {transaction_id}")
        # TODO: Implement learning mechanism
        # This could involve storing feedback in a database for model retraining
    
    async def record_negative_feedback(
        self, 
        transaction_id: str, 
        rejected_category_id: str, 
        correct_category_id: Optional[str] = None,
        reason: Optional[str] = None
    ) -> None:
        """Record negative feedback for AI learning"""
        logger.info(f"Recording negative feedback for transaction {transaction_id}")
        # TODO: Implement learning mechanism
        # This could involve storing feedback in a database for model retraining
    
    @monitor_performance
    async def bulk_categorize_transactions(
        self,
        transactions: List[Dict[str, Any]],
        user_id: str
    ) -> Dict[str, Any]:
        """Categorize multiple transactions in bulk"""
        start_time = time.time()
        results = []
        success_count = 0
        failure_count = 0
        
        for transaction in transactions:
            try:
                result = await self.categorize_transaction(
                    description=transaction["description"],
                    amount=transaction["amount"],
                    transaction_type=transaction["type"],
                    user_id=user_id
                )
                result["transaction_id"] = transaction["id"]
                results.append(result)
                success_count += 1
                
            except Exception as e:
                logger.error(f"Failed to categorize transaction {transaction.get('id')}: {str(e)}")
                failure_count += 1
                results.append({
                    "transaction_id": transaction.get("id"),
                    "error": str(e),
                    "success": False
                })
        
        processing_time = int((time.time() - start_time) * 1000)
        
        return {
            "batch_id": str(uuid4()),
            "processed_count": len(transactions),
            "success_count": success_count,
            "failure_count": failure_count,
            "results": results,
            "processing_time": processing_time
        }
