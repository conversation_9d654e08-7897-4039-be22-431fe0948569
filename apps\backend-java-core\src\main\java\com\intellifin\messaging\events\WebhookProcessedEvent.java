package com.intellifin.messaging.events;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * Event published when a webhook is processed
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WebhookProcessedEvent {

    private UUID webhookEventId;
    private UUID transactionId;
    private String externalTransactionId;
    private String source;
    private boolean success;
    private String errorMessage;
    private Long processingTimeMs;
    private Integer retryCount;
    private boolean canRetry;
    private OffsetDateTime timestamp;
}
