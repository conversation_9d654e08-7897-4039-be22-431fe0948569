package com.intellifin.dto;

import com.intellifin.model.Transaction;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

/**
 * DTO for manual transaction creation and updates
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ManualTransactionDto {

    private UUID id;

    @NotNull(message = "User ID is required")
    private UUID userId;

    @NotBlank(message = "Description is required")
    @Size(min = 3, max = 255, message = "Description must be between 3 and 255 characters")
    @Pattern(regexp = "^[a-zA-Z0-9\\s\\-_.,()]+$", message = "Description contains invalid characters")
    private String description;

    @NotNull(message = "Amount is required")
    @DecimalMin(value = "0.01", message = "Amount must be greater than 0")
    @DecimalMax(value = "999999999.99", message = "Amount exceeds maximum limit")
    @Digits(integer = 9, fraction = 2, message = "Amount must have at most 2 decimal places")
    private BigDecimal amount;

    @NotNull(message = "Transaction type is required")
    private Transaction.TransactionType type;

    @NotNull(message = "Transaction date is required")
    @PastOrPresent(message = "Transaction date cannot be in the future")
    private LocalDate date;

    private UUID categoryId;

    @Size(max = 1000, message = "Notes must not exceed 1000 characters")
    private String notes;

    private Boolean isDraft = false;

    private UUID draftId; // Reference to saved draft

    // AI suggestion fields
    private UUID suggestedCategoryId;
    private BigDecimal aiConfidence;
    private String aiExplanation;

    // Validation flags
    private Boolean skipValidation = false;
    private Boolean forceCreate = false;

    /**
     * Convert to Transaction entity
     */
    public Transaction toTransaction() {
        return Transaction.builder()
                .id(this.id)
                .userId(this.userId)
                .description(this.description)
                .amount(this.amount)
                .type(this.type)
                .date(this.date)
                .categoryId(this.categoryId)
                .aiCategoryId(this.suggestedCategoryId)
                .aiConfidence(this.aiConfidence)
                .aiExplanation(this.aiExplanation)
                .status(this.categoryId != null ? 
                        Transaction.TransactionStatus.CATEGORIZED : 
                        Transaction.TransactionStatus.PENDING_CLASSIFICATION)
                .source("MANUAL")
                .notes(this.notes)
                .build();
    }

    /**
     * Create from Transaction entity
     */
    public static ManualTransactionDto fromTransaction(Transaction transaction) {
        return ManualTransactionDto.builder()
                .id(transaction.getId())
                .userId(transaction.getUserId())
                .description(transaction.getDescription())
                .amount(transaction.getAmount())
                .type(transaction.getType())
                .date(transaction.getDate())
                .categoryId(transaction.getCategoryId())
                .notes(transaction.getNotes())
                .suggestedCategoryId(transaction.getAiCategoryId())
                .aiConfidence(transaction.getAiConfidence())
                .aiExplanation(transaction.getAiExplanation())
                .isDraft(false)
                .build();
    }

    /**
     * Validation for business rules
     */
    public boolean isValidForCreation() {
        if (description == null || description.trim().length() < 3) {
            return false;
        }
        
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }
        
        if (type == null) {
            return false;
        }
        
        if (date == null || date.isAfter(LocalDate.now())) {
            return false;
        }
        
        // Check if date is not too far in the past (2 years)
        if (date.isBefore(LocalDate.now().minusYears(2))) {
            return false;
        }
        
        return true;
    }

    /**
     * Check if this is a draft transaction
     */
    public boolean isDraftTransaction() {
        return Boolean.TRUE.equals(isDraft) || draftId != null;
    }

    /**
     * Check if AI suggestion is available
     */
    public boolean hasAISuggestion() {
        return suggestedCategoryId != null && aiConfidence != null;
    }

    /**
     * Check if AI suggestion has high confidence
     */
    public boolean hasHighConfidenceAISuggestion() {
        return hasAISuggestion() && aiConfidence.compareTo(new BigDecimal("0.8")) >= 0;
    }
}
