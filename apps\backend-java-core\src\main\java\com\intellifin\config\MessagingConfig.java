package com.intellifin.config;

import com.intellifin.service.MessageHandler;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

@Configuration
public class MessagingConfig {

    @Value("${messaging.provider:local}")
    private String messagingProvider;

    @Value("${messaging.rabbitmq.host:localhost}")
    private String rabbitmqHost;

    @Value("${messaging.rabbitmq.port:5672}")
    private int rabbitmqPort;

    @Value("${messaging.rabbitmq.username:guest}")
    private String rabbitmqUsername;

    @Value("${messaging.rabbitmq.password:guest}")
    private String rabbitmqPassword;

    @Value("${messaging.azure.connection-string:}")
    private String azureServiceBusConnectionString;

    @Bean
    @Profile({"local", "development"})
    public MessageHandler rabbitMQMessagingService() {
        return new RabbitMQMessagingService(
            rabbitmqHost,
            rabbitmqPort,
            rabbitmqUsername,
            rabbitmqPassword
        );
    }

    @Bean
    @Profile({"production", "staging"})
    public MessageHandler azureServiceBusMessagingService() {
        return new AzureServiceBusMessagingService(azureServiceBusConnectionString);
    }
}
