package com.intellifin.repository;

import com.intellifin.model.Account;
import com.intellifin.model.JournalEntry;
import com.intellifin.model.JournalEntryLine;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Repository
public interface JournalEntryLineRepository extends JpaRepository<JournalEntryLine, UUID> {

    /**
     * Find all lines for a specific journal entry
     */
    List<JournalEntryLine> findByJournalEntryOrderByLineNumber(JournalEntry journalEntry);

    /**
     * Find all lines for a specific account
     */
    List<JournalEntryLine> findByAccountOrderByCreatedAtDesc(Account account);

    /**
     * Find lines for an account within a date range
     */
    @Query("""
        SELECT jel FROM JournalEntryLine jel 
        JOIN jel.journalEntry je 
        WHERE jel.account = :account 
        AND je.status = 'POSTED' 
        AND je.entryDate BETWEEN :startDate AND :endDate 
        ORDER BY je.entryDate DESC
    """)
    List<JournalEntryLine> findByAccountAndDateRange(
            @Param("account") Account account,
            @Param("startDate") OffsetDateTime startDate,
            @Param("endDate") OffsetDateTime endDate
    );

    /**
     * Calculate account balance from posted journal entries
     */
    @Query("""
        SELECT\s
            COALESCE(SUM(jel.debitAmount), 0) - COALESCE(SUM(jel.creditAmount), 0)
        FROM JournalEntryLine jel\s
        JOIN jel.journalEntry je\s
        WHERE jel.account = :account\s
        AND je.status = 'POSTED'
   \s""")
    BigDecimal calculateAccountBalance(@Param("account") Account account);

    /**
     * Calculate account balance up to a specific date
     */
    @Query("""
        SELECT\s
            COALESCE(SUM(jel.debitAmount), 0) - COALESCE(SUM(jel.creditAmount), 0)
        FROM JournalEntryLine jel\s
        JOIN jel.journalEntry je\s
        WHERE jel.account = :account\s
        AND je.status = 'POSTED'\s
        AND je.entryDate <= :asOfDate
   \s""")
    BigDecimal calculateAccountBalanceAsOf(
            @Param("account") Account account,
            @Param("asOfDate") OffsetDateTime asOfDate
    );

    /**
     * Get account activity summary
     */
    @Query("""
        SELECT\s
            COUNT(jel.id) as transactionCount,
            COALESCE(SUM(jel.debitAmount), 0) as totalDebits,
            COALESCE(SUM(jel.creditAmount), 0) as totalCredits,
            MAX(je.entryDate) as lastTransactionDate
        FROM JournalEntryLine jel\s
        JOIN jel.journalEntry je\s
        WHERE jel.account = :account\s
        AND je.status = 'POSTED'
   \s""")
    Object[] getAccountActivitySummary(@Param("account") Account account);

    /**
     * Find lines with debit amounts for an account
     */
    @Query("""
        SELECT jel FROM JournalEntryLine jel 
        JOIN jel.journalEntry je 
        WHERE jel.account = :account 
        AND jel.debitAmount > 0 
        AND je.status = 'POSTED' 
        ORDER BY je.entryDate DESC
    """)
    List<JournalEntryLine> findDebitLinesByAccount(@Param("account") Account account);

    /**
     * Find lines with credit amounts for an account
     */
    @Query("""
        SELECT jel FROM JournalEntryLine jel 
        JOIN jel.journalEntry je 
        WHERE jel.account = :account 
        AND jel.creditAmount > 0 
        AND je.status = 'POSTED' 
        ORDER BY je.entryDate DESC
    """)
    List<JournalEntryLine> findCreditLinesByAccount(@Param("account") Account account);

    /**
     * Validate that a journal entry is balanced
     */
    @Query("""
        SELECT\s
            SUM(jel.debitAmount) = SUM(jel.creditAmount)\s
        FROM JournalEntryLine jel\s
        WHERE jel.journalEntry = :journalEntry
   \s""")
    Boolean isJournalEntryBalanced(@Param("journalEntry") JournalEntry journalEntry);

    /**
     * Get total debits for a journal entry
     */
    @Query("""
        SELECT COALESCE(SUM(jel.debitAmount), 0) 
        FROM JournalEntryLine jel 
        WHERE jel.journalEntry = :journalEntry
    """)
    BigDecimal getTotalDebits(@Param("journalEntry") JournalEntry journalEntry);

    /**
     * Get total credits for a journal entry
     */
    @Query("""
        SELECT COALESCE(SUM(jel.creditAmount), 0) 
        FROM JournalEntryLine jel 
        WHERE jel.journalEntry = :journalEntry
    """)
    BigDecimal getTotalCredits(@Param("journalEntry") JournalEntry journalEntry);
}
