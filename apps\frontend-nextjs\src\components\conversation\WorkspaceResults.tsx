"use client"

import React from 'react';
import { motion } from 'framer-motion';
import { 
  Table, 
  FileText, 
  TrendingUp, 
  DollarSign, 
  Calendar,
  Filter,
  Download,
  Eye
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useFinancialStore } from '@/stores';
import { formatCurrency, formatDate } from '@/utils';

interface WorkspaceResultsProps {
  currentTask?: string | null;
}

export function WorkspaceResults({ currentTask }: WorkspaceResultsProps) {
  const { transactions, accounts, dashboardData } = useFinancialStore();

  // Determine result type based on task and AI intent
  const getResultType = (task: string | null): string => {
    if (!task) return 'empty';

    // Check if we have AI intent information in the conversation store
    // For now, use the task-based logic, but this can be enhanced with actual AI intent
    const taskLower = task.toLowerCase();

    // Map common intents to result types
    if (taskLower.includes('transaction') || taskLower.includes('recent') || taskLower.includes('spent') || taskLower.includes('paid')) {
      return 'transactions';
    }
    if (taskLower.includes('balance') || taskLower.includes('account')) {
      return 'accounts';
    }
    if (taskLower.includes('invoice') || taskLower.includes('bill')) {
      return 'invoices';
    }
    if (taskLower.includes('profit') || taskLower.includes('summary') || taskLower.includes('month') || taskLower.includes('income')) {
      return 'summary';
    }
    if (taskLower.includes('help') || taskLower.includes('what can you')) {
      return 'help';
    }

    return 'general';
  };

  const resultType = getResultType(currentTask);

  const renderTransactionResults = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="font-semibold text-gray-900">Recent Transactions</h4>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>
      
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b">
                <tr>
                  <th className="text-left p-3 text-sm font-medium text-gray-600">Date</th>
                  <th className="text-left p-3 text-sm font-medium text-gray-600">Description</th>
                  <th className="text-left p-3 text-sm font-medium text-gray-600">Category</th>
                  <th className="text-right p-3 text-sm font-medium text-gray-600">Amount</th>
                  <th className="text-center p-3 text-sm font-medium text-gray-600">Actions</th>
                </tr>
              </thead>
              <tbody>
                {transactions.slice(0, 10).map((transaction, index) => (
                  <motion.tr
                    key={transaction.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="border-b hover:bg-gray-50"
                  >
                    <td className="p-3 text-sm text-gray-600">
                      {formatDate(transaction.date)}
                    </td>
                    <td className="p-3">
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {transaction.description}
                        </p>
                        <p className="text-xs text-gray-500">
                          {transaction.reference}
                        </p>
                      </div>
                    </td>
                    <td className="p-3">
                      <Badge variant={transaction.category ? 'default' : 'secondary'}>
                        {transaction.category || 'Uncategorized'}
                      </Badge>
                    </td>
                    <td className="p-3 text-right">
                      <span className={`text-sm font-medium ${
                        transaction.amount >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {transaction.amount >= 0 ? '+' : ''}{formatCurrency(transaction.amount)}
                      </span>
                    </td>
                    <td className="p-3 text-center">
                      <Button variant="ghost" size="sm">
                        <Eye className="w-4 h-4" />
                      </Button>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderAccountResults = () => (
    <div className="space-y-4">
      <h4 className="font-semibold text-gray-900">Account Balances</h4>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {accounts.map((account, index) => (
          <motion.div
            key={account.id}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h5 className="font-medium text-gray-900">{account.name}</h5>
                    <p className="text-sm text-gray-500">{account.type}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-semibold text-gray-900">
                      {formatCurrency(account.balance)}
                    </p>
                    <p className="text-xs text-gray-500">
                      Last updated: {formatDate(account.lastUpdated)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  );

  const renderSummaryResults = () => (
    <div className="space-y-4">
      <h4 className="font-semibold text-gray-900">Financial Summary</h4>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Total Income</p>
                  <p className="text-xl font-semibold text-gray-900">
                    {formatCurrency(dashboardData?.summary?.totalIncome || 0)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                  <DollarSign className="w-5 h-5 text-red-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Total Expenses</p>
                  <p className="text-xl font-semibold text-gray-900">
                    {formatCurrency(dashboardData?.summary?.totalExpenses || 0)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Net Profit</p>
                  <p className="text-xl font-semibold text-gray-900">
                    {formatCurrency((dashboardData?.summary?.totalIncome || 0) - (dashboardData?.summary?.totalExpenses || 0))}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );

  const renderGeneralResults = () => (
    <div className="space-y-4">
      <h4 className="font-semibold text-gray-900">AI Response</h4>
      <Card>
        <CardContent className="p-4">
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <FileText className="w-8 h-8 text-blue-600" />
            </div>
            <p className="text-gray-600">
              Processing your request: "{currentTask}"
            </p>
            <p className="text-sm text-gray-500 mt-2">
              Results will appear here once the AI completes the analysis.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderHelpResults = () => (
    <div className="space-y-4">
      <h4 className="font-semibold text-gray-900">How I Can Help You</h4>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <Table className="w-5 h-5 text-blue-600" />
              </div>
              <h5 className="font-medium text-gray-900">View Transactions</h5>
            </div>
            <p className="text-sm text-gray-600 mb-3">
              I can show you recent transactions, filter by date ranges, and help you analyze your spending patterns.
            </p>
            <div className="text-xs text-gray-500">
              Try: "Show me transactions from last week" or "What did I spend on utilities?"
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                <FileText className="w-5 h-5 text-green-600" />
              </div>
              <h5 className="font-medium text-gray-900">Create Invoices</h5>
            </div>
            <p className="text-sm text-gray-600 mb-3">
              I can help you create professional invoices for your clients quickly and easily.
            </p>
            <div className="text-xs text-gray-500">
              Try: "Create invoice for ZESCO" or "Bill John Banda for consulting"
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                <TrendingUp className="w-5 h-5 text-purple-600" />
              </div>
              <h5 className="font-medium text-gray-900">Financial Summaries</h5>
            </div>
            <p className="text-sm text-gray-600 mb-3">
              Get insights into your business performance with profit/loss summaries and financial reports.
            </p>
            <div className="text-xs text-gray-500">
              Try: "Show my profit this month" or "How is my business doing?"
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                <DollarSign className="w-5 h-5 text-orange-600" />
              </div>
              <h5 className="font-medium text-gray-900">Account Management</h5>
            </div>
            <p className="text-sm text-gray-600 mb-3">
              Check balances, connect new accounts like MTN Mobile Money, and manage your financial accounts.
            </p>
            <div className="text-xs text-gray-500">
              Try: "What's my balance?" or "Connect my MTN account"
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderEmptyState = () => (
    <div className="flex items-center justify-center h-full">
      <div className="text-center">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Table className="w-8 h-8 text-gray-400" />
        </div>
        <p className="text-gray-500">
          Results will appear here when you ask me something
        </p>
      </div>
    </div>
  );

  return (
    <div className="h-full overflow-y-auto">
      {resultType === 'transactions' && renderTransactionResults()}
      {resultType === 'accounts' && renderAccountResults()}
      {resultType === 'summary' && renderSummaryResults()}
      {resultType === 'help' && renderHelpResults()}
      {resultType === 'general' && renderGeneralResults()}
      {resultType === 'empty' && renderEmptyState()}
    </div>
  );
}
