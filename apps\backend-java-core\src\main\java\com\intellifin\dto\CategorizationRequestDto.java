package com.intellifin.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

/**
 * DTO for transaction categorization requests
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CategorizationRequestDto {
    
    @NotNull(message = "Transaction ID is required")
    private UUID transactionId;
    
    @NotNull(message = "Category ID is required")
    private UUID categoryId;
    
    @NotNull(message = "AI suggestion flag is required")
    private Boolean isAISuggestion;
    
    private String explanation;
}
