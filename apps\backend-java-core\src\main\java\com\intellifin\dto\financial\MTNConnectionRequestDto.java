package com.intellifin.dto.financial;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MTNConnectionRequestDto {

    @NotBlank(message = "User ID is required")
    private String userId;

    @NotBlank(message = "Account name is required")
    private String accountName;

    @NotBlank(message = "Phone number is required")
    @Pattern(regexp = "^\\+?[1-9]([\\s-]?\\d){9,14}$", message = "Invalid phone number format")
    private String phoneNumber;
}
