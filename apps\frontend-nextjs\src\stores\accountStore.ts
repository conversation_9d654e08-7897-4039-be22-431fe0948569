import { create } from 'zustand';
import { Account } from '@intellifin/data-models';
import * as accountService from '../services/accountService';

interface AccountState {
  accounts: Account[];
  loading: boolean;
  error: string | null;
  fetchAccounts: () => Promise<void>;
  createAccount: (account: Omit<Account, 'id' | 'currentBalance' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateAccount: (id: string, account: Partial<Account>) => Promise<void>;
  deleteAccount: (id: string) => Promise<void>;
}

export const useAccountStore = create<AccountState>((set) => ({
  accounts: [],
  loading: false,
  error: null,
  fetchAccounts: async () => {
    set({ loading: true, error: null });
    try {
      const accounts = await accountService.getAccounts();
      set({ accounts, loading: false });
    } catch (err) {
      set({ error: 'Failed to fetch accounts', loading: false });
    }
  },
  createAccount: async (account) => {
    try {
      const newAccount = await accountService.createAccount(account);
      set((state) => ({ accounts: [...state.accounts, newAccount] }));
    } catch (err) {
      // Handle error
    }
  },
  updateAccount: async (id, account) => {
    try {
      const updatedAccount = await accountService.updateAccount(id, account);
      set((state) => ({
        accounts: state.accounts.map((a) => (a.id === id ? updatedAccount : a)),
      }));
    } catch (err) {
      // Handle error
    }
  },
  deleteAccount: async (id) => {
    try {
      await accountService.deleteAccount(id);
      set((state) => ({
        accounts: state.accounts.filter((a) => a.id !== id),
      }));
    } catch (err) {
      // Handle error
    }
  },
}));
