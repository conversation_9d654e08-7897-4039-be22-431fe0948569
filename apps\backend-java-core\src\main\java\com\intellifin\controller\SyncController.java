package com.intellifin.controller;

import com.intellifin.dto.sync.SyncStatusDto;
import com.intellifin.service.TransactionSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

/**
 * Controller for transaction synchronization status and management
 */
@RestController
@RequestMapping("/api/v1/sync")
@RequiredArgsConstructor
@Slf4j
public class SyncController {

    private final TransactionSyncService transactionSyncService;

    /**
     * Get sync status for a financial account
     */
    @GetMapping("/status")
    public ResponseEntity<SyncStatusDto> getSyncStatus(@RequestParam UUID accountId) {
        try {
            log.info("Getting sync status for account: {}", accountId);
            
            SyncStatusDto syncStatus = transactionSyncService.getSyncStatus(accountId);
            
            return ResponseEntity.ok(syncStatus);
            
        } catch (Exception e) {
            log.error("Error getting sync status for account: {}", accountId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Trigger manual sync for a financial account
     */
    @PostMapping("/trigger")
    public ResponseEntity<SyncStatusDto> triggerSync(@RequestParam UUID accountId) {
        try {
            log.info("Triggering manual sync for account: {}", accountId);
            
            // In a real implementation, this would trigger a manual sync process
            // For now, just return the current status
            SyncStatusDto syncStatus = transactionSyncService.getSyncStatus(accountId);
            
            return ResponseEntity.ok(syncStatus);
            
        } catch (Exception e) {
            log.error("Error triggering sync for account: {}", accountId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Pause sync for a financial account
     */
    @PostMapping("/pause")
    public ResponseEntity<Void> pauseSync(@RequestParam UUID accountId) {
        try {
            log.info("Pausing sync for account: {}", accountId);
            
            // In a real implementation, this would pause the sync process
            // For now, just log the action
            
            return ResponseEntity.ok().build();
            
        } catch (Exception e) {
            log.error("Error pausing sync for account: {}", accountId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Resume sync for a financial account
     */
    @PostMapping("/resume")
    public ResponseEntity<Void> resumeSync(@RequestParam UUID accountId) {
        try {
            log.info("Resuming sync for account: {}", accountId);
            
            // In a real implementation, this would resume the sync process
            // For now, just log the action
            
            return ResponseEntity.ok().build();
            
        } catch (Exception e) {
            log.error("Error resuming sync for account: {}", accountId, e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
