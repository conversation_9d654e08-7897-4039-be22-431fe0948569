"use client"

import { useState, useCallback } from 'react';

interface Intent {
  name: string;
  confidence: number;
  description: string;
}

interface Entity {
  value: string;
  confidence: number;
  startIndex: number;
  endIndex: number;
  entityType: string;
}

interface IntentRecognitionResult {
  intent: Intent;
  entities: Record<string, Entity[]>;
  suggestions?: string[];
  fallback?: {
    type: 'CLARIFICATION' | 'ALTERNATIVE' | 'ERROR';
    message: string;
    options?: string[];
  };
  processingTimeMs: number;
}

interface UseIntentRecognitionReturn {
  recognizeIntent: (command: string, context?: any) => Promise<IntentRecognitionResult>;
  isProcessing: boolean;
  error: string | null;
  lastResult: IntentRecognitionResult | null;
  clearError: () => void;
}

export function useIntentRecognition(): UseIntentRecognitionReturn {
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastResult, setLastResult] = useState<IntentRecognitionResult | null>(null);

  const recognizeIntent = useCallback(async (
    command: string, 
    context?: any
  ): Promise<IntentRecognitionResult> => {
    setIsProcessing(true);
    setError(null);

    try {
      const startTime = Date.now();

      // Call the AI service for intent recognition
      const response = await fetch('/api/ai/intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          command,
          userId: 'current-user', // TODO: Get from auth context
          sessionId: `session_${Date.now()}`,
          context: context || {}
        })
      });

      if (!response.ok) {
        throw new Error(`Intent recognition failed: ${response.status}`);
      }

      const result = await response.json();
      const processingTime = Date.now() - startTime;

      // Transform the response to match our interface
      const intentResult: IntentRecognitionResult = {
        intent: {
          name: result.intent?.name || 'UNCLEAR',
          confidence: result.intent?.confidence || 0.0,
          description: result.intent?.description || 'Intent not clearly identified'
        },
        entities: result.entities || {},
        suggestions: result.suggestions || [],
        fallback: result.fallback,
        processingTimeMs: result.processing_time_ms || processingTime
      };

      setLastResult(intentResult);
      return intentResult;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      
      // Return a fallback result
      const fallbackResult: IntentRecognitionResult = {
        intent: {
          name: 'UNCLEAR',
          confidence: 0.0,
          description: 'Error processing command'
        },
        entities: {},
        fallback: {
          type: 'ERROR',
          message: errorMessage
        },
        processingTimeMs: 0
      };

      setLastResult(fallbackResult);
      return fallbackResult;

    } finally {
      setIsProcessing(false);
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    recognizeIntent,
    isProcessing,
    error,
    lastResult,
    clearError
  };
}

// Helper functions for working with intent recognition results

export function getIntentDisplayName(intentName: string): string {
  switch (intentName) {
    case 'CREATE_INVOICE':
      return 'Create Invoice';
    case 'SHOW_SUMMARY':
      return 'Show Summary';
    case 'CATEGORIZE_TRANSACTION':
      return 'Categorize Transaction';
    case 'VIEW_TRANSACTIONS':
      return 'View Transactions';
    case 'CONNECT_ACCOUNT':
      return 'Connect Account';
    case 'GET_HELP':
      return 'Get Help';
    case 'UNCLEAR':
      return 'Unclear Intent';
    default:
      return intentName.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  }
}

export function getIntentColor(intentName: string): string {
  switch (intentName) {
    case 'CREATE_INVOICE':
      return 'blue';
    case 'SHOW_SUMMARY':
      return 'green';
    case 'CATEGORIZE_TRANSACTION':
      return 'purple';
    case 'VIEW_TRANSACTIONS':
      return 'orange';
    case 'CONNECT_ACCOUNT':
      return 'cyan';
    case 'GET_HELP':
      return 'gray';
    case 'UNCLEAR':
      return 'red';
    default:
      return 'gray';
  }
}

export function isHighConfidenceIntent(confidence: number): boolean {
  return confidence >= 0.8;
}

export function isMediumConfidenceIntent(confidence: number): boolean {
  return confidence >= 0.5 && confidence < 0.8;
}

export function isLowConfidenceIntent(confidence: number): boolean {
  return confidence < 0.5;
}

export function getConfidenceLevel(confidence: number): 'high' | 'medium' | 'low' {
  if (confidence >= 0.8) return 'high';
  if (confidence >= 0.5) return 'medium';
  return 'low';
}

export function extractEntityValue(entities: Record<string, Entity[]>, entityType: string): string | null {
  const entityList = entities[entityType];
  if (entityList && entityList.length > 0) {
    return entityList[0].value;
  }
  return null;
}

export function extractAllEntityValues(entities: Record<string, Entity[]>, entityType: string): string[] {
  const entityList = entities[entityType];
  if (entityList && entityList.length > 0) {
    return entityList.map(entity => entity.value);
  }
  return [];
}

export function hasEntity(entities: Record<string, Entity[]>, entityType: string): boolean {
  const entityList = entities[entityType];
  return entityList && entityList.length > 0;
}

export function getEntityCount(entities: Record<string, Entity[]>): number {
  return Object.values(entities).reduce((total, entityList) => total + entityList.length, 0);
}

export function getHighConfidenceEntities(entities: Record<string, Entity[]>, threshold: number = 0.8): Record<string, Entity[]> {
  const highConfidenceEntities: Record<string, Entity[]> = {};
  
  Object.entries(entities).forEach(([type, entityList]) => {
    const highConfEntities = entityList.filter(entity => entity.confidence >= threshold);
    if (highConfEntities.length > 0) {
      highConfidenceEntities[type] = highConfEntities;
    }
  });
  
  return highConfidenceEntities;
}
