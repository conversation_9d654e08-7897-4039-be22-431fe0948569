package com.intellifin.repository;

import com.intellifin.model.SyncStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository for SyncStatus entities
 */
@Repository
public interface SyncStatusRepository extends JpaRepository<SyncStatus, UUID> {

    /**
     * Find sync status by financial account ID and sync type
     */
    Optional<SyncStatus> findByFinancialAccountIdAndSyncType(UUID financialAccountId, SyncStatus.SyncType syncType);

    /**
     * Find sync statuses by financial account ID
     */
    List<SyncStatus> findByFinancialAccountId(UUID financialAccountId);

    /**
     * Find sync statuses by sync type and status
     */
    List<SyncStatus> findBySyncTypeAndSyncStatus(SyncStatus.SyncType syncType, SyncStatus.Status status);

    /**
     * Find sync statuses that need attention (failed or haven't synced recently)
     */
    @Query("SELECT s FROM SyncStatus s WHERE s.syncStatus = 'FAILED' OR " +
           "(s.syncStatus = 'ACTIVE' AND s.lastSuccessfulSyncAt < :cutoffTime)")
    List<SyncStatus> findSyncStatusesNeedingAttention(@Param("cutoffTime") OffsetDateTime cutoffTime);

    /**
     * Find active webhook sync statuses
     */
    @Query("SELECT s FROM SyncStatus s WHERE s.syncType = 'WEBHOOK' AND s.syncStatus = 'ACTIVE'")
    List<SyncStatus> findActiveWebhookSyncStatuses();

    /**
     * Find sync statuses with pending transactions
     */
    @Query("SELECT s FROM SyncStatus s WHERE s.pendingTransactions > 0")
    List<SyncStatus> findSyncStatusesWithPendingTransactions();

    /**
     * Count sync statuses by status
     */
    @Query("SELECT COUNT(s) FROM SyncStatus s WHERE s.syncStatus = :status")
    Long countByStatus(@Param("status") SyncStatus.Status status);

    /**
     * Find sync statuses by user ID
     */
    @Query("SELECT s FROM SyncStatus s WHERE s.financialAccount.user.id = :userId")
    List<SyncStatus> findByUserId(@Param("userId") UUID userId);

    /**
     * Find sync statuses that haven't synced for a while
     */
    @Query("SELECT s FROM SyncStatus s WHERE s.syncStatus = 'ACTIVE' AND " +
           "(s.lastSyncAt IS NULL OR s.lastSyncAt < :cutoffTime)")
    List<SyncStatus> findStaleActiveSyncStatuses(@Param("cutoffTime") OffsetDateTime cutoffTime);
}
