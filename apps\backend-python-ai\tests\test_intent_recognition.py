"""
Tests for intent recognition service
"""

import pytest
from unittest.mock import Mock, patch

from src.services.intent_recognition import IntentRecognitionService
from src.models.schemas import IntentRecognitionRequest, IntentType, EntityType


class TestIntentRecognitionService:
    """Test cases for intent recognition service"""
    
    @pytest.fixture
    def service(self):
        """Create intent recognition service instance"""
        with patch('src.services.intent_recognition.get_ai_config') as mock_config:
            mock_model = Mock()
            mock_config.return_value.get_model.return_value = mock_model
            service = IntentRecognitionService()
            service.model = mock_model
            return service
    
    @pytest.fixture
    def sample_request(self):
        """Sample intent recognition request"""
        return IntentRecognitionRequest(
            command="Show me recent transactions",
            user_id="test_user",
            session_id="test_session"
        )
    
    def test_preprocess_command(self, service):
        """Test command preprocessing"""
        # Test basic preprocessing
        result = service._preprocess_command("Show Me Recent Transactions")
        assert result == "show me recent transactions"
        
        # Test Zambian term normalization
        result = service._preprocess_command("I bought mealie meal")
        assert "food_supplies" in result
        
        # Test currency normalization
        result = service._preprocess_command("I spent ZMW 500")
        assert "kwacha" in result
    
    def test_build_context_string(self, service):
        """Test context string building"""
        # Test empty context
        result = service._build_context_string(None)
        assert result == "No additional context provided."
        
        # Test with previous commands
        context = {
            "previous_commands": ["show balance", "create invoice", "view transactions"]
        }
        result = service._build_context_string(context)
        assert "Previous commands" in result
        assert "view transactions" in result
    
    @pytest.mark.asyncio
    async def test_recognize_intent_success(self, service, sample_request):
        """Test successful intent recognition"""
        # Mock AI response
        mock_response = '''
        {
            "intent": "VIEW_TRANSACTIONS",
            "confidence": 0.95,
            "description": "User wants to view transaction history",
            "entities": {
                "date_range": [
                    {
                        "value": "recent",
                        "confidence": 0.8,
                        "start_index": 8,
                        "end_index": 14
                    }
                ]
            },
            "needs_clarification": false,
            "suggestions": ["Show transactions from last week", "View all transactions"]
        }
        '''
        
        service.model.invoke.return_value = mock_response
        
        response = await service.recognize_intent(sample_request)
        
        assert response.intent.name == IntentType.VIEW_TRANSACTIONS
        assert response.intent.confidence == 0.95
        assert EntityType.DATE_RANGE in response.entities
        assert len(response.suggestions) == 2
        assert response.processing_time_ms > 0
    
    @pytest.mark.asyncio
    async def test_recognize_intent_with_fallback(self, service):
        """Test intent recognition with fallback"""
        request = IntentRecognitionRequest(
            command="I want to do something",
            user_id="test_user",
            session_id="test_session"
        )
        
        # Mock AI response with low confidence
        mock_response = '''
        {
            "intent": "UNCLEAR",
            "confidence": 0.3,
            "description": "Intent is not clear",
            "entities": {},
            "needs_clarification": true,
            "clarification_questions": ["What would you like to do?"]
        }
        '''
        
        service.model.invoke.return_value = mock_response
        
        response = await service.recognize_intent(request)
        
        assert response.intent.name == IntentType.UNCLEAR
        assert response.fallback is not None
        assert response.fallback.type.value == "CLARIFICATION"
    
    @pytest.mark.asyncio
    async def test_recognize_intent_error_handling(self, service, sample_request):
        """Test error handling in intent recognition"""
        # Mock AI model to raise exception
        service.model.invoke.side_effect = Exception("AI model error")
        
        response = await service.recognize_intent(sample_request)
        
        assert response.intent.name == IntentType.UNCLEAR
        assert response.intent.confidence == 0.0
        assert response.fallback is not None
        assert response.fallback.type.value == "ERROR"
    
    def test_parse_entities(self, service):
        """Test entity parsing"""
        entities_data = {
            "amount": [
                {
                    "value": 500.0,
                    "confidence": 0.9,
                    "start_index": 10,
                    "end_index": 15
                }
            ],
            "client_name": [
                {
                    "value": "ZESCO",
                    "confidence": 0.85,
                    "start_index": 20,
                    "end_index": 25
                }
            ]
        }
        
        result = service._parse_entities(entities_data, "I paid K500 to ZESCO")
        
        assert EntityType.AMOUNT in result
        assert EntityType.CLIENT_NAME in result
        assert result[EntityType.AMOUNT][0].value == 500.0
        assert result[EntityType.CLIENT_NAME][0].value == "ZESCO"
    
    def test_get_supported_intents(self, service):
        """Test getting supported intents"""
        intents = service.get_supported_intents()
        
        assert len(intents) > 0
        assert any(intent["intent"] == "VIEW_TRANSACTIONS" for intent in intents)
        assert any(intent["intent"] == "CREATE_INVOICE" for intent in intents)
        
        # Check structure
        for intent in intents:
            assert "intent" in intent
            assert "description" in intent
            assert "examples" in intent


@pytest.mark.integration
class TestIntentRecognitionIntegration:
    """Integration tests for intent recognition"""
    
    @pytest.mark.asyncio
    async def test_real_intent_recognition(self):
        """Test with real AI model (requires model to be available)"""
        try:
            service = IntentRecognitionService()
            
            request = IntentRecognitionRequest(
                command="Show me transactions from last week",
                user_id="test_user",
                session_id="test_session"
            )
            
            response = await service.recognize_intent(request)
            
            # Basic assertions
            assert response.intent is not None
            assert response.processing_time_ms > 0
            
        except Exception as e:
            pytest.skip(f"AI model not available: {e}")
    
    @pytest.mark.asyncio
    async def test_zambian_business_context(self):
        """Test recognition with Zambian business context"""
        try:
            service = IntentRecognitionService()
            
            request = IntentRecognitionRequest(
                command="I paid K2500 to ZESCO for electricity",
                user_id="test_user",
                session_id="test_session"
            )
            
            response = await service.recognize_intent(request)
            
            # Should recognize as transaction-related
            assert response.intent.name in [
                IntentType.VIEW_TRANSACTIONS, 
                IntentType.CATEGORIZE_TRANSACTION
            ]
            
        except Exception as e:
            pytest.skip(f"AI model not available: {e}")


if __name__ == "__main__":
    pytest.main([__file__])
