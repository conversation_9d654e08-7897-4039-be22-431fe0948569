package com.intellifin.service;

import com.intellifin.dto.financial.DocumentProcessingStatusDto;
import com.intellifin.dto.financial.DocumentUploadRequestDto;
import com.intellifin.dto.financial.DocumentUploadResponseDto;
import com.intellifin.exception.ValidationException;
import com.intellifin.model.DocumentUpload;
import com.intellifin.model.FinancialAccount;
import com.intellifin.model.User;
import com.intellifin.repository.DocumentUploadRepository;
import com.intellifin.repository.FinancialAccountRepository;
import com.intellifin.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * Service for handling document uploads and processing
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DocumentUploadService {

    private final DocumentUploadRepository documentUploadRepository;
    private final UserRepository userRepository;
    private final FinancialAccountRepository financialAccountRepository;
    private final DocumentProcessingMessagingService messagingService;
    private final FileStorageService fileStorageService;

    @Value("${document.upload.max-size-mb:10}")
    private long maxFileSizeMB;

    @Value("${document.upload.url-expiry-minutes:60}")
    private long urlExpiryMinutes;

    /**
     * Initiate document upload
     */
    @Transactional
    public DocumentUploadResponseDto initiateUpload(DocumentUploadRequestDto request) {
        log.info("Initiating document upload for user: {}", request.getUserId());

        // Validate request
        validateUploadRequest(request);

        UUID userId = UUID.fromString(request.getUserId());
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ValidationException("User not found"));

        // Generate unique upload ID
        String uploadId = generateUploadId();

        // Create document upload record
        DocumentUpload upload = DocumentUpload.builder()
                .uploadId(uploadId)
                .user(user)
                .fileName(request.getFileName())
                .fileType(mapFileType(request.getFileType()))
                .fileSizeBytes(request.getFileSizeBytes())
                .processingStatus(DocumentUpload.ProcessingStatus.UPLOADING)
                .build();

        // Create or link to financial account if account name provided
        if (request.getAccountName() != null && !request.getAccountName().trim().isEmpty()) {
            FinancialAccount account = findOrCreateFinancialAccount(user, request);
            upload.setFinancialAccount(account);
        }

        upload = documentUploadRepository.save(upload);
        log.debug("Created document upload record: {}", upload.getId());

        // Generate pre-signed upload URL
        String uploadUrl = fileStorageService.generateUploadUrl(uploadId, request.getFileName());
        OffsetDateTime expiresAt = OffsetDateTime.now().plusMinutes(urlExpiryMinutes);

        // Update upload record with URL details
        upload.setUploadUrl(uploadUrl);
        upload.setUploadUrlExpiresAt(expiresAt);
        documentUploadRepository.save(upload);

        // Publish upload started event
        messagingService.publishDocumentUploadStarted(upload);

        return DocumentUploadResponseDto.builder()
                .uploadId(uploadId)
                .uploadUrl(uploadUrl)
                .expiresAt(expiresAt)
                .build();
    }

    /**
     * Get document upload status
     */
    public DocumentProcessingStatusDto getUploadStatus(String uploadId) {
        log.debug("Getting upload status for: {}", uploadId);

        DocumentUpload upload = documentUploadRepository.findByUploadId(uploadId)
                .orElseThrow(() -> new ValidationException("Upload not found"));

        return mapToStatusDto(upload);
    }

    /**
     * Confirm document upload completion
     */
    @Transactional
    public void confirmUpload(String uploadId) {
        log.info("Confirming upload completion: {}", uploadId);

        DocumentUpload upload = documentUploadRepository.findByUploadId(uploadId)
                .orElseThrow(() -> new ValidationException("Upload not found"));

        if (!upload.isUploading()) {
            throw new ValidationException("Upload is not in uploading status");
        }

        // Mark as processing and trigger document parsing
        upload.markAsProcessing();
        documentUploadRepository.save(upload);

        // Publish document processing event
        messagingService.publishDocumentProcessingRequested(upload);

        log.info("Upload confirmed and processing started: {}", uploadId);
    }

    /**
     * Handle document processing completion
     */
    @Transactional
    public void handleProcessingCompletion(String uploadId, int transactionCount, double confidence) {
        log.info("Handling processing completion for upload: {}", uploadId);

        DocumentUpload upload = documentUploadRepository.findByUploadId(uploadId)
                .orElseThrow(() -> new ValidationException("Upload not found"));

        upload.markAsCompleted(transactionCount, java.math.BigDecimal.valueOf(confidence));
        documentUploadRepository.save(upload);

        log.info("Processing completed for upload: {} with {} transactions", uploadId, transactionCount);
    }

    /**
     * Handle document processing failure
     */
    @Transactional
    public void handleProcessingFailure(String uploadId, String errorMessage) {
        log.error("Handling processing failure for upload: {} - {}", uploadId, errorMessage);

        DocumentUpload upload = documentUploadRepository.findByUploadId(uploadId)
                .orElseThrow(() -> new ValidationException("Upload not found"));

        upload.markAsFailed(errorMessage);
        documentUploadRepository.save(upload);
    }

    /**
     * Validate upload request
     */
    private void validateUploadRequest(DocumentUploadRequestDto request) {
        // Check file size
        long maxSizeBytes = maxFileSizeMB * 1024 * 1024;
        if (request.getFileSizeBytes() > maxSizeBytes) {
            throw new ValidationException("File size exceeds maximum allowed size of " + maxFileSizeMB + "MB");
        }

        // Validate file type
        if (!isValidFileType(request.getFileType())) {
            throw new ValidationException("Unsupported file type. Supported types: PDF, CSV, Excel");
        }

        // Validate file name
        if (!isValidFileName(request.getFileName())) {
            throw new ValidationException("Invalid file name");
        }
    }

    /**
     * Find or create financial account for document upload
     */
    private FinancialAccount findOrCreateFinancialAccount(User user, DocumentUploadRequestDto request) {
        // Try to find existing account
        return financialAccountRepository.findByUserIdAndProviderAndAccountName(
                user.getId(), "MTN", request.getAccountName())
                .orElseGet(() -> {
                    // Create new financial account for manual upload
                    FinancialAccount account = FinancialAccount.builder()
                            .user(user)
                            .accountType(FinancialAccount.AccountType.MTN_MOBILE_MONEY)
                            .accountName(request.getAccountName())
                            .provider("MTN")
                            .connectionStatus(FinancialAccount.ConnectionStatus.DISCONNECTED)
                            .build();
                    return financialAccountRepository.save(account);
                });
    }

    /**
     * Generate unique upload ID
     */
    private String generateUploadId() {
        return "upload_" + UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * Map DTO file type to entity file type
     */
    private DocumentUpload.FileType mapFileType(DocumentUploadRequestDto.FileType fileType) {
        return switch (fileType) {
            case PDF -> DocumentUpload.FileType.PDF;
            case CSV -> DocumentUpload.FileType.CSV;
            case EXCEL -> DocumentUpload.FileType.EXCEL;
        };
    }

    /**
     * Map DocumentUpload to status DTO
     */
    private DocumentProcessingStatusDto mapToStatusDto(DocumentUpload upload) {
        return DocumentProcessingStatusDto.builder()
                .uploadId(upload.getUploadId())
                .status(mapProcessingStatus(upload.getProcessingStatus()))
                .progress(upload.getProgressPercentage())
                .extractedTransactions(upload.getExtractedTransactionsCount())
                .errorMessage(upload.getErrorMessage())
                .processedAt(upload.getProcessingCompletedAt())
                .confidenceScore(upload.getConfidenceScore())
                .build();
    }

    /**
     * Map internal processing status to DTO status
     */
    private DocumentProcessingStatusDto.ProcessingStatus mapProcessingStatus(DocumentUpload.ProcessingStatus status) {
        return switch (status) {
            case UPLOADING -> DocumentProcessingStatusDto.ProcessingStatus.UPLOADING;
            case PROCESSING -> DocumentProcessingStatusDto.ProcessingStatus.PROCESSING;
            case COMPLETED -> DocumentProcessingStatusDto.ProcessingStatus.COMPLETED;
            case FAILED -> DocumentProcessingStatusDto.ProcessingStatus.FAILED;
        };
    }

    /**
     * Validate file type
     */
    private boolean isValidFileType(DocumentUploadRequestDto.FileType fileType) {
        return fileType != null;
    }

    /**
     * Validate file name
     */
    private boolean isValidFileName(String fileName) {
        return fileName != null && !fileName.trim().isEmpty() && 
               fileName.length() <= 255 && !fileName.contains("..");
    }
}
