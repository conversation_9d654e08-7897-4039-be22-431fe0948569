'use client';

import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Brain, 
  Check, 
  X, 
  Loader2, 
  Lightbulb,
  TrendingUp,
  AlertCircle
} from 'lucide-react';

interface AISuggestion {
  categoryId: string;
  categoryName: string;
  confidence: number;
  explanation: string;
  keywords?: string[];
  alternativeCategories?: Array<{
    categoryId: string;
    categoryName: string;
    confidence: number;
  }>;
}

interface AICategoryHelperProps {
  suggestion: AISuggestion | null;
  isLoading: boolean;
  onAccept: () => void;
  onReject: () => void;
  onShowSelector: () => void;
  className?: string;
}

export const AICategoryHelper: React.FC<AICategoryHelperProps> = ({
  suggestion,
  isLoading,
  onAccept,
  onReject,
  onShowSelector,
  className
}) => {
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-50 border-green-200';
    if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-red-600 bg-red-50 border-red-200';
  };

  const getConfidenceLabel = (confidence: number) => {
    if (confidence >= 0.9) return 'Very High';
    if (confidence >= 0.8) return 'High';
    if (confidence >= 0.6) return 'Medium';
    if (confidence >= 0.4) return 'Low';
    return 'Very Low';
  };

  const formatConfidence = (confidence: number) => {
    return `${Math.round(confidence * 100)}%`;
  };

  if (isLoading) {
    return (
      <Card className={`p-4 border-blue-200 bg-blue-50 ${className}`}>
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <Loader2 className="h-5 w-5 text-blue-600 animate-spin" />
          </div>
          <div className="flex-1">
            <h4 className="text-sm font-medium text-blue-900">
              AI is analyzing your transaction...
            </h4>
            <p className="text-sm text-blue-700 mt-1">
              Getting category suggestions based on the description
            </p>
          </div>
        </div>
      </Card>
    );
  }

  if (!suggestion) {
    return null;
  }

  return (
    <Card className={`p-4 border-purple-200 bg-purple-50 ${className}`}>
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center space-x-2">
          <Brain className="h-5 w-5 text-purple-600" />
          <h4 className="text-sm font-medium text-purple-900">
            AI Category Suggestion
          </h4>
          <Badge 
            variant="outline" 
            className={getConfidenceColor(suggestion.confidence)}
          >
            {getConfidenceLabel(suggestion.confidence)} ({formatConfidence(suggestion.confidence)})
          </Badge>
        </div>

        {/* Main Suggestion */}
        <div className="bg-white rounded-lg p-3 border border-purple-200">
          <div className="flex items-center justify-between mb-2">
            <h5 className="font-medium text-gray-900">{suggestion.categoryName}</h5>
            <div className="flex items-center space-x-1">
              <TrendingUp className="h-4 w-4 text-purple-600" />
              <span className="text-sm font-medium text-purple-600">
                {formatConfidence(suggestion.confidence)}
              </span>
            </div>
          </div>
          
          {suggestion.explanation && (
            <p className="text-sm text-gray-600 mb-3">{suggestion.explanation}</p>
          )}
          
          {suggestion.keywords && suggestion.keywords.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-3">
              {suggestion.keywords.map((keyword, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {keyword}
                </Badge>
              ))}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex items-center space-x-2">
            <Button
              size="sm"
              onClick={onAccept}
              className="flex items-center space-x-1 bg-green-600 hover:bg-green-700"
            >
              <Check className="h-4 w-4" />
              <span>Accept</span>
            </Button>
            
            <Button
              size="sm"
              variant="outline"
              onClick={onReject}
              className="flex items-center space-x-1"
            >
              <X className="h-4 w-4" />
              <span>Reject</span>
            </Button>
            
            <Button
              size="sm"
              variant="outline"
              onClick={onShowSelector}
              className="flex items-center space-x-1"
            >
              <Lightbulb className="h-4 w-4" />
              <span>Choose Different</span>
            </Button>
          </div>
        </div>

        {/* Alternative Suggestions */}
        {suggestion.alternativeCategories && suggestion.alternativeCategories.length > 0 && (
          <div className="space-y-2">
            <h6 className="text-xs font-medium text-gray-700 uppercase tracking-wide">
              Alternative Suggestions
            </h6>
            <div className="space-y-1">
              {suggestion.alternativeCategories.slice(0, 3).map((alt, index) => (
                <div 
                  key={index}
                  className="flex items-center justify-between p-2 bg-white rounded border border-gray-200 hover:border-purple-300 cursor-pointer transition-colors"
                  onClick={() => {
                    // Handle alternative selection
                    // This would need to be passed as a prop or handled differently
                  }}
                >
                  <span className="text-sm text-gray-700">{alt.categoryName}</span>
                  <Badge variant="outline" className="text-xs">
                    {formatConfidence(alt.confidence)}
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Confidence Warning */}
        {suggestion.confidence < 0.6 && (
          <div className="flex items-start space-x-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="font-medium text-yellow-800">Low Confidence Suggestion</p>
              <p className="text-yellow-700 mt-1">
                The AI is not very confident about this categorization. 
                You may want to review and select a different category.
              </p>
            </div>
          </div>
        )}

        {/* Tips */}
        <div className="text-xs text-gray-500 space-y-1">
          <p>💡 <strong>Tip:</strong> The more specific your description, the better the AI suggestions.</p>
          <p>🎯 <strong>Accuracy:</strong> AI suggestions improve over time based on your choices.</p>
        </div>
      </div>
    </Card>
  );
};
