import { useState, useCallback } from 'react';
import { apiClient } from '@/services/apiClient';

export interface AccountInfo {
  phoneNumber: string;
  accountName: string;
  balance?: number;
  currency?: string;
}

export interface MTNConnectionStatus {
  accountId: string;
  status: 'CONNECTING' | 'CONNECTED' | 'DISCONNECTED' | 'ERROR';
  lastSync?: string;
  errorMessage?: string;
  accountInfo?: AccountInfo;
}

export const useAccountConnection = (userId: string) => {
  const [accounts, setAccounts] = useState<MTNConnectionStatus[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const refreshAccounts = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiClient.get('/api/v1/financial-accounts');
      setAccounts(response.data);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to load accounts');
      console.error('Failed to load accounts:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const getAccountStatus = useCallback(async (accountId: string): Promise<MTNConnectionStatus | null> => {
    try {
      const response = await apiClient.get(`/api/v1/financial-accounts/${accountId}/status`);
      return response.data;
    } catch (err: any) {
      console.error('Failed to get account status:', err);
      return null;
    }
  }, []);

  const disconnectAccount = useCallback(async (accountId: string): Promise<boolean> => {
    setLoading(true);
    setError(null);
    
    try {
      await apiClient.delete(`/api/v1/financial-accounts/${accountId}/disconnect`);
      
      // Update local state
      setAccounts(prev => prev.filter(account => account.accountId !== accountId));
      
      return true;
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to disconnect account');
      console.error('Failed to disconnect account:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  const checkConnectionStatus = useCallback(async (): Promise<{ hasConnectedAccounts: boolean }> => {
    try {
      const response = await apiClient.get('/api/v1/financial-accounts/connected/status');
      return response.data;
    } catch (err: any) {
      console.error('Failed to check connection status:', err);
      return { hasConnectedAccounts: false };
    }
  }, []);

  const hasConnectedAccounts = accounts.some(account => account.status === 'CONNECTED');

  return {
    accounts,
    loading,
    error,
    hasConnectedAccounts,
    refreshAccounts,
    getAccountStatus,
    disconnectAccount,
    checkConnectionStatus
  };
};
