import { NextRequest, NextResponse } from 'next/server';

const AI_SERVICE_URL = process.env.AI_SERVICE_URL || 'http://localhost:8002';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.command || !body.user_id || !body.session_id) {
      return NextResponse.json(
        { error: 'Missing required fields: command, user_id, session_id' },
        { status: 400 }
      );
    }

    // Forward request to AI service
    const aiResponse = await fetch(`${AI_SERVICE_URL}/api/v1/process-command`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!aiResponse.ok) {
      const errorText = await aiResponse.text();
      console.error('AI service error:', errorText);
      
      // Return fallback response
      return NextResponse.json({
        intent: {
          name: 'UNCLEAR',
          confidence: 0.1,
          description: 'AI service temporarily unavailable'
        },
        entities: {},
        response: "I'm having trouble processing your request right now. Please try again in a moment.",
        requires_follow_up: false,
        suggestions: [
          "Show me recent transactions",
          "Create a new invoice",
          "Help me with something else"
        ],
        metadata: {
          fallback: true,
          ai_service_error: true
        },
        processing_time_ms: 0
      });
    }

    const aiResult = await aiResponse.json();
    return NextResponse.json(aiResult);

  } catch (error) {
    console.error('Error in AI proxy:', error);
    
    // Return fallback response on error
    return NextResponse.json({
      intent: {
        name: 'UNCLEAR',
        confidence: 0.0,
        description: 'Error processing request'
      },
      entities: {},
      response: "I'm experiencing technical difficulties. Please try again later.",
      requires_follow_up: false,
      suggestions: [
        "Show me recent transactions",
        "Create a new invoice",
        "Get help"
      ],
      metadata: {
        fallback: true,
        error: true
      },
      processing_time_ms: 0
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'AI Command Processing API',
    endpoints: {
      'POST /api/ai/process-command': 'Process conversational commands'
    }
  });
}
