package com.intellifin.service;

import com.intellifin.model.ConversationMessage;
import com.intellifin.model.User;
import com.intellifin.service.MessagingService;
import com.intellifin.service.MessageHandler;
import org.springframework.stereotype.Service;
import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Dead Letter Queue Handler for Story 2.2
 * Handles failed intent processing events and implements retry logic
 */
@Service
public class DeadLetterQueueHandler {

    private final MessagingService messagingService;
    
    // Dead letter queue topics
    private static final String INTENT_DLQ = "intent.dlq";
    private static final String ENTITY_DLQ = "entity.dlq";
    private static final String COMMAND_DLQ = "command.dlq";
    
    // Retry configuration
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long RETRY_DELAY_MS = 5000; // 5 seconds
    
    // Metrics
    private final AtomicInteger totalDlqMessages = new AtomicInteger(0);
    private final AtomicInteger retriedMessages = new AtomicInteger(0);
    private final AtomicInteger permanentFailures = new AtomicInteger(0);

    public DeadLetterQueueHandler(MessagingService messagingService) {
        this.messagingService = messagingService;
    }

    /**
     * Initialize DLQ subscriptions
     */
    @PostConstruct
    public void initializeDlqSubscriptions() {
        // Subscribe to dead letter queues
        // This is a simplified approach. In a real application, you'd have a more robust way to handle this.
    }

    /**
     * Handler for intent processing failures
     */
    private class IntentDlqHandler implements MessageHandler {
        @Override
        public void handle(ConversationMessage message) {
            try {
                totalDlqMessages.incrementAndGet();
                System.err.println("Processing intent DLQ message: " + message.toString());
                // Assuming no retry logic from message, move to permanent failure
                handlePermanentFailure(new DlqMessage(message, INTENT_DLQ, new RuntimeException("Max retries exceeded")), "intent");
            } catch (Exception e) {
                System.err.println("Error processing intent DLQ message: " + e.getMessage());
            }
        }
    }

    /**
     * Handler for entity extraction failures
     */
    private class EntityDlqHandler implements MessageHandler {
        @Override
        public void handle(ConversationMessage message) {
            try {
                totalDlqMessages.incrementAndGet();
                System.err.println("Processing entity DLQ message: " + message.toString());
                // Assuming no retry logic from message, move to permanent failure
                handlePermanentFailure(new DlqMessage(message, ENTITY_DLQ, new RuntimeException("Max retries exceeded")), "entity");
            } catch (Exception e) {
                System.err.println("Error processing entity DLQ message: " + e.getMessage());
            }
        }
    }

    /**
     * Handler for command processing failures
     */
    private class CommandDlqHandler implements MessageHandler {
        @Override
        public void handle(ConversationMessage message) {
            try {
                totalDlqMessages.incrementAndGet();
                System.err.println("Processing command DLQ message: " + message.toString());
                // Assuming no retry logic from message, move to permanent failure
                handlePermanentFailure(new DlqMessage(message, COMMAND_DLQ, new RuntimeException("Max retries exceeded")), "command");
            } catch (Exception e) {
                System.err.println("Error processing command DLQ message: " + e.getMessage());
            }
        }
    }

    /**
     * Retry intent processing
     */
    private void retryIntentProcessing(DlqMessage dlqMessage) {
        try {
            // Wait before retry
            Thread.sleep(RETRY_DELAY_MS);
            
            // Increment retry count
            dlqMessage.incrementRetryCount();
            retriedMessages.incrementAndGet();
            
            System.out.println("Retrying intent processing (attempt " + dlqMessage.getRetryCount() + ")");
            
            // Republish to original topic
            ConversationMessage originalMessage = (ConversationMessage) dlqMessage.getOriginalMessage();
            User user = originalMessage.getConversation().getUser();
            messagingService.sendToUser(user.getId(), "intent.recognized", dlqMessage.getOriginalMessage());
            
        } catch (Exception e) {
            System.err.println("Error retrying intent processing: " + e.getMessage());
            // Send back to DLQ
            ConversationMessage originalMessage = (ConversationMessage) dlqMessage.getOriginalMessage();
            User user = originalMessage.getConversation().getUser();
            messagingService.sendToUser(user.getId(), INTENT_DLQ, dlqMessage);
        }
    }

    /**
     * Retry entity processing
     */
    private void retryEntityProcessing(DlqMessage dlqMessage) {
        try {
            // Wait before retry
            Thread.sleep(RETRY_DELAY_MS);
            
            // Increment retry count
            dlqMessage.incrementRetryCount();
            retriedMessages.incrementAndGet();
            
            System.out.println("Retrying entity processing (attempt " + dlqMessage.getRetryCount() + ")");
            
            // Republish to original topic
            ConversationMessage originalMessage = (ConversationMessage) dlqMessage.getOriginalMessage();
            User user = originalMessage.getConversation().getUser();
            messagingService.sendToUser(user.getId(), "entity.extracted", dlqMessage.getOriginalMessage());
            
        } catch (Exception e) {
            System.err.println("Error retrying entity processing: " + e.getMessage());
            // Send back to DLQ
            ConversationMessage originalMessage = (ConversationMessage) dlqMessage.getOriginalMessage();
            User user = originalMessage.getConversation().getUser();
            messagingService.sendToUser(user.getId(), ENTITY_DLQ, dlqMessage);
        }
    }

    /**
     * Retry command processing
     */
    private void retryCommandProcessing(DlqMessage dlqMessage) {
        try {
            // Wait before retry
            Thread.sleep(RETRY_DELAY_MS);
            
            // Increment retry count
            dlqMessage.incrementRetryCount();
            retriedMessages.incrementAndGet();
            
            System.out.println("Retrying command processing (attempt " + dlqMessage.getRetryCount() + ")");
            
            // Republish to original topic
            ConversationMessage originalMessage = (ConversationMessage) dlqMessage.getOriginalMessage();
            User user = originalMessage.getConversation().getUser();
            messagingService.sendToUser(user.getId(), "command.processed", dlqMessage.getOriginalMessage());
            
        } catch (Exception e) {
            System.err.println("Error retrying command processing: " + e.getMessage());
            // Send back to DLQ
            ConversationMessage originalMessage = (ConversationMessage) dlqMessage.getOriginalMessage();
            User user = originalMessage.getConversation().getUser();
            messagingService.sendToUser(user.getId(), COMMAND_DLQ, dlqMessage);
        }
    }

    /**
     * Handle permanent failures (exceeded retry limit)
     */
    private void handlePermanentFailure(DlqMessage dlqMessage, String messageType) {
        permanentFailures.incrementAndGet();
        
        System.err.println("Permanent failure for " + messageType + " message after " + 
                         dlqMessage.getRetryCount() + " retries");
        
        // Log to persistent storage for manual investigation
        logPermanentFailure(dlqMessage, messageType);
        
        // Send alert to monitoring system
        sendFailureAlert(dlqMessage, messageType);
        
        // TODO: Implement manual review queue
    }

    /**
     * Log permanent failure for investigation
     */
    private void logPermanentFailure(DlqMessage dlqMessage, String messageType) {
        // TODO: Implement persistent logging
        System.err.println("PERMANENT FAILURE LOG: " + 
                         "Type: " + messageType + 
                         ", Message: " + dlqMessage.getOriginalMessage() + 
                         ", Error: " + dlqMessage.getLastError() + 
                         ", Timestamp: " + LocalDateTime.now());
    }

    /**
     * Send failure alert to monitoring system
     */
    private void sendFailureAlert(DlqMessage dlqMessage, String messageType) {
        // TODO: Implement alerting system
        System.err.println("ALERT: Permanent failure in " + messageType + " processing");
    }

    /**
     * Get DLQ metrics
     */
    public DlqMetrics getMetrics() {
        return new DlqMetrics(
            totalDlqMessages.get(),
            retriedMessages.get(),
            permanentFailures.get()
        );
    }
}

/**
 * Dead Letter Queue Message wrapper
 */
class DlqMessage {
    private Object originalMessage;
    private String originalTopic;
    private Exception lastError;
    private int retryCount;
    private LocalDateTime firstFailureTime;
    private LocalDateTime lastRetryTime;

    public DlqMessage(Object originalMessage, String originalTopic, Exception lastError) {
        this.originalMessage = originalMessage;
        this.originalTopic = originalTopic;
        this.lastError = lastError;
        this.retryCount = 0;
        this.firstFailureTime = LocalDateTime.now();
    }

    public void incrementRetryCount() {
        this.retryCount++;
        this.lastRetryTime = LocalDateTime.now();
    }

    // Getters
    public Object getOriginalMessage() { return originalMessage; }
    public String getOriginalTopic() { return originalTopic; }
    public Exception getLastError() { return lastError; }
    public int getRetryCount() { return retryCount; }
    public LocalDateTime getFirstFailureTime() { return firstFailureTime; }
    public LocalDateTime getLastRetryTime() { return lastRetryTime; }
}

/**
 * DLQ Metrics
 */
class DlqMetrics {
    private final int totalDlqMessages;
    private final int retriedMessages;
    private final int permanentFailures;

    public DlqMetrics(int totalDlqMessages, int retriedMessages, int permanentFailures) {
        this.totalDlqMessages = totalDlqMessages;
        this.retriedMessages = retriedMessages;
        this.permanentFailures = permanentFailures;
    }

    // Getters
    public int getTotalDlqMessages() { return totalDlqMessages; }
    public int getRetriedMessages() { return retriedMessages; }
    public int getPermanentFailures() { return permanentFailures; }
}
