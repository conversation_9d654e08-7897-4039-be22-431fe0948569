package com.intellifin.messaging;

import com.intellifin.messaging.events.TransactionReceivedEvent;
import com.intellifin.messaging.events.WebhookProcessedEvent;
import com.intellifin.model.WebhookEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * Service for publishing webhook-related messaging events
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WebhookMessagingService {

    private final StreamBridge streamBridge;

    /**
     * Publish transaction received event from webhook
     */
    public void publishTransactionReceived(WebhookEvent webhookEvent, UUID transactionId) {
        try {
            log.info("Publishing transaction received event for webhook: {} and transaction: {}", 
                    webhookEvent.getId(), transactionId);
            
            TransactionReceivedEvent event = TransactionReceivedEvent.builder()
                    .webhookEventId(webhookEvent.getId())
                    .transactionId(transactionId)
                    .externalTransactionId(webhookEvent.getTransactionId())
                    .source(webhookEvent.getSource())
                    .eventType(webhookEvent.getEventType())
                    .financialAccountId(webhookEvent.getFinancialAccount() != null ? 
                            webhookEvent.getFinancialAccount().getId() : null)
                    .userId(webhookEvent.getFinancialAccount() != null && 
                            webhookEvent.getFinancialAccount().getUser() != null ? 
                            webhookEvent.getFinancialAccount().getUser().getId() : null)
                    .timestamp(OffsetDateTime.now())
                    .build();
            
            Message<TransactionReceivedEvent> message = MessageBuilder
                    .withPayload(event)
                    .setHeader("eventType", "TransactionReceived")
                    .setHeader("webhookEventId", webhookEvent.getId().toString())
                    .setHeader("transactionId", transactionId.toString())
                    .setHeader("source", webhookEvent.getSource())
                    .build();

            boolean sent = streamBridge.send("transactionReceived-out-0", message);
            
            if (sent) {
                log.info("Successfully published transaction received event for webhook: {}", webhookEvent.getId());
            } else {
                log.error("Failed to publish transaction received event for webhook: {}", webhookEvent.getId());
            }
            
        } catch (Exception e) {
            log.error("Error publishing transaction received event for webhook: {}", webhookEvent.getId(), e);
        }
    }

    /**
     * Publish webhook processed event
     */
    public void publishWebhookProcessed(WebhookEvent webhookEvent, UUID transactionId, boolean success) {
        try {
            log.info("Publishing webhook processed event for webhook: {} with success: {}", 
                    webhookEvent.getId(), success);
            
            WebhookProcessedEvent event = WebhookProcessedEvent.builder()
                    .webhookEventId(webhookEvent.getId())
                    .transactionId(transactionId)
                    .externalTransactionId(webhookEvent.getTransactionId())
                    .source(webhookEvent.getSource())
                    .success(success)
                    .errorMessage(success ? null : webhookEvent.getErrorMessage())
                    .processingTimeMs(webhookEvent.getProcessedAt() != null && webhookEvent.getCreatedAt() != null ?
                            java.time.Duration.between(webhookEvent.getCreatedAt(), webhookEvent.getProcessedAt()).toMillis() : null)
                    .timestamp(OffsetDateTime.now())
                    .build();
            
            Message<WebhookProcessedEvent> message = MessageBuilder
                    .withPayload(event)
                    .setHeader("eventType", "WebhookProcessed")
                    .setHeader("webhookEventId", webhookEvent.getId().toString())
                    .setHeader("success", success)
                    .setHeader("source", webhookEvent.getSource())
                    .build();

            boolean sent = streamBridge.send("webhookProcessed-out-0", message);
            
            if (sent) {
                log.info("Successfully published webhook processed event for webhook: {}", webhookEvent.getId());
            } else {
                log.error("Failed to publish webhook processed event for webhook: {}", webhookEvent.getId());
            }
            
        } catch (Exception e) {
            log.error("Error publishing webhook processed event for webhook: {}", webhookEvent.getId(), e);
        }
    }

    /**
     * Publish webhook failed event
     */
    public void publishWebhookFailed(WebhookEvent webhookEvent, String errorMessage) {
        try {
            log.info("Publishing webhook failed event for webhook: {}", webhookEvent.getId());
            
            WebhookProcessedEvent event = WebhookProcessedEvent.builder()
                    .webhookEventId(webhookEvent.getId())
                    .externalTransactionId(webhookEvent.getTransactionId())
                    .source(webhookEvent.getSource())
                    .success(false)
                    .errorMessage(errorMessage)
                    .retryCount(webhookEvent.getRetryCount())
                    .canRetry(webhookEvent.canRetry())
                    .timestamp(OffsetDateTime.now())
                    .build();
            
            Message<WebhookProcessedEvent> message = MessageBuilder
                    .withPayload(event)
                    .setHeader("eventType", "WebhookFailed")
                    .setHeader("webhookEventId", webhookEvent.getId().toString())
                    .setHeader("success", false)
                    .setHeader("source", webhookEvent.getSource())
                    .setHeader("canRetry", webhookEvent.canRetry())
                    .build();

            boolean sent = streamBridge.send("webhookFailed-out-0", message);
            
            if (sent) {
                log.info("Successfully published webhook failed event for webhook: {}", webhookEvent.getId());
            } else {
                log.error("Failed to publish webhook failed event for webhook: {}", webhookEvent.getId());
            }
            
        } catch (Exception e) {
            log.error("Error publishing webhook failed event for webhook: {}", webhookEvent.getId(), e);
        }
    }
}
