import { Account } from '@intellifin/data-models';
import { apiClient } from './apiClient';

export const getAccounts = async (): Promise<Account[]> => {
  const response = await apiClient.get('/accounts');
  return response.data;
};

export const createAccount = async (account: Omit<Account, 'id' | 'currentBalance' | 'createdAt' | 'updatedAt'>): Promise<Account> => {
  const response = await apiClient.post('/accounts', account);
  return response.data;
};

export const updateAccount = async (id: string, account: Partial<Account>): Promise<Account> => {
  const response = await apiClient.put(`/accounts/${id}`, account);
  return response.data;
};

export const deleteAccount = async (id: string): Promise<void> => {
  await apiClient.delete(`/accounts/${id}`);
};
