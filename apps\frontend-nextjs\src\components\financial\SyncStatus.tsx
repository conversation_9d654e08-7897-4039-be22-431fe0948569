'use client';

import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert } from '@/components/ui/alert';
import { 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  Pause, 
  Play, 
  RefreshCw,
  Wifi,
  WifiOff,
  Activity,
  TrendingUp,
  AlertTriangle
} from 'lucide-react';

interface SyncStatusProps {
  accountId: string;
  accountName: string;
  provider: string;
}

interface SyncStatusData {
  accountId: string;
  lastSync: string | null;
  pendingTransactions: number;
  syncStatus: 'ACTIVE' | 'FAILED' | 'PAUSED' | 'DISABLED';
  errorMessage?: string;
  nextSync?: string;
  totalSyncedTransactions: number;
  failedTransactions: number;
  syncType: 'WEBHOOK' | 'POLLING' | 'MANUAL';
}

export const SyncStatus: React.FC<SyncStatusProps> = ({
  accountId,
  accountName,
  provider
}) => {
  const [syncStatus, setSyncStatus] = useState<SyncStatusData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const fetchSyncStatus = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/v1/sync/status?accountId=${accountId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch sync status');
      }
      
      const data = await response.json();
      setSyncStatus(data);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const handleTriggerSync = async () => {
    try {
      setIsRefreshing(true);
      
      const response = await fetch(`/api/v1/sync/trigger?accountId=${accountId}`, {
        method: 'POST'
      });
      
      if (!response.ok) {
        throw new Error('Failed to trigger sync');
      }
      
      await fetchSyncStatus();
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to trigger sync');
    } finally {
      setIsRefreshing(false);
    }
  };

  const handlePauseSync = async () => {
    try {
      const response = await fetch(`/api/v1/sync/pause?accountId=${accountId}`, {
        method: 'POST'
      });
      
      if (!response.ok) {
        throw new Error('Failed to pause sync');
      }
      
      await fetchSyncStatus();
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to pause sync');
    }
  };

  const handleResumeSync = async () => {
    try {
      const response = await fetch(`/api/v1/sync/resume?accountId=${accountId}`, {
        method: 'POST'
      });
      
      if (!response.ok) {
        throw new Error('Failed to resume sync');
      }
      
      await fetchSyncStatus();
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to resume sync');
    }
  };

  useEffect(() => {
    fetchSyncStatus();
    
    // Set up polling for sync status updates
    const interval = setInterval(fetchSyncStatus, 30000); // Poll every 30 seconds
    
    return () => clearInterval(interval);
  }, [accountId]);

  const getStatusIcon = () => {
    if (!syncStatus) return <Clock className="h-5 w-5 text-gray-400" />;
    
    switch (syncStatus.syncStatus) {
      case 'ACTIVE':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'FAILED':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      case 'PAUSED':
        return <Pause className="h-5 w-5 text-yellow-500" />;
      case 'DISABLED':
        return <WifiOff className="h-5 w-5 text-gray-400" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusBadge = () => {
    if (!syncStatus) return <Badge variant="outline">Loading...</Badge>;
    
    switch (syncStatus.syncStatus) {
      case 'ACTIVE':
        return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>;
      case 'FAILED':
        return <Badge variant="destructive">Failed</Badge>;
      case 'PAUSED':
        return <Badge variant="secondary">Paused</Badge>;
      case 'DISABLED':
        return <Badge variant="outline">Disabled</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const formatTimestamp = (timestamp: string | null) => {
    if (!timestamp) return 'Never';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minutes ago`;
    if (diffHours < 24) return `${diffHours} hours ago`;
    if (diffDays < 7) return `${diffDays} days ago`;
    
    return date.toLocaleDateString();
  };

  if (loading) {
    return (
      <Card className="p-4">
        <div className="flex items-center space-x-3">
          <div className="animate-spin">
            <RefreshCw className="h-5 w-5 text-gray-400" />
          </div>
          <span className="text-sm text-gray-600">Loading sync status...</span>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="p-4 border-red-200 bg-red-50">
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <div>
            <h4 className="font-medium">Sync Status Error</h4>
            <p className="text-sm text-gray-600 mt-1">{error}</p>
            <Button
              size="sm"
              variant="outline"
              onClick={fetchSyncStatus}
              className="mt-2"
            >
              Retry
            </Button>
          </div>
        </Alert>
      </Card>
    );
  }

  return (
    <Card className="p-4">
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {getStatusIcon()}
            <div>
              <h3 className="text-sm font-medium text-gray-900">
                {accountName} Sync Status
              </h3>
              <p className="text-xs text-gray-500">{provider}</p>
            </div>
          </div>
          {getStatusBadge()}
        </div>

        {/* Sync Details */}
        {syncStatus && (
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-500">Last Sync:</span>
              <p className="font-medium">{formatTimestamp(syncStatus.lastSync)}</p>
            </div>
            
            <div>
              <span className="text-gray-500">Sync Type:</span>
              <p className="font-medium capitalize">{syncStatus.syncType.toLowerCase()}</p>
            </div>
            
            <div>
              <span className="text-gray-500">Pending:</span>
              <p className="font-medium">{syncStatus.pendingTransactions} transactions</p>
            </div>
            
            <div>
              <span className="text-gray-500">Total Synced:</span>
              <p className="font-medium">{syncStatus.totalSyncedTransactions}</p>
            </div>
          </div>
        )}

        {/* Error Message */}
        {syncStatus?.errorMessage && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <div>
              <h4 className="font-medium">Sync Error</h4>
              <p className="text-sm text-gray-600 mt-1">{syncStatus.errorMessage}</p>
            </div>
          </Alert>
        )}

        {/* Actions */}
        <div className="flex items-center space-x-2 pt-2 border-t">
          <Button
            size="sm"
            variant="outline"
            onClick={handleTriggerSync}
            disabled={isRefreshing}
            className="flex items-center space-x-1"
          >
            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            <span>Sync Now</span>
          </Button>
          
          {syncStatus?.syncStatus === 'ACTIVE' ? (
            <Button
              size="sm"
              variant="outline"
              onClick={handlePauseSync}
              className="flex items-center space-x-1"
            >
              <Pause className="h-4 w-4" />
              <span>Pause</span>
            </Button>
          ) : (
            <Button
              size="sm"
              variant="outline"
              onClick={handleResumeSync}
              className="flex items-center space-x-1"
            >
              <Play className="h-4 w-4" />
              <span>Resume</span>
            </Button>
          )}
        </div>
      </div>
    </Card>
  );
};
