package com.intellifin.controller;

import com.intellifin.model.JournalEntry;
import com.intellifin.service.JournalService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

/**
 * REST controller for journal entry management
 */
@RestController
@RequestMapping("/api/v1/journal")
@RequiredArgsConstructor
@Slf4j
public class JournalController {

    private final JournalService journalService;

    /**
     * Create a new journal entry
     */
    @PostMapping("/entries")
    public ResponseEntity<JournalEntry> createJournalEntry(@Valid @RequestBody JournalEntry journalEntry) {
        try {
            log.info("Creating journal entry: {}", journalEntry.getDescription());
            JournalEntry createdEntry = journalService.createJournalEntry(journalEntry);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdEntry);
        } catch (Exception e) {
            log.error("Error creating journal entry", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }

    /**
     * Get journal entry by ID
     */
    @GetMapping("/entries/{id}")
    public ResponseEntity<JournalEntry> getJournalEntry(@PathVariable UUID id) {
        try {
            return journalService.getJournalEntryById(id)
                    .map(entry -> ResponseEntity.ok(entry))
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            log.error("Error retrieving journal entry: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get all journal entries for current user
     */
    @GetMapping("/entries")
    public ResponseEntity<List<JournalEntry>> getJournalEntries() {
        try {
            List<JournalEntry> entries = journalService.getJournalEntriesForCurrentUser();
            return ResponseEntity.ok(entries);
        } catch (Exception e) {
            log.error("Error retrieving journal entries", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get journal entries with pagination
     */
    @GetMapping("/entries/paged")
    public ResponseEntity<Page<JournalEntry>> getJournalEntriesPaged(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<JournalEntry> entries = journalService.getJournalEntriesForCurrentUser(pageable);
            return ResponseEntity.ok(entries);
        } catch (Exception e) {
            log.error("Error retrieving paged journal entries", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Update a journal entry (only if in DRAFT status)
     */
    @PutMapping("/entries/{id}")
    public ResponseEntity<JournalEntry> updateJournalEntry(
            @PathVariable UUID id,
            @Valid @RequestBody JournalEntry journalEntry) {
        try {
            log.info("Updating journal entry: {}", id);
            JournalEntry updatedEntry = journalService.updateJournalEntry(id, journalEntry);
            return ResponseEntity.ok(updatedEntry);
        } catch (IllegalStateException e) {
            log.warn("Cannot update journal entry: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        } catch (Exception e) {
            log.error("Error updating journal entry: {}", id, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }

    /**
     * Post a journal entry (change status from DRAFT to POSTED)
     */
    @PostMapping("/entries/{id}/post")
    public ResponseEntity<JournalEntry> postJournalEntry(@PathVariable UUID id) {
        try {
            log.info("Posting journal entry: {}", id);
            JournalEntry postedEntry = journalService.postJournalEntry(id);
            return ResponseEntity.ok(postedEntry);
        } catch (IllegalStateException e) {
            log.warn("Cannot post journal entry: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        } catch (Exception e) {
            log.error("Error posting journal entry: {}", id, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }

    /**
     * Reverse a journal entry
     */
    @PostMapping("/entries/{id}/reverse")
    public ResponseEntity<JournalEntry> reverseJournalEntry(
            @PathVariable UUID id,
            @RequestParam String reason) {
        try {
            log.info("Reversing journal entry: {} with reason: {}", id, reason);
            JournalEntry reversalEntry = journalService.reverseJournalEntry(id, reason);
            return ResponseEntity.ok(reversalEntry);
        } catch (IllegalStateException e) {
            log.warn("Cannot reverse journal entry: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        } catch (Exception e) {
            log.error("Error reversing journal entry: {}", id, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }

    /**
     * Delete a journal entry (only if in DRAFT status)
     */
    @DeleteMapping("/entries/{id}")
    public ResponseEntity<Void> deleteJournalEntry(@PathVariable UUID id) {
        try {
            log.info("Deleting journal entry: {}", id);
            journalService.deleteJournalEntry(id);
            return ResponseEntity.noContent().build();
        } catch (IllegalStateException e) {
            log.warn("Cannot delete journal entry: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        } catch (Exception e) {
            log.error("Error deleting journal entry: {}", id, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }

    /**
     * Calculate account balance
     */
    @GetMapping("/accounts/{accountId}/balance")
    public ResponseEntity<BigDecimal> getAccountBalance(@PathVariable UUID accountId) {
        try {
            BigDecimal balance = journalService.calculateAccountBalance(accountId);
            return ResponseEntity.ok(balance);
        } catch (Exception e) {
            log.error("Error calculating account balance for account: {}", accountId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Validate journal entry balance
     */
    @PostMapping("/entries/validate")
    public ResponseEntity<ValidationResponse> validateJournalEntry(@Valid @RequestBody JournalEntry journalEntry) {
        try {
            boolean isBalanced = journalEntry.isBalanced();
            BigDecimal totalDebits = journalEntry.getTotalDebits();
            BigDecimal totalCredits = journalEntry.getTotalCredits();
            
            ValidationResponse response = ValidationResponse.builder()
                    .isValid(isBalanced)
                    .isBalanced(isBalanced)
                    .totalDebits(totalDebits)
                    .totalCredits(totalCredits)
                    .difference(totalDebits.subtract(totalCredits))
                    .message(isBalanced ? "Journal entry is balanced" : "Journal entry is not balanced")
                    .build();
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error validating journal entry", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }

    /**
     * Response class for validation
     */
    public static class ValidationResponse {
        private boolean isValid;
        private boolean isBalanced;
        private BigDecimal totalDebits;
        private BigDecimal totalCredits;
        private BigDecimal difference;
        private String message;

        public static ValidationResponseBuilder builder() {
            return new ValidationResponseBuilder();
        }

        // Getters and setters
        public boolean isValid() { return isValid; }
        public void setValid(boolean valid) { isValid = valid; }
        public boolean isBalanced() { return isBalanced; }
        public void setBalanced(boolean balanced) { isBalanced = balanced; }
        public BigDecimal getTotalDebits() { return totalDebits; }
        public void setTotalDebits(BigDecimal totalDebits) { this.totalDebits = totalDebits; }
        public BigDecimal getTotalCredits() { return totalCredits; }
        public void setTotalCredits(BigDecimal totalCredits) { this.totalCredits = totalCredits; }
        public BigDecimal getDifference() { return difference; }
        public void setDifference(BigDecimal difference) { this.difference = difference; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }

        public static class ValidationResponseBuilder {
            private boolean isValid;
            private boolean isBalanced;
            private BigDecimal totalDebits;
            private BigDecimal totalCredits;
            private BigDecimal difference;
            private String message;

            public ValidationResponseBuilder isValid(boolean isValid) { this.isValid = isValid; return this; }
            public ValidationResponseBuilder isBalanced(boolean isBalanced) { this.isBalanced = isBalanced; return this; }
            public ValidationResponseBuilder totalDebits(BigDecimal totalDebits) { this.totalDebits = totalDebits; return this; }
            public ValidationResponseBuilder totalCredits(BigDecimal totalCredits) { this.totalCredits = totalCredits; return this; }
            public ValidationResponseBuilder difference(BigDecimal difference) { this.difference = difference; return this; }
            public ValidationResponseBuilder message(String message) { this.message = message; return this; }

            public ValidationResponse build() {
                ValidationResponse response = new ValidationResponse();
                response.setValid(isValid);
                response.setBalanced(isBalanced);
                response.setTotalDebits(totalDebits);
                response.setTotalCredits(totalCredits);
                response.setDifference(difference);
                response.setMessage(message);
                return response;
            }
        }
    }
}
