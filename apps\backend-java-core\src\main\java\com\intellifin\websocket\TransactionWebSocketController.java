package com.intellifin.websocket;

import com.intellifin.messaging.events.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.messaging.simp.annotation.SubscribeMapping;
import org.springframework.stereotype.Controller;

import java.security.Principal;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

/**
 * WebSocket controller for real-time transaction updates
 */
@Controller
@RequiredArgsConstructor
@Slf4j
public class TransactionWebSocketController {

    private final SimpMessagingTemplate messagingTemplate;

    /**
     * Handle subscription to transaction updates
     */
    @SubscribeMapping("/user/queue/transaction-updates")
    public void subscribeToTransactionUpdates(Principal principal) {
        log.info("User {} subscribed to transaction updates", principal.getName());
        
        // Send welcome message
        TransactionWebSocketMessage welcomeMessage = TransactionWebSocketMessage.builder()
                .messageId(UUID.randomUUID().toString())
                .userId(principal.getName())
                .eventType("SUBSCRIPTION_CONFIRMED")
                .data(Map.of(
                    "message", "Successfully subscribed to transaction updates",
                    "timestamp", LocalDateTime.now().toString()
                ))
                .timestamp(LocalDateTime.now().toString())
                .build();
        
        messagingTemplate.convertAndSendToUser(
            principal.getName(),
            "/queue/transaction-updates",
            welcomeMessage
        );
    }

    /**
     * Send transaction categorized event to user
     */
    public void sendTransactionCategorized(String userId, TransactionCategorizedEvent event) {
        log.info("Sending transaction categorized event to user: {}", userId);
        
        TransactionWebSocketMessage message = TransactionWebSocketMessage.builder()
                .messageId(UUID.randomUUID().toString())
                .userId(userId)
                .eventType("TRANSACTION_CATEGORIZED")
                .data(event)
                .timestamp(LocalDateTime.now().toString())
                .build();
        
        messagingTemplate.convertAndSendToUser(
            userId,
            "/queue/transaction-updates",
            message
        );
    }

    /**
     * Send categorization accepted event to user
     */
    public void sendCategorizationAccepted(String userId, CategorizationAcceptedEvent event) {
        log.info("Sending categorization accepted event to user: {}", userId);
        
        TransactionWebSocketMessage message = TransactionWebSocketMessage.builder()
                .messageId(UUID.randomUUID().toString())
                .userId(userId)
                .eventType("CATEGORIZATION_ACCEPTED")
                .data(event)
                .timestamp(LocalDateTime.now().toString())
                .build();
        
        messagingTemplate.convertAndSendToUser(
            userId,
            "/queue/transaction-updates",
            message
        );
    }

    /**
     * Send categorization rejected event to user
     */
    public void sendCategorizationRejected(String userId, CategorizationRejectedEvent event) {
        log.info("Sending categorization rejected event to user: {}", userId);
        
        TransactionWebSocketMessage message = TransactionWebSocketMessage.builder()
                .messageId(UUID.randomUUID().toString())
                .userId(userId)
                .eventType("CATEGORIZATION_REJECTED")
                .data(event)
                .timestamp(LocalDateTime.now().toString())
                .build();
        
        messagingTemplate.convertAndSendToUser(
            userId,
            "/queue/transaction-updates",
            message
        );
    }

    /**
     * Send bulk categorization completed event to user
     */
    public void sendBulkCategorizationCompleted(String userId, BulkCategorizationCompletedEvent event) {
        log.info("Sending bulk categorization completed event to user: {}", userId);
        
        TransactionWebSocketMessage message = TransactionWebSocketMessage.builder()
                .messageId(UUID.randomUUID().toString())
                .userId(userId)
                .eventType("BULK_CATEGORIZATION_COMPLETED")
                .data(event)
                .timestamp(LocalDateTime.now().toString())
                .build();
        
        messagingTemplate.convertAndSendToUser(
            userId,
            "/queue/transaction-updates",
            message
        );
    }

    /**
     * Handle ping messages for connection health check
     */
    @MessageMapping("/transaction-updates/ping")
    public void handlePing(@Payload Map<String, Object> payload, Principal principal) {
        log.debug("Received ping from user: {}", principal.getName());
        
        TransactionWebSocketMessage pongMessage = TransactionWebSocketMessage.builder()
                .messageId(UUID.randomUUID().toString())
                .userId(principal.getName())
                .eventType("PONG")
                .data(Map.of(
                    "timestamp", LocalDateTime.now().toString(),
                    "originalPayload", payload
                ))
                .timestamp(LocalDateTime.now().toString())
                .build();
        
        messagingTemplate.convertAndSendToUser(
            principal.getName(),
            "/queue/transaction-updates",
            pongMessage
        );
    }

    /**
     * WebSocket message wrapper for transaction updates
     */
    public static class TransactionWebSocketMessage {
        private String messageId;
        private String userId;
        private String eventType;
        private Object data;
        private String timestamp;

        // Builder pattern
        public static TransactionWebSocketMessageBuilder builder() {
            return new TransactionWebSocketMessageBuilder();
        }

        public static class TransactionWebSocketMessageBuilder {
            private String messageId;
            private String userId;
            private String eventType;
            private Object data;
            private String timestamp;

            public TransactionWebSocketMessageBuilder messageId(String messageId) {
                this.messageId = messageId;
                return this;
            }

            public TransactionWebSocketMessageBuilder userId(String userId) {
                this.userId = userId;
                return this;
            }

            public TransactionWebSocketMessageBuilder eventType(String eventType) {
                this.eventType = eventType;
                return this;
            }

            public TransactionWebSocketMessageBuilder data(Object data) {
                this.data = data;
                return this;
            }

            public TransactionWebSocketMessageBuilder timestamp(String timestamp) {
                this.timestamp = timestamp;
                return this;
            }

            public TransactionWebSocketMessage build() {
                TransactionWebSocketMessage message = new TransactionWebSocketMessage();
                message.messageId = this.messageId;
                message.userId = this.userId;
                message.eventType = this.eventType;
                message.data = this.data;
                message.timestamp = this.timestamp;
                return message;
            }
        }

        // Getters and setters
        public String getMessageId() { return messageId; }
        public void setMessageId(String messageId) { this.messageId = messageId; }

        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }

        public String getEventType() { return eventType; }
        public void setEventType(String eventType) { this.eventType = eventType; }

        public Object getData() { return data; }
        public void setData(Object data) { this.data = data; }

        public String getTimestamp() { return timestamp; }
        public void setTimestamp(String timestamp) { this.timestamp = timestamp; }
    }
}
