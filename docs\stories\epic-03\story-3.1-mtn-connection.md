# Story 3.1: Dual-Track Data Ingestion - MTN API & PDF Upload
## DETAILED READY-FOR-DEVELOPMENT SPECIFICATION

**Epic:** 3 - Seamless Data Ingestion (Pillar 2)
**Priority:** CRITICAL - Heart of Pillar 2
**Estimated Effort:** 6-7 developer days
**Dependencies:** Story 2.1 (Chart of Accounts) for transaction categorization

---

## User Story

**As a** user
**I want** to connect my mobile money account either through a direct API sync or by uploading a statement
**So that** I can get all my transactions into IntelliFin reliably, regardless of API availability

---

## Business Context

This story implements the core of **Pillar 2: Seamless Data Ingestion** by providing two equally important, first-class methods for getting transaction data into IntelliFin. Both the MTN API connection and PDF statement upload must be treated as primary features, not fallbacks, ensuring users can always access their financial data.

**Why This Matters:**
- Provides reliable data ingestion regardless of API availability or user preference
- Implements our StatementProcessing CrewAI crew for intelligent PDF parsing
- Ensures both paths result in identical downstream processing via event-driven architecture
- Creates foundation for expanding to other mobile money providers
- Enables users to start using IntelliFin immediately, even without API access

---

## Technical Implementation Details

### 1. Frontend Implementation - Dual-Track UI

#### Main Data Ingestion Interface:

**DataIngestionHub.tsx:**
```typescript
import React, { useState } from 'react';
import { MTNAPIConnection } from './MTNAPIConnection';
import { PDFStatementUpload } from './PDFStatementUpload';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export const DataIngestionHub: React.FC = () => {
  const [activeMethod, setActiveMethod] = useState<'api' | 'upload'>('api');

  return (
    <div className="data-ingestion-hub max-w-4xl mx-auto p-6">
      <div className="header mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Connect Your Mobile Money</h1>
        <p className="text-gray-600 mt-2">
          Choose how you'd like to sync your transactions with IntelliFin
        </p>
      </div>

      <Tabs value={activeMethod} onValueChange={(value) => setActiveMethod(value as 'api' | 'upload')}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="api" className="flex items-center gap-2">
            <span className="w-2 h-2 bg-green-500 rounded-full"></span>
            Live API Sync
          </TabsTrigger>
          <TabsTrigger value="upload" className="flex items-center gap-2">
            <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
            Upload Statement
          </TabsTrigger>
        </TabsList>

        <TabsContent value="api" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Connect MTN Mobile Money API</CardTitle>
              <p className="text-sm text-gray-600">
                Real-time transaction sync with automatic categorization
              </p>
            </CardHeader>
            <CardContent>
              <MTNAPIConnection />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="upload" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Upload Mobile Money Statement</CardTitle>
              <p className="text-sm text-gray-600">
                Upload your PDF statement for AI-powered transaction extraction
              </p>
            </CardHeader>
            <CardContent>
              <PDFStatementUpload />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
```

#### PDF Statement Upload Component:

**PDFStatementUpload.tsx:**
```typescript
import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Upload, FileText, CheckCircle, AlertCircle } from 'lucide-react';
import { documentService } from '@/services/documentService';

interface UploadStatus {
  status: 'idle' | 'uploading' | 'processing' | 'completed' | 'error';
  progress: number;
  message: string;
  extractedTransactions?: number;
}

export const PDFStatementUpload: React.FC = () => {
  const [uploadStatus, setUploadStatus] = useState<UploadStatus>({
    status: 'idle',
    progress: 0,
    message: ''
  });

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (!file) return;

    try {
      setUploadStatus({ status: 'uploading', progress: 10, message: 'Uploading statement...' });

      // Initiate upload
      const uploadResponse = await documentService.initiateUpload({
        fileName: file.name,
        fileType: 'PDF',
        accountType: 'MTN_MOBILE_MONEY'
      });

      // Upload file to signed URL
      await documentService.uploadFile(uploadResponse.uploadUrl, file);

      setUploadStatus({ status: 'processing', progress: 50, message: 'AI is analyzing your statement...' });

      // Poll for processing status
      const result = await documentService.pollProcessingStatus(uploadResponse.uploadId);

      if (result.status === 'COMPLETED') {
        setUploadStatus({
          status: 'completed',
          progress: 100,
          message: `Successfully extracted ${result.extractedTransactions} transactions`,
          extractedTransactions: result.extractedTransactions
        });
      } else {
        throw new Error(result.errorMessage || 'Processing failed');
      }
    } catch (error) {
      setUploadStatus({
        status: 'error',
        progress: 0,
        message: error instanceof Error ? error.message : 'Upload failed'
      });
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: { 'application/pdf': ['.pdf'] },
    maxFiles: 1,
    disabled: uploadStatus.status === 'uploading' || uploadStatus.status === 'processing'
  });

  return (
    <div className="pdf-upload-container">
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
          ${isDragActive ? 'border-blue-400 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}
          ${uploadStatus.status === 'uploading' || uploadStatus.status === 'processing' ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        <input {...getInputProps()} />

        {uploadStatus.status === 'idle' && (
          <>
            <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p className="text-lg font-medium text-gray-900 mb-2">
              {isDragActive ? 'Drop your statement here' : 'Upload Mobile Money Statement'}
            </p>
            <p className="text-sm text-gray-600 mb-4">
              Drag and drop your PDF statement, or click to browse
            </p>
            <Button variant="outline">Choose File</Button>
          </>
        )}

        {(uploadStatus.status === 'uploading' || uploadStatus.status === 'processing') && (
          <>
            <FileText className="mx-auto h-12 w-12 text-blue-500 mb-4" />
            <p className="text-lg font-medium text-gray-900 mb-4">{uploadStatus.message}</p>
            <Progress value={uploadStatus.progress} className="w-full max-w-md mx-auto" />
          </>
        )}

        {uploadStatus.status === 'completed' && (
          <>
            <CheckCircle className="mx-auto h-12 w-12 text-green-500 mb-4" />
            <p className="text-lg font-medium text-green-900 mb-2">Statement Processed Successfully!</p>
            <p className="text-sm text-gray-600">{uploadStatus.message}</p>
          </>
        )}

        {uploadStatus.status === 'error' && (
          <>
            <AlertCircle className="mx-auto h-12 w-12 text-red-500 mb-4" />
            <Alert className="mt-4">
              <AlertDescription>{uploadStatus.message}</AlertDescription>
            </Alert>
          </>
        )}
      </div>

      {uploadStatus.status === 'completed' && (
        <div className="mt-6 p-4 bg-green-50 rounded-lg">
          <h3 className="font-medium text-green-900 mb-2">What happens next?</h3>
          <ul className="text-sm text-green-800 space-y-1">
            <li>• Your transactions are now available for review and categorization</li>
            <li>• AI has pre-categorized transactions based on Zambian business patterns</li>
            <li>• You can review and confirm categories to improve future accuracy</li>
          </ul>
        </div>
      )}
    </div>
  );
};
```

### 2. Backend Implementation - Event-Driven Architecture

#### Document Service with Event Publishing:

**DocumentProcessingService.java:**
```java
@Service
@Transactional
public class DocumentProcessingService {

    private final DocumentUploadRepository documentUploadRepository;
    private final FileStorageService fileStorageService;
    private final ApplicationEventPublisher eventPublisher;
    private final EncryptionService encryptionService;

    public DocumentProcessingService(DocumentUploadRepository documentUploadRepository,
                                   FileStorageService fileStorageService,
                                   ApplicationEventPublisher eventPublisher,
                                   EncryptionService encryptionService) {
        this.documentUploadRepository = documentUploadRepository;
        this.fileStorageService = fileStorageService;
        this.eventPublisher = eventPublisher;
        this.encryptionService = encryptionService;
    }

    public DocumentUploadResponse initiateUpload(DocumentUploadRequest request, User user) {
        // Create upload record
        DocumentUpload upload = new DocumentUpload();
        upload.setUserId(user.getId());
        upload.setFileName(request.getFileName());
        upload.setFileType(DocumentType.valueOf(request.getFileType()));
        upload.setAccountType(AccountType.valueOf(request.getAccountType()));
        upload.setStatus(DocumentStatus.INITIATED);
        upload.setCreatedAt(LocalDateTime.now());

        DocumentUpload savedUpload = documentUploadRepository.save(upload);

        // Generate signed upload URL
        String uploadUrl = fileStorageService.generateSignedUploadUrl(
            savedUpload.getId().toString(),
            request.getFileName(),
            Duration.ofHours(1)
        );

        // Publish event for audit trail
        eventPublisher.publishEvent(new DocumentUploadInitiatedEvent(
            savedUpload.getId(),
            user.getId(),
            request.getFileName(),
            request.getFileType()
        ));

        return new DocumentUploadResponse(
            savedUpload.getId().toString(),
            uploadUrl,
            LocalDateTime.now().plus(Duration.ofHours(1)).toString()
        );
    }

    @EventListener
    @Async
    public void handleFileUploaded(FileUploadedEvent event) {
        try {
            DocumentUpload upload = documentUploadRepository.findById(Long.valueOf(event.getUploadId()))
                .orElseThrow(() -> new EntityNotFoundException("Upload not found"));

            upload.setStatus(DocumentStatus.UPLOADED);
            upload.setFileSize(event.getFileSize());
            upload.setUploadedAt(LocalDateTime.now());
            documentUploadRepository.save(upload);

            // Publish event to trigger AI processing
            eventPublisher.publishEvent(new DocumentUploadedEvent(
                upload.getId().toString(),
                upload.getUserId(),
                upload.getFileName(),
                upload.getFileType().toString(),
                event.getFileUrl()
            ));

        } catch (Exception e) {
            log.error("Failed to handle file uploaded event: {}", e.getMessage(), e);
        }
    }

    @EventListener
    @Async
    public void handleDocumentProcessingCompleted(DocumentProcessingCompletedEvent event) {
        try {
            DocumentUpload upload = documentUploadRepository.findById(Long.valueOf(event.getUploadId()))
                .orElseThrow(() -> new EntityNotFoundException("Upload not found"));

            upload.setStatus(DocumentStatus.COMPLETED);
            upload.setExtractedTransactions(event.getExtractedTransactions().size());
            upload.setProcessedAt(LocalDateTime.now());
            upload.setConfidenceScore(event.getConfidenceScore());
            documentUploadRepository.save(upload);

            // Publish individual transaction events
            for (ExtractedTransaction transaction : event.getExtractedTransactions()) {
                eventPublisher.publishEvent(new RawTransactionReceivedEvent(
                    transaction.getExternalId(),
                    upload.getUserId(),
                    transaction.getAmount(),
                    transaction.getDescription(),
                    transaction.getTransactionDate(),
                    transaction.getTransactionType(),
                    "PDF_UPLOAD", // source
                    upload.getId().toString() // sourceReference
                ));
            }

        } catch (Exception e) {
            log.error("Failed to handle document processing completed event: {}", e.getMessage(), e);
        }
    }

    public DocumentProcessingStatus getProcessingStatus(String uploadId) {
        DocumentUpload upload = documentUploadRepository.findById(Long.valueOf(uploadId))
            .orElseThrow(() -> new EntityNotFoundException("Upload not found"));

        return DocumentProcessingStatus.builder()
            .uploadId(uploadId)
            .status(upload.getStatus())
            .progress(calculateProgress(upload.getStatus()))
            .extractedTransactions(upload.getExtractedTransactions())
            .errorMessage(upload.getErrorMessage())
            .processedAt(upload.getProcessedAt())
            .build();
    }

    private int calculateProgress(DocumentStatus status) {
        return switch (status) {
            case INITIATED -> 10;
            case UPLOADED -> 30;
            case PROCESSING -> 70;
            case COMPLETED -> 100;
            case FAILED -> 0;
        };
    }
}
```

#### MTN API Integration Service:

**MTNIntegrationService.java:**
```java
@Service
public class MTNIntegrationService {

    private final MTNApiClient mtnApiClient;
    private final FinancialAccountRepository accountRepository;
    private final ApplicationEventPublisher eventPublisher;
    private final EncryptionService encryptionService;

    public MTNIntegrationService(MTNApiClient mtnApiClient,
                               FinancialAccountRepository accountRepository,
                               ApplicationEventPublisher eventPublisher,
                               EncryptionService encryptionService) {
        this.mtnApiClient = mtnApiClient;
        this.accountRepository = accountRepository;
        this.eventPublisher = eventPublisher;
        this.encryptionService = encryptionService;
    }

    public MTNOAuthResponse initiateConnection(MTNConnectionRequest request, User user) {
        // Create financial account record
        FinancialAccount account = new FinancialAccount();
        account.setUserId(user.getId());
        account.setAccountName(request.getAccountName());
        account.setAccountType(AccountType.MTN_MOBILE_MONEY);
        account.setPhoneNumber(encryptionService.encrypt(request.getPhoneNumber()));
        account.setStatus(ConnectionStatus.CONNECTING);
        account.setCreatedAt(LocalDateTime.now());

        FinancialAccount savedAccount = accountRepository.save(account);

        // Initiate OAuth flow with MTN
        MTNOAuthResponse oauthResponse = mtnApiClient.initiateOAuth(
            request.getPhoneNumber(),
            generateCallbackUrl(savedAccount.getId())
        );

        // Store OAuth state
        savedAccount.setOauthState(oauthResponse.getState());
        accountRepository.save(savedAccount);

        return oauthResponse;
    }

    public void handleOAuthCallback(MTNConnectionCallback callback) {
        FinancialAccount account = accountRepository.findByOauthState(callback.getState())
            .orElseThrow(() -> new BusinessException("Invalid OAuth state"));

        try {
            if (callback.getError() != null) {
                account.setStatus(ConnectionStatus.ERROR);
                account.setErrorMessage(callback.getError());
                accountRepository.save(account);
                return;
            }

            // Exchange code for tokens
            MTNTokenResponse tokens = mtnApiClient.exchangeCodeForTokens(
                callback.getCode(),
                generateCallbackUrl(account.getId())
            );

            // Store encrypted tokens
            account.setAccessToken(encryptionService.encrypt(tokens.getAccessToken()));
            account.setRefreshToken(encryptionService.encrypt(tokens.getRefreshToken()));
            account.setTokenExpiresAt(tokens.getExpiresAt());
            account.setStatus(ConnectionStatus.CONNECTED);
            account.setConnectedAt(LocalDateTime.now());
            accountRepository.save(account);

            // Start initial transaction sync
            syncTransactions(account);

        } catch (Exception e) {
            account.setStatus(ConnectionStatus.ERROR);
            account.setErrorMessage("Connection failed: " + e.getMessage());
            accountRepository.save(account);
            log.error("MTN OAuth callback failed for account {}: {}", account.getId(), e.getMessage(), e);
        }
    }

    @Scheduled(fixedRate = 300000) // Every 5 minutes
    public void syncAllConnectedAccounts() {
        List<FinancialAccount> connectedAccounts = accountRepository.findByStatusAndAccountType(
            ConnectionStatus.CONNECTED,
            AccountType.MTN_MOBILE_MONEY
        );

        for (FinancialAccount account : connectedAccounts) {
            try {
                syncTransactions(account);
            } catch (Exception e) {
                log.error("Failed to sync transactions for account {}: {}", account.getId(), e.getMessage(), e);
            }
        }
    }

    private void syncTransactions(FinancialAccount account) {
        try {
            String accessToken = encryptionService.decrypt(account.getAccessToken());
            LocalDateTime lastSync = account.getLastSyncAt() != null ?
                account.getLastSyncAt() : account.getConnectedAt().minusDays(30);

            List<MTNTransaction> transactions = mtnApiClient.getTransactions(
                accessToken,
                lastSync,
                LocalDateTime.now()
            );

            // Publish events for each new transaction
            for (MTNTransaction transaction : transactions) {
                eventPublisher.publishEvent(new RawTransactionReceivedEvent(
                    transaction.getTransactionId(),
                    account.getUserId(),
                    transaction.getAmount(),
                    transaction.getDescription(),
                    transaction.getTransactionDate(),
                    transaction.getType(),
                    "MTN_API", // source
                    account.getId().toString() // sourceReference
                ));
            }

            account.setLastSyncAt(LocalDateTime.now());
            account.setLastSyncTransactionCount(transactions.size());
            accountRepository.save(account);

        } catch (Exception e) {
            account.setStatus(ConnectionStatus.ERROR);
            account.setErrorMessage("Sync failed: " + e.getMessage());
            accountRepository.save(account);
            throw e;
        }
    }

    private String generateCallbackUrl(Long accountId) {
        return String.format("%s/api/v1/financial-accounts/mtn/callback?accountId=%d",
                           applicationProperties.getBaseUrl(), accountId);
    }
}
```

### 3. CrewAI Implementation - StatementProcessing Crew

#### Python AI Service Implementation:

**statement_processing_crew.py:**
```python
from crewai import Agent, Task, Crew
from crewai.tools import FileReadTool
from typing import List, Dict, Any
import json
import logging
from datetime import datetime
from dataclasses import dataclass

@dataclass
class ExtractedTransaction:
    external_id: str
    amount: float
    description: str
    transaction_date: datetime
    transaction_type: str
    balance_after: float = None
    reference_number: str = None

class StatementProcessingCrew:
    """
    CrewAI crew for processing uploaded PDF mobile money statements.
    Implements the Statement Processing workflow from the master blueprint.
    """

    def __init__(self):
        self.file_read_tool = FileReadTool()
        self.logger = logging.getLogger(__name__)

    def create_agents(self):
        """Create the three specialized agents for statement processing."""

        # Agent 1: RawTextAnalyst
        raw_text_analyst = Agent(
            role="Zambian Mobile Money Statement Analyst",
            goal="To read the raw text from a document and identify the core patterns, blocks of text, and key sections (summary, details)",
            backstory="""You are an expert in analyzing Zambian mobile money statements, particularly MTN Mobile Money, Airtel Money, and Zamtel Kwacha statements. You understand the typical format, layout, and language patterns used in these statements. You can identify transaction blocks, summary sections, balance information, and distinguish between different types of transactions like deposits, withdrawals, transfers, and payments.""",
            tools=[self.file_read_tool],
            verbose=True,
            allow_delegation=False
        )

        # Agent 2: DataStructuringSpecialist
        data_structuring_specialist = Agent(
            role="Financial Data Formatting Expert",
            goal="To transform the semi-structured text identified by the Analyst into a perfectly structured JSON object matching our Transaction data model",
            backstory="""You are a specialist in converting unstructured financial text into clean, structured data. You understand transaction patterns, can parse dates in various formats (DD/MM/YYYY, DD-MM-YYYY, etc.), handle currency amounts with or without currency symbols, and can standardize transaction descriptions. You know how to extract transaction IDs, reference numbers, and categorize transaction types (DEBIT, CREDIT, TRANSFER, etc.).""",
            verbose=True,
            allow_delegation=False
        )

        # Agent 3: ValidationAccountant
        validation_accountant = Agent(
            role="Digital Accountant & Auditor",
            goal="To validate the structured data against the statement's own summary totals and flag any discrepancies",
            backstory="""You are a meticulous digital accountant who specializes in validating financial data for accuracy. You can perform mathematical validation, check that opening balance + transactions = closing balance, verify that debit and credit totals match statement summaries, and identify any missing or duplicate transactions. You ensure data integrity before the transactions are imported into the accounting system.""",
            verbose=True,
            allow_delegation=False
        )

        return raw_text_analyst, data_structuring_specialist, validation_accountant

    def create_tasks(self, file_path: str, upload_id: str):
        """Create the sequential tasks for statement processing."""

        raw_text_analyst, data_structuring_specialist, validation_accountant = self.create_agents()

        # Task 1: Analyze raw text
        analyze_task = Task(
            description=f"""
            Read and analyze the mobile money statement file at {file_path}.

            Your task is to:
            1. Read the entire document content
            2. Identify the statement header with account information
            3. Locate the summary section with opening/closing balances and totals
            4. Find the detailed transaction section
            5. Identify the structure and patterns of individual transactions
            6. Note any special formatting, currency symbols, or date formats used

            Provide a detailed analysis of the document structure, highlighting:
            - Account holder information
            - Statement period
            - Summary totals (opening balance, total debits, total credits, closing balance)
            - Transaction section layout and patterns
            - Any irregularities or special formatting
            """,
            agent=raw_text_analyst,
            expected_output="A comprehensive analysis of the statement structure and content patterns"
        )

        # Task 2: Structure the data
        structure_task = Task(
            description=f"""
            Based on the analysis from the RawTextAnalyst, extract and structure all transaction data into a clean JSON format.

            For each transaction, extract:
            - Transaction ID or reference number
            - Date (convert to YYYY-MM-DD format)
            - Description/narrative
            - Amount (as positive number)
            - Transaction type (DEBIT, CREDIT, TRANSFER, PAYMENT, etc.)
            - Balance after transaction (if available)
            - Any additional reference numbers

            Also extract summary information:
            - Account number
            - Statement period (start and end dates)
            - Opening balance
            - Closing balance
            - Total debits
            - Total credits

            Return the data as a structured JSON object with 'summary' and 'transactions' sections.
            """,
            agent=data_structuring_specialist,
            expected_output="A perfectly structured JSON object containing all extracted transaction and summary data",
            context=[analyze_task]
        )

        # Task 3: Validate the data
        validate_task = Task(
            description=f"""
            Validate the structured transaction data for mathematical accuracy and completeness.

            Perform these validations:
            1. Verify that opening balance + total credits - total debits = closing balance
            2. Check that the sum of individual transaction amounts matches the summary totals
            3. Ensure all transactions have required fields (date, amount, description)
            4. Validate that dates are within the statement period
            5. Check for duplicate transactions (same amount, date, and description)
            6. Verify that transaction types are correctly identified

            If discrepancies are found:
            - Document the specific issues
            - Suggest corrections where possible
            - Flag the confidence level of the extraction

            Provide a validation report with:
            - Overall validation status (PASSED, FAILED, WARNING)
            - List of any issues found
            - Confidence score (0-100)
            - Recommended actions
            """,
            agent=validation_accountant,
            expected_output="A comprehensive validation report with confidence score and any identified issues",
            context=[analyze_task, structure_task]
        )

        return [analyze_task, structure_task, validate_task]

    def process_statement(self, file_path: str, upload_id: str) -> Dict[str, Any]:
        """
        Process a mobile money statement using the CrewAI crew.

        Args:
            file_path: Path to the uploaded statement file
            upload_id: Unique identifier for this upload

        Returns:
            Dict containing extracted transactions and validation results
        """
        try:
            self.logger.info(f"Starting statement processing for upload {upload_id}")

            # Create the crew
            raw_text_analyst, data_structuring_specialist, validation_accountant = self.create_agents()
            tasks = self.create_tasks(file_path, upload_id)

            crew = Crew(
                agents=[raw_text_analyst, data_structuring_specialist, validation_accountant],
                tasks=tasks,
                verbose=True,
                process="sequential"  # Tasks execute in order: Analyst -> Specialist -> Accountant
            )

            # Execute the crew
            result = crew.kickoff()

            # Parse the final validation result
            validation_result = self._parse_validation_result(result)

            self.logger.info(f"Statement processing completed for upload {upload_id} with confidence {validation_result.get('confidence_score', 0)}")

            return validation_result

        except Exception as e:
            self.logger.error(f"Statement processing failed for upload {upload_id}: {str(e)}")
            raise

    def _parse_validation_result(self, crew_result: str) -> Dict[str, Any]:
        """Parse the crew result into a structured format."""
        try:
            # The validation_accountant should return structured data
            # This is a simplified parser - in practice, you'd have more robust parsing

            # Extract JSON from the result if present
            import re
            json_match = re.search(r'\{.*\}', crew_result, re.DOTALL)
            if json_match:
                data = json.loads(json_match.group())
            else:
                # Fallback parsing logic
                data = {
                    "status": "COMPLETED",
                    "confidence_score": 85,
                    "transactions": [],
                    "summary": {},
                    "validation_issues": []
                }

            return data

        except Exception as e:
            self.logger.error(f"Failed to parse crew result: {str(e)}")
            return {
                "status": "FAILED",
                "confidence_score": 0,
                "error": str(e),
                "transactions": [],
                "summary": {},
                "validation_issues": ["Failed to parse crew result"]
            }
```

#### Event Handler for Document Processing:

**document_event_handler.py:**
```python
import asyncio
import logging
from typing import Dict, Any
from datetime import datetime

from .statement_processing_crew import StatementProcessingCrew
from .messaging_service import MessagingService
from .file_storage_service import FileStorageService

class DocumentEventHandler:
    """
    Handles document processing events and coordinates with CrewAI.
    """

    def __init__(self, messaging_service: MessagingService, file_storage_service: FileStorageService):
        self.messaging_service = messaging_service
        self.file_storage_service = file_storage_service
        self.statement_crew = StatementProcessingCrew()
        self.logger = logging.getLogger(__name__)

    async def handle_document_uploaded_event(self, event: Dict[str, Any]):
        """
        Handle DocumentUploadedEvent by triggering CrewAI processing.
        """
        try:
            upload_id = event['uploadId']
            user_id = event['userId']
            file_name = event['fileName']
            file_url = event['fileUrl']

            self.logger.info(f"Processing document upload {upload_id} for user {user_id}")

            # Download file to local processing directory
            local_file_path = await self.file_storage_service.download_file(file_url, upload_id)

            # Update status to processing
            await self.messaging_service.publish_event('document.events', {
                'eventType': 'DocumentProcessingStarted',
                'uploadId': upload_id,
                'userId': user_id,
                'timestamp': datetime.utcnow().isoformat()
            })

            # Process with CrewAI
            processing_result = await asyncio.get_event_loop().run_in_executor(
                None,
                self.statement_crew.process_statement,
                local_file_path,
                upload_id
            )

            if processing_result['status'] == 'COMPLETED':
                # Publish success event
                await self.messaging_service.publish_event('document.events', {
                    'eventType': 'DocumentProcessingCompleted',
                    'uploadId': upload_id,
                    'userId': user_id,
                    'extractedTransactions': processing_result['transactions'],
                    'confidenceScore': processing_result['confidence_score'],
                    'summary': processing_result['summary'],
                    'validationIssues': processing_result.get('validation_issues', []),
                    'timestamp': datetime.utcnow().isoformat()
                })
            else:
                # Publish failure event
                await self.messaging_service.publish_event('document.events', {
                    'eventType': 'DocumentProcessingFailed',
                    'uploadId': upload_id,
                    'userId': user_id,
                    'error': processing_result.get('error', 'Processing failed'),
                    'timestamp': datetime.utcnow().isoformat()
                })

            # Clean up local file
            await self.file_storage_service.cleanup_local_file(local_file_path)

        except Exception as e:
            self.logger.error(f"Failed to process document upload {upload_id}: {str(e)}")

            # Publish failure event
            await self.messaging_service.publish_event('document.events', {
                'eventType': 'DocumentProcessingFailed',
                'uploadId': upload_id,
                'userId': user_id,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            })
```

---

## Acceptance Criteria

### Core Dual-Track Functionality:
- [ ] **Clean UI Choice**: Users see clear, equal options for "Live API Sync" and "Upload Statement"
- [ ] **MTN API Integration**: Secure OAuth flow with real-time transaction sync every 5 minutes
- [ ] **PDF Statement Upload**: Drag-and-drop interface with AI-powered extraction via CrewAI
- [ ] **Identical Event Output**: Both paths publish `RawTransactionReceivedEvent` with same structure
- [ ] **Real-Time Status**: Users see live progress for both API connection and PDF processing
- [ ] **Error Handling**: Graceful failure handling with clear user feedback and retry options

### CrewAI StatementProcessing Crew:
- [ ] **RawTextAnalyst Agent**: Successfully identifies statement structure and transaction patterns
- [ ] **DataStructuringSpecialist Agent**: Converts text to structured JSON matching Transaction model
- [ ] **ValidationAccountant Agent**: Validates mathematical accuracy and flags discrepancies
- [ ] **Sequential Processing**: Agents execute in order with proper context passing
- [ ] **Confidence Scoring**: Processing results include confidence score (0-100)
- [ ] **Zambian Context**: Handles MTN, Airtel, and Zamtel statement formats correctly

### Event-Driven Architecture:
- [ ] **Document Events**: `DocumentUploadedEvent` → CrewAI processing → `DocumentProcessingCompletedEvent`
- [ ] **Transaction Events**: Both API and PDF paths publish identical `RawTransactionReceivedEvent`
- [ ] **Messaging Abstraction**: Events work consistently across RabbitMQ (local) and Azure Service Bus (prod)
- [ ] **Dead Letter Handling**: Failed processing events routed to dead letter queues
- [ ] **Event Ordering**: Transaction events maintain chronological order

### Data Quality & Security:
- [ ] **High Extraction Accuracy**: PDF processing achieves >90% accuracy on standard MTN statements
- [ ] **Data Validation**: Mathematical validation ensures opening + credits - debits = closing balance
- [ ] **Secure Storage**: OAuth tokens and uploaded files encrypted at rest
- [ ] **Audit Trail**: Complete audit trail for all API connections and document uploads
- [ ] **User Data Isolation**: Each user's data completely isolated from others

---

## API Contracts

### Document Upload API:

```typescript
interface DocumentUploadRequest {
  fileName: string;
  fileType: 'PDF';
  accountType: 'MTN_MOBILE_MONEY' | 'AIRTEL_MONEY' | 'ZAMTEL_KWACHA';
  accountName?: string;
}

interface DocumentUploadResponse {
  uploadId: string;
  uploadUrl: string; // Signed URL for direct upload
  expiresAt: string; // ISO datetime
}

interface DocumentProcessingStatus {
  uploadId: string;
  status: 'INITIATED' | 'UPLOADED' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  progress: number; // 0-100
  extractedTransactions?: number;
  confidenceScore?: number; // 0-100
  errorMessage?: string;
  processedAt?: string;
  validationIssues?: string[];
}

interface DocumentUploadAPI {
  POST /api/v1/data-ingestion/upload/initiate: {
    body: DocumentUploadRequest;
    response: DocumentUploadResponse;
    errors: {
      400: "Invalid request",
      413: "File too large (max 10MB)",
      415: "Unsupported file type"
    };
  };

  GET /api/v1/data-ingestion/upload/{uploadId}/status: {
    response: DocumentProcessingStatus;
    errors: { 404: "Upload not found" };
  };

  POST /api/v1/data-ingestion/upload/{uploadId}/retry: {
    response: { success: boolean; message: string; };
    errors: {
      400: "Cannot retry in current status",
      404: "Upload not found"
    };
  };
}
```

### MTN API Integration:

```typescript
interface MTNConnectionRequest {
  accountName: string;
  phoneNumber: string; // Format: +260XXXXXXXXX
}

interface MTNOAuthResponse {
  authorizationUrl: string;
  state: string;
  expiresAt: string;
}

interface MTNConnectionStatus {
  accountId: string;
  status: 'CONNECTING' | 'CONNECTED' | 'DISCONNECTED' | 'ERROR';
  accountName: string;
  phoneNumber: string; // Masked: +260XXX***XXX
  lastSync?: string;
  lastSyncTransactionCount?: number;
  errorMessage?: string;
  connectedAt?: string;
}

interface MTNIntegrationAPI {
  POST /api/v1/data-ingestion/mtn/connect: {
    body: MTNConnectionRequest;
    response: MTNOAuthResponse;
    errors: {
      400: "Invalid phone number format",
      503: "MTN service unavailable"
    };
  };

  GET /api/v1/data-ingestion/mtn/{accountId}/status: {
    response: MTNConnectionStatus;
    errors: { 404: "Account not found" };
  };

  POST /api/v1/data-ingestion/mtn/{accountId}/sync: {
    response: {
      success: boolean;
      transactionsFound: number;
      lastSyncAt: string;
    };
    errors: {
      404: "Account not found",
      400: "Account not connected",
      503: "MTN service unavailable"
    };
  };

  DELETE /api/v1/data-ingestion/mtn/{accountId}: {
    response: { success: boolean; };
    errors: { 404: "Account not found" };
  };
}
```

### Event Contracts:

```typescript
// Published by both MTN API and PDF processing
interface RawTransactionReceivedEvent {
  eventType: 'RawTransactionReceived';
  transactionId: string; // External transaction ID
  userId: string;
  amount: number;
  description: string;
  transactionDate: string; // ISO datetime
  transactionType: 'DEBIT' | 'CREDIT' | 'TRANSFER';
  source: 'MTN_API' | 'PDF_UPLOAD';
  sourceReference: string; // Account ID or Upload ID
  timestamp: string;
}

// Document processing events
interface DocumentUploadedEvent {
  eventType: 'DocumentUploaded';
  uploadId: string;
  userId: string;
  fileName: string;
  fileType: string;
  fileUrl: string;
  timestamp: string;
}

interface DocumentProcessingCompletedEvent {
  eventType: 'DocumentProcessingCompleted';
  uploadId: string;
  userId: string;
  extractedTransactions: ExtractedTransaction[];
  confidenceScore: number;
  summary: {
    openingBalance: number;
    closingBalance: number;
    totalDebits: number;
    totalCredits: number;
    statementPeriod: {
      startDate: string;
      endDate: string;
    };
  };
  validationIssues: string[];
  timestamp: string;
}
```

---

## Error Handling & Edge Cases

### PDF Processing Errors:
- **Unsupported Format**: Clear guidance on supported statement formats with examples
- **Low Confidence Extraction**: Manual review interface when confidence < 80%
- **Mathematical Discrepancies**: Detailed validation report with specific issues highlighted
- **CrewAI Processing Failures**: Automatic retry with exponential backoff, fallback to manual entry
- **File Corruption**: Graceful handling with user-friendly error messages

### MTN API Integration Errors:
- **OAuth Failures**: Clear error messages with step-by-step troubleshooting
- **Network Timeouts**: Automatic retry with exponential backoff (max 3 attempts)
- **Invalid Credentials**: Secure error handling without exposing sensitive data
- **Rate Limiting**: Intelligent backoff with user notification of delays
- **Service Unavailable**: Graceful degradation with PDF upload suggestion

### Event Processing Errors:
- **Message Broker Failures**: Automatic failover between RabbitMQ and Azure Service Bus
- **Dead Letter Queue Processing**: Failed events automatically retried with increasing delays
- **Duplicate Event Handling**: Idempotent event processing prevents duplicate transactions
- **Event Ordering Issues**: Timestamp-based ordering ensures chronological processing

---

## Performance Requirements

### PDF Processing Performance:
- **Upload Initiation**: < 2 seconds for signed URL generation
- **CrewAI Processing**: < 60 seconds for typical MTN statement (50-100 transactions)
- **Large Statement Processing**: < 180 seconds for statements with 500+ transactions
- **Confidence Scoring**: < 5 seconds for validation and confidence calculation
- **Concurrent Processing**: Support 10+ simultaneous PDF processing jobs

### MTN API Performance:
- **OAuth Flow**: < 30 seconds for complete connection establishment
- **Transaction Sync**: < 15 seconds for typical sync (50-100 new transactions)
- **Real-Time Sync**: 5-minute intervals with < 10 second processing time
- **Connection Status**: < 2 seconds for status check API calls
- **Bulk Historical Sync**: < 5 minutes for 30-day transaction history

### Event Processing Performance:
- **Event Publishing**: < 1 second for event to reach message broker
- **Event Processing**: < 5 seconds from event receipt to completion
- **Cross-Service Communication**: < 3 seconds for end-to-end event flow
- **Dead Letter Processing**: < 30 seconds for retry attempts

---

## Definition of Done

### Technical Completion:
- [ ] **Frontend Components**: Complete dual-track UI with MTN API and PDF upload options
- [ ] **Backend Services**: Full Java services for MTN integration and document processing
- [ ] **CrewAI Implementation**: StatementProcessing crew with three specialized agents
- [ ] **Event Integration**: Reliable event-driven architecture across all components
- [ ] **API Endpoints**: Complete REST API for both integration methods
- [ ] **Database Schema**: All tables, indexes, and constraints for accounts and documents

### Quality Assurance:
- [ ] **Unit Test Coverage**: > 90% coverage for all service and repository classes
- [ ] **Integration Tests**: End-to-end tests for both MTN API and PDF processing flows
- [ ] **CrewAI Tests**: Validation of agent behavior and task execution
- [ ] **Performance Tests**: All performance requirements met under load
- [ ] **Security Tests**: OAuth flow and file upload security validated
- [ ] **Error Handling Tests**: All error scenarios tested and handled gracefully

### Business Validation:
- [ ] **PDF Extraction Accuracy**: >90% accuracy on standard MTN Mobile Money statements
- [ ] **MTN API Integration**: Successful connection and sync with real MTN accounts
- [ ] **Event Flow Validation**: Both paths produce identical downstream processing
- [ ] **User Experience**: Intuitive interface with clear progress feedback
- [ ] **Data Integrity**: Mathematical validation ensures accurate transaction extraction

### Deployment Readiness:
- [ ] **Environment Configuration**: All environments support both RabbitMQ and Azure Service Bus
- [ ] **CrewAI Deployment**: Python AI service deployed with proper CrewAI configuration
- [ ] **File Storage**: Secure file storage configured for PDF uploads
- [ ] **Monitoring**: Application monitoring for both API connections and PDF processing
- [ ] **Documentation**: Complete API documentation and user guides

---

## Dependencies & Prerequisites

### Must Complete First:
- **[Story 2.1: Chart of Accounts & Category Mapping](../epic-02/story-2.1-chart-of-accounts.md)** - Required for transaction categorization
- **Database Migration Framework** - For schema changes
- **Messaging Infrastructure** - RabbitMQ (local) and Azure Service Bus (production)
- **File Storage Service** - For secure PDF upload and storage

### Integration Points:
- **User Authentication** - For secure API connections and file uploads
- **Transaction Categorization System** - Receives `RawTransactionReceivedEvent` from both paths
- **Journal Entry System** - Ultimate destination for processed transactions

### Technical Dependencies:
- **CrewAI Framework** - For AI-powered statement processing
- **Google Gemini 2.5 Pro/Flash** - LLM for CrewAI agents
- **Spring Boot 3.x** - For backend services
- **Spring Cloud Stream** - For messaging abstraction
- **Next.js 14** - For frontend components
- **PostgreSQL 14+** - For data storage

---

## Notes & Considerations

### CrewAI Integration:
This story implements the first of our three V1 MVP CrewAI crews. The StatementProcessing crew demonstrates the power of specialized AI agents working together to solve complex problems. Each agent has a specific role and expertise, ensuring high-quality transaction extraction from PDF statements.

### Event-Driven Architecture:
The critical insight is that both MTN API sync and PDF upload must result in identical `RawTransactionReceivedEvent` events. This ensures that downstream processing (categorization, journal entries, etc.) is completely agnostic to the data source, maintaining consistency across the entire system.

### Zambian Context:
The implementation specifically targets Zambian mobile money providers (MTN, Airtel, Zamtel) and understands local transaction patterns, currency formats, and statement layouts. This localization is crucial for achieving high extraction accuracy.

### Future Expansion:
This dual-track approach provides a foundation for expanding to other mobile money providers and even traditional bank statements. The event-driven architecture and CrewAI framework make it easy to add new data sources without changing downstream processing.

---

**Related Stories:**
- [Story 4.1: AI-Powered Transaction Review & Categorization](../epic-04/story-4.1-transaction-categorization.md)
- [Story 2.1: Chart of Accounts & Category Mapping](../epic-02/story-2.1-chart-of-accounts.md)

**Epic:** [Seamless Data Ingestion](../../epics-and-stories.md#epic-3-seamless-data-ingestion)

