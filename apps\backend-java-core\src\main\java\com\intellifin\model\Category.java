package com.intellifin.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.OffsetDateTime;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "categories")
public class Category {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Column(nullable = false)
    private String name;

    private String description;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private CategoryType type;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "account_id", nullable = false)
    private Account account;

    @Column(name = "is_system_defined", nullable = false)
    private boolean isSystemDefined;

    @Column(name = "is_active", nullable = false)
    private boolean isActive;

    private String color;

    private String icon;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private OffsetDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private OffsetDateTime updatedAt;

    public UUID getUserId() {
        return this.user != null ? this.user.getId() : null;
    }

    public boolean getIsSystemDefined() {
        return this.isSystemDefined;
    }

    public boolean getIsActive() {
        return this.isActive;
    }

    public boolean isSystemCategory() {
        return this.isSystemDefined;
    }

    public static Category createSystemCategory(String name, boolean isExpense, String description, String icon) {
        return Category.builder()
                .name(name)
                .type(isExpense ? CategoryType.EXPENSE : CategoryType.INCOME)
                .description(description)
                .icon(icon)
                .isSystemDefined(true)
                .isActive(true)
                // Note: 'user' and 'account' are not set here. This might need to be handled by the caller.
                .build();
    }

    public enum CategoryType {
        INCOME, EXPENSE
    }
}
