'use client';

import React from 'react';
import { AuthProvider, useAuth } from '@/contexts/AuthContext';
import { AccountConnection } from '@/components/settings/AccountConnection';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';

function AccountsSettingsPageContent() {
  const { user } = useAuth();

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Authentication Required</h2>
          <p className="text-gray-600 mb-4">Please log in to access account settings.</p>
          <Link href="/login">
            <Button>Go to Login</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-gray-50 overflow-y-auto">
      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <Link href="/settings">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Settings
              </Button>
            </Link>
          </div>
          
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Account Connections</h1>
            <p className="text-gray-600 mt-2">
              Connect your financial accounts to automatically import and categorize transactions
            </p>
          </div>
        </div>

        {/* Account Connection Component */}
        <AccountConnection userId={user.id} />

        {/* Help Section */}
        <Card className="mt-8 p-6 bg-blue-50 border-blue-200">
          <h3 className="text-lg font-semibold text-blue-900 mb-4">Getting Started</h3>
          <div className="space-y-3 text-blue-800">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center text-sm font-medium">
                1
              </div>
              <div>
                <h4 className="font-medium">Connect Your Account</h4>
                <p className="text-sm">
                  Use the "Live Connection" tab to securely connect your MTN Mobile Money account via OAuth.
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center text-sm font-medium">
                2
              </div>
              <div>
                <h4 className="font-medium">Upload Statements (Alternative)</h4>
                <p className="text-sm">
                  If live connection isn't available, upload your bank statements (PDF, CSV, or Excel) for automatic processing.
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center text-sm font-medium">
                3
              </div>
              <div>
                <h4 className="font-medium">Automatic Import</h4>
                <p className="text-sm">
                  Once connected, your transactions will be automatically imported and categorized using AI.
                </p>
              </div>
            </div>
          </div>
        </Card>

        {/* Security Notice */}
        <Card className="mt-6 p-6 bg-green-50 border-green-200">
          <h3 className="text-lg font-semibold text-green-900 mb-2">Security & Privacy</h3>
          <div className="text-green-800 text-sm space-y-2">
            <p>
              <strong>Bank-level Security:</strong> All connections use OAuth 2.0 and your credentials are never stored by IntelliFin.
            </p>
            <p>
              <strong>Encrypted Storage:</strong> All financial data is encrypted both in transit and at rest.
            </p>
            <p>
              <strong>Read-only Access:</strong> We can only read your transaction history, never initiate transactions.
            </p>
          </div>
        </Card>
      </div>
    </div>
  );
}

export default function AccountsSettingsPage() {
  return (
    <AuthProvider>
      <AccountsSettingsPageContent />
    </AuthProvider>
  );
}
