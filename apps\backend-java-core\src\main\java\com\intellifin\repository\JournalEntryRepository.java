package com.intellifin.repository;

import com.intellifin.model.JournalEntry;
import com.intellifin.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface JournalEntryRepository extends JpaRepository<JournalEntry, UUID> {

    /**
     * Find all journal entries for a specific user
     */
    List<JournalEntry> findByUserOrderByCreatedAtDesc(User user);

    /**
     * Find journal entries for a user with pagination
     */
    Page<JournalEntry> findByUserOrderByCreatedAtDesc(User user, Pageable pageable);

    /**
     * Find journal entries by status
     */
    List<JournalEntry> findByUserAndStatusOrderByCreatedAtDesc(User user, JournalEntry.JournalEntryStatus status);

    /**
     * Find journal entry by entry number
     */
    Optional<JournalEntry> findByEntryNumber(String entryNumber);

    /**
     * Find journal entries by transaction ID
     */
    List<JournalEntry> findByTransactionId(UUID transactionId);

    /**
     * Find journal entries by source type and source ID
     */
    List<JournalEntry> findBySourceTypeAndSourceId(String sourceType, String sourceId);

    /**
     * Find journal entries within a date range
     */
    @Query("SELECT je FROM JournalEntry je WHERE je.user = :user AND je.entryDate BETWEEN :startDate AND :endDate ORDER BY je.entryDate DESC")
    List<JournalEntry> findByUserAndEntryDateBetween(
            @Param("user") User user,
            @Param("startDate") OffsetDateTime startDate,
            @Param("endDate") OffsetDateTime endDate
    );

    /**
     * Find posted journal entries for account balance calculations
     */
    @Query("SELECT je FROM JournalEntry je WHERE je.status = 'POSTED' AND je.user = :user ORDER BY je.postedAt DESC")
    List<JournalEntry> findPostedEntriesByUser(@Param("user") User user);

    /**
     * Count journal entries by status for a user
     */
    long countByUserAndStatus(User user, JournalEntry.JournalEntryStatus status);

    /**
     * Find journal entries that need to be posted (DRAFT status with balanced lines)
     */
    @Query("""
        SELECT DISTINCT je FROM JournalEntry je 
        LEFT JOIN FETCH je.lines jel 
        WHERE je.user = :user 
        AND je.status = 'DRAFT' 
        AND je.id IN (
            SELECT jel2.journalEntry.id 
            FROM JournalEntryLine jel2 
            GROUP BY jel2.journalEntry.id 
            HAVING SUM(jel2.debitAmount) = SUM(jel2.creditAmount) 
            AND SUM(jel2.debitAmount) > 0
        )
        ORDER BY je.createdAt ASC
    """)
    List<JournalEntry> findBalancedDraftEntries(@Param("user") User user);

    /**
     * Find journal entries with unbalanced lines
     */
    @Query("""
        SELECT DISTINCT je FROM JournalEntry je 
        LEFT JOIN FETCH je.lines jel 
        WHERE je.user = :user 
        AND je.id IN (
            SELECT jel2.journalEntry.id 
            FROM JournalEntryLine jel2 
            GROUP BY jel2.journalEntry.id 
            HAVING SUM(jel2.debitAmount) != SUM(jel2.creditAmount)
        )
        ORDER BY je.createdAt DESC
    """)
    List<JournalEntry> findUnbalancedEntries(@Param("user") User user);
}
