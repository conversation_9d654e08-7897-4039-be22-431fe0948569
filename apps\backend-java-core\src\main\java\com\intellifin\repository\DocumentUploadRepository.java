package com.intellifin.repository;

import com.intellifin.model.DocumentUpload;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface DocumentUploadRepository extends JpaRepository<DocumentUpload, UUID> {

    /**
     * Find document upload by upload ID
     */
    Optional<DocumentUpload> findByUploadId(String uploadId);

    /**
     * Find all document uploads for a specific user
     */
    List<DocumentUpload> findByUserIdOrderByCreatedAtDesc(UUID userId);

    /**
     * Find document uploads by user and processing status
     */
    List<DocumentUpload> findByUserIdAndProcessingStatusOrderByCreatedAtDesc(
            UUID userId, 
            DocumentUpload.ProcessingStatus processingStatus
    );

    /**
     * Find document uploads by financial account
     */
    List<DocumentUpload> findByFinancialAccountIdOrderByCreatedAtDesc(UUID financialAccountId);

    /**
     * Find pending document uploads (uploading or processing)
     */
    @Query("SELECT du FROM DocumentUpload du WHERE du.processingStatus IN ('UPLOADING', 'PROCESSING') ORDER BY du.createdAt ASC")
    List<DocumentUpload> findPendingUploads();

    /**
     * Find document uploads by user and file type
     */
    List<DocumentUpload> findByUserIdAndFileTypeOrderByCreatedAtDesc(
            UUID userId, 
            DocumentUpload.FileType fileType
    );

    /**
     * Find completed document uploads for a user
     */
    @Query("SELECT du FROM DocumentUpload du WHERE du.user.id = :userId AND du.processingStatus = 'COMPLETED' ORDER BY du.processingCompletedAt DESC")
    List<DocumentUpload> findCompletedUploadsByUserId(@Param("userId") UUID userId);

    /**
     * Find failed document uploads for a user
     */
    @Query("SELECT du FROM DocumentUpload du WHERE du.user.id = :userId AND du.processingStatus = 'FAILED' ORDER BY du.updatedAt DESC")
    List<DocumentUpload> findFailedUploadsByUserId(@Param("userId") UUID userId);

    /**
     * Find document uploads that are stuck in processing (processing for too long)
     */
    @Query("SELECT du FROM DocumentUpload du WHERE du.processingStatus = 'PROCESSING' AND du.processingStartedAt < :cutoffTime")
    List<DocumentUpload> findStuckProcessingUploads(@Param("cutoffTime") OffsetDateTime cutoffTime);

    /**
     * Find expired upload URLs
     */
    @Query("SELECT du FROM DocumentUpload du WHERE du.uploadUrl IS NOT NULL AND du.uploadUrlExpiresAt < :now AND du.processingStatus = 'UPLOADING'")
    List<DocumentUpload> findExpiredUploadUrls(@Param("now") OffsetDateTime now);

    /**
     * Count document uploads by user and status
     */
    long countByUserIdAndProcessingStatus(UUID userId, DocumentUpload.ProcessingStatus processingStatus);

    /**
     * Count total extracted transactions for a user
     */
    @Query("SELECT COALESCE(SUM(du.extractedTransactionsCount), 0) FROM DocumentUpload du WHERE du.user.id = :userId AND du.processingStatus = 'COMPLETED'")
    Long countTotalExtractedTransactionsByUserId(@Param("userId") UUID userId);

    /**
     * Find recent document uploads for a user (last 30 days)
     */
    @Query("SELECT du FROM DocumentUpload du WHERE du.user.id = :userId AND du.createdAt >= :since ORDER BY du.createdAt DESC")
    List<DocumentUpload> findRecentUploadsByUserId(@Param("userId") UUID userId, @Param("since") OffsetDateTime since);
}
