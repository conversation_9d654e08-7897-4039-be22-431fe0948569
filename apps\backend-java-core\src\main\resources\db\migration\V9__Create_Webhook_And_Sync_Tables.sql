-- V9__Create_Webhook_And_Sync_Tables.sql
-- Create tables for webhook handling and transaction synchronization

-- Webhook events table for tracking incoming webhook notifications
CREATE TABLE webhook_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    webhook_id VARCHAR(255) NOT NULL UNIQUE, -- External webhook ID for deduplication
    source VARCHAR(50) NOT NULL, -- 'MTN', 'BANK', etc.
    event_type VARCHAR(50) NOT NULL, -- 'TRANSACTION_CREATED', 'TRANSACTION_UPDATED', etc.
    financial_account_id UUID REFERENCES financial_accounts(id) ON DELETE SET NULL,
    transaction_id VARCHAR(255), -- External transaction ID from webhook
    payload JSONB NOT NULL, -- Raw webhook payload
    signature VARCHAR(512), -- Webhook signature for verification
    processing_status VARCHAR(20) NOT NULL DEFAULT 'PENDING' CHECK (processing_status IN ('PENDING', 'PROCESSING', 'PROCESSED', 'FAILED', 'DUPLICATE')),
    retry_count INTEGER DEFAULT 0,
    error_message TEXT,
    processed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Sync status table for account synchronization tracking
CREATE TABLE sync_status (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    financial_account_id UUID NOT NULL REFERENCES financial_accounts(id) ON DELETE CASCADE,
    sync_type VARCHAR(20) NOT NULL CHECK (sync_type IN ('WEBHOOK', 'POLLING', 'MANUAL')),
    sync_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' CHECK (sync_status IN ('ACTIVE', 'FAILED', 'PAUSED', 'DISABLED')),
    last_sync_at TIMESTAMPTZ,
    last_successful_sync_at TIMESTAMPTZ,
    next_sync_at TIMESTAMPTZ,
    pending_transactions INTEGER DEFAULT 0,
    failed_transactions INTEGER DEFAULT 0,
    total_synced_transactions INTEGER DEFAULT 0,
    error_message TEXT,
    sync_metadata JSONB, -- Additional sync-specific data
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(financial_account_id, sync_type)
);

-- Transaction sync events table for tracking sync operations
CREATE TABLE transaction_sync_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sync_status_id UUID NOT NULL REFERENCES sync_status(id) ON DELETE CASCADE,
    webhook_event_id UUID REFERENCES webhook_events(id) ON DELETE SET NULL,
    transaction_id UUID REFERENCES transactions(id) ON DELETE SET NULL,
    external_transaction_id VARCHAR(255), -- Transaction ID from external system
    sync_operation VARCHAR(20) NOT NULL CHECK (sync_operation IN ('CREATE', 'UPDATE', 'DELETE', 'CATEGORIZE')),
    operation_status VARCHAR(20) NOT NULL CHECK (operation_status IN ('PENDING', 'SUCCESS', 'FAILED', 'SKIPPED')),
    error_details TEXT,
    processing_time_ms BIGINT,
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Webhook security table for storing webhook endpoint configurations
CREATE TABLE webhook_configurations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    financial_account_id UUID NOT NULL REFERENCES financial_accounts(id) ON DELETE CASCADE,
    provider VARCHAR(50) NOT NULL, -- 'MTN', 'BANK', etc.
    webhook_url TEXT NOT NULL,
    secret_key_hash VARCHAR(512), -- Hashed webhook secret for verification
    is_active BOOLEAN DEFAULT true,
    supported_events TEXT[] NOT NULL, -- Array of supported event types
    rate_limit_per_minute INTEGER DEFAULT 60,
    last_webhook_at TIMESTAMPTZ,
    webhook_count_today INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(financial_account_id, provider)
);

-- Create indexes for performance
CREATE INDEX idx_webhook_events_webhook_id ON webhook_events(webhook_id);
CREATE INDEX idx_webhook_events_source_status ON webhook_events(source, processing_status);
CREATE INDEX idx_webhook_events_financial_account_id ON webhook_events(financial_account_id);
CREATE INDEX idx_webhook_events_created_at ON webhook_events(created_at);
CREATE INDEX idx_webhook_events_transaction_id ON webhook_events(transaction_id);

CREATE INDEX idx_sync_status_financial_account_id ON sync_status(financial_account_id);
CREATE INDEX idx_sync_status_sync_type_status ON sync_status(sync_type, sync_status);
CREATE INDEX idx_sync_status_last_sync_at ON sync_status(last_sync_at);
CREATE INDEX idx_sync_status_next_sync_at ON sync_status(next_sync_at);

CREATE INDEX idx_transaction_sync_events_sync_status_id ON transaction_sync_events(sync_status_id);
CREATE INDEX idx_transaction_sync_events_webhook_event_id ON transaction_sync_events(webhook_event_id);
CREATE INDEX idx_transaction_sync_events_transaction_id ON transaction_sync_events(transaction_id);
CREATE INDEX idx_transaction_sync_events_external_transaction_id ON transaction_sync_events(external_transaction_id);
CREATE INDEX idx_transaction_sync_events_created_at ON transaction_sync_events(created_at);

CREATE INDEX idx_webhook_configurations_financial_account_id ON webhook_configurations(financial_account_id);
CREATE INDEX idx_webhook_configurations_provider ON webhook_configurations(provider);
CREATE INDEX idx_webhook_configurations_is_active ON webhook_configurations(is_active);

-- Create triggers to automatically update updated_at
CREATE TRIGGER trigger_webhook_events_updated_at
    BEFORE UPDATE ON webhook_events
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_sync_status_updated_at
    BEFORE UPDATE ON sync_status
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_webhook_configurations_updated_at
    BEFORE UPDATE ON webhook_configurations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
