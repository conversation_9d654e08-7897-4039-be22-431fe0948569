import { Category } from '@intellifin/data-models';
import { apiClient } from './apiClient';

export const getCategories = async (): Promise<Category[]> => {
  const response = await apiClient.get('/categories');
  return response.data;
};

export const createCategory = async (category: Omit<Category, 'id' | 'createdAt' | 'updatedAt' | 'account'>): Promise<Category> => {
  const response = await apiClient.post('/categories', category);
  return response.data;
};

export const updateCategory = async (id: string, category: Partial<Category>): Promise<Category> => {
  const response = await apiClient.put(`/categories/${id}`, category);
  return response.data;
};

export const deleteCategory = async (id: string): Promise<void> => {
  await apiClient.delete(`/categories/${id}`);
};

export const mapCategoryToAccount = async (categoryId: string, accountId: string): Promise<Category> => {
  const response = await apiClient.put(`/categories/${categoryId}/account-mapping`, { accountId });
  return response.data;
};
