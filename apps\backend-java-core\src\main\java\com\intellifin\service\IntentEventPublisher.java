package com.intellifin.service;

import com.intellifin.events.CommandProcessedEvent;
import com.intellifin.events.EntityExtractedEvent;
import com.intellifin.events.IntentFailedEvent;
import com.intellifin.events.IntentRecognizedEvent;
import com.intellifin.model.ConversationMessage;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class IntentEventPublisher {

    private final MessageHandler messageHandler;
    private final ObjectMapper objectMapper;

    public void publishIntentRecognized(IntentRecognizedEvent event) {
        publish(event);
    }

    public void publishEntityExtracted(EntityExtractedEvent event) {
        publish(event);
    }

    public void publishCommandProcessed(CommandProcessedEvent event) {
        publish(event);
    }

    public void publishIntentFailed(IntentFailedEvent event) {
        publish(event);
    }

    private void publish(Object event) {
        try {
            String json = objectMapper.writeValueAsString(event);
            ConversationMessage message = new ConversationMessage();
            message.setContent(json);
            messageHandler.handle(message);
        } catch (Exception e) {
            log.error("Failed to publish event", e);
        }
    }
}
