import { useState, useCallback } from 'react';
import axios from 'axios';
import { apiClient } from '@/services/apiClient';

export interface DocumentUploadRequest {
  userId: string;
  file: File;
  accountType: 'MTN_MOBILE_MONEY';
  accountName?: string;
}

export interface DocumentUploadResponse {
  uploadId: string;
  uploadUrl: string;
  expiresAt: string;
}

export interface DocumentProcessingStatus {
  uploadId: string;
  status: 'UPLOADING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  progress?: number;
  extractedTransactions?: number;
  errorMessage?: string;
  processedAt?: string;
  confidenceScore?: number;
}

export const useDocumentUpload = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadStatus, setUploadStatus] = useState<DocumentProcessingStatus | null>(null);
  const [progress, setProgress] = useState<number>(0);

  const getFileType = (file: File): 'PDF' | 'CSV' | 'EXCEL' => {
    const fileName = file.name.toLowerCase();
    if (fileName.endsWith('.pdf')) return 'PDF';
    if (fileName.endsWith('.csv')) return 'CSV';
    return 'EXCEL';
  };

  const uploadFile = useCallback(async (request: DocumentUploadRequest): Promise<boolean> => {
    setLoading(true);
    setError(null);
    setProgress(0);
    setUploadStatus(null);

    try {
      // Step 1: Initiate upload
      const initiateResponse = await apiClient.post('/api/v1/financial-accounts/upload/initiate', {
        userId: request.userId,
        accountType: request.accountType,
        fileName: request.file.name,
        fileType: getFileType(request.file),
        fileSizeBytes: request.file.size,
        accountName: request.accountName
      });

      const uploadData: DocumentUploadResponse = initiateResponse.data;
      
      setUploadStatus({
        uploadId: uploadData.uploadId,
        status: 'UPLOADING',
        progress: 0
      });

      // Step 2: Upload file to the provided pre-signed URL
      await axios.put(uploadData.uploadUrl, request.file, {
        headers: {
          'Content-Type': request.file.type,
        },
        onUploadProgress: (progressEvent) => {
          const { loaded, total } = progressEvent;
          if (total) {
            const percentCompleted = Math.round((loaded * 100) / total);
            setProgress(percentCompleted);
            setUploadStatus(prev => prev ? { ...prev, progress: percentCompleted } : null);
          }
        },
      });

      // Step 3: Confirm upload completion
      await apiClient.post(`/api/v1/financial-accounts/upload/${uploadData.uploadId}/confirm`);
      
      setUploadStatus(prev => prev ? { ...prev, status: 'PROCESSING', progress: 100 } : null);

      // Step 4: Poll for processing status
      await pollProcessingStatus(uploadData.uploadId);

      return true;
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to upload document';
      setError(errorMessage);
      console.error('Failed to upload document:', err);
      
      setUploadStatus(prev => prev ? { 
        ...prev, 
        status: 'FAILED', 
        errorMessage 
      } : null);
      
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  const pollProcessingStatus = useCallback(async (uploadId: string) => {
    const maxAttempts = 60; // 5 minutes with 5-second intervals
    let attempts = 0;

    const poll = async (): Promise<void> => {
      try {
        const response = await apiClient.get(`/api/v1/financial-accounts/upload/${uploadId}/status`);
        const status: DocumentProcessingStatus = response.data;
        
        setUploadStatus(status);

        if (status.status === 'COMPLETED' || status.status === 'FAILED') {
          return; // Stop polling
        }

        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000); // Poll every 5 seconds
        } else {
          setError('Processing timeout - please check back later');
          setUploadStatus(prev => prev ? {
            ...prev,
            status: 'FAILED',
            errorMessage: 'Processing timeout'
          } : null);
        }
      } catch (err: any) {
        console.error('Failed to poll processing status:', err);
        setError('Failed to check processing status');
      }
    };

    // Start polling after a short delay
    setTimeout(poll, 2000);
  }, []);

  const getUploadStatus = useCallback(async (uploadId: string): Promise<DocumentProcessingStatus | null> => {
    try {
      const response = await apiClient.get(`/api/v1/financial-accounts/upload/${uploadId}/status`);
      return response.data;
    } catch (err: any) {
      console.error('Failed to get upload status:', err);
      return null;
    }
  }, []);

  const resetUpload = useCallback(() => {
    setError(null);
    setUploadStatus(null);
    setProgress(0);
  }, []);

  return {
    loading,
    error,
    uploadStatus,
    progress,
    uploadFile,
    getUploadStatus,
    resetUpload
  };
};
