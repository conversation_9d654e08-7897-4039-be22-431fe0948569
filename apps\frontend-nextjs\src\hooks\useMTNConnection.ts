import { useState, useCallback } from 'react';
import { apiClient } from '@/services/apiClient';

export interface MTNConnectionRequest {
  userId: string;
  accountName: string;
  phoneNumber: string;
}

export interface MTNOAuthResponse {
  authorizationUrl: string;
  state: string;
  expiresAt: string;
}

export const useMTNConnection = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [oauthUrl, setOauthUrl] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<string | null>(null);

  const initiateConnection = useCallback(async (request: MTNConnectionRequest): Promise<boolean> => {
    setLoading(true);
    setError(null);
    setOauthUrl(null);
    
    try {
      const response = await apiClient.post('/api/v1/financial-accounts/connect/mtn', request);
      const data: MTNOAuthResponse = response.data;
      
      setOauthUrl(data.authorizationUrl);
      return true;
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to initiate MTN connection';
      setError(errorMessage);
      console.error('Failed to initiate MTN connection:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  const handleOAuthCallback = useCallback(async (code: string, state: string): Promise<boolean> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiClient.post('/api/v1/financial-accounts/mtn/callback', {
        code,
        state
      });
      
      if (response.data.success) {
        setConnectionStatus('CONNECTED');
        return true;
      } else {
        setError(response.data.error || 'OAuth callback failed');
        return false;
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Failed to complete OAuth flow';
      setError(errorMessage);
      console.error('Failed to handle OAuth callback:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  const resetConnection = useCallback(() => {
    setError(null);
    setOauthUrl(null);
    setConnectionStatus(null);
  }, []);

  return {
    loading,
    error,
    oauthUrl,
    connectionStatus,
    initiateConnection,
    handleOAuthCallback,
    resetConnection
  };
};
