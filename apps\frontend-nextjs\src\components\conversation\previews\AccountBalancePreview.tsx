'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  ExternalLink, 
  AlertCircle,
  Building,
  CreditCard,
  Wallet
} from 'lucide-react';
import { formatCurrency, formatDate } from '@/utils/formatting';
import { ROUTES } from '@/utils/constants';

interface AccountBalance {
  accountId: string;
  accountName: string;
  accountCode: string;
  accountType: 'ASSET' | 'LIABILITY' | 'EQUITY' | 'INCOME' | 'EXPENSE';
  balance: number;
  normalBalance: 'DEBIT' | 'CREDIT';
  lastUpdated: string;
  isActive: boolean;
}

interface AccountBalancePreviewProps {
  balances: AccountBalance[];
  title?: string;
  showTrends?: boolean;
  maxAccounts?: number;
  className?: string;
}

export function AccountBalancePreview({ 
  balances, 
  title = "Account Balances",
  showTrends = true,
  maxAccounts = 5,
  className = '' 
}: AccountBalancePreviewProps) {
  const getAccountIcon = (accountType: string) => {
    switch (accountType) {
      case 'ASSET':
        return <Building className="w-4 h-4" />;
      case 'LIABILITY':
        return <CreditCard className="w-4 h-4" />;
      case 'EQUITY':
        return <DollarSign className="w-4 h-4" />;
      case 'INCOME':
        return <TrendingUp className="w-4 h-4" />;
      case 'EXPENSE':
        return <TrendingDown className="w-4 h-4" />;
      default:
        return <Wallet className="w-4 h-4" />;
    }
  };

  const getAccountTypeColor = (accountType: string) => {
    switch (accountType) {
      case 'ASSET':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'LIABILITY':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'EQUITY':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'INCOME':
        return 'bg-emerald-100 text-emerald-800 border-emerald-200';
      case 'EXPENSE':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getBalanceColor = (balance: number, normalBalance: string) => {
    const isNormalPositive = (normalBalance === 'DEBIT' && balance >= 0) || 
                            (normalBalance === 'CREDIT' && balance <= 0);
    return isNormalPositive ? 'text-green-600' : 'text-red-600';
  };

  const displayBalances = balances.slice(0, maxAccounts);
  const hasMoreAccounts = balances.length > maxAccounts;

  // Calculate summary totals
  const totalAssets = balances
    .filter(b => b.accountType === 'ASSET')
    .reduce((sum, b) => sum + b.balance, 0);
  
  const totalLiabilities = balances
    .filter(b => b.accountType === 'LIABILITY')
    .reduce((sum, b) => sum + Math.abs(b.balance), 0);

  const totalEquity = balances
    .filter(b => b.accountType === 'EQUITY')
    .reduce((sum, b) => sum + Math.abs(b.balance), 0);

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={className}
    >
      <Card className="border-l-4 border-l-green-500 hover:shadow-md transition-shadow">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <DollarSign className="w-5 h-5 text-green-600" />
              <CardTitle className="text-lg">{title}</CardTitle>
            </div>
            <Badge variant="outline" className="text-xs">
              {balances.length} accounts
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          {/* Summary Cards */}
          <div className="grid grid-cols-3 gap-3 mb-4">
            <div className="text-center p-3 bg-green-50 rounded-lg border border-green-200">
              <p className="text-xs text-green-600 uppercase tracking-wide">Assets</p>
              <p className="text-lg font-semibold text-green-700">
                {formatCurrency(totalAssets)}
              </p>
            </div>
            <div className="text-center p-3 bg-red-50 rounded-lg border border-red-200">
              <p className="text-xs text-red-600 uppercase tracking-wide">Liabilities</p>
              <p className="text-lg font-semibold text-red-700">
                {formatCurrency(totalLiabilities)}
              </p>
            </div>
            <div className="text-center p-3 bg-blue-50 rounded-lg border border-blue-200">
              <p className="text-xs text-blue-600 uppercase tracking-wide">Equity</p>
              <p className="text-lg font-semibold text-blue-700">
                {formatCurrency(totalEquity)}
              </p>
            </div>
          </div>

          {/* Account List */}
          <div className="space-y-2 mb-4">
            <h4 className="text-sm font-medium text-gray-700">Account Details:</h4>
            <div className="space-y-1">
              {displayBalances.map((account) => (
                <div 
                  key={account.accountId} 
                  className="flex justify-between items-center text-sm p-3 bg-white border rounded hover:bg-gray-50"
                >
                  <div className="flex items-center space-x-3 flex-1">
                    <div className={`p-1 rounded ${getAccountTypeColor(account.accountType)}`}>
                      {getAccountIcon(account.accountType)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium truncate">{account.accountName}</span>
                        <span className="text-gray-400 text-xs">({account.accountCode})</span>
                      </div>
                      <Badge 
                        variant="outline" 
                        className={`text-xs mt-1 ${getAccountTypeColor(account.accountType)}`}
                      >
                        {account.accountType}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <p className={`font-semibold ${getBalanceColor(account.balance, account.normalBalance)}`}>
                      {formatCurrency(Math.abs(account.balance))}
                    </p>
                    <p className="text-xs text-gray-500">
                      {account.normalBalance} balance
                    </p>
                  </div>
                </div>
              ))}
              
              {hasMoreAccounts && (
                <div className="text-center py-2">
                  <span className="text-sm text-gray-500">
                    +{balances.length - maxAccounts} more accounts
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Balance Check */}
          <div className="p-3 bg-gray-50 rounded-lg mb-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Balance Check:</span>
              <div className="flex items-center space-x-2">
                {Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01 ? (
                  <>
                    <Badge variant="outline" className="bg-green-100 text-green-700 border-green-200">
                      ✓ Balanced
                    </Badge>
                  </>
                ) : (
                  <>
                    <AlertCircle className="w-4 h-4 text-red-500" />
                    <Badge variant="destructive">
                      Unbalanced
                    </Badge>
                  </>
                )}
              </div>
            </div>
            <p className="text-xs text-gray-600 mt-1">
              Assets = Liabilities + Equity
            </p>
          </div>

          {/* Progressive Disclosure CTA */}
          <div className="flex justify-between items-center pt-3 border-t">
            <div className="text-xs text-gray-500">
              Last updated: {formatDate(new Date().toISOString())}
            </div>
            <Button 
              variant="outline" 
              size="sm"
              className="flex items-center space-x-2 hover:bg-green-50 hover:border-green-300"
              onClick={() => {
                window.location.href = ROUTES.JOURNAL_ENTRIES;
              }}
            >
              <span>View Full Journal</span>
              <ExternalLink className="w-4 h-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

/**
 * Compact inline version for chat responses
 */
export function AccountBalanceInlinePreview({ 
  totalAssets, 
  totalLiabilities, 
  totalEquity 
}: { 
  totalAssets: number; 
  totalLiabilities: number; 
  totalEquity: number; 
}) {
  const isBalanced = Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01;
  
  return (
    <div className="inline-flex items-center space-x-3 bg-green-50 border border-green-200 rounded-lg px-3 py-2 text-sm">
      <DollarSign className="w-4 h-4 text-green-600" />
      <div className="flex items-center space-x-4">
        <span><strong>Assets:</strong> {formatCurrency(totalAssets)}</span>
        <span><strong>Liabilities:</strong> {formatCurrency(totalLiabilities)}</span>
        <span><strong>Equity:</strong> {formatCurrency(totalEquity)}</span>
      </div>
      {isBalanced ? (
        <Badge variant="outline" className="bg-green-100 text-green-700 text-xs">
          ✓ Balanced
        </Badge>
      ) : (
        <Badge variant="destructive" className="text-xs">
          Unbalanced
        </Badge>
      )}
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-6 px-2 text-green-600 hover:bg-green-100"
        onClick={() => {
          window.location.href = ROUTES.JOURNAL_ENTRIES;
        }}
      >
        View <ExternalLink className="w-3 h-3 ml-1" />
      </Button>
    </div>
  );
}
