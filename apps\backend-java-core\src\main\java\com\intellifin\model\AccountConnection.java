package com.intellifin.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.type.SqlTypes;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "account_connections")
public class AccountConnection {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "financial_account_id", nullable = false)
    private FinancialAccount financialAccount;

    @Enumerated(EnumType.STRING)
    @Column(name = "connection_type", nullable = false)
    private ConnectionType connectionType;

    @Column(name = "encrypted_credentials", columnDefinition = "TEXT")
    private String encryptedCredentials;

    @Column(name = "authorization_url", columnDefinition = "TEXT")
    private String authorizationUrl;

    @Column(name = "oauth_state")
    private String oauthState;

    @Column(name = "access_token_expires_at")
    private OffsetDateTime accessTokenExpiresAt;

    @Column(name = "refresh_token_expires_at")
    private OffsetDateTime refreshTokenExpiresAt;

    @Column(name = "scopes", columnDefinition = "TEXT[]")
    @JdbcTypeCode(SqlTypes.ARRAY)
    private List<String> scopes;

    @Column(name = "connection_metadata", columnDefinition = "jsonb")
    @JdbcTypeCode(SqlTypes.JSON)
    private String connectionMetadata;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private OffsetDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private OffsetDateTime updatedAt;

    public enum ConnectionType {
        OAUTH,
        API_KEY,
        MANUAL
    }

    // Helper methods
    public boolean isOAuth() {
        return ConnectionType.OAUTH.equals(connectionType);
    }

    public boolean isApiKey() {
        return ConnectionType.API_KEY.equals(connectionType);
    }

    public boolean isManual() {
        return ConnectionType.MANUAL.equals(connectionType);
    }

    public boolean isAccessTokenExpired() {
        return accessTokenExpiresAt != null && accessTokenExpiresAt.isBefore(OffsetDateTime.now());
    }

    public boolean isRefreshTokenExpired() {
        return refreshTokenExpiresAt != null && refreshTokenExpiresAt.isBefore(OffsetDateTime.now());
    }

    public boolean needsRefresh() {
        return isOAuth() && isAccessTokenExpired() && !isRefreshTokenExpired();
    }

    public void updateTokenExpiry(OffsetDateTime accessTokenExpiry, OffsetDateTime refreshTokenExpiry) {
        this.accessTokenExpiresAt = accessTokenExpiry;
        this.refreshTokenExpiresAt = refreshTokenExpiry;
    }
}
