package com.intellifin.messaging.events;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;

/**
 * Event published when transaction synchronization fails
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionSyncFailedEvent {

    private String externalTransactionId;
    private String errorMessage;
    private Integer retryCount;
    private OffsetDateTime timestamp;
}
