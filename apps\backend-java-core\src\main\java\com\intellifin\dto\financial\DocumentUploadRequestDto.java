package com.intellifin.dto.financial;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentUploadRequestDto {

    @NotBlank(message = "User ID is required")
    private String userId;

    @NotNull(message = "Account type is required")
    private AccountType accountType;

    @NotBlank(message = "File name is required")
    private String fileName;

    @NotNull(message = "File type is required")
    private FileType fileType;

    @Positive(message = "File size must be positive")
    private Long fileSizeBytes;

    private String accountName;

    public enum AccountType {
        MTN_MOBILE_MONEY
    }

    public enum FileType {
        PDF,
        CSV,
        EXCEL
    }
}
