package com.intellifin.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import java.time.Duration;

/**
 * Cache configuration for improving performance of frequently accessed data
 */
@Configuration
@EnableCaching
public class CacheConfig {

    /**
     * Simple in-memory cache manager for development and testing
     */
    @Bean
    @Profile({"default", "test"})
    public CacheManager simpleCacheManager() {
        return new ConcurrentMapCacheManager(
            "systemCategories",
            "userCategories",
            "transactionStats",
            "aiModelInfo"
        );
    }

    /**
     * Redis cache manager for production (requires Redis configuration)
     */
    @Bean
    @Profile("production")
    public CacheManager redisCacheManager() {
        // TODO: Implement Redis cache manager when Redis is available
        // For now, fall back to simple cache manager
        return new ConcurrentMapCacheManager(
            "systemCategories",
            "userCategories", 
            "transactionStats",
            "aiModelInfo"
        );
    }
}
