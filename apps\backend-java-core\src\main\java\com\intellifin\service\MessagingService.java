package com.intellifin.service;

import com.intellifin.model.ConversationMessage;
import com.intellifin.model.User;
import com.intellifin.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

@Service
@RequiredArgsConstructor
@Slf4j
public class MessagingService {

    private final SimpMessagingTemplate messagingTemplate;
    private final UserRepository userRepository;
    private final Map<UUID, Consumer<ConversationMessage>> messageHandlers = new ConcurrentHashMap<>();

    public void registerMessageHandler(UUID userId, Consumer<ConversationMessage> handler) {
        log.debug("Registering message handler for user: {}", userId);
        messageHandlers.put(userId, handler);
    }

    public void removeMessageHandler(UUID userId) {
        log.debug("Removing message handler for user: {}", userId);
        messageHandlers.remove(userId);
    }

    public void processIncomingMessage(ConversationMessage message) {
        log.debug("Processing incoming message for user: {}", message.getConversation().getUser().getId());
        Consumer<ConversationMessage> handler = messageHandlers.get(message.getConversation().getUser().getId());
        if (handler != null) {
            handler.accept(message);
        } else {
            log.warn("No message handler registered for user: {}", message.getConversation().getUser().getId());
        }
    }

    public void sendToUser(UUID userId, String destination, Object payload) {
        log.debug("Sending message to user {} on destination {}", userId, destination);
        messagingTemplate.convertAndSendToUser(userId.toString(), destination, payload);
    }

    public void sendToUser(String username, String destination, Object payload) {
        log.debug("Sending message to user {} on destination {}", username, destination);
        User user = userRepository.findByEmail(username).orElse(null);
        if (user != null) {
            sendToUser(user.getId(), destination, payload);
        } else {
            log.warn("User not found: {}", username);
        }
    }

    public void sendToAll(String destination, Object payload) {
        log.debug("Sending message to all users on destination {}", destination);
        messagingTemplate.convertAndSend(destination, payload);
    }

    public void sendErrorToUser(UUID userId, String errorMessage) {
        log.error("Sending error to user {}: {}", userId, errorMessage);
        sendToUser(userId, "/queue/errors", Map.of("error", errorMessage));
    }
}
