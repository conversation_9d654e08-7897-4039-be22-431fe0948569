# Story 3.1: MTN Mobile Money Account Connection with Document Upload Fallback

**Epic:** Financial Account Integration
**Status:** ✅ COMPLETED
**Priority:** High
**Story Points:** 15
**Completed Date:** 2025-01-31

## User Story

**As a** user
**I want** to connect my MTN Mobile Money account to IntelliFin either via API integration or document upload
**So that** my transactions are imported and categorized regardless of API availability

## Acceptance Criteria

### Primary Integration (API-based)
- [x] User can initiate MTN Mobile Money connection from settings
- [x] Secure OAuth flow with MTN for account authorization
- [x] Connection status is clearly displayed (connected, disconnected, error)
- [x] User can disconnect and reconnect their MTN account
- [x] Connection details are securely stored and encrypted
- [x] Error handling for connection failures and timeouts
- [x] Clear feedback during connection process
- [x] Support for multiple MTN accounts per user
- [x] Connection health monitoring and alerts

### Fallback Integration (Document Upload)
- [x] User can upload MTN Mobile Money statements (PDF, CSV, Excel)
- [x] Document parsing extracts transaction data automatically
- [x] Manual transaction mapping interface for unrecognized formats
- [x] Document upload events are processed via messaging abstraction layer
- [x] Uploaded documents are securely stored and encrypted
- [x] Document processing status is tracked and displayed
- [x] Error handling for unsupported file formats and parsing failures
- [x] API contract is defined and documented

## Technical Implementation

### Frontend Changes
- `src/components/settings/AccountConnection.tsx` - Account connection interface with upload option
- `src/components/settings/MTNConnectionFlow.tsx` - MTN-specific connection flow
- `src/components/settings/DocumentUpload.tsx` - Document upload interface
- `src/components/settings/ConnectionStatus.tsx` - Connection status display
- `src/hooks/useAccountConnection.ts` - Account connection management
- `src/hooks/useDocumentUpload.ts` - Document upload management
- `src/services/mtn-api.ts` - MTN API integration service

### API Gateway Changes
- `src/main/java/com/intellifin/gateway/routes/FinancialAccountController.java` - Account management routing
- `src/main/java/com/intellifin/gateway/middleware/MTNOAuthInterceptor.java` - MTN OAuth handling
- `src/main/java/com/intellifin/gateway/security/EncryptionService.java` - Connection details encryption

### Service Changes
- **Core Backend**: `src/main/java/com/intellifin/accounts/` - Financial account management with Spring Cloud Stream
- **Core Backend**: `src/main/java/com/intellifin/integrations/mtn/` - MTN integration service
- **Core Backend**: `src/main/java/com/intellifin/documents/` - Document upload and parsing service
- **Core Backend**: `src/main/java/com/intellifin/messaging/` - Document processing event handling
- **AI Service**: `src/document_parser.py` - Document parsing and transaction extraction with MessagingService
- **Database**: Financial account, connection, and document data models

### Database Changes
- FinancialAccount table with connection details
- AccountConnection table for OAuth tokens and status
- DocumentUpload table for uploaded statements and processing status
- Connection audit trail for security monitoring
- Document processing audit trail for compliance

## API Contracts

```typescript
// MTN integration contracts
interface MTNConnectionRequest {
  userId: string;
  accountName: string;
  phoneNumber: string;
}

interface MTNOAuthResponse {
  authorizationUrl: string;
  state: string;
  expiresAt: string;
}

interface MTNConnectionCallback {
  code: string;
  state: string;
  error?: string;
}

interface MTNConnectionStatus {
  accountId: string;
  status: 'CONNECTING' | 'CONNECTED' | 'DISCONNECTED' | 'ERROR';
  lastSync?: string;
  errorMessage?: string;
  accountInfo?: {
    phoneNumber: string;
    accountName: string;
    balance?: number;
  };
}

interface MTNIntegrationAPI {
  POST /api/v1/financial-accounts/connect/mtn: {
    body: MTNConnectionRequest;
    response: MTNOAuthResponse;
    errors: { 400: "Invalid request", 503: "MTN service unavailable" };
  };
  
  POST /api/v1/financial-accounts/mtn/callback: {
    body: MTNConnectionCallback;
    response: { success: boolean; accountId: string; };
    errors: { 400: "Invalid callback", 401: "OAuth error" };
  };
  
  GET /api/v1/financial-accounts/{accountId}/status: {
    response: MTNConnectionStatus;
    errors: { 404: "Account not found" };
  };
  
  DELETE /api/v1/financial-accounts/{accountId}/disconnect: {
    response: { success: boolean; };
    errors: { 404: "Account not found", 500: "Disconnect failed" };
  };
}

// Document upload contracts (fallback integration)
interface DocumentUploadRequest {
  userId: string;
  accountType: 'MTN_MOBILE_MONEY';
  fileName: string;
  fileType: 'PDF' | 'CSV' | 'EXCEL';
  accountName?: string;
}

interface DocumentUploadResponse {
  uploadId: string;
  uploadUrl: string;
  expiresAt: string;
}

interface DocumentProcessingStatus {
  uploadId: string;
  status: 'UPLOADING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  progress?: number;
  extractedTransactions?: number;
  errorMessage?: string;
  processedAt?: string;
}

interface DocumentUploadAPI {
  POST /api/v1/financial-accounts/upload/initiate: {
    body: DocumentUploadRequest;
    response: DocumentUploadResponse;
    errors: { 400: "Invalid request", 413: "File too large" };
  };

  GET /api/v1/financial-accounts/upload/{uploadId}/status: {
    response: DocumentProcessingStatus;
    errors: { 404: "Upload not found" };
  };

  POST /api/v1/financial-accounts/upload/{uploadId}/confirm: {
    response: { success: boolean; accountId: string; };
    errors: { 400: "Processing not complete", 404: "Upload not found" };
  };
}

// Internal messaging events for document processing (abstracted across RabbitMQ/Azure Service Bus)
interface DocumentProcessingEvents {
  DocumentUploadStarted: {
    uploadId: string;
    userId: string;
    fileName: string;
    fileType: string;
    timestamp: string;
  };
  DocumentParsingCompleted: {
    uploadId: string;
    extractedTransactions: Transaction[];
    confidence: number;
    processingTime: number;
  };
  DocumentProcessingFailed: {
    uploadId: string;
    error: string;
    retryCount: number;
    timestamp: string;
  };
}
```

## Error Handling

### API Integration Errors
- **OAuth Failures:** Clear error messages with retry options
- **Network Timeouts:** Retry mechanisms with exponential backoff
- **Invalid Credentials:** Secure error handling without exposing sensitive data
- **Service Unavailable:** Graceful degradation with user feedback
- **Connection Loss:** Automatic reconnection attempts with user notification

### Document Upload Errors
- **Unsupported File Formats:** Clear guidance on supported formats with examples
- **File Size Limits:** Helpful messages about file size restrictions
- **Parsing Failures:** Manual transaction mapping interface for unrecognized data
- **Document Processing Failures:** Events routed to dead-letter queues (RabbitMQ DLX or Azure Service Bus DLQ)
- **Messaging Broker Connectivity:** Issues handled by abstraction layer with automatic failover

## Definition of Done

### API Integration
- [x] Users can successfully connect their MTN Mobile Money accounts
- [x] OAuth flow is secure and handles all error scenarios
- [x] Connection status is accurately displayed and updated
- [x] Users can disconnect and reconnect accounts
- [x] Connection details are properly encrypted and secured

### Document Upload Integration
- [x] Users can upload MTN Mobile Money statements in supported formats
- [x] Document parsing extracts transaction data with high accuracy
- [x] Document processing events work consistently across RabbitMQ (local) and Azure Service Bus (production)
- [x] Manual transaction mapping interface works for unrecognized formats
- [x] Uploaded documents are securely stored and encrypted

### General Requirements
- [x] Error scenarios are handled gracefully with helpful messages
- [x] Performance meets requirements (< 30 seconds for connection, < 2 minutes for document processing)
- [x] Security audit passes for both OAuth and document upload implementations
- [x] Tests cover connection workflows, document upload, and error scenarios
- [x] Service can be deployed independently
- [x] No breaking changes to other services
- [x] Messaging abstraction layer handles document processing failures gracefully

## Dependencies

- [Story 1.1: User Registration and Authentication Flow](../epic-01/story-1.1-user-auth.md)
- [Story 1.2: Monorepo Setup and CI/CD Pipeline](../epic-01/story-1.2-monorepo-cicd.md)

## Implementation Summary

**✅ COMPLETED - All acceptance criteria have been successfully implemented.**

### Key Implementation Files:

#### Backend Services:
- `FinancialAccountService.java` - Account management and OAuth flow coordination
- `MTNIntegrationService.java` - MTN-specific OAuth integration and API calls
- `DocumentUploadService.java` - Document upload and processing coordination
- `EncryptionService.java` - Secure credential storage using AES-256-GCM
- `FileStorageService.java` - File storage abstraction (local/cloud)
- `DocumentProcessingMessagingService.java` - Event publishing for document processing

#### Frontend Components:
- `AccountConnection.tsx` - Main account connection interface with tabs
- `MTNConnectionFlow.tsx` - MTN OAuth flow with form validation
- `DocumentUpload.tsx` - Drag-and-drop document upload interface
- `ConnectionStatus.tsx` - Real-time connection status display

#### Database Schema:
- `financial_accounts` - External account connections
- `account_connections` - OAuth tokens and connection details
- `document_uploads` - Document upload tracking and processing status
- `connection_audit_trail` - Security monitoring
- `document_processing_audit` - Compliance audit trail

#### API Endpoints Implemented:
- `POST /api/v1/financial-accounts/connect/mtn` - Initiate MTN connection
- `POST /api/v1/financial-accounts/mtn/callback` - OAuth callback handling
- `GET /api/v1/financial-accounts/{accountId}/status` - Connection status
- `DELETE /api/v1/financial-accounts/{accountId}/disconnect` - Disconnect account
- `POST /api/v1/financial-accounts/upload/initiate` - Document upload initiation
- `GET /api/v1/financial-accounts/upload/{uploadId}/status` - Upload status
- `POST /api/v1/financial-accounts/upload/{uploadId}/confirm` - Confirm upload

### Security Features Implemented:
- AES-256-GCM encryption for OAuth tokens and credentials
- Secure audit trails for all operations
- JWT-based authentication
- User-scoped data access
- Secure OAuth state validation

### Messaging Integration:
- Spring Cloud Stream configuration for RabbitMQ/Azure Service Bus
- Document processing events with dead-letter queue handling
- Messaging abstraction layer for cross-environment compatibility

## Notes

This story establishes the foundation for financial data integration. The MTN connection is secure and reliable as the primary source of transaction data for users. The implementation includes both live API integration and document upload fallback, ensuring users can connect their accounts regardless of API availability.

---

**Related Stories:**
- [Story 4.2: Transaction Synchronization via Webhooks](story-4.2-transaction-sync.md)

**Epic:** [Financial Account Integration](../../epics-and-stories.md#epic-4-financial-account-integration) 