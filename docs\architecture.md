# IntelliFin Fullstack Architecture Document - V1 Strategic Focus

## 1. Introduction

This document outlines the complete fullstack architecture for IntelliFin V1, focusing exclusively on the three non-negotiable pillars: Rock-Solid Accounting Engine, Seamless Data Ingestion, and Conversational Experience. It serves as the single source of truth for V1 development, ensuring consistency across the technology stack while deliberately excluding out-of-scope features.

This unified approach combines backend systems, frontend implementation, and their integration, streamlined for the V1 strategic focus on core financial management capabilities.

### 1.1 V1 Architectural Principles

**Foundation First:** The accounting engine (Chart of Accounts, Double-Entry Bookkeeping) must be completed before any advanced features.

**Simplicity Over Complexity:** V1 excludes ZRA integration, multi-user features, and advanced analytics to focus on core value delivery.

**Conversational-First Design:** The 2+1 dynamic panel layout and WebSocket-based conversational interface are central to the user experience.

**Polyglot Microservices:** Java Spring Boot for core financial logic, Python FastAPI for AI/ML components, Next.js for frontend.

### 1.2 V1 Scope Boundaries

**INCLUDED IN V1:**
- Core accounting engine with Chart of Accounts and double-entry bookkeeping
- MTN Mobile Money API integration and PDF statement upload with AI parsing
- Conversational interface with 2+1 dynamic panel layout
- Basic transaction categorization and financial summaries

**EXCLUDED FROM V1 (Future Implementation):**
- ZRA Smart Invoice integration and compliance service
- Multi-user access and accountant collaboration features
- Automated bill payments and advanced workflow automation
- Advanced reporting, analytics, and performance optimization

### 1.3 Change Log

| Date | Version | Description | Author |
| :--------- | :------ | :----------------- | :--------- |
| 2024-07-30 | 1.0 | Initial draft, based on Project Genesis Document and UI/UX Specification. | Architect |
| 2024-07-31 | 2.0 | V1 Strategic Realignment - 3 Pillar Focus, ZRA Exclusion | Architect |

---

## 2. High Level Architecture

This section establishes the foundational architectural decisions for IntelliFin.

### 2.1 Technical Summary - V1 Focus

IntelliFin V1 adopts a focused polyglot microservices architecture centered on the three core pillars. The frontend, built with Next.js, implements a revolutionary 2+1 dynamic panel layout with WebSocket-first conversational interface. Backend services utilize Java Spring Boot for the core accounting engine and financial logic, and Python FastAPI for AI-powered transaction categorization and PDF parsing. The V1 architecture deliberately excludes ZRA compliance services and multi-user features to ensure solid foundation delivery. The entire system is containerized with Docker and managed via GitHub Actions for CI/CD.

### 2.2 Platform and Infrastructure Choice

**Platform Recommendation:** Microsoft Azure
**Rationale:** Azure provides a comprehensive enterprise-grade platform with strong support for our polyglot microservices architecture. With robust Java Spring Boot support, excellent Python FastAPI hosting via App Services, and native integration with our chosen AI services, Azure offers the optimal balance of enterprise features, security, and cost-effectiveness for our fintech platform.

**Alternative 1: Google Cloud Platform (GCP)**
**Pros:** Strong AI/ML services, good for Google Gemini integration, robust container orchestration.
**Cons:** Less enterprise-focused than Azure, potentially higher costs for our specific use case.

**Alternative 2: AWS Full Stack**
**Pros:** Broadest set of services, very scalable, mature ecosystem.
**Cons:** Can be more complex to manage, potentially higher learning curve for our team.

**Selected Platform:** Microsoft Azure
**V1 Key Services:** Azure App Service (for microservices deployment), Azure Database for PostgreSQL, Azure Cache for Redis, Azure Service Bus (production messaging), Azure Storage (PDF file storage), Azure Application Insights (basic monitoring), Azure Key Vault (secrets management).
**V1 Excluded Services:** Azure AI Search (advanced vector database), Azure Active Directory (multi-user identity), advanced monitoring and analytics services.
**Deployment Host and Regions:** Single region deployment (e.g., `West Europe` or `East US`) for V1 MVP, with multi-region expansion planned for post-V1.

### 2.3 Repository Structure

**Structure:** Monorepo
**V1 Package Organization:**
- `apps/`: V1 applications (`frontend-nextjs`, `backend-java-core`, `backend-python-ai`)
- `libs/`: Shared libraries (`data-models`, `api-interfaces`, `shared-utils`, `auth-client`)
- `docs/`: V1-focused documentation excluding out-of-scope features
**V1 Excluded:** `backend-java-zra` service and related ZRA compliance libraries

### 2.4 V1 High Level Architecture Diagram

```mermaid
graph TD
    subgraph "Frontend Layer - 2+1 Dynamic Panel Layout"
        A[Next.js Frontend<br/>Left: Navigation<br/>Main: Conversational Workspace<br/>Right: Context Sidebar] --> B[WebSocket Gateway]
        A --> C[REST API Gateway]
    end

    subgraph "V1 Backend Services"
        B --> D[Core Backend - Java Spring Boot<br/>Accounting Engine + Financial Logic]
        C --> D
        D --> E[AI Service - Python FastAPI<br/>Categorization + PDF Parsing]
    end

    subgraph "Messaging Layer - Environment Specific"
        G[Local Development: RabbitMQ]
        H[Production/Staging: Azure Service Bus]
    end

    subgraph "Abstraction Layer"
        I[Spring Cloud Stream - Java Services]
        J[MessagingService Wrapper - Python Services]
    end

    subgraph "V1 Data Layer"
        K[PostgreSQL Database<br/>Chart of Accounts + Journal Entries]
        L[Redis Cache]
        M[Azure Storage<br/>PDF Statement Files]
    end

    subgraph "V1 External Integrations"
        N[MTN Mobile Money API]
        O[PDF Statement Upload]
    end

    subgraph "V1 EXCLUDED (Future Implementation)"
        P[ZRA VSDC API - Excluded]
        Q[Stitch Aggregator API - Deferred]
        R[Multi-User Services - Excluded]
    end

    D --> I
    E --> J
    F --> I
    I -.->|Local Profile| G
    I -.->|Cloud Profile| H
    J -.->|Environment Variable| G
    J -.->|Environment Variable| H

    D --> K
    D --> L
    D --> M
    F --> P
    D --> N
    D --> O

    style A fill:#E1F5FE,stroke:#0277BD,stroke-width:2px
    style D fill:#F3E5F5,stroke:#7B1FA2,stroke-width:2px
    style E fill:#E8F5E8,stroke:#2E7D32,stroke-width:2px
    style F fill:#FFF3E0,stroke:#F57C00,stroke-width:2px
    style G fill:#FFEBEE,stroke:#C62828,stroke-width:2px
    style H fill:#E3F2FD,stroke:#1565C0,stroke-width:2px
    style I fill:#F1F8E9,stroke:#558B2F,stroke-width:2px
    style J fill:#F1F8E9,stroke:#558B2F,stroke-width:2px

```

### 2.5 Architectural Patterns

- **Polyglot Microservices:** IntelliFin's backend will be composed of small, independent services written in different languages (Java for core financial logic, Python for AI/ML). - _Rationale:_ Allows for choosing the best tool for each specific job, promotes team autonomy, and enhances scalability and resilience.
- **API Gateway Pattern:** A single entry point for all client requests, routing them to appropriate microservices. - _Rationale:_ Centralized authentication, rate limiting, logging, and load balancing, simplifying client-side complexity and enhancing security.
- **Backend For Frontend (BFF) Pattern (Conversational Gateway):** A specialized service (WebSocket Conversational Gateway) tailored for the frontend's conversational needs. - _Rationale:_ Optimizes communication between the frontend and backend, handles real-time conversational state, and decouples frontend from core business logic.
- **Event-Driven Choreography with Hybrid Messaging:** Core inter-service communication uses event-driven choreography with a hybrid messaging bus strategy. Azure Service Bus for production/staging environments, RabbitMQ for local development. All services use abstraction layers (Spring Cloud Stream for Java, MessagingService wrapper for Python) to decouple from specific broker implementations. - _Rationale:_ Provides zero-cost local development while ensuring production-grade reliability and scalability. Abstraction layers enable seamless environment transitions and broker independence.
- **Native-First Local Development:** Primary local development workflow uses natively installed services (Java, Python, PostgreSQL, RabbitMQ) on developer machines for maximum performance and debugging ease. Docker remains critical for CI/CD and production parity testing. - _Rationale:_ Maximizes developer velocity and debugging capabilities while maintaining production consistency through containerized deployment pipelines.
- **Component-Based UI (Frontend):** Building the frontend with reusable React components (leveraging Shadcn/ui). - _Rationale:_ Promotes modularity, reusability, maintainability, and accelerates development.
- **Repository Pattern (Backend):** Abstracting data access logic from business logic. - _Rationale:_ Decouples services from specific database implementations, facilitating easier data source changes and improving testability.
- **Double-Entry Bookkeeping (Core Financial Engine):** Core accounting logic will be based on the double-entry system. - _Rationale:_ Ensures financial accuracy, integrity, and compliance, providing a robust foundation for all accounting operations.

---

## 3. Tech Stack

This section defines the definitive technology selection for the entire project.

### 3.1 Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
| :--------- | :--------- | :--------- | :--------- | :--------- |
| Frontend Language | TypeScript | 5.x | Primary language for frontend development, ensuring type safety and robust code. | Enhanced developer experience, reduced runtime errors, improved code maintainability. |
| Frontend Framework | Next.js | 14.x | React framework for building server-rendered and static web applications. | Performance optimization (SSR/SSG), SEO benefits, simplified routing, robust ecosystem. |
| UI Component Library | Shadcn/ui | Latest | Collection of customizable UI components built with Radix UI and Tailwind CSS. | Shadcn/ui is not a traditional component library; it's a collection of reusable components that we copy into our project. This aligns perfectly with our "greenfield, full control" philosophy. It gives us maximum customizability and avoids dependency bloat, while being built on modern, accessible primitives (Tailwind CSS and Radix UI). This choice ensures a modern, trustworthy look and feel from day one. |
| State Management | Zustand | Latest | Lightweight and flexible state management for local and global states. | For our MVP, we must avoid over-engineering. Zustand is a powerful yet minimalist state management solution that provides just what we need without the heavy boilerplate of other libraries. Its simplicity will improve developer velocity and is a perfect fit for the focused, conversational UI we are building. |
| Backend Language (Core) | Java | 17/21 LTS | Robust, mature language for building scalable and secure financial services. | Enterprise-grade reliability, strong ecosystem, high performance, excellent security features. |
| Backend Framework (Core) | Spring Boot | 3.x | Opinionated framework for building production-ready, stand-alone Java applications. | Rapid development, strong convention over configuration, extensive features (security, data access). |
| Backend Language (AI/ML) | Python | 3.10+ | Widely used for AI/ML, rich ecosystem of libraries. | Access to cutting-edge AI/ML libraries (LangChain, TensorFlow, PyTorch), rapid prototyping. |
| Backend Framework (AI/ML) | FastAPI | Latest | Modern, fast (high-performance) web framework for building APIs with Python. | Asynchronous support, automatic OpenAPI/Swagger documentation, Pydantic for data validation. |
| API Style | Hybrid (WebSocket & REST) | N/A | WebSocket for real-time conversational gateway, RESTful for resource management. | Optimizes real-time chat interactions while providing structured access to financial data. |
| Database | PostgreSQL | 15.x | Robust, open-source relational database. | ACID compliance, strong support for complex queries, extensibility, widely adopted. |
| Cache | Redis | Latest | In-memory data store for caching and real-time data needs. | High performance, supports various data structures (e.g., for session management, real-time analytics). |
| File Storage | Azure Storage | N/A | Scalable, highly available object storage. | Securely store documents (invoices, receipts), backups, and other large data. |
| Messaging (Production) | Azure Service Bus | Latest | Enterprise-grade message broker for production environments. | Provides reliable, scalable messaging for inter-service communication in cloud deployments with advanced features like dead-letter queues, message sessions, and duplicate detection. |
| Messaging (Local Dev) | RabbitMQ | 3.x | Open-source message broker for local development. | Enables zero-cost local development with feature parity to production messaging patterns. Runs natively on developer machines for optimal performance. |
| Messaging Abstraction (Java) | Spring Cloud Stream | Latest | Abstraction layer for messaging in Java services. | Decouples business logic from specific message broker implementations. Uses profile-based binders (rabbit for local, servicebus for cloud). |
| Messaging Abstraction (Python) | Custom MessagingService | N/A | Python wrapper for messaging implementations. | Provides unified interface with RabbitMQMessagingService and AzureServiceBusMessagingService implementations chosen via environment variables. |
| Authentication | Spring Security with JWT | N/A | Industry-standard security framework for user authentication and authorization. | Leveraging the robust, industry-standard security framework built into our core backend platform is the most secure and capital-efficient choice. Implementing a stateless JWT (JSON Web Token) authentication flow is the standard for modern PWAs interacting with microservices and ensures we build a secure, scalable solution in-house without incurring third-party costs initially. |
| Frontend Testing | React Testing Library / Jest | Latest | User-centric testing for React components. | Encourages testing actual user behavior, fast execution. |
| Backend Testing | JUnit / Mockito (Java); Pytest (Python) | Latest | Unit and integration testing frameworks for respective backend languages. | Comprehensive test coverage, robust test capabilities. |
| E2E Testing | Playwright | Latest | End-to-end testing for web applications. | Cross-browser support, reliable, faster than Selenium. |
| Build Tool | Gradle (Java); Poetry (Python); npm/Yarn (Next.js) | Latest | Build automation for multi-language monorepo. | Efficient dependency management, build processes across services. |
| Bundler | Webpack (via Next.js) | N/A | JavaScript module bundler. | Optimized asset delivery for frontend. |
| IaC Tool | Terraform | Latest | Infrastructure as Code for provisioning and managing cloud resources. | Version-controlled, reproducible infrastructure, reduces manual errors. |
| CI/CD | GitHub Actions | N/A | Automated workflows for continuous integration and continuous deployment. | Streamlined development pipeline, automated testing and deployment. |
| Monitoring | Azure Application Insights / Azure Monitor | N/A | Centralized monitoring for application health, performance, and resource utilization. | We are all-in on Azure. Leveraging the native, first-party monitoring solution is the most strategic choice. It will provide seamless integration with our entire stack (App Services, Databases, etc.), giving us a single pane of glass for distributed tracing, performance metrics, and log analysis with minimal setup and operational overhead. |
| Logging | SLF4J / Logback (JSON) | Latest | Centralized application logging and error reporting in structured JSON format. | Using the standard is efficient. The key is outputting structured JSON logs, which will allow for easy, automated ingestion and searching by our monitoring platform. |
| CSS Framework | Tailwind CSS | 3.x | Utility-first CSS framework. | Rapid UI development, highly customizable, small production CSS file sizes. |

---

## 4. Data Models

This section defines the core data models/entities that will be shared between frontend and backend.

### 4.1 Transaction

**Purpose:** Represents a single financial transaction, either incoming (income) or outgoing (expense). This is the fundamental unit for financial tracking.

**Key Attributes:**
- `id`: UUID - Unique identifier for the transaction.
- `userId`: UUID - Foreign key to the User who owns this transaction.
- `financialAccountId`: UUID - Foreign key to the FinancialAccount (e.g., bank, mobile money wallet) the transaction belongs to.
- `date`: Date - The date the transaction occurred.
- `description`: String - A brief description of the transaction (e.g., "Shoprite purchase", "Client Payment").
- `amount`: Decimal - The amount of the transaction.
- `type`: Enum (INCOME, EXPENSE) - Whether the transaction is income or an expense.
- `categoryId`: UUID - Foreign key to the Category this transaction is classified under.
- `status`: Enum (PENDING_CLASSIFICATION, CLASSIFIED, RECONCILED) - Current status of the transaction.
- `source`: String - Where the transaction data originated from (e.g., "MTN Mobile Money", "Bank Statement", "Manual Entry").
- `createdAt`: DateTime - Timestamp of when the record was created.
- `updatedAt`: DateTime - Timestamp of when the record was last updated.

### 4.1.1 TypeScript Interface

```typescript
interface Transaction {
  id: string;
  userId: string;
  financialAccountId: string;
  date: string; // ISO 8601 date string
  description: string;
  amount: number; // Use number for decimal, handle precision carefully
  type: 'INCOME' | 'EXPENSE';
  categoryId: string;
  status: 'PENDING_CLASSIFICATION' | 'CLASSIFIED' | 'RECONCILED';
  source: string;
  createdAt: string; // ISO 8601 datetime string
  updatedAt: string; // ISO 8601 datetime string
}
```

### 4.1.2 Relationships

- One-to-Many: User has many Transactions.
- One-to-Many: FinancialAccount has many Transactions.
- Many-to-One: Transaction belongs to one Category.

### 4.2 Category

**Purpose:** Defines categories for classifying transactions (e.g., "Groceries", "Utilities", "Sales Revenue"). This helps users organize and analyze their financial data.

**Key Attributes:**
- `id`: UUID - Unique identifier for the category.
- `userId`: UUID - Foreign key to the User who owns this category (can be global or user-defined).
- `name`: String - The name of the category (e.g., "Transport", "Rent").
- `type`: Enum (INCOME, EXPENSE) - Whether this category is for income or expense transactions.
- `isSystemDefined`: Boolean - True if it's a default system category, false if user-created.
- `createdAt`: DateTime - Timestamp of when the record was created.
- `updatedAt`: DateTime - Timestamp of when the record was last updated.

### 4.2.1 TypeScript Interface

```typescript
interface Category {
  id: string;
  userId?: string; // Optional for system-defined categories
  name: string;
  type: 'INCOME' | 'EXPENSE';
  isSystemDefined: boolean;
  createdAt: string;
  updatedAt: string;
}
```

### 4.2.2 Relationships

- Many-to-One: Many Transactions can belong to one Category.
- One-to-Many: User has many user-defined Categories.

### 4.3 User

**Purpose:** Represents an individual user account within IntelliFin.

**Key Attributes:**
- `id`: UUID - Unique identifier for the user.
- `email`: String - User's email address (unique, used for login).
- `passwordHash`: String - Hashed password for security.
- `firstName`: String - User's first name.
- `lastName`: String - User's last name.
- `organizationName`: String - The name of the user's business/organization.
- `tpin`: String - User's Taxpayer Identification Number (ZRA specific).
- `onboardingStatus`: Enum (STARTED, PROFILE_COMPLETE, ACCOUNTS_CONNECTED, ALL_SET) - Tracks user onboarding progress.
- `preferences`: JSONB - User-specific settings (e.g., currency, notification preferences).
- `createdAt`: DateTime - Timestamp of when the record was created.
- `updatedAt`: DateTime - Timestamp of when the record was last updated.

### 4.3.1 TypeScript Interface

```typescript
interface User {
  id: string;
  email: string;
  passwordHash: string;
  firstName: string;
  lastName: string;
  organizationName: string;
  tpin?: string; // Optional if not yet provided
  onboardingStatus: 'STARTED' | 'PROFILE_COMPLETE' | 'ACCOUNTS_CONNECTED' | 'ALL_SET';
  preferences: Record<string, any>; // JSON object for flexible preferences
  createdAt: string;
  updatedAt: string;
}
```

### 4.3.2 Relationships

- One-to-Many: User has many FinancialAccounts.
- One-to-Many: User has many Transactions.
- One-to-Many: User has many Clients.
- One-to-Many: User has many Invoices.
- One-to-Many: User has many Categories.

### 4.4 FinancialAccount

**Purpose:** Represents a financial account connected to IntelliFin (e.g., bank account, mobile money wallet).

**Key Attributes:**
- `id`: UUID - Unique identifier for the financial account.
- `userId`: UUID - Foreign key to the User who owns this account.
- `provider`: String - The financial institution or service (e.g., "Standard Chartered", "MTN Mobile Money").
- `accountName`: String - User-defined name for the account (e.g., "My Business Savings", "MTN Wallet").
- `accountNumberLast4`: String - Last 4 digits of account number (for identification, not sensitive).
- `balance`: Decimal - Current balance of the account (can be synced).
- `currency`: String (ISO 4217) - Currency code (e.g., "ZMW", "USD").
- `connectionStatus`: Enum (CONNECTED, DISCONNECTED, ERROR) - Status of the connection to the external provider.
- `connectionDetails`: JSONB - Encrypted details needed for API integration (e.g., API keys, access tokens).
- `createdAt`: DateTime - Timestamp of when the record was created.
- `updatedAt`: DateTime - Timestamp of when the record was last updated.

### 4.4.1 TypeScript Interface

```typescript
interface FinancialAccount {
  id: string;
  userId: string;
  provider: string;
  accountName: string;
  accountNumberLast4: string;
  balance: number;
  currency: string;
  connectionStatus: 'CONNECTED' | 'DISCONNECTED' | 'ERROR';
  // connectionDetails should be handled securely on the backend only
  createdAt: string;
  updatedAt: string;
}
```

### 4.4.2 Relationships

- Many-to-One: Many FinancialAccounts belong to one User.
- One-to-Many: FinancialAccount has many Transactions.

### 4.5 Client

**Purpose:** Represents a client or customer of the IntelliFin user's business. Essential for invoicing and tracking.

**Key Attributes:**
- `id`: UUID - Unique identifier for the client.
- `userId`: UUID - Foreign key to the User who created this client.
- `name`: String - Client's full name or business name.
- `email`: String - Client's email address.
- `phone`: String - Client's phone number.
- `address`: String - Client's physical address.
- `tpin`: String - Client's Taxpayer Identification Number (ZRA specific, if applicable).
- `createdAt`: DateTime - Timestamp of when the record was created.
- `updatedAt`: DateTime - Timestamp of when the record was last updated.

### 4.5.1 TypeScript Interface

```typescript
interface Client {
  id: string;
  userId: string;
  name: string;
  email?: string; // Optional
  phone?: string; // Optional
  address?: string; // Optional
  tpin?: string; // Optional
  createdAt: string;
  updatedAt: string;
}
```

### 4.5.2 Relationships

- Many-to-One: Many Clients belong to one User.
- One-to-Many: Client can have many Invoices.

### 4.6 Invoice

**Purpose:** Represents a sales invoice generated by the IntelliFin user for a client.

**Key Attributes:**
- `id`: UUID - Unique identifier for the invoice.
- `userId`: UUID - Foreign key to the User who created this invoice.
- `clientId`: UUID - Foreign key to the Client the invoice is issued to.
- `invoiceNumber`: String - Unique invoice number (can be system-generated or sequential).
- `issueDate`: Date - Date the invoice was issued.
- `dueDate`: Date - Date the invoice is due.
- `subTotal`: Decimal - Sum of all line item amounts before tax.
- `taxAmount`: Decimal - Total tax applied to the invoice.
- `totalAmount`: Decimal - SubTotal + TaxAmount.
- `currency`: String (ISO 4217) - Currency code (e.g., "ZMW").
- `status`: Enum (DRAFT, PENDING_SUBMISSION, SUBMITTED, PAID, OVERDUE, CANCELLED) - Current status of the invoice.
- `zraStatus`: Enum (PENDING, SUBMITTED, FAILED, N/A) - Status of submission to ZRA VSDC.
- `zraInvoiceId`: String - Official invoice ID returned by ZRA VSDC upon successful submission.
- `paymentTerms`: String - Description of payment terms (e.g., "Net 30", "Due on receipt").
- `notes`: String - Any additional notes for the client.
- `createdAt`: DateTime - Timestamp of when the record was created.
- `updatedAt`: DateTime - Timestamp of when the record was last updated.

### 4.6.1 TypeScript Interface

```typescript
interface Invoice {
  id: string;
  userId: string;
  clientId: string;
  invoiceNumber: string;
  issueDate: string; // ISO 8601 date string
  dueDate: string;   // ISO 8601 date string
  subTotal: number;
  taxAmount: number;
  totalAmount: number;
  currency: string;
  status: 'DRAFT' | 'PENDING_SUBMISSION' | 'SUBMITTED' | 'PAID' | 'OVERDUE' | 'CANCELLED';
  zraStatus: 'PENDING' | 'SUBMITTED' | 'FAILED' | 'N/A';
  zraInvoiceId?: string; // Optional, only present after successful ZRA submission
  paymentTerms?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}
```

### 4.6.2 Relationships

- Many-to-One: Many Invoices belong to one User.
- Many-to-One: Many Invoices are issued to one Client.
- One-to-Many: Invoice has many InvoiceLineItems.

### 4.7 InvoiceLineItem

**Purpose:** Represents a single line item within an Invoice (e.g., "Consulting Fee", "Product X").

**Key Attributes:**
- `id`: UUID - Unique identifier for the line item.
- `invoiceId`: UUID - Foreign key to the Invoice this line item belongs to.
- `description`: String - Description of the item/service.
- `quantity`: Decimal - Quantity of the item/service.
- `unitPrice`: Decimal - Price per unit.
- `amount`: Decimal - Quantity * UnitPrice.
- `createdAt`: DateTime - Timestamp of when the record was created.
- `updatedAt`: DateTime - Timestamp of when the record was last updated.

### 4.7.1 TypeScript Interface

```typescript
interface InvoiceLineItem {
  id: string;
  invoiceId: string;
  description: string;
  quantity: number;
  unitPrice: number;
  amount: number; // Calculated: quantity * unitPrice
  createdAt: string;
  updatedAt: string;
}
```

### 4.7.2 Relationships

- Many-to-One: Many InvoiceLineItems belong to one Invoice.

### 4.8 JournalEntry

**Purpose:** Represents a single accounting entry following the double-entry bookkeeping principle. All financial transactions eventually translate into one or more journal entries.

**Key Attributes:**
- `id`: UUID - Unique identifier for the journal entry.
- `userId`: UUID - Foreign key to the User who owns this entry.
- `transactionId`: UUID - Optional foreign key to the Transaction that generated this entry.
- `invoiceId`: UUID - Optional foreign key to the Invoice that generated this entry.
- `date`: Date - The date the entry was recorded.
- `description`: String - A description of the entry.
- `debitAccountId`: UUID - Foreign key to the Account that is debited.
- `creditAccountId`: UUID - Foreign key to the Account that is credited.
- `amount`: Decimal - The amount of the debit/credit.
- `entryType`: Enum (INCOME, EXPENSE, ASSET_ACQUISITION, LIABILITY_PAYMENT, etc.) - Type of the journal entry.
- `createdAt`: DateTime - Timestamp of when the record was created.
- `updatedAt`: DateTime - Timestamp of when the record was last updated.

### 4.8.1 TypeScript Interface

```typescript
interface JournalEntry {
  id: string;
  userId: string;
  transactionId?: string; // Optional
  invoiceId?: string; // Optional
  date: string; // ISO 8601 date string
  description: string;
  debitAccountId: string;
  creditAccountId: string;
  amount: number;
  entryType: string; // e.g., 'INCOME', 'EXPENSE', 'ASSET_ACQUISITION'
  createdAt: string;
  updatedAt: string;
}
```

### 4.8.2 Relationships

- One-to-Many: User has many JournalEntries.
- Many-to-One: JournalEntry debits/credits one Account.
- Many-to-One: JournalEntry can originate from one Transaction or one Invoice.

### 4.9 Account (Chart of Accounts)

**Purpose:** Represents a financial account in the Chart of Accounts (e.g., "Cash", "Accounts Receivable", "Sales Revenue", "Rent Expense"). This forms the backbone of the double-entry system.

**Key Attributes:**
- `id`: UUID - Unique identifier for the account.
- `userId`: UUID - Foreign key to the User who owns this account (can be system-defined or user-defined).
- `name`: String - Name of the account (e.g., "Bank - Standard Chartered", "Mobile Money - MTN").
- `accountType`: Enum (ASSET, LIABILITY, EQUITY, REVENUE, EXPENSE) - Type of account.
- `balance`: Decimal - Current balance of the account (calculated from journal entries).
- `isSystemDefined`: Boolean - True if it's a default system account, false if user-created.
- `createdAt`: DateTime - Timestamp of when the record was created.
- `updatedAt`: DateTime - Timestamp of when the record was last updated.

### 4.9.1 TypeScript Interface

```typescript
interface Account {
  id: string;
  userId?: string; // Optional for system-defined accounts
  name: string;
  accountType: 'ASSET' | 'LIABILITY' | 'EQUITY' | 'REVENUE' | 'EXPENSE';
  balance: number;
  isSystemDefined: boolean;
  createdAt: string;
  updatedAt: string;
}
```

### 4.9.2 Relationships

- One-to-Many: User has many user-defined Accounts.
- One-to-Many: Many JournalEntries debit/credit an Account.

---

## 5. API Specification

This section defines IntelliFin's Hybrid API Strategy, combining a real-time conversational gateway with standard RESTful endpoints. This strategy is fundamental to how our frontend and backend will communicate to create the unique IntelliFin experience.

### The Hybrid API Strategy: Combining Real-Time Intent with RESTful Resources

Our API will operate on two distinct levels:

*   **The Conversational Gateway (WebSocket):** This is the primary entry point for all user intent. It's a real-time, stateful channel where the user sends their natural language commands. The frontend's main job is to send raw user text through this gateway.
*   **The Resource-Oriented APIs (REST):** These are the "workhorse" APIs for creating, reading, updating, and deleting our core data models (User, Invoice, etc.). These APIs are primarily used by our own backend orchestrator, not directly by the user's conversational commands.

To make this crystal clear, here is the end-to-end flow for our "Draft Invoice" vision:

1.  **User Input:** The user types their command into the Next.js UI.
2.  **WebSocket Message:** The frontend sends a simple message over our secure Spring WebSocket (STOMP) connection to `/app/conversation/command`. The payload is just the raw text.
3.  **AI Orchestration:** Our core Spring Boot backend receives this command and passes it to our AI Service.
4.  **Intent Recognition:** The AI Service (Python/FastAPI) uses LangChain4j and Gemini to parse the command and returns a structured object like `{"intent": "CREATE_INVOICE", "entities": {...}}`.
5.  **Internal REST Calls:** The Spring Boot orchestrator now uses our own internal workhorse APIs to execute the intent. It might call `GET /api/v1/clients?name=Phiri Corp`, then construct a valid invoice object and call `POST /api/v1/invoices`.
6.  **WebSocket Response:** The orchestrator gets the newly created draft Invoice object and pushes it back to the user's frontend over a dedicated WebSocket topic.
7.  **Dynamic UI Update:** The frontend receives the Invoice object and dynamically renders the "Invoice Draft/Approval Screen" view.

This hybrid approach allows us to have a revolutionary conversational UI for the user while building our backend on a foundation of robust, stateless, and well-defined RESTful services.

With that context, here is the formal API specification for the MVP:

### 5.1 Conversational Command API (WebSocket)

This is the primary entry point for all user interactions.

*   **WebSocket Topic:** `/app/conversation/command`
*   **Purpose:** To receive a natural language command from the user for processing and orchestration by the backend.
*   **Message Body (Client to Server):** A JSON object containing the user's command string. Ex: `{"command": "Show me my profit this month"}`.
*   **Response (Server to Client):** Asynchronous push to a user-specific topic (e.g., `/user/queue/responses`). The payload will be a structured object representing the result (e.g., a FinancialSummary object, a draft Invoice object, a confirmation message).
*   **Authentication:** The WebSocket connection must be established and authenticated using the user's JWT.

### 5.2 Resource Management API (REST)

These are the stateless, "workhorse" endpoints used primarily by the backend orchestrator and for basic, non-conversational data retrieval by the frontend. All authenticated endpoints expect a JWT.

#### User Management & Authentication (`/api/v1/auth`, `/api/v1/users`)

*   `POST /api/v1/auth/register`: Creates a new user account. (Public)
*   `POST /api/v1/auth/login`: Authenticates a user and returns a JWT. (Public)
*   `GET /api/v1/users/me`: Fetches the profile of the currently authenticated user. (Requires JWT)

#### Financial Account Management (`/api/v1/financial-accounts`)

*   `POST /api/v1/financial-accounts/connect/mtn`: Initiates the secure connection process for an MTN Mobile Money account. (Requires JWT)
*   `GET /api/v1/financial-accounts`: Retrieves a list of all financial accounts connected by the current user. (Requires JWT)
*   `GET /api/v1/financial-accounts/{accountId}`: Retrieves the details of a specific financial account. (Requires JWT) 

---

## 6. External Integrations

This section outlines IntelliFin's strategy for integrating with key external financial services and regulatory bodies. Our approach prioritizes reliability, security, and compliance, leveraging asynchronous communication patterns where appropriate.

### 6.1 MTN Mobile Money API

*   **Purpose:** To enable users to connect their MTN Mobile Money accounts, fetch transaction data, and potentially initiate payments (future phase).
*   **Integration Method:** Primarily via **Webhooks (preferred)** and API polling (as a fallback/supplementary mechanism).
    *   **Webhook Prioritization:** We will strongly prioritize integrating with MTN's webhook capabilities (if available) for real-time transaction notifications. This minimizes the need for frequent polling, reduces API call costs, and ensures timely data synchronization. **Our initial technical due diligence must focus on confirming the availability and robustness of MTN's webhook service. If webhooks are not available, our backup polling strategy must be designed for maximum efficiency to minimize cost and system load.**
    *   **Fallback/Polling:** If comprehensive webhooks are not available or sufficient, a robust polling mechanism will be implemented for periodic transaction fetching.
*   **Security:** OAuth 2.0 or API key-based authentication with MTN, secure storage of credentials, and strict validation of incoming webhook payloads.
*   **Data Flow:**
        1.  User initiates connection via IntelliFin UI (triggers `POST /api/v1/financial-accounts/connect/mtn`).
        2.  IntelliFin backend completes authentication/authorization with MTN.
        3.  MTN pushes transaction notifications via webhook to IntelliFin's secure endpoint.
        4.  IntelliFin polls MTN API for historical transactions and reconciliation.
        5.  Fetched transactions are processed, categorized (via AI Service), and stored in our database.
*   **Error Handling:** Robust retry mechanisms for API calls, dead-letter queues for webhook processing failures (using Azure Service Bus in production, RabbitMQ dead-letter exchanges in local development), and clear alerting for integration issues. Event processing failures are handled consistently across environments through the messaging abstraction layer.

### 6.2 Stitch Aggregator API

*   **Purpose:** To connect to a broader range of banks and financial institutions in Zambia for fetching transaction data, enhancing financial visibility beyond mobile money.
*   **Integration Method:** Standard RESTful API integration.
*   **Security:** OAuth 2.0 for user consent and secure data access, ensuring compliance with data privacy regulations. All sensitive data (e.g., access tokens) will be encrypted at rest and in transit.
*   **Data Flow:**
    1.  User initiates bank connection via IntelliFin UI.
    2.  IntelliFin redirects user to Stitch for bank authentication (OAuth flow).
    3.  Stitch provides API tokens to IntelliFin after successful authorization.
    4.  IntelliFin uses Stitch APIs to fetch account balances and transaction histories.
    5.  Fetched data is processed, categorized, and stored.
*   **Considerations:** Handling varying data formats and latency across different bank integrations via Stitch.

### 6.3 ZRA VSDC API (Zambia Revenue Authority Virtual Sales Device Controller)

*   **Purpose:** To ensure all invoices generated by IntelliFin users are officially registered and compliant with the Zambia Revenue Authority's electronic invoicing requirements.
*   **Integration Method:** Internal API Contract with a dedicated **ZRA Compliance Service**.
    *   **Internal API Contract:** The core Spring Boot services will *not* directly call the external ZRA VSDC API. Instead, they will communicate with a dedicated `ZRA Compliance Service` (a separate Java microservice, as identified in the Tech Stack), which will encapsulate all ZRA-specific logic, data formatting, and external API calls. **This is not a simple library call; it's a service-to-service communication link (e.g., via a private REST or gRPC API). Defining this internal API clearly will ensure a clean separation of concerns, allowing the ZRA component to be managed and updated independently without impacting the core backend logic.**
    *   **Rationale:** This promotes strong decoupling, isolates ZRA compliance logic, simplifies testing, and allows the `ZRA Compliance Service` to handle specific ZRA requirements (e.g., Java application, specific libraries, local VSDC client communication) without burdening other services.
*   **Security & Compliance:** Adherence to ZRA's stringent security protocols, proper handling of TPINs, and secure communication with the VSDC API.
*   **Data Flow (as per User Flow 3.1.1 for Invoice):**
    1.  IntelliFin user drafts and approves an invoice.
    2.  Core backend orchestrator calls the internal `ZRA Compliance Service` API (e.g., `POST /internal/zra/invoices/submit`).
    3.  `ZRA Compliance Service` formats invoice data according to ZRA VSDC specifications.
    4.  `ZRA Compliance Service` communicates with the external ZRA VSDC API.
    5.  `ZRA Compliance Service` receives response from ZRA, updates invoice status (`zraStatus`) and `zraInvoiceId` in the database, and pushes confirmation/error to frontend via WebSocket.
*   **Error Handling:** Robust error reporting from the `ZRA Compliance Service` back to the core backend, providing user-friendly guidance on resolution (e.g., invalid TPIN, ZRA service unavailable).

### 6.4 General Integration Principles

*   **Idempotency:** Implement idempotency for all external API calls where possible to prevent duplicate processing of events or transactions.
*   **Circuit Breaker Pattern:** Utilize circuit breakers to prevent cascading failures when external services are unavailable or slow.
*   **Data Mapping & Transformation:** Implement clear data mapping layers to translate external API responses into IntelliFin's internal data models and vice-versa.
*   **Rate Limiting:** Adhere to and manage external API rate limits to avoid throttling or service interruptions.
*   **Monitoring & Alerting:** Comprehensive monitoring and alerting for all integration points to quickly detect and respond to failures or anomalies.

---

## 7. Security & Compliance

This section details the security measures and compliance strategies implemented within IntelliFin to protect user data, ensure financial integrity, and adhere to relevant regulations. Security is a foundational pillar of our platform, designed to instill trust and confidence in our users.

### 7.1 Data Security & Encryption

*   **Data at Rest Encryption:** All sensitive data stored in our primary database (Azure Database for PostgreSQL) and file storage (Azure Storage) will be encrypted at rest using industry-standard encryption mechanisms provided by Microsoft Azure (e.g., Azure Storage Service Encryption and Transparent Data Encryption).
*   **Data in Transit Encryption:** All communication between frontend and backend, and between internal microservices, will be secured using TLS 1.2 or higher. This includes HTTPS for REST APIs, WSS (WebSocket Secure) for the conversational gateway, and encrypted messaging channels for both Azure Service Bus (production) and RabbitMQ (local development with TLS enabled).
*   **Sensitive Data Handling:** User passwords will be stored as strong, one-way hashes (e.g., using Argon2 or bcrypt). API keys, access tokens, and other sensitive credentials for external integrations will be securely managed using Azure Key Vault, retrieved at runtime, and never hardcoded. Messaging credentials for both Azure Service Bus and RabbitMQ will be managed through the same secure credential management system.

### 7.2 Authentication & Authorization

*   **JWT-Based Authentication:** As defined in the Tech Stack, Spring Security with JWT will be used for stateless authentication. This allows for scalable and secure access control across microservices.
*   **Role-Based Access Control (RBAC):** Implement RBAC to define granular permissions based on user roles (e.g., Business Owner, Accountant, Read-Only User).
*   **Multi-Factor Authentication (MFA):** Implement MFA as an option for users, especially for critical actions or account access.
*   **Session Management:** Secure session management practices, including short-lived JWTs, refresh tokens, and server-side invalidation mechanisms.

### 7.3 Operational Security

*   **"Accountant's Constitution" / Confirm & Commit Protocol:** A core principle (from Project Genesis Document) where users must explicitly review and confirm any sensitive financial action (e.g., ZRA invoice submission, payment initiation) through a clear, multi-step UI confirmation. This provides an auditable trail and prevents accidental actions.
*   **Audit Trails:** Comprehensive logging of all critical user actions and system events, including inter-service messaging events, ensuring traceability and accountability. These logs will be immutable and stored in a centralized, secure logging solution (Azure Monitor/Application Insights, as per Tech Stack). Message processing events from both Azure Service Bus and RabbitMQ are captured through the messaging abstraction layer for consistent audit trails across environments.
*   **Vulnerability Management:** Regular security scanning (SAST/DAST) of code and infrastructure, penetration testing, and timely patching of vulnerabilities.
*   **Incident Response Plan:** A defined process for detecting, responding to, and recovering from security incidents, including communication protocols.

### 7.4 Regulatory Compliance

*   **ZRA Compliance:** All ZRA-related functionalities (e.g., invoice submission) will strictly adhere to the Zambia Revenue Authority's Virtual Sales Device Controller (VSDC) requirements and any other relevant tax regulations. The dedicated ZRA Compliance Service encapsulates this logic.
*   **Data Privacy (GDPR/Local Equivalents):** Implement principles of data minimization, purpose limitation, and user rights (e.g., right to access, rectification, erasure) in accordance with relevant data protection laws.
*   **Financial Regulations:** Adherence to any applicable financial regulations in Zambia concerning payment processing, financial data handling, and reporting.
*   **Regular Audits:** Subject the platform to regular internal and external security and compliance audits.

### 7.5 Infrastructure Security

*   **Network Security:** Utilize virtual private clouds (VPCs), network segmentation, firewalls, and security groups to isolate services and control traffic flow.
*   **Least Privilege Principle:** Apply the principle of least privilege to all user accounts, service accounts, and system components, granting only the necessary permissions.
*   **Container Security:** Regular scanning of Docker images for vulnerabilities, using minimal base images, and ensuring secure container configurations.
*   **Secrets Management:** As mentioned in Data Security, securely manage all application and infrastructure secrets using a dedicated service.

---

## 8. Core Financial Engine & Accounting Logic

### 8.1 Double-Entry Bookkeeping

*   **Principle:** The system strictly follows the double-entry bookkeeping principle, where every financial transaction must have a corresponding debit and credit entry.
*   **Purpose:** Ensures financial accuracy, integrity, and compliance with accounting standards.
*   **Example:** A transaction of 100 ZMW from `Cash` (Asset) to `Sales Revenue` (Revenue) would be recorded as:
    *   Debit: `Cash` (100 ZMW)
    *   Credit: `Sales Revenue` (100 ZMW)
*   **Validation:** Automated checks ensure that the sum of debits equals the sum of credits for each `JournalEntry`.

### 8.2 Transaction Processing

*   **Flow:**
    1.  User initiates a transaction (e.g., "Paid ZESCO for electricity").
    2.  The frontend sends a raw text command to the WebSocket gateway.
    3.  The WebSocket gateway forwards the command to the AI Service.
    4.  The AI Service uses LangChain4j and Gemini to parse the command and identify `Transaction` and `FinancialAccount` entities.
    5.  The AI Service proposes a `Category` and `Amount`.
    6.  The frontend receives the proposed `Transaction` object and displays it for user confirmation.
    7.  Upon user approval, the frontend sends a `POST` request to the `Transaction` endpoint.
    8.  The backend validates the transaction, creates `JournalEntry` for debits and credits, updates `FinancialAccount` balances, and triggers reconciliation.
*   **Error Handling:** Comprehensive error handling for invalid transactions, insufficient funds, and connection issues.

### 8.3 Category Classification

*   **Purpose:** To automatically categorize transactions based on their descriptions and context.
*   **Flow:**
    1.  A new `Transaction` is created with an `originalDescription`.
    2.  The `AI Service` receives this `Transaction` and uses LangChain4j and Gemini to analyze the `originalDescription`.
    3.  The AI Service proposes a `Category` and `Justification`.
    4.  The frontend displays the proposed `Category` and `Justification` for user review.
    5.  Upon user approval, the frontend sends a `PUT` request to update the `Transaction`'s `Category` and `Justification`.
    6.  The backend validates the classification, updates the `JournalEntry`, and triggers reconciliation.
*   **Error Handling:** Robust error handling for invalid classifications and connection issues.

### 8.4 Financial Reporting

*   **Real-time Reporting:** The system will support real-time generation of key financial statements by querying `JournalEntry` and `Account` data.
*   **Key Reports (MVP):**
    *   **Income Statement (Profit & Loss):** Summarizes revenues and expenses over a period to show profitability.
    *   **Balance Sheet:** Presents a snapshot of assets, liabilities, and equity at a specific point in time.
    *   **General Ledger/Journal:** Provides detailed, chronological records of all transactions.
*   **Customizable Reporting:** Future iterations may include customizable report builders and advanced analytics dashboards.

### 8.5 AI's Role & Explainability (XAI)

*   **AI's Primary Role:** The AI's job is to analyze the `originalDescription` of a `Transaction` and propose the most likely `Category`.
*   **Explainability ("Why?"):** To fulfill our promise of educating the user, the AI's response will include a justification field. For a "ZESCO" payment categorized as "Utilities," the justification might be: "ZESCO is a provider of electricity, which is typically classified as a 'Utilities' business expense."

---

## 9. Frontend Architecture

This section provides detailed technical guidance for the Next.js frontend implementation, ensuring consistency, maintainability, and optimal performance for IntelliFin's sophisticated conversational interface.

### 9.1 Project Structure & Organization

The frontend application will be organized within the monorepo structure as `apps/frontend-nextjs/` with the following directory organization:

```
apps/frontend-nextjs/
├── src/
│   ├── app/                    # Next.js 14 App Router
│   │   ├── (auth)/            # Authentication routes (grouped)
│   │   │   ├── login/
│   │   │   └── register/
│   │   ├── dashboard/         # Main conversational workspace
│   │   ├── settings/          # User settings and account management
│   │   ├── globals.css        # Global styles and Tailwind imports
│   │   ├── layout.tsx         # Root layout with providers
│   │   └── page.tsx           # Landing page
│   ├── components/            # Reusable UI components
│   │   ├── ui/               # Shadcn/ui components (copied and customized)
│   │   │   ├── button.tsx
│   │   │   ├── input.tsx
│   │   │   ├── card.tsx
│   │   │   └── ...
│   │   ├── conversation/     # Conversational interface components
│   │   │   ├── CommandBar.tsx
│   │   │   ├── ChatHistory.tsx
│   │   │   ├── MessageBubble.tsx
│   │   │   └── ...
│   │   ├── financial/        # Financial data display components
│   │   │   ├── TransactionList.tsx
│   │   │   ├── InvoiceDraft.tsx
│   │   │   ├── FinancialSummary.tsx
│   │   │   └── ...
│   │   └── layout/           # Layout and navigation components
│   │       ├── Header.tsx
│   │       ├── Sidebar.tsx
│   │       └── ...
│   ├── hooks/                # Custom React hooks
│   │   ├── useWebSocket.ts   # WebSocket connection management
│   │   ├── useConversation.ts # Conversational state management
│   │   ├── useAuth.ts        # Authentication state
│   │   └── ...
│   ├── stores/               # Zustand state stores
│   │   ├── authStore.ts      # Authentication state
│   │   ├── conversationStore.ts # Conversational state
│   │   ├── financialStore.ts # Financial data state
│   │   └── ...
│   ├── services/             # API and external service integrations
│   │   ├── api/             # REST API client
│   │   │   ├── client.ts    # Axios instance and interceptors
│   │   │   ├── auth.ts      # Authentication API calls
│   │   │   ├── transactions.ts # Transaction API calls
│   │   │   └── ...
│   │   ├── websocket.ts     # WebSocket service for conversational gateway
│   │   └── ...
│   ├── types/                # TypeScript type definitions
│   │   ├── api.ts           # API response types
│   │   ├── conversation.ts  # Conversational interface types
│   │   ├── financial.ts     # Financial data types
│   │   └── ...
│   ├── utils/                # Utility functions
│   │   ├── formatting.ts    # Date, currency, number formatting
│   │   ├── validation.ts    # Form validation utilities
│   │   ├── constants.ts     # Application constants
│   │   └── ...
│   └── lib/                  # Third-party library configurations
│       ├── tailwind.ts      # Tailwind CSS configuration
│       ├── utils.ts         # Shadcn/ui utilities
│       └── ...
├── public/                   # Static assets
│   ├── icons/               # PWA icons
│   ├── manifest.json        # PWA manifest
│   └── ...
├── next.config.js           # Next.js configuration
├── tailwind.config.js       # Tailwind CSS configuration
├── tsconfig.json            # TypeScript configuration
└── package.json             # Dependencies and scripts
```

### 9.2 Component Architecture & Patterns

#### 9.2.1 Component Organization Principles

*   **Atomic Design Methodology:** Components will follow atomic design principles (atoms, molecules, organisms, templates, pages) for consistent composition and reusability.
*   **Single Responsibility:** Each component will have a single, well-defined purpose and responsibility.
*   **Props Interface:** All components will have explicit TypeScript interfaces for props, ensuring type safety and clear contracts.
*   **Composition over Inheritance:** Prefer component composition and prop drilling over complex inheritance hierarchies.

#### 9.2.2 Core Component Patterns

**Conversational Interface Components:**
```typescript
// Example: CommandBar component pattern
interface CommandBarProps {
  onCommandSubmit: (command: string) => void;
  isLoading?: boolean;
  placeholder?: string;
}

const CommandBar: React.FC<CommandBarProps> = ({
  onCommandSubmit,
  isLoading = false,
  placeholder = "Ask me anything about your finances..."
}) => {
  // Implementation
};
```

**Financial Data Components:**
```typescript
// Example: TransactionList component pattern
interface TransactionListProps {
  transactions: Transaction[];
  onTransactionUpdate: (id: string, updates: Partial<Transaction>) => void;
  onTransactionConfirm: (id: string) => void;
}
```

#### 9.2.3 Shadcn/ui Integration Strategy

*   **Component Copying:** Shadcn/ui components will be copied directly into `src/components/ui/` and customized for IntelliFin's design system.
*   **Customization Layer:** Custom styling will be applied through Tailwind CSS classes and CSS custom properties, maintaining the component's accessibility features.
*   **Design Token Integration:** Components will use our defined design tokens (colors, typography, spacing) for consistent theming.

### 9.3 State Management Architecture

#### 9.3.1 Zustand Store Structure

**Authentication Store:**
```typescript
interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
}

const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  isAuthenticated: false,
  isLoading: false,
  login: async (credentials) => {
    // Implementation
  },
  logout: () => {
    // Implementation
  },
  refreshToken: async () => {
    // Implementation
  }
}));
```

**Conversation Store:**
```typescript
interface ConversationState {
  messages: Message[];
  currentContext: ConversationContext | null;
  isLoading: boolean;
  sendMessage: (command: string) => Promise<void>;
  updateContext: (context: ConversationContext) => void;
}

const useConversationStore = create<ConversationState>((set, get) => ({
  messages: [],
  currentContext: null,
  isLoading: false,
  sendMessage: async (command) => {
    // WebSocket implementation
  },
  updateContext: (context) => {
    // Implementation
  }
}));
```

#### 9.3.2 State Synchronization Patterns

*   **WebSocket-Driven Updates:** Real-time updates from the conversational gateway will trigger store updates, which will automatically re-render relevant components.
*   **Optimistic Updates:** For user actions with immediate feedback (e.g., confirming a transaction), optimistic updates will be applied immediately, with rollback on error.
*   **Persistence Strategy:** Critical state (authentication, user preferences) will be persisted to localStorage, while ephemeral state (conversation history) will remain in memory.

### 9.4 WebSocket Integration & Real-Time Communication

#### 9.4.1 WebSocket Service Architecture

```typescript
class WebSocketService {
  private socket: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  connect(token: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.socket = new WebSocket(`wss://api.intellifin.com/ws?token=${token}`);
      
      this.socket.onopen = () => {
        this.reconnectAttempts = 0;
        resolve();
      };
      
      this.socket.onmessage = (event) => {
        this.handleMessage(JSON.parse(event.data));
      };
      
      this.socket.onclose = () => {
        this.handleDisconnect();
      };
      
      this.socket.onerror = (error) => {
        reject(error);
      };
    });
  }

  sendCommand(command: string): void {
    if (this.socket?.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify({
        type: 'COMMAND',
        payload: { command }
      }));
    }
  }

  private handleMessage(message: WebSocketMessage): void {
    switch (message.type) {
      case 'CONVERSATION_RESPONSE':
        useConversationStore.getState().addMessage(message.payload);
        break;
      case 'FINANCIAL_UPDATE':
        useFinancialStore.getState().updateData(message.payload);
        break;
      case 'ERROR':
        // Handle error messages
        break;
    }
  }

  private handleDisconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      setTimeout(() => {
        this.reconnectAttempts++;
        this.connect(useAuthStore.getState().token);
      }, this.reconnectDelay * Math.pow(2, this.reconnectAttempts));
    }
  }
}
```

#### 9.4.2 Real-Time Update Patterns

*   **Message Broadcasting:** All conversational responses and financial updates will be broadcast via WebSocket to ensure real-time synchronization.
*   **Connection Resilience:** Automatic reconnection with exponential backoff and fallback to polling if WebSocket is unavailable.
*   **Message Queuing:** Messages sent while disconnected will be queued and sent upon reconnection.

### 9.5 Progressive Web App (PWA) Configuration

#### 9.5.1 PWA Setup

**Next.js PWA Configuration:**
```javascript
// next.config.js
const withPWA = require('next-pwa')({
  dest: 'public',
  register: true,
  skipWaiting: true,
  disable: process.env.NODE_ENV === 'development'
});

module.exports = withPWA({
  // Other Next.js configuration
});
```

**PWA Manifest:**
```json
{
  "name": "IntelliFin - Smart Financial Management",
  "short_name": "IntelliFin",
  "description": "AI-powered financial management for Zambian SMEs",
  "start_url": "/dashboard",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#2563eb",
  "icons": [
    {
      "src": "/icons/icon-192x192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-512x512.png",
      "sizes": "512x512",
      "type": "image/png"
    }
  ]
}
```

#### 9.5.2 Offline Capabilities

*   **Service Worker:** Implement service worker for caching critical resources and enabling offline functionality.
*   **Offline Queue:** Queue user actions when offline and sync when connection is restored.
*   **Cache Strategy:** Cache static assets and API responses for improved performance.

### 9.6 Performance Optimization

#### 9.6.1 Code Splitting & Lazy Loading

*   **Route-Based Splitting:** Each major route will be code-split to reduce initial bundle size.
*   **Component Lazy Loading:** Heavy components (e.g., financial charts) will be lazy-loaded.
*   **Dynamic Imports:** Use Next.js dynamic imports for conditional component loading.

#### 9.6.2 Rendering Optimization

*   **React.memo:** Wrap pure components with React.memo to prevent unnecessary re-renders.
*   **useMemo & useCallback:** Optimize expensive calculations and callback functions.
*   **Virtual Scrolling:** Implement virtual scrolling for large lists (e.g., transaction history).

#### 9.6.3 Bundle Optimization

*   **Tree Shaking:** Ensure proper tree shaking for all dependencies.
*   **Bundle Analysis:** Regular bundle analysis to identify and eliminate unnecessary dependencies.
*   **Image Optimization:** Use Next.js Image component for automatic image optimization.

### 9.7 Error Handling & User Experience

#### 9.7.1 Error Boundaries

```typescript
class ErrorBoundary extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error to monitoring service
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <ErrorFallback error={this.state.error} />;
    }

    return this.props.children;
  }
}
```

#### 9.7.2 Loading States & Feedback

*   **Skeleton Loading:** Implement skeleton loading for data-heavy components.
*   **Progressive Loading:** Show partial data as it becomes available.
*   **Toast Notifications:** Provide immediate feedback for user actions.
*   **Error Messages:** Clear, actionable error messages with recovery options.

### 9.8 Testing Strategy

#### 9.8.1 Testing Pyramid

*   **Unit Tests:** Test individual components and utilities in isolation.
*   **Integration Tests:** Test component interactions and API integrations.
*   **E2E Tests:** Test complete user workflows using Playwright.

#### 9.8.2 Testing Tools & Patterns

*   **React Testing Library:** For component testing with user-centric approach.
*   **Jest:** For unit testing and mocking.
*   **MSW (Mock Service Worker):** For API mocking in tests.
*   **Playwright:** For end-to-end testing across browsers.

### 9.9 Build & Deployment Configuration

#### 9.9.1 Environment Configuration

```typescript
// Environment variables structure
interface Environment {
  NEXT_PUBLIC_API_URL: string;
  NEXT_PUBLIC_WS_URL: string;
  NEXT_PUBLIC_APP_ENV: 'development' | 'staging' | 'production';
  NEXT_PUBLIC_ANALYTICS_ID?: string;
}
```

#### 9.9.2 Build Optimization

*   **Production Build:** Optimized for performance with minification and tree shaking.
*   **Bundle Analysis:** Regular analysis to maintain optimal bundle sizes.
*   **CDN Integration:** Static assets served via CDN for improved global performance.

This frontend architecture provides a solid foundation for building IntelliFin's sophisticated conversational interface while maintaining code quality, performance, and developer experience.

---