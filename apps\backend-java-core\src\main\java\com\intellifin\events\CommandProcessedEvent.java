package com.intellifin.events;

import java.time.LocalDateTime;
import java.util.UUID;

public class CommandProcessedEvent {
    private String eventId;
    private String userId;
    private String sessionId;
    private String command;
    private String intentName;
    private String response;
    private boolean success;
    private long processingTimeMs;
    private LocalDateTime timestamp;

    public CommandProcessedEvent(String userId, String sessionId, String command,
                               String intentName, String response, boolean success, long processingTimeMs) {
        this.eventId = UUID.randomUUID().toString();
        this.userId = userId;
        this.sessionId = sessionId;
        this.command = command;
        this.intentName = intentName;
        this.response = response;
        this.success = success;
        this.processingTimeMs = processingTimeMs;
        this.timestamp = LocalDateTime.now();
    }

    // Getters
    public String getEventId() { return eventId; }
    public String getUserId() { return userId; }
    public String getSessionId() { return sessionId; }
    public String getCommand() { return command; }
    public String getIntentName() { return intentName; }
    public String getResponse() { return response; }
    public boolean isSuccess() { return success; }
    public long getProcessingTimeMs() { return processingTimeMs; }
    public LocalDateTime getTimestamp() { return timestamp; }
}
