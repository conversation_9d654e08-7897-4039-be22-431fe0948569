"""
RabbitMQ implementation of the messaging service
"""
import asyncio
import json
import logging
from typing import Dict, Callable
import aio_pika
from aio_pika import ExchangeType, DeliveryMode
from .messaging_service import MessagingService, MessageEvent

logger = logging.getLogger(__name__)


class RabbitMQMessagingService(MessagingService):
    """RabbitMQ implementation of messaging service"""
    
    def __init__(self, host: str, port: int, username: str, password: str, virtual_host: str = '/'):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.virtual_host = virtual_host
        self.connection = None
        self.channel = None
        self.exchanges: Dict[str, aio_pika.Exchange] = {}
        self.queues: Dict[str, aio_pika.Queue] = {}
        self.consumers: Dict[str, Callable] = {}
    
    async def start(self) -> None:
        """Start the RabbitMQ connection"""
        try:
            connection_url = f"amqp://{self.username}:{self.password}@{self.host}:{self.port}{self.virtual_host}"
            self.connection = await aio_pika.connect_robust(
                connection_url,
                heartbeat=600,
                blocked_connection_timeout=300,
            )
            self.channel = await self.connection.channel()
            await self.channel.set_qos(prefetch_count=10)
            
            logger.info(f"Connected to RabbitMQ at {self.host}:{self.port}")
            
            # Declare exchanges and queues for transaction categorization
            await self._setup_categorization_infrastructure()
            
        except Exception as e:
            logger.error(f"Failed to connect to RabbitMQ: {str(e)}")
            raise
    
    async def stop(self) -> None:
        """Stop the RabbitMQ connection"""
        try:
            if self.connection and not self.connection.is_closed:
                await self.connection.close()
                logger.info("Disconnected from RabbitMQ")
        except Exception as e:
            logger.error(f"Error closing RabbitMQ connection: {str(e)}")
    
    async def _setup_categorization_infrastructure(self) -> None:
        """Set up exchanges and queues for transaction categorization"""
        # Declare topic exchanges
        exchanges_to_create = [
            "transaction.categorization.requested",
            "transaction.categorized",
            "categorization.accepted",
            "categorization.rejected",
            "bulk.categorization.completed"
        ]
        
        for exchange_name in exchanges_to_create:
            exchange = await self.channel.declare_exchange(
                exchange_name,
                ExchangeType.TOPIC,
                durable=True
            )
            self.exchanges[exchange_name] = exchange
            logger.info(f"Declared exchange: {exchange_name}")
        
        # Declare queues for AI service consumption
        ai_service_queues = [
            ("transaction.categorization.requested.ai", "transaction.categorization.requested"),
            ("categorization.accepted.ai", "categorization.accepted"),
            ("categorization.rejected.ai", "categorization.rejected")
        ]
        
        for queue_name, exchange_name in ai_service_queues:
            queue = await self.channel.declare_queue(
                queue_name,
                durable=True,
                arguments={
                    "x-dead-letter-exchange": f"{exchange_name}.dlx",
                    "x-dead-letter-routing-key": "failed",
                    "x-message-ttl": 3600000,  # 1 hour TTL
                }
            )
            
            # Bind queue to exchange
            await queue.bind(self.exchanges[exchange_name], routing_key="#")
            self.queues[queue_name] = queue
            logger.info(f"Declared and bound queue: {queue_name}")
            
            # Declare dead letter exchange and queue
            dlx_exchange = await self.channel.declare_exchange(
                f"{exchange_name}.dlx",
                ExchangeType.DIRECT,
                durable=True
            )
            
            dlx_queue = await self.channel.declare_queue(
                f"{queue_name}.dlq",
                durable=True
            )
            await dlx_queue.bind(dlx_exchange, routing_key="failed")
            logger.info(f"Declared dead letter queue: {queue_name}.dlq")
    
    async def publish_event(self, topic: str, event: MessageEvent) -> bool:
        """Publish an event to the specified topic"""
        try:
            if topic not in self.exchanges:
                logger.error(f"Exchange not found: {topic}")
                return False
            
            exchange = self.exchanges[topic]
            
            message_body = json.dumps({
                "event_type": event.event_type,
                "correlation_id": event.correlation_id,
                "timestamp": event.timestamp,
                "payload": event.payload
            })
            
            message = aio_pika.Message(
                message_body.encode(),
                content_type="application/json",
                delivery_mode=DeliveryMode.PERSISTENT,
                correlation_id=event.correlation_id,
                headers={
                    "event_type": event.event_type,
                    "timestamp": event.timestamp
                }
            )
            
            await exchange.publish(message, routing_key=event.event_type)
            logger.info(f"Published event {event.event_type} to {topic}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to publish event to {topic}: {str(e)}")
            await self._handle_publish_error(topic, event, e)
            return False
    
    async def subscribe(self, topic: str, handler: Callable[[MessageEvent], None]) -> None:
        """Subscribe to events on the specified topic"""
        try:
            queue_name = f"{topic}.ai"
            if queue_name not in self.queues:
                logger.error(f"Queue not found: {queue_name}")
                return
            
            queue = self.queues[queue_name]
            self.consumers[topic] = handler
            
            async def message_handler(message: aio_pika.IncomingMessage):
                try:
                    async with message.process(requeue=False):
                        event_data = json.loads(message.body.decode())
                        event = MessageEvent(
                            event_type=event_data["event_type"],
                            correlation_id=event_data["correlation_id"],
                            timestamp=event_data["timestamp"],
                            payload=event_data["payload"]
                        )
                        
                        await handler(event)
                        logger.info(f"Successfully processed message: {event.correlation_id}")
                        
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to decode message: {str(e)}")
                    # Don't requeue malformed messages
                    
                except Exception as e:
                    logger.error(f"Error processing message: {str(e)}")
                    # Let the message be requeued for retry
                    raise
            
            await queue.consume(message_handler)
            logger.info(f"Subscribed to topic: {topic}")
            
        except Exception as e:
            logger.error(f"Failed to subscribe to {topic}: {str(e)}")
            raise
    
    async def _handle_publish_error(self, topic: str, event: MessageEvent, error: Exception) -> None:
        """Handle publish errors by sending to dead letter queue"""
        try:
            dlx_exchange_name = f"{topic}.dlx"
            if dlx_exchange_name in self.exchanges:
                dlx_exchange = self.exchanges[dlx_exchange_name]
                
                error_message = json.dumps({
                    "original_event": {
                        "event_type": event.event_type,
                        "correlation_id": event.correlation_id,
                        "timestamp": event.timestamp,
                        "payload": event.payload
                    },
                    "error": str(error),
                    "failed_at": event.timestamp
                })
                
                message = aio_pika.Message(
                    error_message.encode(),
                    content_type="application/json",
                    delivery_mode=DeliveryMode.PERSISTENT
                )
                
                await dlx_exchange.publish(message, routing_key="failed")
                logger.info(f"Sent failed event to dead letter queue: {event.correlation_id}")
                
        except Exception as dlx_error:
            logger.error(f"Failed to send to dead letter queue: {str(dlx_error)}")
    
    async def get_queue_stats(self, queue_name: str) -> Dict:
        """Get statistics for a specific queue"""
        try:
            if queue_name in self.queues:
                queue = self.queues[queue_name]
                return {
                    "name": queue_name,
                    "message_count": queue.declaration_result.message_count,
                    "consumer_count": queue.declaration_result.consumer_count
                }
        except Exception as e:
            logger.error(f"Failed to get queue stats for {queue_name}: {str(e)}")
        return {}
