package com.intellifin.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Service for handling file storage operations
 * In production, this would integrate with cloud storage (AWS S3, Azure Blob, etc.)
 */
@Service
@Slf4j
public class FileStorageService {

    @Value("${file.storage.base-path:./uploads}")
    private String basePath;

    @Value("${file.storage.use-cloud:false}")
    private boolean useCloudStorage;

    @Value("${file.storage.cloud.bucket:intellifin-documents}")
    private String cloudBucket;

    @Value("${app.base-url:http://localhost:8080}")
    private String baseUrl;

    /**
     * Generate pre-signed upload URL for document upload
     */
    public String generateUploadUrl(String uploadId, String fileName) {
        log.debug("Generating upload URL for uploadId: {}, fileName: {}", uploadId, fileName);

        if (useCloudStorage) {
            return generateCloudUploadUrl(uploadId, fileName);
        } else {
            return generateLocalUploadUrl(uploadId, fileName);
        }
    }

    /**
     * Get file path for stored document
     */
    public String getFilePath(String uploadId, String fileName) {
        if (useCloudStorage) {
            return getCloudFilePath(uploadId, fileName);
        } else {
            return getLocalFilePath(uploadId, fileName);
        }
    }

    /**
     * Store file content (for local storage)
     */
    public void storeFile(String uploadId, String fileName, byte[] content) throws IOException {
        if (useCloudStorage) {
            storeFileInCloud(uploadId, fileName, content);
        } else {
            storeFileLocally(uploadId, fileName, content);
        }
    }

    /**
     * Delete file
     */
    public void deleteFile(String uploadId, String fileName) {
        if (useCloudStorage) {
            deleteFileFromCloud(uploadId, fileName);
        } else {
            deleteFileLocally(uploadId, fileName);
        }
    }

    /**
     * Check if file exists
     */
    public boolean fileExists(String uploadId, String fileName) {
        if (useCloudStorage) {
            return cloudFileExists(uploadId, fileName);
        } else {
            return localFileExists(uploadId, fileName);
        }
    }

    /**
     * Generate local upload URL
     */
    private String generateLocalUploadUrl(String uploadId, String fileName) {
        // In a real implementation, this would generate a secure upload endpoint
        // For now, return a mock URL that the frontend can use
        return baseUrl + "/api/v1/financial-accounts/upload/" + uploadId + "/file";
    }

    /**
     * Generate cloud upload URL (AWS S3, Azure Blob, etc.)
     */
    private String generateCloudUploadUrl(String uploadId, String fileName) {
        // In production, this would generate a pre-signed URL for cloud storage
        // Example for AWS S3:
        // return s3Client.generatePresignedUrl(bucket, getCloudFilePath(uploadId, fileName), expiration);
        
        log.warn("Cloud storage not implemented, using local storage");
        return generateLocalUploadUrl(uploadId, fileName);
    }

    /**
     * Get local file path
     */
    private String getLocalFilePath(String uploadId, String fileName) {
        String datePrefix = OffsetDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        return Paths.get(basePath, "documents", datePrefix, uploadId, sanitizeFileName(fileName)).toString();
    }

    /**
     * Get cloud file path
     */
    private String getCloudFilePath(String uploadId, String fileName) {
        String datePrefix = OffsetDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        return "documents/" + datePrefix + "/" + uploadId + "/" + sanitizeFileName(fileName);
    }

    /**
     * Store file locally
     */
    private void storeFileLocally(String uploadId, String fileName, byte[] content) throws IOException {
        String filePath = getLocalFilePath(uploadId, fileName);
        Path path = Paths.get(filePath);
        
        // Create directories if they don't exist
        Files.createDirectories(path.getParent());
        
        // Write file content
        Files.write(path, content);
        
        log.info("File stored locally: {}", filePath);
    }

    /**
     * Store file in cloud storage
     */
    private void storeFileInCloud(String uploadId, String fileName, byte[] content) {
        // In production, implement cloud storage upload
        // Example for AWS S3:
        // s3Client.putObject(bucket, getCloudFilePath(uploadId, fileName), content);
        
        log.warn("Cloud storage not implemented, falling back to local storage");
        try {
            storeFileLocally(uploadId, fileName, content);
        } catch (IOException e) {
            throw new RuntimeException("Failed to store file", e);
        }
    }

    /**
     * Delete file locally
     */
    private void deleteFileLocally(String uploadId, String fileName) {
        try {
            String filePath = getLocalFilePath(uploadId, fileName);
            Path path = Paths.get(filePath);
            Files.deleteIfExists(path);
            log.info("File deleted locally: {}", filePath);
        } catch (IOException e) {
            log.error("Failed to delete local file: {}/{}", uploadId, fileName, e);
        }
    }

    /**
     * Delete file from cloud storage
     */
    private void deleteFileFromCloud(String uploadId, String fileName) {
        // In production, implement cloud storage deletion
        // Example for AWS S3:
        // s3Client.deleteObject(bucket, getCloudFilePath(uploadId, fileName));
        
        log.warn("Cloud storage not implemented, falling back to local storage");
        deleteFileLocally(uploadId, fileName);
    }

    /**
     * Check if local file exists
     */
    private boolean localFileExists(String uploadId, String fileName) {
        String filePath = getLocalFilePath(uploadId, fileName);
        return Files.exists(Paths.get(filePath));
    }

    /**
     * Check if cloud file exists
     */
    private boolean cloudFileExists(String uploadId, String fileName) {
        // In production, implement cloud storage existence check
        // Example for AWS S3:
        // return s3Client.doesObjectExist(bucket, getCloudFilePath(uploadId, fileName));
        
        log.warn("Cloud storage not implemented, falling back to local storage");
        return localFileExists(uploadId, fileName);
    }

    /**
     * Sanitize file name to prevent path traversal attacks
     */
    private String sanitizeFileName(String fileName) {
        if (fileName == null) {
            return "unknown";
        }
        
        // Remove path separators and other dangerous characters
        return fileName.replaceAll("[^a-zA-Z0-9._-]", "_");
    }

    /**
     * Initialize storage directories
     */
    public void initializeStorage() {
        if (!useCloudStorage) {
            try {
                Path documentsPath = Paths.get(basePath, "documents");
                Files.createDirectories(documentsPath);
                log.info("Initialized local storage at: {}", documentsPath.toAbsolutePath());
            } catch (IOException e) {
                log.error("Failed to initialize local storage", e);
                throw new RuntimeException("Storage initialization failed", e);
            }
        }
    }
}
