package com.intellifin.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * Entity representing transaction drafts for manual entry
 */
@Entity
@Table(name = "transaction_drafts")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionDraft {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    @Column(name = "user_id", nullable = false)
    private UUID userId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;

    @Column(name = "draft_name")
    private String draftName;

    @Column(name = "description", nullable = false)
    private String description;

    @Column(name = "amount", nullable = false, precision = 15, scale = 2)
    private BigDecimal amount;

    @Enumerated(EnumType.STRING)
    @Column(name = "transaction_type", nullable = false)
    private Transaction.TransactionType transactionType;

    @Column(name = "transaction_date", nullable = false)
    private LocalDate transactionDate;

    @Column(name = "category_id")
    private UUID categoryId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "category_id", insertable = false, updatable = false)
    private Category category;

    @Column(name = "notes", columnDefinition = "text")
    private String notes;

    @Column(name = "suggested_category_id")
    private UUID suggestedCategoryId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "suggested_category_id", insertable = false, updatable = false)
    private Category suggestedCategory;

    @Column(name = "ai_confidence", precision = 3, scale = 2)
    private BigDecimal aiConfidence;

    @Column(name = "ai_explanation", columnDefinition = "text")
    private String aiExplanation;

    @Column(name = "form_data", columnDefinition = "jsonb")
    private String formData;

    @Column(name = "is_auto_saved")
    private Boolean isAutoSaved = true;

    @Column(name = "expires_at")
    private OffsetDateTime expiresAt;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false)
    private OffsetDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private OffsetDateTime updatedAt;

    /**
     * Check if draft is expired
     */
    public boolean isExpired() {
        return expiresAt != null && expiresAt.isBefore(OffsetDateTime.now());
    }

    /**
     * Check if draft is complete enough to create transaction
     */
    public boolean isComplete() {
        return description != null && !description.trim().isEmpty() &&
               amount != null && amount.compareTo(BigDecimal.ZERO) > 0 &&
               transactionType != null &&
               transactionDate != null;
    }

    /**
     * Check if draft has AI suggestion
     */
    public boolean hasAISuggestion() {
        return suggestedCategoryId != null && aiConfidence != null;
    }

    /**
     * Set expiration date (default 7 days from now)
     */
    public void setDefaultExpiration() {
        this.expiresAt = OffsetDateTime.now().plusDays(7);
    }

    /**
     * Mark as manually saved (not auto-saved)
     */
    public void markAsManuallySaved() {
        this.isAutoSaved = false;
    }

    /**
     * Update AI suggestion
     */
    public void updateAISuggestion(UUID categoryId, BigDecimal confidence, String explanation) {
        this.suggestedCategoryId = categoryId;
        this.aiConfidence = confidence;
        this.aiExplanation = explanation;
    }

    /**
     * Clear AI suggestion
     */
    public void clearAISuggestion() {
        this.suggestedCategoryId = null;
        this.aiConfidence = null;
        this.aiExplanation = null;
    }
}
