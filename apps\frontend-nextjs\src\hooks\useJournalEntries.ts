'use client';

import { useState, useCallback } from 'react';
import { JournalEntry, JournalEntryValidationResponse } from '@intellifin/data-models';
import { apiClient } from '@/services/api/client';

interface UseJournalEntriesReturn {
  entries: JournalEntry[];
  loading: boolean;
  error: string | null;
  refreshEntries: () => Promise<void>;
  createEntry: (entry: Omit<JournalEntry, 'id' | 'entryNumber' | 'createdAt' | 'updatedAt'>) => Promise<JournalEntry>;
  updateEntry: (id: string, entry: Partial<JournalEntry>) => Promise<JournalEntry>;
  deleteEntry: (id: string) => Promise<void>;
  postEntry: (id: string) => Promise<JournalEntry>;
  reverseEntry: (id: string, reason: string) => Promise<JournalEntry>;
  validateEntry: (entry: JournalEntry) => Promise<JournalEntryValidationResponse>;
  getAccountBalance: (accountId: string) => Promise<number>;
}

export function useJournalEntries(): UseJournalEntriesReturn {
  const [entries, setEntries] = useState<JournalEntry[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const refreshEntries = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.get('/api/v1/journal/entries');
      setEntries(response.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load journal entries');
    } finally {
      setLoading(false);
    }
  }, []);

  const createEntry = useCallback(async (entry: Omit<JournalEntry, 'id' | 'entryNumber' | 'createdAt' | 'updatedAt'>): Promise<JournalEntry> => {
    try {
      const response = await apiClient.post('/api/v1/journal/entries', entry);
      const newEntry = response.data;
      setEntries(prev => [newEntry, ...prev]);
      return newEntry;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create journal entry';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  const updateEntry = useCallback(async (id: string, entry: Partial<JournalEntry>): Promise<JournalEntry> => {
    try {
      const response = await apiClient.put(`/api/v1/journal/entries/${id}`, entry);
      const updatedEntry = response.data;
      setEntries(prev => prev.map(e => e.id === id ? updatedEntry : e));
      return updatedEntry;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update journal entry';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  const deleteEntry = useCallback(async (id: string): Promise<void> => {
    try {
      await apiClient.delete(`/api/v1/journal/entries/${id}`);
      setEntries(prev => prev.filter(e => e.id !== id));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete journal entry';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  const postEntry = useCallback(async (id: string): Promise<JournalEntry> => {
    try {
      const response = await apiClient.post(`/api/v1/journal/entries/${id}/post`);
      const postedEntry = response.data;
      setEntries(prev => prev.map(e => e.id === id ? postedEntry : e));
      return postedEntry;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to post journal entry';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  const reverseEntry = useCallback(async (id: string, reason: string): Promise<JournalEntry> => {
    try {
      const response = await apiClient.post(`/api/v1/journal/entries/${id}/reverse`, null, {
        params: { reason }
      });
      const reversalEntry = response.data;

      // Update the original entry status and add the reversal entry
      setEntries(prev => {
        const updated = prev.map(e =>
          e.id === id ? { ...e, status: 'REVERSED' as const } : e
        );
        return [reversalEntry, ...updated];
      });

      return reversalEntry;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to reverse journal entry';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  const validateEntry = useCallback(async (entry: JournalEntry): Promise<JournalEntryValidationResponse> => {
    try {
      const response = await apiClient.post('/api/v1/journal/entries/validate', entry);
      return response.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to validate journal entry';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  const getAccountBalance = useCallback(async (accountId: string): Promise<number> => {
    try {
      const response = await apiClient.get(`/api/v1/journal/accounts/${accountId}/balance`);
      return response.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get account balance';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  return {
    entries,
    loading,
    error,
    refreshEntries,
    createEntry,
    updateEntry,
    deleteEntry,
    postEntry,
    reverseEntry,
    validateEntry,
    getAccountBalance
  };
}
