-- Migration V5: Add last_login_at column to users table
-- This migration adds the missing lastLoginAt field to support authentication tracking

-- Add last_login_at column to users table (only if it doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'users' AND column_name = 'last_login_at'
    ) THEN
        ALTER TABLE users ADD COLUMN last_login_at TIMESTAMP;
    END IF;
END $$;

-- Add comment for documentation
COMMENT ON COLUMN users.last_login_at IS 'Timestamp of the user''s last successful login';

-- Create index for performance on login queries (only if it doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes
        WHERE tablename = 'users' AND indexname = 'idx_users_last_login_at'
    ) THEN
        CREATE INDEX idx_users_last_login_at ON users(last_login_at);
    END IF;
END $$;
