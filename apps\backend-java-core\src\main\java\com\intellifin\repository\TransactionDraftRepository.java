package com.intellifin.repository;

import com.intellifin.model.TransactionDraft;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository for TransactionDraft entities
 */
@Repository
public interface TransactionDraftRepository extends JpaRepository<TransactionDraft, UUID> {

    /**
     * Find all drafts for a user
     */
    Page<TransactionDraft> findByUserIdOrderByUpdatedAtDesc(UUID userId, Pageable pageable);

    /**
     * Find all drafts for a user (list)
     */
    List<TransactionDraft> findByUserIdOrderByUpdatedAtDesc(UUID userId);

    /**
     * Find draft by user and draft name
     */
    Optional<TransactionDraft> findByUserIdAndDraftName(UUID userId, String draftName);

    /**
     * Find auto-saved drafts for a user
     */
    List<TransactionDraft> findByUserIdAndIsAutoSavedTrueOrderByUpdatedAtDesc(UUID userId);

    /**
     * Find manually saved drafts for a user
     */
    List<TransactionDraft> findByUserIdAndIsAutoSavedFalseOrderByUpdatedAtDesc(UUID userId);

    /**
     * Find expired drafts
     */
    @Query("SELECT d FROM TransactionDraft d WHERE d.expiresAt IS NOT NULL AND d.expiresAt < :now")
    List<TransactionDraft> findExpiredDrafts(@Param("now") OffsetDateTime now);

    /**
     * Find drafts that should be auto-expired (old auto-saved drafts)
     */
    @Query("SELECT d FROM TransactionDraft d WHERE d.isAutoSaved = true AND d.expiresAt IS NULL AND d.createdAt < :cutoffDate")
    List<TransactionDraft> findDraftsToAutoExpire(@Param("cutoffDate") OffsetDateTime cutoffDate);

    /**
     * Find recent drafts for a user (last 7 days)
     */
    @Query("SELECT d FROM TransactionDraft d WHERE d.userId = :userId AND d.createdAt >= :since ORDER BY d.updatedAt DESC")
    List<TransactionDraft> findRecentDrafts(@Param("userId") UUID userId, @Param("since") OffsetDateTime since);

    /**
     * Count drafts for a user
     */
    long countByUserId(UUID userId);

    /**
     * Count auto-saved drafts for a user
     */
    long countByUserIdAndIsAutoSavedTrue(UUID userId);

    /**
     * Count manually saved drafts for a user
     */
    long countByUserIdAndIsAutoSavedFalse(UUID userId);

    /**
     * Find drafts with AI suggestions
     */
    @Query("SELECT d FROM TransactionDraft d WHERE d.userId = :userId AND d.suggestedCategoryId IS NOT NULL ORDER BY d.updatedAt DESC")
    List<TransactionDraft> findDraftsWithAISuggestions(@Param("userId") UUID userId);

    /**
     * Find drafts by category
     */
    List<TransactionDraft> findByUserIdAndCategoryIdOrderByUpdatedAtDesc(UUID userId, UUID categoryId);

    /**
     * Find drafts by suggested category
     */
    List<TransactionDraft> findByUserIdAndSuggestedCategoryIdOrderByUpdatedAtDesc(UUID userId, UUID categoryId);

    /**
     * Delete expired drafts
     */
    @Modifying
    @Query("DELETE FROM TransactionDraft d WHERE d.expiresAt IS NOT NULL AND d.expiresAt < :now")
    int deleteExpiredDrafts(@Param("now") OffsetDateTime now);

    /**
     * Delete old auto-saved drafts for a user (keep only recent ones)
     */
    @Modifying
    @Query("DELETE FROM TransactionDraft d WHERE d.userId = :userId AND d.isAutoSaved = true AND d.createdAt < :cutoffDate")
    int deleteOldAutoSavedDrafts(@Param("userId") UUID userId, @Param("cutoffDate") OffsetDateTime cutoffDate);

    /**
     * Update expiration date for old drafts
     */
    @Modifying
    @Query("UPDATE TransactionDraft d SET d.expiresAt = :expiresAt WHERE d.isAutoSaved = true AND d.expiresAt IS NULL AND d.createdAt < :cutoffDate")
    int updateExpirationForOldDrafts(@Param("expiresAt") OffsetDateTime expiresAt, @Param("cutoffDate") OffsetDateTime cutoffDate);

    /**
     * Find drafts that are complete enough to create transactions
     */
    @Query("SELECT d FROM TransactionDraft d WHERE d.userId = :userId AND " +
           "d.description IS NOT NULL AND d.description != '' AND " +
           "d.amount IS NOT NULL AND d.amount > 0 AND " +
           "d.transactionType IS NOT NULL AND " +
           "d.transactionDate IS NOT NULL " +
           "ORDER BY d.updatedAt DESC")
    List<TransactionDraft> findCompleteDrafts(@Param("userId") UUID userId);

    /**
     * Search drafts by description
     */
    @Query("SELECT d FROM TransactionDraft d WHERE d.userId = :userId AND " +
           "LOWER(d.description) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
           "ORDER BY d.updatedAt DESC")
    List<TransactionDraft> searchDraftsByDescription(@Param("userId") UUID userId, @Param("searchTerm") String searchTerm);

    /**
     * Find the most recent auto-saved draft for a user
     */
    @Query("SELECT d FROM TransactionDraft d WHERE d.userId = :userId AND d.isAutoSaved = true " +
           "ORDER BY d.updatedAt DESC LIMIT 1")
    Optional<TransactionDraft> findMostRecentAutoSavedDraft(@Param("userId") UUID userId);
}
