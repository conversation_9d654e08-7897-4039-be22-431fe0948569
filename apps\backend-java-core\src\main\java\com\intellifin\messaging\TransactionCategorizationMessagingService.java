package com.intellifin.messaging;

import com.intellifin.messaging.events.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.context.annotation.Bean;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import java.util.function.Consumer;

/**
 * Service for handling transaction categorization messaging
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TransactionCategorizationMessagingService {

    private final StreamBridge streamBridge;

    /**
     * Publish transaction categorization request to AI service
     */
    public void publishCategorizationRequest(TransactionCategorizationRequestedEvent event) {
        try {
            log.info("Publishing categorization request for transaction: {}", event.getTransactionId());
            
            Message<TransactionCategorizationRequestedEvent> message = MessageBuilder
                    .withPayload(event)
                    .setHeader("eventType", "TransactionCategorizationRequested")
                    .setHeader("transactionId", event.getTransactionId().toString())
                    .setHeader("userId", event.getUserId().toString())
                    .build();

            boolean sent = streamBridge.send("transactionCategorizationRequested-out-0", message);
            
            if (sent) {
                log.info("Successfully published categorization request for transaction: {}", event.getTransactionId());
            } else {
                log.error("Failed to publish categorization request for transaction: {}", event.getTransactionId());
            }
        } catch (Exception e) {
            log.error("Error publishing categorization request for transaction: {}", event.getTransactionId(), e);
            throw new com.intellifin.exception.MessagingException("Failed to publish categorization request", e);
        }
    }

    /**
     * Publish categorization acceptance feedback
     */
    public void publishCategorizationAccepted(CategorizationAcceptedEvent event) {
        try {
            log.info("Publishing categorization acceptance for transaction: {}", event.getTransactionId());
            
            Message<CategorizationAcceptedEvent> message = MessageBuilder
                    .withPayload(event)
                    .setHeader("eventType", "CategorizationAccepted")
                    .setHeader("transactionId", event.getTransactionId().toString())
                    .build();

            boolean sent = streamBridge.send("categorizationAccepted-out-0", message);
            
            if (sent) {
                log.info("Successfully published categorization acceptance for transaction: {}", event.getTransactionId());
            } else {
                log.error("Failed to publish categorization acceptance for transaction: {}", event.getTransactionId());
            }
        } catch (Exception e) {
            log.error("Error publishing categorization acceptance for transaction: {}", event.getTransactionId(), e);
        }
    }

    /**
     * Publish categorization rejection feedback
     */
    public void publishCategorizationRejected(CategorizationRejectedEvent event) {
        try {
            log.info("Publishing categorization rejection for transaction: {}", event.getTransactionId());
            
            Message<CategorizationRejectedEvent> message = MessageBuilder
                    .withPayload(event)
                    .setHeader("eventType", "CategorizationRejected")
                    .setHeader("transactionId", event.getTransactionId().toString())
                    .build();

            boolean sent = streamBridge.send("categorizationRejected-out-0", message);
            
            if (sent) {
                log.info("Successfully published categorization rejection for transaction: {}", event.getTransactionId());
            } else {
                log.error("Failed to publish categorization rejection for transaction: {}", event.getTransactionId());
            }
        } catch (Exception e) {
            log.error("Error publishing categorization rejection for transaction: {}", event.getTransactionId(), e);
        }
    }

    /**
     * Consumer for transaction categorized events from AI service
     */
    @Bean
    public Consumer<TransactionCategorizedEvent> transactionCategorized() {
        return event -> {
            try {
                log.info("Received transaction categorized event for transaction: {}", event.getTransactionId());

                if (transactionService != null) {
                    transactionService.handleAICategorizationResult(event);
                } else {
                    log.warn("TransactionService not available for handling AI categorization result");
                }

            } catch (Exception e) {
                log.error("Error processing transaction categorized event for transaction: {}",
                         event.getTransactionId(), e);
                throw e; // Re-throw to trigger dead letter queue
            }
        };
    }

    /**
     * Consumer for bulk categorization completed events
     */
    @Bean
    public Consumer<BulkCategorizationCompletedEvent> bulkCategorizationCompleted() {
        return event -> {
            try {
                log.info("Received bulk categorization completed event for batch: {}", event.getBatchId());
                log.info("Processed: {}, Success: {}, Failures: {}",
                        event.getProcessedCount(),
                        event.getSuccessCount(),
                        event.getFailureCount());

                // This will be handled by the TransactionService for batch status updates

            } catch (Exception e) {
                log.error("Error processing bulk categorization completed event for batch: {}",
                         event.getBatchId(), e);
                throw e; // Re-throw to trigger dead letter queue
            }
        };
    }

    /**
     * Set the transaction service for handling AI categorization results
     */
    public void setTransactionService(com.intellifin.service.TransactionService transactionService) {
        this.transactionService = transactionService;
    }

    private com.intellifin.service.TransactionService transactionService;
}
