'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  JournalEntryPreview, 
  JournalEntryInlinePreview,
  AccountBalancePreview,
  AccountBalanceInlinePreview,
  TransactionJournalPreview,
  TransactionJournalInlinePreview
} from '@/components/conversation/previews';
import { Eye, MessageSquare, Zap } from 'lucide-react';

// Mock data for demonstration
const mockJournalEntry = {
  id: 'je-001',
  userId: 'user-001',
  entryNumber: 'JE-2024-001',
  entryDate: '2024-01-15',
  description: 'Payment received from ZESCO for electricity services',
  reference: 'INV-2024-001',
  status: 'POSTED' as const,
  isBalanced: true,
  totalAmount: 2500.00,
  createdAt: '2024-01-15T10:30:00Z',
  updatedAt: '2024-01-15T10:30:00Z',
  createdBy: 'user-001',
  lines: [
    {
      id: 'jel-001',
      journalEntryId: 'je-001',
      accountId: 'acc-cash',
      accountName: 'Cash - Stanbic Bank',
      description: 'Payment received',
      debitAmount: 2500.00,
      creditAmount: 0,
      createdAt: '2024-01-15T10:30:00Z',
      updatedAt: '2024-01-15T10:30:00Z',
    },
    {
      id: 'jel-002',
      journalEntryId: 'je-001',
      accountId: 'acc-revenue',
      accountName: 'Service Revenue',
      description: 'Electricity services rendered',
      debitAmount: 0,
      creditAmount: 2500.00,
      createdAt: '2024-01-15T10:30:00Z',
      updatedAt: '2024-01-15T10:30:00Z',
    }
  ]
} as any;

const mockAccountBalances = [
  {
    accountId: 'acc-cash',
    accountName: 'Cash - Stanbic Bank',
    accountCode: '1001',
    accountType: 'ASSET' as const,
    balance: 15750.00,
    normalBalance: 'DEBIT' as const,
    lastUpdated: '2024-01-15T10:30:00Z',
    isActive: true
  },
  {
    accountId: 'acc-revenue',
    accountName: 'Service Revenue',
    accountCode: '4001',
    accountType: 'INCOME' as const,
    balance: -8500.00,
    normalBalance: 'CREDIT' as const,
    lastUpdated: '2024-01-15T10:30:00Z',
    isActive: true
  },
  {
    accountId: 'acc-expenses',
    accountName: 'Operating Expenses',
    accountCode: '5001',
    accountType: 'EXPENSE' as const,
    balance: 3200.00,
    normalBalance: 'DEBIT' as const,
    lastUpdated: '2024-01-15T10:30:00Z',
    isActive: true
  }
];

const mockTransaction = {
  id: 'txn-001',
  amount: 2500.00,
  transactionDate: '2024-01-15',
  description: 'Payment from ZESCO',
  type: 'CREDIT' as const,
  status: 'CONFIRMED' as const,
  categoryName: 'Service Revenue'
};

export default function PreviewsPage() {
  return (
    <div className="container mx-auto p-6 space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Conversational Preview Components
          </h1>
          <p className="text-lg text-gray-600 mb-4">
            Progressive Disclosure Pattern - Method 1: Conversational Previews
          </p>
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            <Eye className="w-4 h-4 mr-1" />
            Demo & Documentation
          </Badge>
        </div>
      </motion.div>

      {/* Pattern Explanation */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MessageSquare className="w-5 h-5 text-blue-600" />
            <span>Progressive Disclosure Pattern</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-700 mb-4">
            These preview components implement <strong>Method 1</strong> of our Progressive Disclosure pattern. 
            They provide simplified, read-only views of complex data that can be embedded directly in 
            AI chat responses, with clear CTAs to access full functionality.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="p-3 bg-green-50 rounded-lg border border-green-200">
              <h4 className="font-semibold text-green-800 mb-1">✓ Conversational Previews</h4>
              <p className="text-green-700">Embedded in AI responses with "View Full" CTAs</p>
            </div>
            <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
              <h4 className="font-semibold text-yellow-800 mb-1">⚡ Dashboard Vitals</h4>
              <p className="text-yellow-700">Actionable metrics with direct navigation</p>
            </div>
            <div className="p-3 bg-purple-50 rounded-lg border border-purple-200">
              <h4 className="font-semibold text-purple-800 mb-1">🔔 Toast Notifications</h4>
              <p className="text-purple-700">Real-time alerts with action links</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Journal Entry Previews */}
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <h2 className="text-2xl font-semibold mb-4">Journal Entry Previews</h2>
        
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-2">Full Preview Component</h3>
            <p className="text-sm text-gray-600 mb-4">
              Used in AI chat responses to show journal entry details with balance information and direct navigation.
            </p>
            <JournalEntryPreview entry={mockJournalEntry} />
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-medium mb-2">Inline Preview Component</h3>
            <p className="text-sm text-gray-600 mb-4">
              Used within chat message text for compact references.
            </p>
            <div className="p-4 bg-gray-50 rounded-lg">
              <p className="text-sm">
                I've successfully created journal entry{' '}
                <JournalEntryInlinePreview entry={mockJournalEntry} />{' '}
                for your ZESCO payment. The entry is balanced and ready for posting.
              </p>
            </div>
          </div>
        </div>
      </motion.section>

      {/* Account Balance Previews */}
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <h2 className="text-2xl font-semibold mb-4">Account Balance Previews</h2>
        
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-2">Full Balance Summary</h3>
            <p className="text-sm text-gray-600 mb-4">
              Shows account balances with balance sheet validation and account type categorization.
            </p>
            <AccountBalancePreview balances={mockAccountBalances} />
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-medium mb-2">Inline Balance Summary</h3>
            <p className="text-sm text-gray-600 mb-4">
              Compact balance overview for chat responses.
            </p>
            <div className="p-4 bg-gray-50 rounded-lg">
              <p className="text-sm">
                Your current financial position:{' '}
                <AccountBalanceInlinePreview 
                  totalAssets={15750} 
                  totalLiabilities={5000} 
                  totalEquity={10750} 
                />{' '}
                Your books are balanced and healthy!
              </p>
            </div>
          </div>
        </div>
      </motion.section>

      {/* Transaction-Journal Previews */}
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <h2 className="text-2xl font-semibold mb-4">Transaction-Journal Relationship</h2>
        
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-2">Full Relationship Preview</h3>
            <p className="text-sm text-gray-600 mb-4">
              Shows how transactions are converted to journal entries with double-entry accounting details.
            </p>
            <TransactionJournalPreview 
              transaction={mockTransaction} 
              journalEntry={mockJournalEntry} 
            />
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-medium mb-2">Inline Relationship Preview</h3>
            <p className="text-sm text-gray-600 mb-4">
              Compact view showing transaction-to-journal conversion status.
            </p>
            <div className="p-4 bg-gray-50 rounded-lg">
              <p className="text-sm">
                Your transaction{' '}
                <TransactionJournalInlinePreview 
                  transaction={mockTransaction} 
                  journalEntry={mockJournalEntry} 
                />{' '}
                has been processed and the journal entry is now posted to your ledger.
              </p>
            </div>
          </div>
        </div>
      </motion.section>

      {/* Usage Guidelines */}
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Zap className="w-5 h-5 text-yellow-600" />
              <span>Implementation Guidelines</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-2">When to Use Full Previews:</h4>
                <ul className="text-sm text-gray-700 space-y-1">
                  <li>• AI responses showing detailed results</li>
                  <li>• Summary views in chat history</li>
                  <li>• Educational explanations of accounting concepts</li>
                  <li>• Status updates with actionable information</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">When to Use Inline Previews:</h4>
                <ul className="text-sm text-gray-700 space-y-1">
                  <li>• References within conversational text</li>
                  <li>• Quick status indicators</li>
                  <li>• Compact notifications</li>
                  <li>• Progressive disclosure hints</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.section>
    </div>
  );
}
