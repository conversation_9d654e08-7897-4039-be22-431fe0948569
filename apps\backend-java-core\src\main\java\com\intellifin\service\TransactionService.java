package com.intellifin.service;

import com.intellifin.dto.*;
import com.intellifin.exception.*;
import com.intellifin.messaging.TransactionCategorizationMessagingService;
import com.intellifin.messaging.events.*;
import com.intellifin.model.Transaction;
import com.intellifin.model.Category;
import com.intellifin.repository.TransactionRepository;
import com.intellifin.repository.CategoryRepository;
import com.intellifin.websocket.TransactionWebSocketController;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Service for transaction management and categorization
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class TransactionService {

    private final TransactionRepository transactionRepository;
    private final CategoryRepository categoryRepository;
    private final TransactionCategorizationMessagingService messagingService;
    private final TransactionWebSocketController webSocketController;

    /**
     * Get paginated transactions for a user with filters
     */
    @Transactional(readOnly = true)
    public Page<TransactionDto> getTransactions(
            UUID userId,
            Transaction.TransactionStatus status,
            Transaction.TransactionType type,
            UUID categoryId,
            UUID accountId,
            LocalDate startDate,
            LocalDate endDate,
            String search,
            Pageable pageable) {
        
        log.info("Fetching transactions for user: {} with filters", userId);
        
        Page<Transaction> transactions = transactionRepository.findTransactionsWithFilters(
                userId, status, type, categoryId, accountId, startDate, endDate, search, pageable);
        
        return transactions.map(TransactionDto::fromEntity);
    }

    /**
     * Get a single transaction by ID
     */
    @Transactional(readOnly = true)
    public Optional<TransactionDto> getTransaction(UUID transactionId, UUID userId) {
        log.info("Fetching transaction: {} for user: {}", transactionId, userId);
        
        return transactionRepository.findById(transactionId)
                .filter(transaction -> transaction.getUserId().equals(userId))
                .map(TransactionDto::fromEntity);
    }

    /**
     * Create a new transaction and trigger AI categorization
     */
    public TransactionDto createTransaction(TransactionDto transactionDto) {
        log.info("Creating new transaction for user: {}", transactionDto.getUserId());
        
        Transaction transaction = transactionDto.toEntity();
        transaction.setStatus(Transaction.TransactionStatus.PENDING_CLASSIFICATION);
        
        Transaction savedTransaction = transactionRepository.save(transaction);
        
        // Trigger AI categorization
        triggerAICategorization(savedTransaction);
        
        log.info("Created transaction: {} and triggered AI categorization", savedTransaction.getId());
        return TransactionDto.fromEntity(savedTransaction);
    }

    /**
     * Update an existing transaction
     */
    public TransactionDto updateTransaction(UUID transactionId, UUID userId, TransactionDto updates) {
        log.info("Updating transaction: {} for user: {}", transactionId, userId);
        
        Transaction transaction = transactionRepository.findById(transactionId)
                .filter(t -> t.getUserId().equals(userId))
                .orElseThrow(() -> new TransactionNotFoundException("Transaction not found or access denied"));
        
        // Update fields
        if (updates.getDescription() != null) {
            transaction.setDescription(updates.getDescription());
        }
        if (updates.getAmount() != null) {
            transaction.setAmount(updates.getAmount());
        }
        if (updates.getType() != null) {
            transaction.setType(updates.getType());
        }
        if (updates.getCategoryId() != null) {
            transaction.setCategoryId(updates.getCategoryId());
            transaction.setStatus(Transaction.TransactionStatus.CLASSIFIED);
        }
        if (updates.getStatus() != null) {
            transaction.setStatus(updates.getStatus());
        }
        
        Transaction savedTransaction = transactionRepository.save(transaction);
        log.info("Updated transaction: {}", savedTransaction.getId());
        
        return TransactionDto.fromEntity(savedTransaction);
    }

    /**
     * Delete a transaction
     */
    public void deleteTransaction(UUID transactionId, UUID userId) {
        log.info("Deleting transaction: {} for user: {}", transactionId, userId);
        
        Transaction transaction = transactionRepository.findById(transactionId)
                .filter(t -> t.getUserId().equals(userId))
                .orElseThrow(() -> new TransactionNotFoundException("Transaction not found or access denied"));
        
        transactionRepository.delete(transaction);
        log.info("Deleted transaction: {}", transactionId);
    }

    /**
     * Categorize a transaction (accept/reject AI suggestion or manual categorization)
     */
    public CategorizationResponseDto categorizeTransaction(UUID transactionId, UUID userId, CategorizationRequestDto request) {
        log.info("Categorizing transaction: {} for user: {}", transactionId, userId);
        
        Transaction transaction = transactionRepository.findById(transactionId)
                .filter(t -> t.getUserId().equals(userId))
                .orElseThrow(() -> new TransactionNotFoundException("Transaction not found or access denied"));

        Category category = categoryRepository.findById(request.getCategoryId())
                .orElseThrow(() -> new CategoryNotFoundException("Category not found"));

        // Validate category belongs to user or is system-defined
        if (!category.isSystemCategory() && !category.getUserId().equals(userId)) {
            throw new UnauthorizedAccessException("Category not accessible to user");
        }
        
        boolean wasAISuggestion = request.getIsAISuggestion() && 
                                 transaction.getAiCategoryId() != null &&
                                 transaction.getAiCategoryId().equals(request.getCategoryId());
        
        // Update transaction
        transaction.setCategoryId(request.getCategoryId());
        transaction.setStatus(Transaction.TransactionStatus.CLASSIFIED);
        
        Transaction savedTransaction = transactionRepository.save(transaction);
        
        // Send feedback to AI service
        if (wasAISuggestion) {
            publishCategorizationAccepted(savedTransaction, request.getExplanation());

            // Send WebSocket notification for acceptance
            try {
                CategorizationAcceptedEvent acceptedEvent = CategorizationAcceptedEvent.builder()
                        .transactionId(savedTransaction.getId())
                        .categoryId(savedTransaction.getCategoryId())
                        .userAccepted(true)
                        .feedback(request.getExplanation())
                        .timestamp(LocalDateTime.now())
                        .build();
                webSocketController.sendCategorizationAccepted(userId.toString(), acceptedEvent);
            } catch (Exception wsError) {
                log.warn("Failed to send WebSocket notification for categorization acceptance: {}", wsError.getMessage());
            }

        } else if (transaction.getAiCategoryId() != null) {
            publishCategorizationRejected(savedTransaction, request.getCategoryId(), request.getExplanation());

            // Send WebSocket notification for rejection
            try {
                CategorizationRejectedEvent rejectedEvent = CategorizationRejectedEvent.builder()
                        .transactionId(savedTransaction.getId())
                        .rejectedCategoryId(transaction.getAiCategoryId())
                        .newCategoryId(request.getCategoryId())
                        .reason(request.getExplanation())
                        .timestamp(LocalDateTime.now())
                        .build();
                webSocketController.sendCategorizationRejected(userId.toString(), rejectedEvent);
            } catch (Exception wsError) {
                log.warn("Failed to send WebSocket notification for categorization rejection: {}", wsError.getMessage());
            }
        }
        
        // Prepare response
        CategorizationResponseDto.AILearningDto aiLearning = null;
        if (transaction.getAiCategoryId() != null) {
            aiLearning = CategorizationResponseDto.AILearningDto.builder()
                    .feedback(wasAISuggestion ? "positive" : "negative")
                    .confidence(transaction.getAiConfidence())
                    .build();
        }
        
        log.info("Categorized transaction: {} with category: {}", savedTransaction.getId(), category.getName());
        
        return CategorizationResponseDto.builder()
                .success(true)
                .transaction(TransactionDto.fromEntity(savedTransaction))
                .aiLearning(aiLearning)
                .build();
    }

    /**
     * Bulk categorize transactions
     */
    public BulkCategorizationResponseDto bulkCategorizeTransactions(UUID userId, BulkCategorizationRequestDto request) {
        log.info("Bulk categorizing {} transactions for user: {}", request.getTransactions().size(), userId);
        
        List<CategorizationResponseDto> results = request.getTransactions().stream()
                .map(categorizationRequest -> {
                    try {
                        return categorizeTransaction(
                                categorizationRequest.getTransactionId(),
                                userId,
                                categorizationRequest
                        );
                    } catch (Exception e) {
                        log.error("Failed to categorize transaction: {}", categorizationRequest.getTransactionId(), e);
                        return CategorizationResponseDto.builder()
                                .success(false)
                                .build();
                    }
                })
                .collect(Collectors.toList());
        
        log.info("Completed bulk categorization for user: {}", userId);
        
        return BulkCategorizationResponseDto.builder()
                .results(results)
                .build();
    }

    /**
     * Handle AI categorization result from messaging
     */
    public void handleAICategorizationResult(TransactionCategorizedEvent event) {
        log.info("Handling AI categorization result for transaction: {}", event.getTransactionId());
        
        try {
            Optional<Transaction> transactionOpt = transactionRepository.findById(event.getTransactionId());
            if (transactionOpt.isEmpty()) {
                log.warn("Transaction not found for AI categorization: {}", event.getTransactionId());
                return;
            }
            
            Transaction transaction = transactionOpt.get();
            
            // Find or create category
            UUID categoryId = findOrCreateCategory(event.getSuggestedCategoryName(), transaction.getType());
            
            // Update transaction with AI suggestion
            transaction.setAiCategoryId(categoryId);
            transaction.setAiConfidence(event.getConfidence());
            transaction.setAiExplanation(event.getExplanation());
            
            transactionRepository.save(transaction);

            log.info("Updated transaction {} with AI categorization: {} (confidence: {})",
                    transaction.getId(), event.getSuggestedCategoryName(), event.getConfidence());

            // Send WebSocket notification to user
            try {
                webSocketController.sendTransactionCategorized(transaction.getUserId().toString(), event);
            } catch (Exception wsError) {
                log.warn("Failed to send WebSocket notification for transaction categorization: {}", wsError.getMessage());
            }

        } catch (Exception e) {
            log.error("Error handling AI categorization result for transaction: {}", event.getTransactionId(), e);
            throw e; // Re-throw to trigger dead letter queue
        }
    }

    /**
     * Trigger AI categorization for a transaction
     */
    private void triggerAICategorization(Transaction transaction) {
        try {
            TransactionCategorizationRequestedEvent event = TransactionCategorizationRequestedEvent.builder()
                    .transactionId(transaction.getId())
                    .description(transaction.getDescription())
                    .amount(transaction.getAmount())
                    .userId(transaction.getUserId())
                    .timestamp(LocalDateTime.now())
                    .transactionType(transaction.getType().name())
                    .source(transaction.getSource())
                    .build();
            
            messagingService.publishCategorizationRequest(event);
            
        } catch (Exception e) {
            log.error("Failed to trigger AI categorization for transaction: {}", transaction.getId(), e);
            // Don't fail the transaction creation if messaging fails
        }
    }

    /**
     * Publish categorization acceptance feedback
     */
    private void publishCategorizationAccepted(Transaction transaction, String feedback) {
        try {
            CategorizationAcceptedEvent event = CategorizationAcceptedEvent.builder()
                    .transactionId(transaction.getId())
                    .categoryId(transaction.getCategoryId())
                    .userAccepted(true)
                    .feedback(feedback)
                    .timestamp(LocalDateTime.now())
                    .build();
            
            messagingService.publishCategorizationAccepted(event);
            
        } catch (Exception e) {
            log.error("Failed to publish categorization acceptance for transaction: {}", transaction.getId(), e);
        }
    }

    /**
     * Publish categorization rejection feedback
     */
    private void publishCategorizationRejected(Transaction transaction, UUID newCategoryId, String reason) {
        try {
            CategorizationRejectedEvent event = CategorizationRejectedEvent.builder()
                    .transactionId(transaction.getId())
                    .rejectedCategoryId(transaction.getAiCategoryId())
                    .newCategoryId(newCategoryId)
                    .reason(reason)
                    .timestamp(LocalDateTime.now())
                    .build();
            
            messagingService.publishCategorizationRejected(event);
            
        } catch (Exception e) {
            log.error("Failed to publish categorization rejection for transaction: {}", transaction.getId(), e);
        }
    }

    /**
     * Find existing category or create a new system category
     */
    private UUID findOrCreateCategory(String categoryName, Transaction.TransactionType type) {
        // First try to find existing system category
        Optional<Category> existingCategory = categoryRepository.findByIsSystemDefinedTrueAndNameAndIsActiveTrue(categoryName);
        
        if (existingCategory.isPresent()) {
            return existingCategory.get().getId();
        }
        
        // Create new system category
        Category newCategory = Category.createSystemCategory(
                categoryName,
                type == Transaction.TransactionType.EXPENSE,
                "System-generated category",
                "tag"
        );
        
        Category savedCategory = categoryRepository.save(newCategory);
        log.info("Created new system category: {} for type: {}", categoryName, type);
        
        return savedCategory.getId();
    }
}
