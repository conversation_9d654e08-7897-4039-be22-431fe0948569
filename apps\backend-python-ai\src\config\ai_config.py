"""
AI model configuration and setup
"""

import os
from typing import Optional, Dict, Any
from functools import lru_cache

from langchain_community.llms import Ollama
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.language_models.base import BaseLanguageModel
from langchain_community.llms.fake import FakeListLLM

from .settings import get_settings


class AIModelConfig:
    """AI model configuration manager"""
    
    def __init__(self):
        self.settings = get_settings()
        self._local_model: Optional[BaseLanguageModel] = None
        self._production_model: Optional[BaseLanguageModel] = None
    
    @property
    def is_production(self) -> bool:
        """Check if running in production environment"""
        return self.settings.environment.lower() in ["production", "prod"]
    
    @property
    def has_google_api_key(self) -> bool:
        """Check if Google API key is available"""
        return bool(self.settings.google_api_key)
    
    def get_model(self) -> BaseLanguageModel:
        """Get the appropriate AI model based on environment"""
        # Always prefer Gemini if API key is available
        if self.has_google_api_key:
            return self.get_production_model()
        else:
            return self.get_local_model()
    
    def get_local_model(self) -> BaseLanguageModel:
        """Get local Ollama model for development"""
        if self._local_model is None:
            print("⚠️  Initializing Ollama model (fallback only)")
            self._local_model = Ollama(
                model="llama3.1:8b",
                base_url=self.settings.ollama_base_url,
                temperature=0.1,  # Low temperature for consistent responses
                top_p=0.9,
                num_predict=512,
                stop=["</response>", "\n\n"],
                timeout=30
            )
        return self._local_model
    
    def get_production_model(self) -> BaseLanguageModel:
        """Get Google Gemini model for production"""
        if self._production_model is None:
            if not self.has_google_api_key:
                raise ValueError("Google API key is required for production model")

            print(f"✅ Initializing Google Gemini model: {self.settings.vertex_ai_model}")
            self._production_model = ChatGoogleGenerativeAI(
                model=self.settings.vertex_ai_model,
                google_api_key=self.settings.google_api_key,
                temperature=0.1,
                max_output_tokens=512
            )
        return self._production_model
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model configuration"""
        return {
            "environment": self.settings.environment,
            "using_production_model": self.is_production and self.has_google_api_key,
            "local_model": {
                "name": "llama3.1:8b",
                "base_url": self.settings.ollama_base_url,
                "available": True  # Assume available for now
            },
            "production_model": {
                "name": self.settings.vertex_ai_model,
                "available": self.has_google_api_key,
                "api_key_configured": bool(self.settings.google_api_key)
            }
        }


# Model configuration constants
LOCAL_MODEL_CONFIG = {
    "model_name": "llama3.1:8b",
    "temperature": 0.1,
    "max_tokens": 512,
    "timeout": 30,
    "retry_attempts": 3,
    "top_p": 0.9,
    "stop_sequences": ["</response>", "\n\n"]
}

PRODUCTION_MODEL_CONFIG = {
    "model_name": get_settings().vertex_ai_model,
    "temperature": 0.1,
    "max_tokens": 512,
    "timeout": 30,
    "retry_attempts": 3,
    "rate_limit": {
        "requests_per_minute": 60,
        "tokens_per_minute": 30000
    },
    "safety_settings": {
        "HARM_CATEGORY_HARASSMENT": "BLOCK_NONE",
        "HARM_CATEGORY_HATE_SPEECH": "BLOCK_NONE",
        "HARM_CATEGORY_SEXUALLY_EXPLICIT": "BLOCK_NONE",
        "HARM_CATEGORY_DANGEROUS_CONTENT": "BLOCK_NONE"
    }
}

# Intent recognition specific configuration
INTENT_RECOGNITION_CONFIG = {
    "confidence_threshold": 0.7,
    "max_entities_per_type": 5,
    "enable_clarification": True,
    "clarification_threshold": 0.5,
    "supported_languages": ["en"],  # English for now
    "zambian_context": True
}

# Entity extraction configuration
ENTITY_EXTRACTION_CONFIG = {
    "confidence_threshold": 0.6,
    "max_entity_length": 100,
    "enable_normalization": True,
    "zambian_currency_patterns": [
        r"K\s*\d+(?:\.\d{2})?",  # K500, K 500.00
        r"\d+(?:\.\d{2})?\s*kwacha",  # 500 kwacha, 500.00 kwacha
        r"ZMW\s*\d+(?:\.\d{2})?",  # ZMW 500.00
    ],
    "date_patterns": [
        r"(?:this|last|next)\s+(?:week|month|year)",
        r"(?:today|yesterday|tomorrow)",
        r"\d{1,2}(?:st|nd|rd|th)?\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)",
        r"\d{4}-\d{2}-\d{2}",
    ]
}


@lru_cache()
def get_ai_config() -> AIModelConfig:
    """Get cached AI configuration instance"""
    return AIModelConfig()


def test_model_connection() -> Dict[str, Any]:
    """Test connection to AI models"""
    config = get_ai_config()
    results = {
        "local_model": {"available": False, "error": None},
        "production_model": {"available": False, "error": None}
    }
    
    # Test local model
    try:
        local_model = config.get_local_model()
        # Simple test prompt
        response = local_model.invoke("Hello, respond with 'OK' if you can understand this.")
        results["local_model"]["available"] = "OK" in response
    except Exception as e:
        results["local_model"]["error"] = str(e)
    
    # Test production model if available
    if config.has_google_api_key:
        try:
            production_model = config.get_production_model()
            response = production_model.invoke("Hello, respond with 'OK' if you can understand this.")
            results["production_model"]["available"] = "OK" in response.content if hasattr(response, 'content') else "OK" in str(response)
        except Exception as e:
            results["production_model"]["error"] = str(e)
    else:
        results["production_model"]["error"] = "Google API key not configured"
    
    return results
