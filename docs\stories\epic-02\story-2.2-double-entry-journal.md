# Story 2.2: Automated Double-Entry Journal System
## DETAILED READY-FOR-DEVELOPMENT SPECIFICATION

**Epic:** 2 - Core Financial Engine & Accounting Integrity
**Priority:** CRITICAL - Must complete after Story 2.1
**Estimated Effort:** 4-5 developer days
**Dependencies:** Story 2.1 (Chart of Accounts) must be completed first

---

## User Story

**As a** business owner
**I want** all financial transactions to automatically create balanced journal entries
**So that** my books maintain proper double-entry accounting integrity and audit compliance

---

## Business Context

This story implements the core double-entry bookkeeping engine that transforms user-friendly transaction categorization into professional accounting journal entries. It ensures that every financial transaction creates balanced journal entries (debits = credits) and maintains real-time account balances, providing the foundation for accurate financial reporting and audit compliance.

**Why This Matters:**
- Enables professional-grade accounting that meets audit standards
- Provides real-time account balances for accurate financial reporting
- Ensures accounting integrity through automated double-entry validation
- Creates audit trail for all financial transactions
- Bridges user-friendly interface with professional accounting requirements

---

## Technical Implementation Details

### 1. Database Schema Changes

#### New Tables to Create:

**journal_entries table:**
```sql
CREATE TABLE journal_entries (
    id BIGSERIAL PRIMARY KEY,
    entry_number VARCHAR(20) NOT NULL UNIQUE,
    description TEXT NOT NULL,
    entry_date DATE NOT NULL,
    total_amount DECIMAL(15,2) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'POSTED', -- DRAFT, POSTED, REVERSED
    source_type VARCHAR(50), -- TRANSACTION, INVOICE, MANUAL, ADJUSTMENT
    source_id BIGINT, -- Reference to source transaction/invoice
    reference_number VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT NOT NULL REFERENCES users(id),
    updated_by BIGINT REFERENCES users(id),
    reversed_by_entry_id BIGINT REFERENCES journal_entries(id),
    reversal_reason TEXT
);

CREATE INDEX idx_journal_entries_date ON journal_entries(entry_date);
CREATE INDEX idx_journal_entries_number ON journal_entries(entry_number);
CREATE INDEX idx_journal_entries_source ON journal_entries(source_type, source_id);
CREATE INDEX idx_journal_entries_status ON journal_entries(status);
```

**journal_entry_lines table:**
```sql
CREATE TABLE journal_entry_lines (
    id BIGSERIAL PRIMARY KEY,
    journal_entry_id BIGINT NOT NULL REFERENCES journal_entries(id) ON DELETE CASCADE,
    account_id BIGINT NOT NULL REFERENCES accounts(id),
    line_number INTEGER NOT NULL,
    description TEXT,
    debit_amount DECIMAL(15,2) DEFAULT 0.00,
    credit_amount DECIMAL(15,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT chk_debit_or_credit CHECK (
        (debit_amount > 0 AND credit_amount = 0) OR
        (credit_amount > 0 AND debit_amount = 0)
    ),
    CONSTRAINT chk_positive_amounts CHECK (
        debit_amount >= 0 AND credit_amount >= 0
    )
);

CREATE INDEX idx_journal_lines_entry ON journal_entry_lines(journal_entry_id);
CREATE INDEX idx_journal_lines_account ON journal_entry_lines(account_id);
CREATE INDEX idx_journal_lines_date_account ON journal_entry_lines(account_id, journal_entry_id);
```

#### Create Account Balance Materialized View:

**account_balances materialized view:**
```sql
CREATE MATERIALIZED VIEW account_balances AS
SELECT
    a.id as account_id,
    a.account_code,
    a.account_name,
    a.account_type,
    a.normal_balance,
    COALESCE(
        CASE
            WHEN a.normal_balance = 'DEBIT' THEN
                SUM(jel.debit_amount) - SUM(jel.credit_amount)
            ELSE
                SUM(jel.credit_amount) - SUM(jel.debit_amount)
        END,
        0
    ) as current_balance,
    COUNT(jel.id) as transaction_count,
    MAX(je.entry_date) as last_transaction_date
FROM accounts a
LEFT JOIN journal_entry_lines jel ON a.id = jel.account_id
LEFT JOIN journal_entries je ON jel.journal_entry_id = je.id AND je.status = 'POSTED'
GROUP BY a.id, a.account_code, a.account_name, a.account_type, a.normal_balance;

CREATE UNIQUE INDEX idx_account_balances_account ON account_balances(account_id);
```

### 2. Java Backend Implementation

#### New Entity Classes:

**JournalEntry.java:**
```java
@Entity
@Table(name = "journal_entries")
public class JournalEntry {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "entry_number", unique = true, nullable = false)
    private String entryNumber;

    @Column(nullable = false)
    private String description;

    @Column(name = "entry_date", nullable = false)
    private LocalDate entryDate;

    @Column(name = "total_amount", nullable = false, precision = 15, scale = 2)
    private BigDecimal totalAmount;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private JournalEntryStatus status = JournalEntryStatus.POSTED;

    @Enumerated(EnumType.STRING)
    @Column(name = "source_type")
    private SourceType sourceType;

    @Column(name = "source_id")
    private Long sourceId;

    @Column(name = "reference_number")
    private String referenceNumber;

    @OneToMany(mappedBy = "journalEntry", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @OrderBy("lineNumber ASC")
    private List<JournalEntryLine> lines = new ArrayList<>();

    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "created_by", nullable = false)
    private User createdBy;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "updated_by")
    private User updatedBy;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "reversed_by_entry_id")
    private JournalEntry reversedByEntry;

    @Column(name = "reversal_reason")
    private String reversalReason;

    // Constructors, getters, setters, equals, hashCode

    public BigDecimal getTotalDebits() {
        return lines.stream()
            .map(JournalEntryLine::getDebitAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public BigDecimal getTotalCredits() {
        return lines.stream()
            .map(JournalEntryLine::getCreditAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public boolean isBalanced() {
        return getTotalDebits().compareTo(getTotalCredits()) == 0;
    }
}

public enum JournalEntryStatus {
    DRAFT, POSTED, REVERSED
}

public enum SourceType {
    TRANSACTION, INVOICE, MANUAL, ADJUSTMENT
}
```

**JournalEntryLine.java:**
```java
@Entity
@Table(name = "journal_entry_lines")
public class JournalEntryLine {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "journal_entry_id", nullable = false)
    private JournalEntry journalEntry;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "account_id", nullable = false)
    private Account account;

    @Column(name = "line_number", nullable = false)
    private Integer lineNumber;

    private String description;

    @Column(name = "debit_amount", precision = 15, scale = 2)
    private BigDecimal debitAmount = BigDecimal.ZERO;

    @Column(name = "credit_amount", precision = 15, scale = 2)
    private BigDecimal creditAmount = BigDecimal.ZERO;

    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    // Constructors, getters, setters, equals, hashCode

    public BigDecimal getAmount() {
        return debitAmount.compareTo(BigDecimal.ZERO) > 0 ? debitAmount : creditAmount;
    }

    public boolean isDebit() {
        return debitAmount.compareTo(BigDecimal.ZERO) > 0;
    }

    public boolean isCredit() {
        return creditAmount.compareTo(BigDecimal.ZERO) > 0;
    }
}
```

#### Repository Interfaces:

**JournalEntryRepository.java:**
```java
@Repository
public interface JournalEntryRepository extends JpaRepository<JournalEntry, Long> {
    Optional<JournalEntry> findByEntryNumber(String entryNumber);

    List<JournalEntry> findByStatusOrderByEntryDateDesc(JournalEntryStatus status);

    List<JournalEntry> findByEntryDateBetweenOrderByEntryDateDesc(LocalDate startDate, LocalDate endDate);

    List<JournalEntry> findBySourceTypeAndSourceId(SourceType sourceType, Long sourceId);

    @Query("SELECT je FROM JournalEntry je WHERE je.createdBy.id = :userId ORDER BY je.entryDate DESC")
    Page<JournalEntry> findByUserId(@Param("userId") Long userId, Pageable pageable);

    @Query("SELECT je FROM JournalEntry je JOIN je.lines jel WHERE jel.account.id = :accountId ORDER BY je.entryDate DESC")
    List<JournalEntry> findByAccountId(@Param("accountId") Long accountId);

    @Query("SELECT COALESCE(MAX(CAST(SUBSTRING(je.entryNumber, 3) AS INTEGER)), 0) FROM JournalEntry je WHERE je.entryNumber LIKE 'JE%'")
    Integer findMaxEntryNumber();

    boolean existsBySourceTypeAndSourceId(SourceType sourceType, Long sourceId);
}
```

**JournalEntryLineRepository.java:**
```java
@Repository
public interface JournalEntryLineRepository extends JpaRepository<JournalEntryLine, Long> {
    List<JournalEntryLine> findByJournalEntryOrderByLineNumber(JournalEntry journalEntry);

    List<JournalEntryLine> findByAccountOrderByJournalEntryEntryDateDesc(Account account);

    @Query("SELECT jel FROM JournalEntryLine jel JOIN jel.journalEntry je WHERE jel.account.id = :accountId AND je.entryDate BETWEEN :startDate AND :endDate ORDER BY je.entryDate DESC")
    List<JournalEntryLine> findByAccountAndDateRange(@Param("accountId") Long accountId,
                                                    @Param("startDate") LocalDate startDate,
                                                    @Param("endDate") LocalDate endDate);

    @Query("SELECT SUM(jel.debitAmount) - SUM(jel.creditAmount) FROM JournalEntryLine jel JOIN jel.journalEntry je WHERE jel.account.id = :accountId AND je.status = 'POSTED'")
    BigDecimal calculateAccountBalance(@Param("accountId") Long accountId);
}
```

#### Service Layer:

**JournalService.java:**
```java
@Service
@Transactional
public class JournalService {

    private final JournalEntryRepository journalEntryRepository;
    private final JournalEntryLineRepository journalEntryLineRepository;
    private final AccountRepository accountRepository;
    private final CategoryRepository categoryRepository;
    private final ApplicationEventPublisher eventPublisher;

    public JournalService(JournalEntryRepository journalEntryRepository,
                         JournalEntryLineRepository journalEntryLineRepository,
                         AccountRepository accountRepository,
                         CategoryRepository categoryRepository,
                         ApplicationEventPublisher eventPublisher) {
        this.journalEntryRepository = journalEntryRepository;
        this.journalEntryLineRepository = journalEntryLineRepository;
        this.accountRepository = accountRepository;
        this.categoryRepository = categoryRepository;
        this.eventPublisher = eventPublisher;
    }

    public JournalEntry createJournalEntry(CreateJournalEntryRequest request, User user) {
        validateJournalEntryRequest(request);

        JournalEntry journalEntry = new JournalEntry();
        journalEntry.setEntryNumber(generateEntryNumber());
        journalEntry.setDescription(request.getDescription());
        journalEntry.setEntryDate(request.getEntryDate());
        journalEntry.setSourceType(request.getSourceType());
        journalEntry.setSourceId(request.getSourceId());
        journalEntry.setReferenceNumber(request.getReferenceNumber());
        journalEntry.setCreatedBy(user);
        journalEntry.setUpdatedBy(user);

        // Create journal entry lines
        BigDecimal totalDebits = BigDecimal.ZERO;
        BigDecimal totalCredits = BigDecimal.ZERO;

        for (int i = 0; i < request.getLines().size(); i++) {
            CreateJournalEntryLineRequest lineRequest = request.getLines().get(i);
            JournalEntryLine line = createJournalEntryLine(journalEntry, lineRequest, i + 1);
            journalEntry.getLines().add(line);

            totalDebits = totalDebits.add(line.getDebitAmount());
            totalCredits = totalCredits.add(line.getCreditAmount());
        }

        // Validate balanced entry
        if (totalDebits.compareTo(totalCredits) != 0) {
            throw new UnbalancedJournalEntryException(
                String.format("Journal entry is not balanced. Debits: %s, Credits: %s",
                             totalDebits, totalCredits));
        }

        journalEntry.setTotalAmount(totalDebits);
        JournalEntry savedEntry = journalEntryRepository.save(journalEntry);

        // Refresh account balances
        refreshAccountBalances();

        // Publish event
        eventPublisher.publishEvent(new JournalEntryCreatedEvent(savedEntry));

        return savedEntry;
    }

    public JournalEntry createTransactionJournalEntry(Transaction transaction, Category category, User user) {
        Account account = getAccountForCategory(category);
        Account cashAccount = getCashAccount();

        CreateJournalEntryRequest request = new CreateJournalEntryRequest();
        request.setDescription(String.format("Transaction: %s", transaction.getDescription()));
        request.setEntryDate(transaction.getTransactionDate().toLocalDate());
        request.setSourceType(SourceType.TRANSACTION);
        request.setSourceId(transaction.getId());
        request.setReferenceNumber(transaction.getExternalTransactionId());

        List<CreateJournalEntryLineRequest> lines = new ArrayList<>();

        if (category.getType() == CategoryType.EXPENSE) {
            // Expense transaction: Dr. Expense Account, Cr. Cash
            lines.add(createLineRequest(account.getId(), transaction.getAmount(), BigDecimal.ZERO,
                                      String.format("Expense: %s", category.getName())));
            lines.add(createLineRequest(cashAccount.getId(), BigDecimal.ZERO, transaction.getAmount(),
                                      "Cash payment"));
        } else if (category.getType() == CategoryType.INCOME) {
            // Income transaction: Dr. Cash, Cr. Revenue Account
            lines.add(createLineRequest(cashAccount.getId(), transaction.getAmount(), BigDecimal.ZERO,
                                      "Cash received"));
            lines.add(createLineRequest(account.getId(), BigDecimal.ZERO, transaction.getAmount(),
                                      String.format("Revenue: %s", category.getName())));
        }

        request.setLines(lines);
        return createJournalEntry(request, user);
    }

    public JournalEntry reverseJournalEntry(Long journalEntryId, String reason, LocalDate reversalDate, User user) {
        JournalEntry originalEntry = journalEntryRepository.findById(journalEntryId)
            .orElseThrow(() -> new EntityNotFoundException("Journal entry not found"));

        if (originalEntry.getStatus() == JournalEntryStatus.REVERSED) {
            throw new BusinessException("Journal entry is already reversed");
        }

        CreateJournalEntryRequest reversalRequest = new CreateJournalEntryRequest();
        reversalRequest.setDescription(String.format("Reversal of JE %s: %s",
                                                    originalEntry.getEntryNumber(), reason));
        reversalRequest.setEntryDate(reversalDate);
        reversalRequest.setSourceType(SourceType.ADJUSTMENT);
        reversalRequest.setReferenceNumber(originalEntry.getEntryNumber());

        // Create reversed lines (swap debits and credits)
        List<CreateJournalEntryLineRequest> reversalLines = new ArrayList<>();
        for (JournalEntryLine originalLine : originalEntry.getLines()) {
            CreateJournalEntryLineRequest reversalLine = new CreateJournalEntryLineRequest();
            reversalLine.setAccountId(originalLine.getAccount().getId());
            reversalLine.setDescription("Reversal: " + originalLine.getDescription());
            // Swap debit and credit amounts
            reversalLine.setDebitAmount(originalLine.getCreditAmount());
            reversalLine.setCreditAmount(originalLine.getDebitAmount());
            reversalLines.add(reversalLine);
        }

        reversalRequest.setLines(reversalLines);
        JournalEntry reversalEntry = createJournalEntry(reversalRequest, user);

        // Update original entry status
        originalEntry.setStatus(JournalEntryStatus.REVERSED);
        originalEntry.setReversedByEntry(reversalEntry);
        originalEntry.setReversalReason(reason);
        originalEntry.setUpdatedBy(user);
        journalEntryRepository.save(originalEntry);

        return reversalEntry;
    }

    public BigDecimal getAccountBalance(Long accountId) {
        return journalEntryLineRepository.calculateAccountBalance(accountId);
    }

    public List<JournalEntry> getAccountJournalEntries(Long accountId, LocalDate startDate, LocalDate endDate) {
        return journalEntryRepository.findByAccountId(accountId);
    }

    private String generateEntryNumber() {
        Integer maxNumber = journalEntryRepository.findMaxEntryNumber();
        return String.format("JE%06d", maxNumber + 1);
    }

    private Account getAccountForCategory(Category category) {
        if (category.getDefaultAccountId() == null) {
            throw new BusinessException("Category is not mapped to an account: " + category.getName());
        }
        return accountRepository.findById(category.getDefaultAccountId())
            .orElseThrow(() -> new EntityNotFoundException("Account not found for category"));
    }

    private Account getCashAccount() {
        return accountRepository.findByAccountCode("1130") // Mobile Money Accounts
            .orElseThrow(() -> new EntityNotFoundException("Cash account not found"));
    }

    private void refreshAccountBalances() {
        // Refresh materialized view
        journalEntryRepository.flush();
        // This would typically be done via a database function or scheduled job
    }

    private void validateJournalEntryRequest(CreateJournalEntryRequest request) {
        if (request.getLines().isEmpty()) {
            throw new BusinessException("Journal entry must have at least one line");
        }

        if (request.getLines().size() < 2) {
            throw new BusinessException("Journal entry must have at least two lines for double-entry");
        }

        // Validate all accounts exist
        for (CreateJournalEntryLineRequest line : request.getLines()) {
            if (!accountRepository.existsById(line.getAccountId())) {
                throw new EntityNotFoundException("Account not found: " + line.getAccountId());
            }
        }
    }
}
```

#### Event Handler for Transaction Integration:

**TransactionJournalEventHandler.java:**
```java
@Component
@EventListener
public class TransactionJournalEventHandler {

    private final JournalService journalService;
    private final TransactionService transactionService;
    private final CategoryService categoryService;

    public TransactionJournalEventHandler(JournalService journalService,
                                        TransactionService transactionService,
                                        CategoryService categoryService) {
        this.journalService = journalService;
        this.transactionService = transactionService;
        this.categoryService = categoryService;
    }

    @EventListener
    @Async
    public void handleTransactionCategorized(TransactionCategorizedEvent event) {
        try {
            Transaction transaction = transactionService.findById(event.getTransactionId());
            Category category = categoryService.findById(event.getCategoryId());
            User user = event.getUser();

            // Check if journal entry already exists
            if (!journalService.journalEntryExistsForTransaction(transaction.getId())) {
                journalService.createTransactionJournalEntry(transaction, category, user);
            }
        } catch (Exception e) {
            // Log error and potentially send to dead letter queue
            log.error("Failed to create journal entry for transaction {}: {}",
                     event.getTransactionId(), e.getMessage(), e);
        }
    }

    @EventListener
    @Async
    public void handleTransactionRecategorized(TransactionRecategorizedEvent event) {
        try {
            // Reverse old journal entry and create new one
            Transaction transaction = transactionService.findById(event.getTransactionId());
            Category newCategory = categoryService.findById(event.getNewCategoryId());
            User user = event.getUser();

            // Find and reverse existing journal entry
            Optional<JournalEntry> existingEntry = journalService.findByTransactionId(transaction.getId());
            if (existingEntry.isPresent()) {
                journalService.reverseJournalEntry(existingEntry.get().getId(),
                                                 "Transaction recategorized",
                                                 LocalDate.now(), user);
            }

            // Create new journal entry with new category
            journalService.createTransactionJournalEntry(transaction, newCategory, user);
        } catch (Exception e) {
            log.error("Failed to handle transaction recategorization for transaction {}: {}",
                     event.getTransactionId(), e.getMessage(), e);
        }
    }
}
```

#### REST Controller:

**JournalController.java:**
```java
@RestController
@RequestMapping("/api/journal-entries")
@Validated
public class JournalController {

    private final JournalService journalService;

    public JournalController(JournalService journalService) {
        this.journalService = journalService;
    }

    @PostMapping
    public ResponseEntity<JournalEntryDto> createJournalEntry(@Valid @RequestBody CreateJournalEntryRequest request,
                                                             Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        JournalEntry journalEntry = journalService.createJournalEntry(request, user);
        return ResponseEntity.ok(JournalEntryMapper.toDto(journalEntry));
    }

    @GetMapping
    public ResponseEntity<PagedResponse<JournalEntryDto>> getJournalEntries(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false) JournalEntryStatus status,
            Authentication authentication) {

        User user = (User) authentication.getPrincipal();
        Pageable pageable = PageRequest.of(page, size, Sort.by("entryDate").descending());

        Page<JournalEntry> journalEntries = journalService.getJournalEntries(
            user.getId(), startDate, endDate, status, pageable);

        List<JournalEntryDto> dtos = journalEntries.getContent().stream()
            .map(JournalEntryMapper::toDto)
            .collect(Collectors.toList());

        return ResponseEntity.ok(new PagedResponse<>(dtos, journalEntries));
    }

    @GetMapping("/{id}")
    public ResponseEntity<JournalEntryDto> getJournalEntry(@PathVariable Long id) {
        JournalEntry journalEntry = journalService.findById(id);
        return ResponseEntity.ok(JournalEntryMapper.toDto(journalEntry));
    }

    @PostMapping("/{id}/reverse")
    public ResponseEntity<JournalEntryReversalDto> reverseJournalEntry(@PathVariable Long id,
                                                                      @Valid @RequestBody ReverseJournalEntryRequest request,
                                                                      Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        JournalEntry reversalEntry = journalService.reverseJournalEntry(id, request.getReason(),
                                                                       request.getReversalDate(), user);

        JournalEntry originalEntry = journalService.findById(id);
        JournalEntryReversalDto dto = new JournalEntryReversalDto();
        dto.setOriginalEntry(JournalEntryMapper.toDto(originalEntry));
        dto.setReversalEntry(JournalEntryMapper.toDto(reversalEntry));

        return ResponseEntity.ok(dto);
    }

    @GetMapping("/accounts/{accountId}")
    public ResponseEntity<List<JournalEntryDto>> getAccountJournalEntries(@PathVariable Long accountId,
                                                                         @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
                                                                         @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        List<JournalEntry> journalEntries = journalService.getAccountJournalEntries(accountId, startDate, endDate);
        List<JournalEntryDto> dtos = journalEntries.stream()
            .map(JournalEntryMapper::toDto)
            .collect(Collectors.toList());
        return ResponseEntity.ok(dtos);
    }

    @GetMapping("/accounts/{accountId}/balance")
    public ResponseEntity<AccountBalanceDto> getAccountBalance(@PathVariable Long accountId) {
        BigDecimal balance = journalService.getAccountBalance(accountId);
        AccountBalanceDto dto = new AccountBalanceDto();
        dto.setAccountId(accountId);
        dto.setBalance(balance);
        dto.setAsOfDate(LocalDate.now());
        return ResponseEntity.ok(dto);
    }

    @PostMapping("/validate")
    public ResponseEntity<JournalEntryValidationDto> validateJournalEntry(@Valid @RequestBody CreateJournalEntryRequest request) {
        JournalEntryValidationDto validation = journalService.validateJournalEntry(request);
        return ResponseEntity.ok(validation);
    }
}
```

### 3. Frontend Implementation

#### React Components:

**JournalEntryView.tsx:**
```typescript
import React, { useState, useEffect } from 'react';
import { JournalEntry, JournalEntryLine } from '@/types/accounting';
import { journalService } from '@/services/journalService';
import { formatCurrency, formatDate } from '@/utils/formatting';

interface JournalEntryViewProps {
  journalEntryId: number;
  onClose: () => void;
}

export const JournalEntryView: React.FC<JournalEntryViewProps> = ({
  journalEntryId,
  onClose
}) => {
  const [journalEntry, setJournalEntry] = useState<JournalEntry | null>(null);
  const [loading, setLoading] = useState(true);
  const [showReverseModal, setShowReverseModal] = useState(false);

  useEffect(() => {
    loadJournalEntry();
  }, [journalEntryId]);

  const loadJournalEntry = async () => {
    try {
      setLoading(true);
      const entry = await journalService.getJournalEntry(journalEntryId);
      setJournalEntry(entry);
    } catch (error) {
      console.error('Failed to load journal entry:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleReverse = async (reason: string, reversalDate: string) => {
    try {
      await journalService.reverseJournalEntry(journalEntryId, reason, reversalDate);
      setShowReverseModal(false);
      loadJournalEntry(); // Reload to show updated status
    } catch (error) {
      console.error('Failed to reverse journal entry:', error);
    }
  };

  if (loading) {
    return <div className="loading">Loading journal entry...</div>;
  }

  if (!journalEntry) {
    return <div className="error">Journal entry not found</div>;
  }

  const totalDebits = journalEntry.lines
    .reduce((sum, line) => sum + line.debitAmount, 0);
  const totalCredits = journalEntry.lines
    .reduce((sum, line) => sum + line.creditAmount, 0);

  return (
    <div className="journal-entry-view">
      <div className="journal-header">
        <div className="header-info">
          <h2>Journal Entry {journalEntry.entryNumber}</h2>
          <span className={`status ${journalEntry.status.toLowerCase()}`}>
            {journalEntry.status}
          </span>
        </div>
        <div className="header-actions">
          {journalEntry.status === 'POSTED' && (
            <button
              onClick={() => setShowReverseModal(true)}
              className="btn btn-secondary"
            >
              Reverse Entry
            </button>
          )}
          <button onClick={onClose} className="btn btn-primary">
            Close
          </button>
        </div>
      </div>

      <div className="journal-details">
        <div className="detail-row">
          <label>Date:</label>
          <span>{formatDate(journalEntry.entryDate)}</span>
        </div>
        <div className="detail-row">
          <label>Description:</label>
          <span>{journalEntry.description}</span>
        </div>
        <div className="detail-row">
          <label>Reference:</label>
          <span>{journalEntry.referenceNumber || 'N/A'}</span>
        </div>
        <div className="detail-row">
          <label>Total Amount:</label>
          <span>{formatCurrency(journalEntry.totalAmount)}</span>
        </div>
      </div>

      <div className="journal-lines">
        <h3>Journal Entry Lines</h3>
        <table className="journal-lines-table">
          <thead>
            <tr>
              <th>Account</th>
              <th>Description</th>
              <th>Debit</th>
              <th>Credit</th>
            </tr>
          </thead>
          <tbody>
            {journalEntry.lines.map((line, index) => (
              <tr key={line.id}>
                <td>
                  <div className="account-info">
                    <span className="account-code">{line.account.accountCode}</span>
                    <span className="account-name">{line.account.accountName}</span>
                  </div>
                </td>
                <td>{line.description}</td>
                <td className="amount debit">
                  {line.debitAmount > 0 ? formatCurrency(line.debitAmount) : ''}
                </td>
                <td className="amount credit">
                  {line.creditAmount > 0 ? formatCurrency(line.creditAmount) : ''}
                </td>
              </tr>
            ))}
          </tbody>
          <tfoot>
            <tr className="totals">
              <td colSpan={2}><strong>Totals:</strong></td>
              <td className="amount debit">
                <strong>{formatCurrency(totalDebits)}</strong>
              </td>
              <td className="amount credit">
                <strong>{formatCurrency(totalCredits)}</strong>
              </td>
            </tr>
          </tfoot>
        </table>
      </div>

      {showReverseModal && (
        <ReverseJournalEntryModal
          journalEntry={journalEntry}
          onReverse={handleReverse}
          onClose={() => setShowReverseModal(false)}
        />
      )}
    </div>
  );
};
```

#### API Service:

**journalService.ts:**
```typescript
import { api } from '@/lib/api';
import {
  JournalEntry,
  CreateJournalEntryRequest,
  JournalEntryValidation,
  PagedResponse
} from '@/types/accounting';

export const journalService = {
  async getJournalEntries(params: {
    page?: number;
    size?: number;
    startDate?: string;
    endDate?: string;
    status?: string;
  }): Promise<PagedResponse<JournalEntry>> {
    const response = await api.get('/journal-entries', { params });
    return response.data;
  },

  async getJournalEntry(id: number): Promise<JournalEntry> {
    const response = await api.get(`/journal-entries/${id}`);
    return response.data;
  },

  async createJournalEntry(request: CreateJournalEntryRequest): Promise<JournalEntry> {
    const response = await api.post('/journal-entries', request);
    return response.data;
  },

  async reverseJournalEntry(id: number, reason: string, reversalDate: string): Promise<{
    originalEntry: JournalEntry;
    reversalEntry: JournalEntry;
  }> {
    const response = await api.post(`/journal-entries/${id}/reverse`, {
      reason,
      reversalDate
    });
    return response.data;
  },

  async getAccountJournalEntries(accountId: number, startDate?: string, endDate?: string): Promise<JournalEntry[]> {
    const params = { startDate, endDate };
    const response = await api.get(`/journal-entries/accounts/${accountId}`, { params });
    return response.data;
  },

  async getAccountBalance(accountId: number): Promise<{ accountId: number; balance: number; asOfDate: string }> {
    const response = await api.get(`/journal-entries/accounts/${accountId}/balance`);
    return response.data;
  },

  async validateJournalEntry(request: CreateJournalEntryRequest): Promise<JournalEntryValidation> {
    const response = await api.post('/journal-entries/validate', request);
    return response.data;
  }
};
```

### 4. Data Migration Script

**V1_002__Create_Journal_Entries.sql:**
```sql
-- Create journal_entries table
-- (Schema creation from above)

-- Create function to refresh account balances
CREATE OR REPLACE FUNCTION refresh_account_balances()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW account_balances;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to refresh balances after journal entry changes
CREATE OR REPLACE FUNCTION trigger_refresh_account_balances()
RETURNS trigger AS $$
BEGIN
    PERFORM refresh_account_balances();
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER journal_entry_balance_refresh
    AFTER INSERT OR UPDATE OR DELETE ON journal_entry_lines
    FOR EACH STATEMENT
    EXECUTE FUNCTION trigger_refresh_account_balances();

-- Create sequence for journal entry numbers
CREATE SEQUENCE journal_entry_number_seq START 1;

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON journal_entries TO intellifin_app;
GRANT SELECT, INSERT, UPDATE ON journal_entry_lines TO intellifin_app;
GRANT SELECT ON account_balances TO intellifin_app;
GRANT USAGE ON journal_entry_number_seq TO intellifin_app;
```

---

## Acceptance Criteria

### Core Functionality:
- [ ] **JournalEntry Data Model**: Complete entity with debit/credit validation, audit trail, and status management
- [ ] **Balanced Entry Validation**: System prevents saving unbalanced journal entries (debits ≠ credits)
- [ ] **Automatic Journal Creation**: All transaction categorization events create appropriate journal entries
- [ ] **Event-Driven Integration**: JournalService subscribes to transaction events via messaging layer
- [ ] **Account Mapping**: Journal entries use Account mapping from Category (Story 2.1 dependency)
- [ ] **Real-Time Balances**: Account balances update immediately when journal entries are posted
- [ ] **Journal Entry Reversal**: Complete reversal functionality with audit trail and reason tracking
- [ ] **Entry Number Generation**: Sequential journal entry numbers (JE000001, JE000002, etc.)

### Transaction Integration:
- [ ] **Expense Transactions**: Create journal entries: Dr. Expense Account, Cr. Cash Account
- [ ] **Income Transactions**: Create journal entries: Dr. Cash Account, Cr. Revenue Account
- [ ] **Transaction Recategorization**: Reverse old journal entry and create new one with correct accounts
- [ ] **Duplicate Prevention**: System prevents creating multiple journal entries for same transaction
- [ ] **Error Handling**: Failed journal creation doesn't break transaction categorization flow

### API Functionality:
- [ ] **Journal Entry CRUD**: Complete REST API for journal entry management
- [ ] **Account Journal History**: API to retrieve all journal entries for specific account
- [ ] **Account Balance API**: Real-time account balance calculation via API
- [ ] **Journal Entry Validation**: API endpoint to validate journal entry before creation
- [ ] **Search and Filtering**: Journal entries filterable by date, account, status, amount
- [ ] **Pagination Support**: Large journal entry lists properly paginated

### Frontend Integration:
- [ ] **Journal Entry Viewer**: Complete UI to view journal entry details with debit/credit layout
- [ ] **Account Balance Display**: Real-time account balances shown in account management
- [ ] **Journal Entry List**: Searchable, filterable list of all journal entries
- [ ] **Reversal Interface**: UI to reverse journal entries with reason and date selection
- [ ] **Integration with Transaction Flow**: Journal entries automatically created during transaction categorization

### Data Integrity:
- [ ] **Database Constraints**: Proper constraints prevent invalid journal entries at database level
- [ ] **Materialized View**: Account balances calculated efficiently via materialized view
- [ ] **Audit Trail**: Complete audit trail for all journal entry operations
- [ ] **Concurrent Access**: Optimistic locking prevents conflicting journal entry modifications
- [ ] **Performance**: Journal entry creation completes in < 2 seconds for typical transactions

---

## Standard Journal Entry Templates

### Transaction Categorization Examples:

**Expense Transaction (Mobile Money Payment):**
```
Dr. Office Supplies Expense               $50.00
    Cr. Mobile Money - MTN                    $50.00
Description: Office supplies purchase via MTN Mobile Money
```

**Income Transaction (Customer Payment):**
```
Dr. Mobile Money - Airtel                $1,200.00
    Cr. Service Revenue                           $1,200.00
Description: Customer payment received via Airtel Money
```

**Transfer Between Accounts:**
```
Dr. Bank Account - Stanbic               $500.00
    Cr. Mobile Money - MTN                    $500.00
Description: Transfer from MTN to bank account
```

### Invoice-Related Entries:

**Invoice Creation:**
```
Dr. Accounts Receivable                  $2,000.00
    Cr. Service Revenue                           $2,000.00
Description: Invoice #INV-001 for consulting services
```

**Invoice Payment Received:**
```
Dr. Mobile Money - MTN                   $2,000.00
    Cr. Accounts Receivable                       $2,000.00
Description: Payment received for Invoice #INV-001
```

---

## Error Handling & Edge Cases

### Validation Errors:
- **Unbalanced Entries**: Clear error message showing debit total vs credit total
- **Missing Account Mapping**: Specific error when Category lacks Account assignment
- **Invalid Account**: Error when journal line references non-existent account
- **Zero Amount Lines**: Prevent journal lines with both debit and credit as zero
- **Negative Amounts**: Prevent negative debit or credit amounts

### Business Logic Errors:
- **Duplicate Journal Entries**: Prevent multiple journal entries for same transaction
- **Reversal of Reversed Entry**: Prevent reversing already-reversed journal entries
- **Invalid Reversal Date**: Prevent reversal dates before original entry date
- **Account Type Validation**: Warn when debiting credit-normal accounts (and vice versa)

### System Errors:
- **Event Processing Failures**: Dead letter queue for failed journal entry creation
- **Database Constraint Violations**: Graceful handling with user-friendly messages
- **Concurrent Modification**: Optimistic locking with retry mechanism
- **Balance Calculation Errors**: Fallback to real-time calculation if materialized view fails

---

## Testing Requirements

### Unit Tests:
- [ ] **JournalService Tests**: All business logic methods with edge cases
- [ ] **Entity Validation Tests**: JournalEntry and JournalEntryLine validation rules
- [ ] **Repository Tests**: Database operations and custom queries
- [ ] **Event Handler Tests**: Transaction event processing and error scenarios
- [ ] **Balance Calculation Tests**: Account balance calculation accuracy

### Integration Tests:
- [ ] **End-to-End Transaction Flow**: Transaction categorization → Journal entry creation
- [ ] **API Integration Tests**: All REST endpoints with various scenarios
- [ ] **Database Integration**: Schema validation and constraint testing
- [ ] **Event Integration**: Messaging layer integration with journal creation
- [ ] **Frontend Integration**: React components with API service integration

### Performance Tests:
- [ ] **Journal Entry Creation**: < 2 seconds for typical transaction
- [ ] **Account Balance Calculation**: < 1 second for accounts with 1000+ entries
- [ ] **Journal Entry Search**: < 3 seconds for filtered searches
- [ ] **Materialized View Refresh**: < 5 seconds for 10,000+ journal entries

---

## Definition of Done

### Technical Completion:
- [ ] **Database Schema**: All tables, indexes, constraints, and materialized views created
- [ ] **Java Backend**: Complete service layer, repositories, controllers, and event handlers
- [ ] **REST API**: All endpoints implemented with proper error handling and validation
- [ ] **Frontend Components**: React components for viewing and managing journal entries
- [ ] **Event Integration**: Reliable event-driven journal creation from transaction events
- [ ] **Data Migration**: Database migration scripts tested and documented

### Quality Assurance:
- [ ] **Unit Test Coverage**: > 90% coverage for all service and repository classes
- [ ] **Integration Tests**: All API endpoints and event flows tested
- [ ] **Performance Requirements**: All performance criteria met under load testing
- [ ] **Error Handling**: Comprehensive error handling with user-friendly messages
- [ ] **Code Review**: All code reviewed and approved by senior developer

### Business Validation:
- [ ] **Accounting Accuracy**: All journal entries properly balanced and categorized
- [ ] **Audit Trail**: Complete audit trail for all journal entry operations
- [ ] **User Experience**: Intuitive interface for viewing and managing journal entries
- [ ] **Integration Seamless**: No disruption to existing transaction categorization flow
- [ ] **Documentation**: Complete API documentation and user guides

### Deployment Readiness:
- [ ] **Environment Configuration**: All environments configured with proper database setup
- [ ] **Monitoring**: Application monitoring for journal entry creation and errors
- [ ] **Backup Strategy**: Database backup strategy includes journal entry data
- [ ] **Rollback Plan**: Tested rollback procedure if deployment issues occur

---

## Dependencies & Prerequisites

### Must Complete First:
- **[Story 2.1: Chart of Accounts & Category Mapping](story-2.1-chart-of-accounts.md)** - Required for Account mapping
- **Database Migration Framework** - For schema changes
- **Messaging Infrastructure** - For event-driven journal creation

### Integration Points:
- **Transaction Categorization System** - Must integrate without breaking existing flow
- **User Authentication** - For audit trail and permissions
- **Account Management** - For account balance display

### Technical Dependencies:
- **Spring Boot 3.x** - For backend framework
- **PostgreSQL 14+** - For materialized view support
- **React 18+** - For frontend components
- **Spring Cloud Stream** - For messaging abstraction

---

## Notes & Considerations

### Accounting Standards:
This implementation follows standard double-entry bookkeeping principles where every transaction affects at least two accounts and total debits must equal total credits. The system maintains proper audit trails and supports standard accounting reports.

### Performance Considerations:
The materialized view approach for account balances provides excellent read performance while maintaining data consistency. For high-volume environments, consider implementing incremental balance updates instead of full materialized view refresh.

### Future Enhancements:
- **Multi-Currency Support**: Journal entries in different currencies
- **Advanced Reporting**: Trial balance, income statement, balance sheet generation
- **Batch Journal Entries**: Support for importing multiple journal entries
- **Approval Workflow**: Multi-step approval process for large journal entries

### Integration with Existing Systems:
This story maintains backward compatibility with existing transaction categorization while adding professional accounting capabilities. Users continue to categorize transactions normally, but the system now maintains proper accounting records behind the scenes.

---

**Related Stories:**
- [Story 2.1: Chart of Accounts & Category Mapping](story-2.1-chart-of-accounts.md)
- [Story 4.1: Transaction Categorization](../epic-04/story-4.1-transaction-categorization.md)

**Epic:** [Core Financial Engine & Accounting Integrity](../../epics-and-stories.md#epic-2-core-financial-engine--accounting-integrity)
```
```

## Technical Implementation

### Backend Changes
- `src/main/java/com/intellifin/model/JournalEntry.java` - Journal entry entity with debit/credit validation
- `src/main/java/com/intellifin/model/JournalEntryLine.java` - Individual debit/credit lines
- `src/main/java/com/intellifin/repository/JournalEntryRepository.java` - Journal entry data access
- `src/main/java/com/intellifin/service/JournalService.java` - Double-entry business logic
- `src/main/java/com/intellifin/service/AccountingEventHandler.java` - Event-driven journal creation
- `src/main/java/com/intellifin/controller/JournalController.java` - Journal entry API endpoints
- `src/main/java/com/intellifin/validation/JournalEntryValidator.java` - Balance validation
- `src/main/resources/db/migration/` - Database migrations for journal tables

### Frontend Changes
- `src/components/financial/JournalEntryView.tsx` - Journal entry display
- `src/components/financial/JournalEntryList.tsx` - Journal entries listing
- `src/components/financial/AccountBalanceDisplay.tsx` - Real-time balance updates
- `src/hooks/useJournalEntries.ts` - Journal entry data management
- `src/stores/journalStore.ts` - Journal entry state management

### Database Changes
- `journal_entries` table with transaction references and audit fields
- `journal_entry_lines` table for individual debit/credit entries
- `account_balances` materialized view for real-time balance calculations
- Triggers for automatic balance updates on journal entry changes
- Indexes for performance on journal entry queries

### Event Integration
- Subscribe to `TransactionConfirmedEvent` for transaction journal entries
- Subscribe to `InvoicePaidEvent` for invoice payment journal entries
- Subscribe to `ExpenseRecordedEvent` for expense journal entries
- Publish `JournalEntryCreatedEvent` for downstream processing
- Handle event failures with dead-letter queue processing

## API Contracts

```typescript
interface JournalEntry {
  id: string;
  transactionId?: string; // Source transaction reference
  invoiceId?: string; // Source invoice reference
  entryNumber: string; // Sequential journal entry number
  description: string;
  entryDate: string;
  totalAmount: number; // Total debit amount (must equal total credit)
  status: 'DRAFT' | 'POSTED' | 'REVERSED';
  userId: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  lines: JournalEntryLine[];
}

interface JournalEntryLine {
  id: string;
  journalEntryId: string;
  accountId: string;
  description: string;
  debitAmount: number; // 0 if credit entry
  creditAmount: number; // 0 if debit entry
  lineNumber: number; // Order within journal entry
  account?: Account; // Populated in responses
}

interface JournalAPI {
  GET /api/v1/journal-entries: {
    query: { 
      userId: string; 
      accountId?: string; 
      fromDate?: string; 
      toDate?: string; 
      status?: string;
      limit?: number; 
      offset?: number; 
    };
    response: { 
      journalEntries: JournalEntry[]; 
      total: number; 
      totalDebits: number;
      totalCredits: number;
    };
    errors: { 401: "Unauthorized", 500: "Server error" };
  };
  
  POST /api/v1/journal-entries: {
    body: {
      description: string;
      entryDate: string;
      lines: Omit<JournalEntryLine, 'id' | 'journalEntryId' | 'account'>[];
    };
    response: JournalEntry;
    errors: { 
      400: "Unbalanced entry or invalid data", 
      409: "Entry number conflict" 
    };
  };
  
  GET /api/v1/journal-entries/{id}: {
    response: JournalEntry;
    errors: { 404: "Journal entry not found" };
  };
  
  POST /api/v1/journal-entries/{id}/reverse: {
    body: { reason: string; reversalDate: string; };
    response: { 
      originalEntry: JournalEntry; 
      reversalEntry: JournalEntry; 
    };
    errors: { 
      400: "Entry already reversed", 
      404: "Journal entry not found" 
    };
  };
  
  GET /api/v1/accounts/{id}/journal-entries: {
    query: { fromDate?: string; toDate?: string; limit?: number; };
    response: { 
      journalEntries: JournalEntry[]; 
      runningBalance: number;
    };
  };
  
  POST /api/v1/journal-entries/validate: {
    body: {
      lines: Omit<JournalEntryLine, 'id' | 'journalEntryId' | 'account'>[];
    };
    response: { 
      isBalanced: boolean; 
      totalDebits: number; 
      totalCredits: number; 
      errors: string[]; 
    };
  };
}
```

## Standard Journal Entry Templates

### Transaction Categorization (Expense)
```
Dr. [Expense Account from Category mapping]     $100.00
    Cr. Cash and Cash Equivalents                   $100.00
```

### Transaction Categorization (Income)
```
Dr. Cash and Cash Equivalents                   $500.00
    Cr. [Revenue Account from Category mapping]         $500.00
```

### Invoice Payment Received
```
Dr. Cash and Cash Equivalents                   $1,000.00
    Cr. Accounts Receivable                             $1,000.00
```

### Invoice Creation
```
Dr. Accounts Receivable                         $1,000.00
    Cr. Service Revenue                                 $1,000.00
```

### Expense Payment
```
Dr. [Expense Account]                           $200.00
    Cr. Cash and Cash Equivalents                      $200.00
```

## Error Handling

- **Unbalanced Entries:** Prevent saving with clear validation messages showing debit/credit totals
- **Invalid Account Mapping:** Show available accounts when Category lacks Account mapping
- **Concurrent Modifications:** Optimistic locking prevents conflicting journal entry updates
- **Event Processing Failures:** Dead-letter queue handling for failed journal entry creation
- **Account Balance Calculation Errors:** Graceful handling with balance recalculation utilities
- **Journal Entry Reversal Errors:** Validate reversal eligibility and prevent duplicate reversals

## Definition of Done

- [ ] JournalEntry data model supports complete double-entry bookkeeping
- [ ] All financial transaction events automatically create balanced journal entries
- [ ] Journal entry validation prevents unbalanced entries from being saved
- [ ] Event-driven journal creation works reliably via messaging abstraction layer
- [ ] Account balances update in real-time based on journal entries
- [ ] Journal entry reversal functionality works correctly
- [ ] API endpoints support full journal entry lifecycle
- [ ] Integration with transaction categorization maintains journal integrity
- [ ] Journal entry search and filtering performs efficiently
- [ ] Audit trail captures all journal entry modifications
- [ ] Performance meets requirements (< 2 seconds for journal entry creation)
- [ ] Tests cover journal entry creation, validation, and reversal scenarios
- [ ] No breaking changes to existing transaction categorization system
- [ ] Documentation includes double-entry bookkeeping guide

## Dependencies

- [Story 2.1: Chart of Accounts & Category Mapping](story-2.1-chart-of-accounts.md) - Required for Account mapping
- [Story 3.1: Transaction Categorization](../epic-03/story-3.1-transaction-categorization.md) - Must integrate with journal creation
- Database migration framework
- Messaging abstraction layer (Spring Cloud Stream)

## Notes

This story implements the core double-entry bookkeeping engine that ensures all financial transactions maintain accounting integrity. Every financial event must result in balanced journal entries where total debits equal total credits.

The integration with the existing transaction categorization system is critical - when users categorize transactions, the system must automatically create the appropriate journal entries based on the Category-to-Account mapping established in Story 2.1.

This foundational accounting engine enables proper financial reporting, audit compliance, and business intelligence while maintaining the user-friendly transaction categorization interface.

---

**Related Stories:**
- [Story 2.1: Chart of Accounts & Category Mapping](story-2.1-chart-of-accounts.md)
- [Story 3.1: Transaction Categorization](../epic-03/story-3.1-transaction-categorization.md)

**Epic:** [Core Financial Engine & Accounting Integrity](../../epics-and-stories.md#epic-2-core-financial-engine--accounting-integrity)
