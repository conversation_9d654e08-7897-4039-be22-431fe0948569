package com.intellifin.events;

import java.time.LocalDateTime;
import java.util.UUID;

public class IntentFailedEvent {
    private String eventId;
    private String userId;
    private String sessionId;
    private String command;
    private String errorMessage;
    private String errorType;
    private LocalDateTime timestamp;

    public IntentFailedEvent(String userId, String sessionId, String command,
                           String errorMessage, String errorType) {
        this.eventId = UUID.randomUUID().toString();
        this.userId = userId;
        this.sessionId = sessionId;
        this.command = command;
        this.errorMessage = errorMessage;
        this.errorType = errorType;
        this.timestamp = LocalDateTime.now();
    }

    // Getters
    public String getEventId() { return eventId; }
    public String getUserId() { return userId; }
    public String getSessionId() { return sessionId; }
    public String getCommand() { return command; }
    public String getErrorMessage() { return errorMessage; }
    public String getErrorType() { return errorType; }
    public LocalDateTime getTimestamp() { return timestamp; }
}
