# IntelliFin Transaction Categorization - Deployment Guide

## Prerequisites

### System Requirements

- **CPU**: 4+ cores recommended
- **Memory**: 8GB+ RAM
- **Storage**: 50GB+ available space
- **Network**: Stable internet connection for AI services

### Software Dependencies

- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Node.js**: 18+ (for local development)
- **Java**: 17+ (for local development)
- **Python**: 3.9+ (for local development)

### External Services

- **Google Cloud**: Gemini API access
- **Database**: PostgreSQL 14+
- **Message Broker**: RabbitMQ 3.9+ or Azure Service Bus
- **Cache**: Redis (optional, for production)

## Environment Setup

### 1. Clone Repository

```bash
git clone https://github.com/your-org/intellifin.git
cd intellifin
```

### 2. Environment Configuration

Create environment files for each service:

#### `.env.java-core`
```env
# Database Configuration
SPRING_DATASOURCE_URL=******************************************
SPRING_DATASOURCE_USERNAME=intellifin
SPRING_DATASOURCE_PASSWORD=your_secure_password

# Messaging Configuration
RABBITMQ_HOST=rabbitmq
RABBITMQ_PORT=5672
RABBITMQ_USERNAME=intellifin
RABBITMQ_PASSWORD=your_rabbitmq_password
RABBITMQ_VHOST=/

# Cache Configuration
SPRING_CACHE_TYPE=simple

# Security
JWT_SECRET=your_jwt_secret_key_here
CORS_ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com

# Monitoring
MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE=health,info,metrics
```

#### `.env.python-ai`
```env
# AI Configuration
GOOGLE_API_KEY=your_google_gemini_api_key
AI_MODEL_NAME=gemini-pro
AI_TEMPERATURE=0.1
AI_MAX_TOKENS=1000

# Messaging Configuration
MESSAGING_PROVIDER=rabbitmq
RABBITMQ_HOST=rabbitmq
RABBITMQ_PORT=5672
RABBITMQ_USERNAME=intellifin
RABBITMQ_PASSWORD=your_rabbitmq_password
RABBITMQ_VHOST=/

# Performance
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=30

# Logging
LOG_LEVEL=INFO
```

#### `.env.frontend`
```env
# API Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080
NEXT_PUBLIC_WS_BASE_URL=ws://localhost:8080

# Authentication
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret

# Features
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING=true
```

### 3. Production Environment Variables

#### `.env.production`
```env
# Database (Production)
SPRING_DATASOURCE_URL=***************************************************
SPRING_DATASOURCE_USERNAME=intellifin_prod
SPRING_DATASOURCE_PASSWORD=your_production_db_password

# Messaging (Azure Service Bus)
MESSAGING_PROVIDER=azure_servicebus
AZURE_SERVICEBUS_CONNECTION_STRING=Endpoint=sb://your-namespace.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=your-key

# Cache (Redis)
SPRING_CACHE_TYPE=redis
SPRING_REDIS_HOST=your-redis-host
SPRING_REDIS_PORT=6379
SPRING_REDIS_PASSWORD=your_redis_password

# Security (Production)
JWT_SECRET=your_production_jwt_secret
CORS_ALLOWED_ORIGINS=https://yourdomain.com

# SSL/TLS
SERVER_SSL_ENABLED=true
SERVER_SSL_KEY_STORE=classpath:keystore.p12
SERVER_SSL_KEY_STORE_PASSWORD=your_keystore_password
```

## Local Development Deployment

### 1. Start Infrastructure Services

```bash
# Start PostgreSQL and RabbitMQ
docker-compose up -d postgres rabbitmq

# Wait for services to be ready
docker-compose logs -f postgres rabbitmq
```

### 2. Initialize Database

```bash
# Run database migrations
cd apps/backend-java-core
./mvnw flyway:migrate

# Initialize default categories
./mvnw spring-boot:run -Dspring-boot.run.arguments="--init-categories=true"
```

### 3. Start Backend Services

```bash
# Terminal 1: Java Core Service
cd apps/backend-java-core
./mvnw spring-boot:run

# Terminal 2: Python AI Service
cd apps/backend-python-ai
pip install -r requirements.txt
python -m uvicorn src.main:app --reload --port 8081
```

### 4. Start Frontend

```bash
# Terminal 3: Next.js Frontend
cd apps/frontend-nextjs
npm install
npm run dev
```

### 5. Verify Deployment

```bash
# Check service health
curl http://localhost:8080/api/v1/health
curl http://localhost:8081/api/v1/categorization/health
curl http://localhost:3000/api/health

# Test categorization
curl -X POST http://localhost:8081/api/v1/categorization/categorize \
  -H "Content-Type: application/json" \
  -d '{
    "transaction_id": "test-123",
    "description": "Fuel purchase at Shell",
    "amount": 150.0,
    "transaction_type": "EXPENSE",
    "user_id": "test-user"
  }'
```

## Production Deployment

### Option 1: Docker Compose

#### 1. Production Docker Compose

Create `docker-compose.prod.yml`:

```yaml
version: '3.8'

services:
  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: intellifin_prod
      POSTGRES_USER: intellifin_prod
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    ports:
      - "6379:6379"
    restart: unless-stopped

  java-core:
    build:
      context: ./apps/backend-java-core
      dockerfile: Dockerfile.prod
    environment:
      SPRING_PROFILES_ACTIVE: production
      SPRING_DATASOURCE_URL: ******************************************_prod
      SPRING_DATASOURCE_USERNAME: intellifin_prod
      SPRING_DATASOURCE_PASSWORD: ${POSTGRES_PASSWORD}
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PASSWORD: ${REDIS_PASSWORD}
      AZURE_SERVICEBUS_CONNECTION_STRING: ${AZURE_SERVICEBUS_CONNECTION_STRING}
    ports:
      - "8080:8080"
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  python-ai:
    build:
      context: ./apps/backend-python-ai
      dockerfile: Dockerfile.prod
    environment:
      GOOGLE_API_KEY: ${GOOGLE_API_KEY}
      MESSAGING_PROVIDER: azure_servicebus
      AZURE_SERVICEBUS_CONNECTION_STRING: ${AZURE_SERVICEBUS_CONNECTION_STRING}
    ports:
      - "8081:8081"
    restart: unless-stopped

  frontend:
    build:
      context: ./apps/frontend-nextjs
      dockerfile: Dockerfile.prod
    environment:
      NEXT_PUBLIC_API_BASE_URL: https://api.yourdomain.com
      NEXT_PUBLIC_WS_BASE_URL: wss://api.yourdomain.com
    ports:
      - "3000:3000"
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - java-core
      - python-ai
      - frontend
    restart: unless-stopped

volumes:
  postgres_data:
```

#### 2. Deploy Production

```bash
# Build and deploy
docker-compose -f docker-compose.prod.yml build
docker-compose -f docker-compose.prod.yml up -d

# Check deployment
docker-compose -f docker-compose.prod.yml ps
docker-compose -f docker-compose.prod.yml logs -f
```

### Option 2: Kubernetes

#### 1. Create Kubernetes Manifests

```bash
# Create namespace
kubectl create namespace intellifin

# Apply configurations
kubectl apply -f k8s/configmaps/
kubectl apply -f k8s/secrets/
kubectl apply -f k8s/deployments/
kubectl apply -f k8s/services/
kubectl apply -f k8s/ingress/
```

#### 2. Monitor Deployment

```bash
# Check pod status
kubectl get pods -n intellifin

# Check services
kubectl get services -n intellifin

# View logs
kubectl logs -f deployment/java-core -n intellifin
kubectl logs -f deployment/python-ai -n intellifin
```

### Option 3: Cloud Platform Deployment

#### Azure Container Apps

```bash
# Create resource group
az group create --name intellifin-rg --location eastus

# Create container app environment
az containerapp env create \
  --name intellifin-env \
  --resource-group intellifin-rg \
  --location eastus

# Deploy services
az containerapp create \
  --name intellifin-java-core \
  --resource-group intellifin-rg \
  --environment intellifin-env \
  --image your-registry/intellifin-java-core:latest \
  --target-port 8080 \
  --ingress external
```

## SSL/TLS Configuration

### 1. Generate SSL Certificates

```bash
# Using Let's Encrypt with Certbot
certbot certonly --standalone -d yourdomain.com -d api.yourdomain.com

# Or use existing certificates
cp your-certificate.crt /etc/ssl/certs/
cp your-private-key.key /etc/ssl/private/
```

### 2. Configure Nginx

Create `nginx/nginx.conf`:

```nginx
events {
    worker_connections 1024;
}

http {
    upstream java-backend {
        server java-core:8080;
    }

    upstream python-backend {
        server python-ai:8081;
    }

    upstream frontend {
        server frontend:3000;
    }

    server {
        listen 80;
        server_name yourdomain.com api.yourdomain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name api.yourdomain.com;

        ssl_certificate /etc/nginx/ssl/certificate.crt;
        ssl_certificate_key /etc/nginx/ssl/private.key;

        location /api/v1/categorization/ {
            proxy_pass http://python-backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }

        location /api/ {
            proxy_pass http://java-backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }

        location /ws/ {
            proxy_pass http://java-backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
    }

    server {
        listen 443 ssl http2;
        server_name yourdomain.com;

        ssl_certificate /etc/nginx/ssl/certificate.crt;
        ssl_certificate_key /etc/nginx/ssl/private.key;

        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
}
```

## Monitoring and Maintenance

### 1. Health Monitoring

```bash
# Create health check script
cat > health-check.sh << 'EOF'
#!/bin/bash

services=("java-core:8080" "python-ai:8081" "frontend:3000")

for service in "${services[@]}"; do
    if curl -f "http://$service/health" > /dev/null 2>&1; then
        echo "✅ $service is healthy"
    else
        echo "❌ $service is unhealthy"
    fi
done
EOF

chmod +x health-check.sh
```

### 2. Log Management

```bash
# Configure log rotation
cat > /etc/logrotate.d/intellifin << 'EOF'
/var/log/intellifin/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
}
EOF
```

### 3. Backup Strategy

```bash
# Database backup script
cat > backup-db.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h postgres -U intellifin_prod intellifin_prod > backup_$DATE.sql
aws s3 cp backup_$DATE.sql s3://your-backup-bucket/
rm backup_$DATE.sql
EOF
```

## Troubleshooting

### Common Issues

1. **Service Won't Start**
   ```bash
   # Check logs
   docker-compose logs service-name
   
   # Check resource usage
   docker stats
   ```

2. **Database Connection Issues**
   ```bash
   # Test database connectivity
   docker exec -it postgres psql -U intellifin_prod -d intellifin_prod
   ```

3. **AI Service Errors**
   ```bash
   # Check API key
   curl -H "Authorization: Bearer $GOOGLE_API_KEY" \
        https://generativelanguage.googleapis.com/v1/models
   ```

4. **WebSocket Connection Failures**
   ```bash
   # Check WebSocket endpoint
   wscat -c ws://localhost:8080/ws/transaction-updates
   ```

### Performance Optimization

1. **Database Optimization**
   ```sql
   -- Add indexes for better performance
   CREATE INDEX idx_transactions_user_date ON transactions(user_id, date DESC);
   CREATE INDEX idx_transactions_status ON transactions(status);
   ```

2. **Cache Configuration**
   ```yaml
   # Redis cache tuning
   redis:
     maxmemory: 256mb
     maxmemory-policy: allkeys-lru
   ```

3. **JVM Tuning**
   ```bash
   # Java service optimization
   JAVA_OPTS="-Xms512m -Xmx2g -XX:+UseG1GC"
   ```

## Security Checklist

- [ ] SSL/TLS certificates configured
- [ ] Database passwords are strong and unique
- [ ] API keys are stored securely
- [ ] CORS is properly configured
- [ ] Rate limiting is enabled
- [ ] Security headers are set
- [ ] Regular security updates applied
- [ ] Backup encryption enabled
- [ ] Access logs monitored
- [ ] Firewall rules configured

## Post-Deployment Verification

### 1. Functional Testing
```bash
# Test transaction creation and categorization flow
curl -X POST https://api.yourdomain.com/api/v1/transactions \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "description": "Test transaction",
    "amount": 100.0,
    "type": "EXPENSE",
    "date": "2024-01-15"
  }'
```

### 2. Performance Testing
```bash
# Load test categorization endpoint
k6 run --vus 10 --duration 30s performance-test.js
```

### 3. Monitoring Setup
```bash
# Configure alerts for critical metrics
# - Response time > 5 seconds
# - Error rate > 5%
# - Memory usage > 80%
# - Disk usage > 90%
```
