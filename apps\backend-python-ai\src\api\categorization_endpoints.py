"""
FastAPI endpoints for transaction categorization
"""
import logging
from typing import List, Dict, Any
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from pydantic import BaseModel, Field

from ..services.categorization import TransactionCategorizationService
from ..services.messaging_service import (
    MessagingServiceFactory, 
    TransactionCategorizationEventHandler
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/categorization", tags=["categorization"])

# Pydantic models for API
class TransactionCategorizationRequest(BaseModel):
    transaction_id: str = Field(..., description="Unique transaction identifier")
    description: str = Field(..., description="Transaction description")
    amount: float = Field(..., description="Transaction amount")
    transaction_type: str = Field(..., description="INCOME or EXPENSE")
    user_id: str = Field(..., description="User identifier")
    custom_categories: List[str] = Field(default=[], description="User-defined categories")


class BulkCategorizationRequest(BaseModel):
    transactions: List[Dict[str, Any]] = Field(..., description="List of transactions to categorize")
    user_id: str = Field(..., description="User identifier")


class CategorizationResponse(BaseModel):
    success: bool
    category_id: str
    category_name: str
    category_type: str
    confidence: float
    explanation: str
    keywords: List[str]
    processing_time: int
    model_used: str


class BulkCategorizationResponse(BaseModel):
    batch_id: str
    processed_count: int
    success_count: int
    failure_count: int
    processing_time: int
    results: List[Dict[str, Any]]


# Dependency injection
async def get_categorization_service() -> TransactionCategorizationService:
    """Get categorization service instance"""
    return TransactionCategorizationService()


async def get_messaging_service():
    """Get messaging service instance"""
    return MessagingServiceFactory.create_messaging_service()


@router.post("/categorize", response_model=CategorizationResponse)
async def categorize_transaction(
    request: TransactionCategorizationRequest,
    categorization_service: TransactionCategorizationService = Depends(get_categorization_service)
):
    """
    Categorize a single transaction using AI
    """
    try:
        logger.info(f"Categorizing transaction {request.transaction_id} for user {request.user_id}")

        result = await categorization_service.categorize_transaction(
            description=request.description,
            amount=request.amount,
            transaction_type=request.transaction_type,
            user_id=request.user_id,
            custom_categories=request.custom_categories if request.custom_categories else None
        )

        return CategorizationResponse(
            success=True,
            category_id=result["category_id"],
            category_name=result["category_name"],
            category_type=result["category_type"],
            confidence=result["confidence"],
            explanation=result["explanation"],
            keywords=result["keywords"],
            processing_time=result["processing_time"],
            model_used=result["model_used"]
        )

    except Exception as e:
        logger.error(f"Error categorizing transaction {request.transaction_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to categorize transaction: {str(e)}"
        )


@router.post("/suggest-category-realtime", response_model=CategorizationResponse)
async def suggest_category_realtime(
    request: TransactionCategorizationRequest,
    categorization_service: TransactionCategorizationService = Depends(get_categorization_service)
):
    """
    Get real-time category suggestions for manual transaction entry
    """
    try:
        logger.info(f"Getting real-time category suggestion for user {request.user_id}")

        # Use the same categorization service but with faster processing
        result = await categorization_service.categorize_transaction(
            description=request.description,
            amount=request.amount,
            transaction_type=request.transaction_type,
            user_id=request.user_id,
            custom_categories=request.custom_categories if request.custom_categories else None
        )

        # Add alternative categories for real-time suggestions
        alternative_categories = []
        if result["confidence"] < 0.9:  # If not highly confident, provide alternatives
            # This would typically use a different method to get alternatives
            # For now, we'll return the main suggestion
            pass

        response = CategorizationResponse(
            success=True,
            category_id=result["category_id"],
            category_name=result["category_name"],
            category_type=result["category_type"],
            confidence=result["confidence"],
            explanation=result["explanation"],
            keywords=result["keywords"],
            processing_time=result["processing_time"],
            model_used=result["model_used"]
        )

        # Add alternative categories to response if available
        # response.alternative_categories = alternative_categories

        return response

    except Exception as e:
        logger.error(f"Error getting real-time category suggestion for user {request.user_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get category suggestion: {str(e)}"
        )


@router.post("/bulk-categorize", response_model=BulkCategorizationResponse)
async def bulk_categorize_transactions(
    request: BulkCategorizationRequest,
    background_tasks: BackgroundTasks,
    categorization_service: TransactionCategorizationService = Depends(get_categorization_service),
    messaging_service = Depends(get_messaging_service)
):
    """
    Categorize multiple transactions in bulk
    """
    try:
        logger.info(f"Bulk categorizing {len(request.transactions)} transactions for user {request.user_id}")
        
        result = await categorization_service.bulk_categorize_transactions(
            transactions=request.transactions,
            user_id=request.user_id
        )
        
        # Publish bulk completion event in background
        background_tasks.add_task(
            publish_bulk_completion_event,
            messaging_service,
            result
        )
        
        return BulkCategorizationResponse(
            batch_id=result["batch_id"],
            processed_count=result["processed_count"],
            success_count=result["success_count"],
            failure_count=result["failure_count"],
            processing_time=result["processing_time"],
            results=result["results"]
        )
        
    except Exception as e:
        logger.error(f"Error in bulk categorization for user {request.user_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to bulk categorize transactions: {str(e)}"
        )


@router.post("/feedback/positive")
async def record_positive_feedback(
    transaction_id: str,
    category_id: str,
    feedback: str = None,
    categorization_service: TransactionCategorizationService = Depends(get_categorization_service)
):
    """
    Record positive feedback for AI learning
    """
    try:
        await categorization_service.record_positive_feedback(
            transaction_id=transaction_id,
            category_id=category_id,
            feedback=feedback
        )
        
        return {"success": True, "message": "Positive feedback recorded"}
        
    except Exception as e:
        logger.error(f"Error recording positive feedback: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to record feedback: {str(e)}"
        )


@router.post("/feedback/negative")
async def record_negative_feedback(
    transaction_id: str,
    rejected_category_id: str,
    correct_category_id: str = None,
    reason: str = None,
    categorization_service: TransactionCategorizationService = Depends(get_categorization_service)
):
    """
    Record negative feedback for AI learning
    """
    try:
        await categorization_service.record_negative_feedback(
            transaction_id=transaction_id,
            rejected_category_id=rejected_category_id,
            correct_category_id=correct_category_id,
            reason=reason
        )
        
        return {"success": True, "message": "Negative feedback recorded"}
        
    except Exception as e:
        logger.error(f"Error recording negative feedback: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to record feedback: {str(e)}"
        )


@router.get("/health")
async def health_check(
    categorization_service: TransactionCategorizationService = Depends(get_categorization_service)
):
    """
    Health check endpoint for categorization service
    """
    try:
        # Test a simple categorization
        test_result = await categorization_service.categorize_transaction(
            description="Test transaction",
            amount=100.0,
            transaction_type="EXPENSE",
            user_id="test-user"
        )
        
        return {
            "status": "healthy",
            "model_info": categorization_service.ai_config.get_model_info(),
            "test_categorization": {
                "category": test_result["category_name"],
                "confidence": test_result["confidence"],
                "processing_time": test_result["processing_time"]
            }
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }


async def publish_bulk_completion_event(messaging_service, result: Dict[str, Any]):
    """Background task to publish bulk completion event"""
    try:
        from ..services.messaging_service import BulkCategorizationCompletedEvent, MessageEvent
        from datetime import datetime
        
        event = BulkCategorizationCompletedEvent(
            batch_id=result["batch_id"],
            processed_count=result["processed_count"],
            success_count=result["success_count"],
            failure_count=result["failure_count"],
            timestamp=datetime.utcnow().isoformat()
        )
        
        message_event = MessageEvent(
            event_type="BulkCategorizationCompleted",
            correlation_id=result["batch_id"],
            timestamp=event.timestamp,
            payload=event.__dict__
        )
        
        await messaging_service.publish_event("bulk.categorization.completed", message_event)
        logger.info(f"Published bulk completion event for batch {result['batch_id']}")
        
    except Exception as e:
        logger.error(f"Failed to publish bulk completion event: {str(e)}")


# Add router to main app
def include_categorization_routes(app):
    """Include categorization routes in the main FastAPI app"""
    app.include_router(router)
