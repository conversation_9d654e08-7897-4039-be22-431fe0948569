# IntelliFin Transaction Categorization System

## 🎯 Overview

The IntelliFin Transaction Categorization System is an AI-powered solution that automatically categorizes financial transactions using Google's Gemini Pro model, with real-time updates, user feedback learning, and comprehensive error handling.

## ✨ Key Features

### 🤖 AI-Powered Categorization
- **Intelligent Classification**: Uses Google Gemini Pro for accurate transaction categorization
- **Zambian Context**: Optimized for Zambian businesses and vendors (Airtel, MTN, ZESCO, etc.)
- **Confidence Scoring**: Provides confidence levels for AI suggestions
- **Fallback Handling**: Keyword-based categorization when AI is unavailable

### 🔄 Real-Time Updates
- **WebSocket Integration**: Live updates for categorization results
- **Event-Driven Architecture**: Asynchronous processing with messaging
- **Performance Monitoring**: Tracks categorization speed and accuracy

### 👤 User Experience
- **Accept/Reject AI Suggestions**: Users can approve or correct AI categorizations
- **Custom Categories**: Support for user-defined categories alongside system defaults
- **Bulk Operations**: Process multiple transactions simultaneously
- **Learning System**: Improves accuracy from user feedback

### 🏗️ Enterprise-Ready
- **Scalable Architecture**: Microservices with horizontal scaling
- **Comprehensive Testing**: Unit, integration, and E2E tests
- **Production Deployment**: Docker, Kubernetes, and cloud-ready
- **Monitoring & Observability**: Health checks, metrics, and logging

## 🏛️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Java Core     │    │   Python AI     │
│   (Next.js)     │◄──►│   (Spring)      │◄──►│   (FastAPI)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └──────────────►│   PostgreSQL    │◄─────────────┘
                        └─────────────────┘
                                 │
                        ┌─────────────────┐
                        │   RabbitMQ/     │
                        │   Azure Bus     │
                        └─────────────────┘
```

### Components

1. **Frontend (Next.js 15)**: React-based UI with real-time updates
2. **Java Core Service**: Main API and transaction management
3. **Python AI Service**: AI categorization using LangChain + Gemini
4. **PostgreSQL**: Primary database for transactions and categories
5. **Message Broker**: Event-driven communication (RabbitMQ/Azure Service Bus)
6. **WebSocket Layer**: Real-time updates to frontend

## 🚀 Quick Start

### Prerequisites

- Docker & Docker Compose
- Node.js 18+ (for local development)
- Java 17+ (for local development)
- Python 3.9+ (for local development)
- Google Cloud API key for Gemini

### 1. Clone and Setup

```bash
git clone https://github.com/your-org/intellifin.git
cd intellifin

# Copy environment template
cp .env.production.template .env.local

# Edit environment variables
nano .env.local
```

### 2. Start Infrastructure

```bash
# Start PostgreSQL and RabbitMQ
docker-compose up -d postgres rabbitmq

# Wait for services to be ready
docker-compose logs -f postgres rabbitmq
```

### 3. Start Services

```bash
# Terminal 1: Java Core Service
cd apps/backend-java-core
./mvnw spring-boot:run

# Terminal 2: Python AI Service
cd apps/backend-python-ai
pip install -r requirements.txt
python -m uvicorn src.main:app --reload --port 8081

# Terminal 3: Frontend
cd apps/frontend-nextjs
npm install
npm run dev
```

### 4. Verify Installation

```bash
# Check service health
curl http://localhost:8080/api/v1/health
curl http://localhost:8081/api/v1/categorization/health

# Open frontend
open http://localhost:3000
```

## 📖 Usage Examples

### Create and Categorize Transaction

```bash
# Create a new transaction
curl -X POST http://localhost:8080/api/v1/transactions \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "description": "Fuel purchase at Shell station",
    "amount": 150.0,
    "type": "EXPENSE",
    "date": "2024-01-15",
    "source": "manual"
  }'

# AI will automatically categorize the transaction
# Check for AI suggestion via WebSocket or polling

# Accept AI suggestion
curl -X PUT http://localhost:8080/api/v1/transactions/{id}/categorize \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "categoryId": "transport-category-id",
    "isAISuggestion": true,
    "explanation": "Accepted AI suggestion"
  }'
```

### Bulk Categorization

```bash
curl -X POST http://localhost:8080/api/v1/transactions/bulk-categorize \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "transactions": [
      {
        "transactionId": "txn-1",
        "categoryId": "transport-id",
        "isAISuggestion": false
      },
      {
        "transactionId": "txn-2", 
        "categoryId": "food-id",
        "isAISuggestion": true
      }
    ]
  }'
```

## 🧪 Testing

### Run All Tests

```bash
# Backend Java tests
cd apps/backend-java-core
./mvnw test

# Python AI service tests
cd apps/backend-python-ai
pytest

# Frontend tests
cd apps/frontend-nextjs
npm test

# E2E tests
npm run test:e2e
```

### Performance Testing

```bash
# Load test categorization endpoint
k6 run tests/performance/categorization-load-test.js
```

## 🚢 Production Deployment

### Docker Compose

```bash
# Build and deploy
docker-compose -f docker-compose.prod.yml build
docker-compose -f docker-compose.prod.yml up -d

# Check deployment
docker-compose -f docker-compose.prod.yml ps
```

### Kubernetes

```bash
# Deploy to Kubernetes
kubectl apply -f k8s/namespace.yaml
kubectl apply -f k8s/configmaps/
kubectl apply -f k8s/secrets/
kubectl apply -f k8s/deployments/
kubectl apply -f k8s/services/
kubectl apply -f k8s/ingress/
```

### Environment Configuration

1. Copy `.env.production.template` to `.env.production`
2. Fill in all required values:
   - Database credentials
   - Google API key
   - JWT secrets
   - Domain names
   - SSL certificates

## 📊 Monitoring

### Health Checks

```bash
# Service health
curl https://api.yourdomain.com/api/v1/health
curl https://ai.yourdomain.com/api/v1/categorization/health

# WebSocket connection
wscat -c wss://api.yourdomain.com/ws/transaction-updates
```

### Metrics

- **Categorization Accuracy**: % of correct AI categorizations
- **Response Time**: Average categorization processing time
- **User Feedback Rate**: % of AI suggestions reviewed
- **Error Rate**: Failed categorization attempts
- **WebSocket Health**: Real-time connection status

### Grafana Dashboards

Access monitoring at `http://localhost:3001` (Grafana) with dashboards for:
- Transaction categorization performance
- AI model accuracy trends
- System resource usage
- Error rates and alerts

## 🔧 Configuration

### AI Model Settings

```env
GOOGLE_API_KEY=your_api_key
AI_MODEL_NAME=gemini-pro
AI_TEMPERATURE=0.1
AI_MAX_TOKENS=1000
```

### Performance Tuning

```env
# Java JVM
JAVA_OPTS=-Xms1g -Xmx2g -XX:+UseG1GC

# Python workers
PYTHON_WORKERS=4
MAX_CONCURRENT_REQUESTS=10

# Database
DB_MAX_CONNECTIONS=20
DB_CONNECTION_TIMEOUT=30
```

### Rate Limiting

```env
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_AI_REQUESTS_PER_MINUTE=50
RATE_LIMIT_BULK_REQUESTS_PER_MINUTE=10
```

## 🐛 Troubleshooting

### Common Issues

1. **AI Service Unavailable**
   ```bash
   # Check API key
   curl -H "Authorization: Bearer $GOOGLE_API_KEY" \
        https://generativelanguage.googleapis.com/v1/models
   ```

2. **Database Connection Issues**
   ```bash
   # Test database
   docker exec -it postgres psql -U intellifin_prod -d intellifin_prod
   ```

3. **WebSocket Connection Failures**
   ```bash
   # Test WebSocket
   wscat -c ws://localhost:8080/ws/transaction-updates
   ```

### Debug Commands

```bash
# View logs
docker logs intellifin-java-core
docker logs intellifin-python-ai

# Check message queues
rabbitmqctl list_queues

# Database queries
psql -h localhost -U intellifin_prod -d intellifin_prod \
     -c "SELECT COUNT(*) FROM transactions WHERE status = 'PENDING_CLASSIFICATION';"
```

## 📚 Documentation

- [API Reference](docs/API_REFERENCE.md)
- [Deployment Guide](docs/DEPLOYMENT_GUIDE.md)
- [Architecture Documentation](docs/TRANSACTION_CATEGORIZATION.md)
- [Testing Guide](docs/TESTING.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the GitHub repository
- Contact the development team
- Check the documentation and troubleshooting guides

## 🔮 Roadmap

- [ ] Advanced ML model training on user data
- [ ] Multi-language support for local languages
- [ ] Mobile app integration
- [ ] Advanced spending analytics
- [ ] Direct bank API integrations
- [ ] Custom rule-based categorization
- [ ] Automated expense reporting
