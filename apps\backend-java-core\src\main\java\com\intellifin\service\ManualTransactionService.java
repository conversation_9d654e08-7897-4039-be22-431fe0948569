package com.intellifin.service;

import com.intellifin.dto.ManualTransactionDto;
import com.intellifin.dto.TransactionDraftDto;
import com.intellifin.model.Transaction;
import com.intellifin.model.TransactionDraft;
import com.intellifin.repository.TransactionRepository;
import com.intellifin.repository.TransactionDraftRepository;
import com.intellifin.validation.TransactionValidator;
import com.intellifin.messaging.TransactionMessagingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Service for manual transaction entry and draft management
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ManualTransactionService {

    private final TransactionRepository transactionRepository;
    private final TransactionDraftRepository draftRepository;
    private final TransactionValidator validator;
    private final TransactionService transactionService;
    private final JournalService journalService;
    private final AICategoryService aiCategoryService;
    private final TransactionMessagingService messagingService;

    /**
     * Create a manual transaction
     */
    @Transactional
    public ManualTransactionDto createManualTransaction(ManualTransactionDto dto) {
        log.info("Creating manual transaction for user: {}", dto.getUserId());
        
        // Validate transaction
        TransactionValidator.ValidationResult validation = validator.validateForCreation(dto);
        if (!validation.isValid() && !Boolean.TRUE.equals(dto.getForceCreate())) {
            throw new IllegalArgumentException("Transaction validation failed: " + 
                    validation.getErrors().stream()
                            .map(TransactionValidator.ValidationError::getMessage)
                            .collect(Collectors.joining(", ")));
        }
        
        // Convert to transaction entity
        Transaction transaction = dto.toTransaction();
        transaction.setSource("MANUAL");
        
        // Set status based on categorization
        if (dto.getCategoryId() != null) {
            transaction.setStatus(Transaction.TransactionStatus.CATEGORIZED);
        } else {
            transaction.setStatus(Transaction.TransactionStatus.PENDING_CLASSIFICATION);
        }
        
        // Save transaction
        Transaction savedTransaction = transactionRepository.save(transaction);
        
        // Create journal entries if categorized
        if (savedTransaction.getCategoryId() != null) {
            try {
                journalService.createJournalEntriesForTransaction(savedTransaction, savedTransaction.getCategoryId());
                log.info("Created journal entries for manual transaction: {}", savedTransaction.getId());
            } catch (Exception e) {
                log.error("Failed to create journal entries for manual transaction: {}", savedTransaction.getId(), e);
                // Don't fail the transaction creation, but log the error
            }
        } else {
            // Trigger AI categorization for uncategorized transactions
            aiCategoryService.requestCategorization(savedTransaction.getId());
        }
        
        // Delete associated draft if exists
        if (dto.getDraftId() != null) {
            draftRepository.deleteById(dto.getDraftId());
            log.info("Deleted draft {} after creating transaction", dto.getDraftId());
        }
        
        // Publish transaction created event
        messagingService.publishTransactionCreated(savedTransaction);
        
        log.info("Successfully created manual transaction: {}", savedTransaction.getId());
        return ManualTransactionDto.fromTransaction(savedTransaction);
    }

    /**
     * Save transaction as draft
     */
    @Transactional
    public TransactionDraftDto saveDraft(TransactionDraftDto dto) {
        log.info("Saving transaction draft for user: {}", dto.getUserId());
        
        TransactionDraft draft;
        
        if (dto.getId() != null) {
            // Update existing draft
            Optional<TransactionDraft> existingOpt = draftRepository.findById(dto.getId());
            if (existingOpt.isEmpty()) {
                throw new IllegalArgumentException("Draft not found: " + dto.getId());
            }
            
            draft = existingOpt.get();
            updateDraftFromDto(draft, dto);
        } else {
            // Create new draft
            draft = dto.toEntity();
            draft.setDefaultExpiration();
        }
        
        TransactionDraft savedDraft = draftRepository.save(draft);
        
        log.info("Saved transaction draft: {}", savedDraft.getId());
        return TransactionDraftDto.fromEntity(savedDraft);
    }

    /**
     * Auto-save draft (for real-time saving as user types)
     */
    @Transactional
    public TransactionDraftDto autoSaveDraft(UUID userId, TransactionDraftDto dto) {
        log.debug("Auto-saving draft for user: {}", userId);
        
        // Find existing auto-saved draft for user
        Optional<TransactionDraft> existingOpt = draftRepository.findMostRecentAutoSavedDraft(userId);
        
        TransactionDraft draft;
        if (existingOpt.isPresent()) {
            draft = existingOpt.get();
            updateDraftFromDto(draft, dto);
        } else {
            draft = dto.toEntity();
            draft.setUserId(userId);
            draft.setIsAutoSaved(true);
            draft.setDefaultExpiration();
        }
        
        TransactionDraft savedDraft = draftRepository.save(draft);
        
        log.debug("Auto-saved draft: {}", savedDraft.getId());
        return TransactionDraftDto.fromEntity(savedDraft);
    }

    /**
     * Get drafts for a user
     */
    @Transactional(readOnly = true)
    public Page<TransactionDraftDto> getDrafts(UUID userId, Pageable pageable) {
        log.debug("Getting drafts for user: {}", userId);
        
        Page<TransactionDraft> drafts = draftRepository.findByUserIdOrderByUpdatedAtDesc(userId, pageable);
        return drafts.map(TransactionDraftDto::fromEntity);
    }

    /**
     * Get draft by ID
     */
    @Transactional(readOnly = true)
    public Optional<TransactionDraftDto> getDraft(UUID draftId, UUID userId) {
        log.debug("Getting draft: {} for user: {}", draftId, userId);
        
        Optional<TransactionDraft> draftOpt = draftRepository.findById(draftId);
        
        if (draftOpt.isPresent() && draftOpt.get().getUserId().equals(userId)) {
            return Optional.of(TransactionDraftDto.fromEntity(draftOpt.get()));
        }
        
        return Optional.empty();
    }

    /**
     * Delete draft
     */
    @Transactional
    public void deleteDraft(UUID draftId, UUID userId) {
        log.info("Deleting draft: {} for user: {}", draftId, userId);
        
        Optional<TransactionDraft> draftOpt = draftRepository.findById(draftId);
        
        if (draftOpt.isPresent() && draftOpt.get().getUserId().equals(userId)) {
            draftRepository.deleteById(draftId);
            log.info("Deleted draft: {}", draftId);
        } else {
            throw new IllegalArgumentException("Draft not found or access denied");
        }
    }

    /**
     * Convert draft to transaction
     */
    @Transactional
    public ManualTransactionDto convertDraftToTransaction(UUID draftId, UUID userId) {
        log.info("Converting draft to transaction: {} for user: {}", draftId, userId);
        
        Optional<TransactionDraft> draftOpt = draftRepository.findById(draftId);
        
        if (draftOpt.isEmpty() || !draftOpt.get().getUserId().equals(userId)) {
            throw new IllegalArgumentException("Draft not found or access denied");
        }
        
        TransactionDraft draft = draftOpt.get();
        
        if (!draft.isComplete()) {
            throw new IllegalArgumentException("Draft is not complete enough to create transaction");
        }
        
        // Convert draft to manual transaction DTO
        ManualTransactionDto dto = TransactionDraftDto.fromEntity(draft).toManualTransactionDto();
        
        // Create the transaction
        return createManualTransaction(dto);
    }

    /**
     * Clean up expired drafts
     */
    @Transactional
    public int cleanupExpiredDrafts() {
        log.info("Cleaning up expired drafts");
        
        int deletedCount = draftRepository.deleteExpiredDrafts(OffsetDateTime.now());
        
        log.info("Deleted {} expired drafts", deletedCount);
        return deletedCount;
    }

    /**
     * Clean up old auto-saved drafts for a user (keep only recent ones)
     */
    @Transactional
    public int cleanupOldAutoSavedDrafts(UUID userId, int keepRecentDays) {
        log.info("Cleaning up old auto-saved drafts for user: {}", userId);
        
        OffsetDateTime cutoffDate = OffsetDateTime.now().minusDays(keepRecentDays);
        int deletedCount = draftRepository.deleteOldAutoSavedDrafts(userId, cutoffDate);
        
        log.info("Deleted {} old auto-saved drafts for user: {}", deletedCount, userId);
        return deletedCount;
    }

    /**
     * Get validation result for transaction data
     */
    public TransactionValidator.ValidationResult validateTransaction(ManualTransactionDto dto) {
        log.debug("Validating transaction data for user: {}", dto.getUserId());
        
        return validator.validateForCreation(dto);
    }

    /**
     * Update draft from DTO
     */
    private void updateDraftFromDto(TransactionDraft draft, TransactionDraftDto dto) {
        if (dto.getDraftName() != null) draft.setDraftName(dto.getDraftName());
        if (dto.getDescription() != null) draft.setDescription(dto.getDescription());
        if (dto.getAmount() != null) draft.setAmount(dto.getAmount());
        if (dto.getTransactionType() != null) draft.setTransactionType(dto.getTransactionType());
        if (dto.getTransactionDate() != null) draft.setTransactionDate(dto.getTransactionDate());
        if (dto.getCategoryId() != null) draft.setCategoryId(dto.getCategoryId());
        if (dto.getNotes() != null) draft.setNotes(dto.getNotes());
        if (dto.getSuggestedCategoryId() != null) draft.setSuggestedCategoryId(dto.getSuggestedCategoryId());
        if (dto.getAiConfidence() != null) draft.setAiConfidence(dto.getAiConfidence());
        if (dto.getAiExplanation() != null) draft.setAiExplanation(dto.getAiExplanation());
        if (dto.getFormData() != null) draft.setFormData(dto.getFormData().toString());
        if (dto.getIsAutoSaved() != null) draft.setIsAutoSaved(dto.getIsAutoSaved());
        if (dto.getExpiresAt() != null) draft.setExpiresAt(dto.getExpiresAt());
    }
}
