"use client"

import React from 'react';
import { motion } from 'framer-motion';
import {
  Wallet,
  TrendingUp,
  TrendingDown,
  AlertCircle,
  Receipt,
  Clock,
  DollarSign,
  BookOpen,
  FileEdit
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useFinancialStore } from '@/stores';
import { useJournalStore } from '@/stores/journalStore';
import { formatCurrency } from '@/utils';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/utils/constants';
import type { DashboardData } from '@/types/financial';

interface BusinessVitalsBarProps {
  data?: DashboardData | null;
  isLoading?: boolean;
}

export function BusinessVitalsBar({ data, isLoading }: BusinessVitalsBarProps) {
  const { accounts } = useFinancialStore();
  const { getDraftEntries, getUnbalancedEntries } = useJournalStore();
  const router = useRouter();

  // Calculate key metrics
  const totalBalance = accounts.reduce((sum, account) => sum + account.balance, 0);
  const totalIncome = data?.summary?.totalIncome || 0;
  const totalExpenses = data?.summary?.totalExpenses || 0;
  const netProfit = totalIncome - totalExpenses;
  const pendingInvoicesCount = data?.pendingInvoices?.length || 0;
  const overdueInvoicesCount = data?.alerts?.filter(
    alert => alert.type === 'OVERDUE_INVOICE'
  ).length || 0;
  const criticalAlertsCount = data?.alerts?.filter(
    alert => alert.severity === 'HIGH' || alert.severity === 'CRITICAL'
  ).length || 0;

  // Journal vitals - Critical actionable metrics following Progressive Disclosure pattern
  const draftEntriesCount = getDraftEntries().length;
  const unbalancedEntriesCount = getUnbalancedEntries().length;

  // Navigation handlers for Progressive Disclosure pattern
  const handleVitalClick = (vitalId: string) => {
    switch (vitalId) {
      case 'draft-entries':
        router.push(`${ROUTES.JOURNAL_ENTRIES}?status=draft`);
        break;
      case 'unbalanced-entries':
        router.push(`${ROUTES.JOURNAL_ENTRIES}?status=unbalanced`);
        break;
      // Add more navigation cases as needed
      default:
        break;
    }
  };

  const vitals = [
    {
      id: 'balance',
      label: 'Total Balance',
      value: formatCurrency(totalBalance),
      icon: Wallet,
      color: 'blue',
      trend: totalBalance > 0 ? 'positive' : 'neutral',
      clickable: false
    },
    {
      id: 'income',
      label: 'This Month Income',
      value: formatCurrency(totalIncome),
      icon: TrendingUp,
      color: 'green',
      trend: 'positive',
      clickable: false
    },
    {
      id: 'expenses',
      label: 'This Month Expenses',
      value: formatCurrency(totalExpenses),
      icon: TrendingDown,
      color: 'red',
      trend: 'negative',
      clickable: false
    },
    {
      id: 'profit',
      label: 'Net Profit',
      value: formatCurrency(netProfit),
      icon: DollarSign,
      color: netProfit >= 0 ? 'green' : 'red',
      trend: netProfit >= 0 ? 'positive' : 'negative',
      clickable: false
    },
    {
      id: 'invoices',
      label: 'Pending Invoices',
      value: pendingInvoicesCount.toString(),
      icon: Receipt,
      color: pendingInvoicesCount > 0 ? 'yellow' : 'gray',
      trend: 'neutral',
      badge: overdueInvoicesCount > 0 ? { count: overdueInvoicesCount, color: 'red' } : undefined,
      clickable: false
    },
    // Journal Vitals - Critical actionable metrics (Progressive Disclosure pattern)
    {
      id: 'draft-entries',
      label: 'Draft Entries',
      value: draftEntriesCount.toString(),
      icon: FileEdit,
      color: draftEntriesCount > 0 ? 'yellow' : 'gray',
      trend: 'neutral',
      clickable: true,
      tooltip: draftEntriesCount > 0 ? 'Click to review and post draft entries' : 'No draft entries'
    },
    {
      id: 'unbalanced-entries',
      label: 'Unbalanced Entries',
      value: unbalancedEntriesCount.toString(),
      icon: AlertCircle,
      color: unbalancedEntriesCount > 0 ? 'red' : 'gray',
      trend: 'neutral',
      clickable: true,
      tooltip: unbalancedEntriesCount > 0 ? 'Click to fix unbalanced journal entries' : 'All entries balanced'
    }
  ];

  const getColorClasses = (color: string, trend: string) => {
    // Use monochromatic icons with colored values only
    const valueColors = {
      blue: 'text-blue-600',
      green: 'text-green-600',
      red: 'text-red-600',
      yellow: 'text-yellow-600',
      gray: 'text-gray-600'
    };
    
    return {
      bg: 'bg-gray-100',
      text: valueColors[color as keyof typeof valueColors] || 'text-gray-600',
      icon: 'text-gray-700' // All icons use the same dark color
    };
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gray-200 rounded-full" />
                  <div className="flex-1">
                    <div className="h-3 bg-gray-200 rounded mb-2" />
                    <div className="h-4 bg-gray-200 rounded w-16" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent className="p-4">
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
          {vitals.map((vital, index) => {
            const colorClasses = getColorClasses(vital.color, vital.trend);
            const Icon = vital.icon;

            return (
              <motion.div
                key={vital.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                className={`flex items-center space-x-3 p-2 rounded-lg transition-colors ${
                  vital.clickable
                    ? 'hover:bg-gray-50 cursor-pointer hover:shadow-sm'
                    : 'cursor-default'
                }`}
                onClick={() => vital.clickable && handleVitalClick(vital.id)}
                title={vital.tooltip}
              >
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${colorClasses.bg} relative`}>
                  <Icon className={`w-5 h-5 ${colorClasses.icon}`} />
                  {vital.badge && (
                    <div className="absolute -top-1 -right-1">
                      <Badge
                        variant="destructive"
                        className="w-5 h-5 p-0 flex items-center justify-center text-xs"
                      >
                        {vital.badge.count}
                      </Badge>
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-xs text-gray-500 truncate">
                    {vital.label}
                  </p>
                  <p className={`text-sm font-semibold truncate ${colorClasses.text}`}>
                    {vital.value}
                  </p>
                  {vital.clickable && (
                    <p className="text-xs text-gray-400 truncate">
                      Click to view
                    </p>
                  )}
                </div>
              </motion.div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
