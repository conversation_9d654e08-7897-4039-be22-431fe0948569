"use client"

import React, { useEffect } from 'react';
import { useConversationStore } from '@/stores';
import { useAuthStore } from '@/stores';

interface WebSocketProviderProps {
  children: React.ReactNode;
}

export function WebSocketProvider({ children }: WebSocketProviderProps) {
  console.log('🔄 WebSocketProvider - Managing connection lifecycle...');

  const { connect, disconnect, isConnected, connectionState } = useConversationStore();
  const { isAuthenticated } = useAuthStore();

  console.log('📊 WebSocketProvider state:', {
    isAuthenticated,
    isConnected,
    connectionState
  });

  // Manage WebSocket connection lifecycle based on authentication
  useEffect(() => {
    console.log('🔄 WebSocketProvider - Auth state changed:', {
      isAuthenticated,
      isConnected,
      connectionState
    });

    if (isAuthenticated && !isConnected && connectionState !== 'CONNECTING') {
      console.log('✅ User authenticated, connecting WebSocket...');
      connect();
    } else if (!isAuthenticated && isConnected) {
      console.log('❌ User not authenticated, disconnecting WebSocket...');
      disconnect();
    }

    // Cleanup on unmount
    return () => {
      if (isConnected) {
        console.log('🧹 WebSocketProvider cleanup - disconnecting...');
        disconnect();
      }
    };
  }, [isAuthenticated, isConnected, connectionState, connect, disconnect]);

  return <>{children}</>;
}
