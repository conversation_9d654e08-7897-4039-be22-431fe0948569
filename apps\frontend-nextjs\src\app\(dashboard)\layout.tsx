"use client"

import { AuthenticatedRoute } from '@/components/auth/ProtectedRoute';
import { DashboardSidebar } from '@/components/dashboard/DashboardSidebar';
import { DashboardHeader } from '@/components/dashboard/DashboardHeader';
import { TransactionWebSocketProvider } from '@/components/providers/TransactionWebSocketProvider';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <AuthenticatedRoute>
      <TransactionWebSocketProvider>
        <div className="h-screen bg-gray-50 flex flex-col overflow-hidden">
          {/* Dashboard Header */}
          <DashboardHeader />

          <div className="flex-1 flex overflow-hidden">
            {/* Sidebar */}
            <DashboardSidebar />

            {/* Main Content */}
            <main className="flex-1 p-6 overflow-hidden">
              {children}
            </main>
          </div>
        </div>
      </TransactionWebSocketProvider>
    </AuthenticatedRoute>
  );
}
