package com.intellifin.controller;

import com.intellifin.dto.financial.*;
import com.intellifin.exception.ValidationException;
import com.intellifin.model.DocumentUpload;
import com.intellifin.repository.DocumentUploadRepository;
import com.intellifin.security.UserPrincipal;
import com.intellifin.service.FileStorageService;
import com.intellifin.service.FinancialAccountService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Controller for financial account management and integration
 */
@RestController
@RequestMapping("/api/v1/financial-accounts")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = {"http://localhost:3000", "https://intellifin.com"})
public class FinancialAccountController {

    private final FinancialAccountService financialAccountService;
    private final DocumentUploadRepository documentUploadRepository;
    private final FileStorageService fileStorageService;

    /**
     * Get all financial accounts for the authenticated user
     */
    @GetMapping
    public ResponseEntity<List<MTNConnectionStatusDto>> getUserAccounts(
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        log.debug("Getting financial accounts for user: {}", userPrincipal.getId());
        
        List<MTNConnectionStatusDto> accounts = financialAccountService.getUserFinancialAccounts(userPrincipal.getId());
        return ResponseEntity.ok(accounts);
    }

    /**
     * Get MTN Mobile Money accounts for the authenticated user
     */
    @GetMapping("/mtn")
    public ResponseEntity<List<MTNConnectionStatusDto>> getUserMTNAccounts(
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        log.debug("Getting MTN accounts for user: {}", userPrincipal.getId());
        
        List<MTNConnectionStatusDto> mtnAccounts = financialAccountService.getUserMTNAccounts(userPrincipal.getId());
        return ResponseEntity.ok(mtnAccounts);
    }

    /**
     * Get financial account status
     */
    @GetMapping("/{accountId}/status")
    public ResponseEntity<MTNConnectionStatusDto> getAccountStatus(@PathVariable UUID accountId) {
        log.debug("Getting status for account: {}", accountId);
        
        MTNConnectionStatusDto status = financialAccountService.getAccountStatus(accountId);
        return ResponseEntity.ok(status);
    }

    /**
     * Initiate MTN Mobile Money connection
     */
    @PostMapping("/connect/mtn")
    public ResponseEntity<MTNOAuthResponseDto> connectMTN(
            @Valid @RequestBody MTNConnectionRequestDto request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        log.info("Initiating MTN connection for user: {}", userPrincipal.getId());
        
        // Set user ID from authenticated principal
        request.setUserId(userPrincipal.getId().toString());
        
        try {
            MTNOAuthResponseDto response = financialAccountService.initiateMTNConnection(request);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Failed to initiate MTN connection for user: {}", userPrincipal.getId(), e);
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                    .body(MTNOAuthResponseDto.builder()
                            .authorizationUrl("")
                            .state("")
                            .build());
        }
    }

    /**
     * Handle MTN OAuth callback
     */
    @PostMapping("/mtn/callback")
    public ResponseEntity<Map<String, Object>> handleMTNCallback(
            @RequestParam String code,
            @RequestParam String state,
            @RequestParam(required = false) String error) {
        
        log.info("Handling MTN OAuth callback with state: {}", state);
        
        if (error != null) {
            log.error("OAuth error received: {}", error);
            return ResponseEntity.badRequest()
                    .body(Map.of("success", false, "error", "OAuth authorization failed: " + error));
        }
        
        try {
            financialAccountService.handleMTNCallback(code, state);
            return ResponseEntity.ok(Map.of("success", true, "message", "Connection successful"));
        } catch (Exception e) {
            log.error("Failed to handle MTN callback", e);
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(Map.of("success", false, "error", "Failed to complete connection"));
        }
    }

    /**
     * Disconnect financial account
     */
    @DeleteMapping("/{accountId}/disconnect")
    public ResponseEntity<Map<String, Object>> disconnectAccount(@PathVariable UUID accountId) {
        log.info("Disconnecting account: {}", accountId);
        
        try {
            financialAccountService.disconnectAccount(accountId);
            return ResponseEntity.ok(Map.of("success", true, "message", "Account disconnected successfully"));
        } catch (Exception e) {
            log.error("Failed to disconnect account: {}", accountId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("success", false, "error", "Failed to disconnect account"));
        }
    }

    /**
     * Initiate document upload
     */
    @PostMapping("/upload/initiate")
    public ResponseEntity<DocumentUploadResponseDto> initiateDocumentUpload(
            @Valid @RequestBody DocumentUploadRequestDto request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        log.info("Initiating document upload for user: {}", userPrincipal.getId());
        
        // Set user ID from authenticated principal
        request.setUserId(userPrincipal.getId().toString());
        
        try {
            DocumentUploadResponseDto response = financialAccountService.initiateDocumentUpload(request);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Failed to initiate document upload for user: {}", userPrincipal.getId(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(DocumentUploadResponseDto.builder()
                            .uploadId("")
                            .uploadUrl("")
                            .build());
        }
    }

    /**
     * Get document upload status
     */
    @GetMapping("/upload/{uploadId}/status")
    public ResponseEntity<DocumentProcessingStatusDto> getDocumentUploadStatus(@PathVariable String uploadId) {
        log.debug("Getting document upload status: {}", uploadId);
        
        try {
            DocumentProcessingStatusDto status = financialAccountService.getDocumentUploadStatus(uploadId);
            return ResponseEntity.ok(status);
        } catch (Exception e) {
            log.error("Failed to get document upload status: {}", uploadId, e);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Confirm document upload completion
     */
    @PostMapping("/upload/{uploadId}/confirm")
    public ResponseEntity<Map<String, Object>> confirmDocumentUpload(@PathVariable String uploadId) {
        log.info("Confirming document upload: {}", uploadId);
        
        try {
            financialAccountService.confirmDocumentUpload(uploadId);
            return ResponseEntity.ok(Map.of("success", true, "message", "Upload confirmed and processing started"));
        } catch (Exception e) {
            log.error("Failed to confirm document upload: {}", uploadId, e);
            return ResponseEntity.badRequest()
                    .body(Map.of("success", false, "error", "Failed to confirm upload"));
        }
    }

    /**
     * Check if user has connected accounts
     */
    @GetMapping("/connected/status")
    public ResponseEntity<Map<String, Object>> getConnectionStatus(
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        boolean hasConnectedAccounts = financialAccountService.hasConnectedAccounts(userPrincipal.getId());
        
        return ResponseEntity.ok(Map.of(
                "hasConnectedAccounts", hasConnectedAccounts,
                "userId", userPrincipal.getId().toString()
        ));
    }

    /**
     * Handles the actual file upload to a pre-signed URL (or local storage for dev)
     */
    @PutMapping("/upload/{uploadId}/file")
    public ResponseEntity<Void> uploadDocumentFile(
            @PathVariable String uploadId,
            @RequestBody byte[] content) {

        log.info("Receiving file for uploadId: {}", uploadId);

        DocumentUpload documentUpload = documentUploadRepository.findByUploadId(uploadId)
                .orElseThrow(() -> new ValidationException("Upload ID not found: " + uploadId));

        try {
            fileStorageService.storeFile(uploadId, documentUpload.getFileName(), content);
            log.info("Successfully stored file for uploadId: {}", uploadId);
            return ResponseEntity.ok().build();
        } catch (IOException e) {
            log.error("Failed to store file for uploadId: {}", uploadId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
