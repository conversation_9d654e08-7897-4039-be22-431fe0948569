package com.intellifin.dto;

import com.intellifin.model.Transaction;
import com.intellifin.model.TransactionDraft;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.Map;
import java.util.UUID;

/**
 * DTO for transaction draft operations
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionDraftDto {

    private UUID id;

    @NotNull(message = "User ID is required")
    private UUID userId;

    @Size(max = 255, message = "Draft name must not exceed 255 characters")
    private String draftName;

    @NotBlank(message = "Description is required")
    @Size(min = 1, max = 255, message = "Description must be between 1 and 255 characters")
    private String description;

    @NotNull(message = "Amount is required")
    @DecimalMin(value = "0.01", message = "Amount must be greater than 0")
    @DecimalMax(value = "999999999.99", message = "Amount exceeds maximum limit")
    private BigDecimal amount;

    @NotNull(message = "Transaction type is required")
    private Transaction.TransactionType transactionType;

    @NotNull(message = "Transaction date is required")
    private LocalDate transactionDate;

    private UUID categoryId;

    @Size(max = 1000, message = "Notes must not exceed 1000 characters")
    private String notes;

    // AI suggestion fields
    private UUID suggestedCategoryId;
    private BigDecimal aiConfidence;
    private String aiExplanation;

    // Form state
    private Map<String, Object> formData;

    private Boolean isAutoSaved = true;

    private OffsetDateTime expiresAt;

    private OffsetDateTime createdAt;

    private OffsetDateTime updatedAt;

    // Related entity information
    private CategoryDto category;
    private CategoryDto suggestedCategory;

    /**
     * Convert to TransactionDraft entity
     */
    public TransactionDraft toEntity() {
        return TransactionDraft.builder()
                .id(this.id)
                .userId(this.userId)
                .draftName(this.draftName)
                .description(this.description)
                .amount(this.amount)
                .transactionType(this.transactionType)
                .transactionDate(this.transactionDate)
                .categoryId(this.categoryId)
                .notes(this.notes)
                .suggestedCategoryId(this.suggestedCategoryId)
                .aiConfidence(this.aiConfidence)
                .aiExplanation(this.aiExplanation)
                .formData(this.formData != null ? this.formData.toString() : null)
                .isAutoSaved(this.isAutoSaved)
                .expiresAt(this.expiresAt)
                .build();
    }

    /**
     * Create from TransactionDraft entity
     */
    public static TransactionDraftDto fromEntity(TransactionDraft draft) {
        if (draft == null) return null;

        return TransactionDraftDto.builder()
                .id(draft.getId())
                .userId(draft.getUserId())
                .draftName(draft.getDraftName())
                .description(draft.getDescription())
                .amount(draft.getAmount())
                .transactionType(draft.getTransactionType())
                .transactionDate(draft.getTransactionDate())
                .categoryId(draft.getCategoryId())
                .notes(draft.getNotes())
                .suggestedCategoryId(draft.getSuggestedCategoryId())
                .aiConfidence(draft.getAiConfidence())
                .aiExplanation(draft.getAiExplanation())
                .isAutoSaved(draft.getIsAutoSaved())
                .expiresAt(draft.getExpiresAt())
                .createdAt(draft.getCreatedAt())
                .updatedAt(draft.getUpdatedAt())
                .category(CategoryDto.fromEntity(draft.getCategory()))
                .suggestedCategory(CategoryDto.fromEntity(draft.getSuggestedCategory()))
                .build();
    }

    /**
     * Convert to ManualTransactionDto for transaction creation
     */
    public ManualTransactionDto toManualTransactionDto() {
        return ManualTransactionDto.builder()
                .userId(this.userId)
                .description(this.description)
                .amount(this.amount)
                .type(this.transactionType)
                .date(this.transactionDate)
                .categoryId(this.categoryId)
                .notes(this.notes)
                .suggestedCategoryId(this.suggestedCategoryId)
                .aiConfidence(this.aiConfidence)
                .aiExplanation(this.aiExplanation)
                .draftId(this.id)
                .isDraft(false)
                .build();
    }

    /**
     * Check if draft is complete enough to create transaction
     */
    public boolean isCompleteForTransaction() {
        return description != null && !description.trim().isEmpty() &&
               amount != null && amount.compareTo(BigDecimal.ZERO) > 0 &&
               transactionType != null &&
               transactionDate != null;
    }

    /**
     * Check if draft has expired
     */
    public boolean isExpired() {
        return expiresAt != null && expiresAt.isBefore(OffsetDateTime.now());
    }

    /**
     * Check if draft has AI suggestion
     */
    public boolean hasAISuggestion() {
        return suggestedCategoryId != null && aiConfidence != null;
    }

    /**
     * Get completion percentage for UI progress indicators
     */
    public int getCompletionPercentage() {
        int totalFields = 5; // description, amount, type, date, category
        int completedFields = 0;

        if (description != null && !description.trim().isEmpty()) completedFields++;
        if (amount != null && amount.compareTo(BigDecimal.ZERO) > 0) completedFields++;
        if (transactionType != null) completedFields++;
        if (transactionDate != null) completedFields++;
        if (categoryId != null || suggestedCategoryId != null) completedFields++;

        return (completedFields * 100) / totalFields;
    }
}
