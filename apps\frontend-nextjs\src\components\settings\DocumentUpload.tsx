'use client';

import React, { useState, useCallback } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Alert } from '@/components/ui/alert';
import { useDocumentUpload } from '@/hooks/useDocumentUpload';
import { 
  Upload, 
  File, 
  AlertCircle, 
  CheckCircle, 
  Clock, 
  X,
  FileText,
  FileSpreadsheet
} from 'lucide-react';

interface DocumentUploadProps {
  userId: string;
  onUploadComplete: () => void;
}

export const DocumentUpload: React.FC<DocumentUploadProps> = ({
  userId,
  onUploadComplete
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [accountName, setAccountName] = useState('');
  const [dragActive, setDragActive] = useState(false);

  const {
    uploadFile,
    uploadStatus,
    loading,
    error,
    progress
  } = useDocumentUpload();

  const acceptedTypes = {
    'application/pdf': ['.pdf'],
    'text/csv': ['.csv'],
    'application/vnd.ms-excel': ['.xls'],
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx']
  };

  const maxFileSize = 10 * 1024 * 1024; // 10MB

  const validateFile = (file: File): string | null => {
    if (file.size > maxFileSize) {
      return 'File size must be less than 10MB';
    }

    const fileType = file.type;
    const fileName = file.name.toLowerCase();
    
    const isValidType = Object.keys(acceptedTypes).includes(fileType) ||
      fileName.endsWith('.pdf') ||
      fileName.endsWith('.csv') ||
      fileName.endsWith('.xls') ||
      fileName.endsWith('.xlsx');

    if (!isValidType) {
      return 'Please select a PDF, CSV, or Excel file';
    }

    return null;
  };

  const getFileType = (file: File): 'PDF' | 'CSV' | 'EXCEL' => {
    const fileName = file.name.toLowerCase();
    if (fileName.endsWith('.pdf')) return 'PDF';
    if (fileName.endsWith('.csv')) return 'CSV';
    return 'EXCEL';
  };

  const getFileIcon = (file: File) => {
    const fileType = getFileType(file);
    switch (fileType) {
      case 'PDF':
        return <FileText className="h-8 w-8 text-red-500" />;
      case 'CSV':
      case 'EXCEL':
        return <FileSpreadsheet className="h-8 w-8 text-green-500" />;
      default:
        return <File className="h-8 w-8 text-gray-500" />;
    }
  };

  const handleFileSelect = (file: File) => {
    const error = validateFile(file);
    if (error) {
      alert(error);
      return;
    }
    setSelectedFile(file);
  };

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  }, []);

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelect(e.target.files[0]);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) return;

    const success = await uploadFile({
      userId,
      file: selectedFile,
      accountType: 'MTN_MOBILE_MONEY',
      accountName: accountName.trim() || undefined
    });

    if (success) {
      setSelectedFile(null);
      setAccountName('');
      onUploadComplete();
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  };

  const getStatusIcon = () => {
    switch (uploadStatus?.status) {
      case 'UPLOADING':
        return <Clock className="h-4 w-4 text-blue-500" />;
      case 'PROCESSING':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'FAILED':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  if (uploadStatus && uploadStatus.status !== 'FAILED') {
    return (
      <Card className="p-6">
        <div className="text-center">
          <div className="flex items-center justify-center mb-4">
            {getStatusIcon()}
            <span className="ml-2 font-medium">
              {uploadStatus.status === 'UPLOADING' && 'Uploading...'}
              {uploadStatus.status === 'PROCESSING' && 'Processing...'}
              {uploadStatus.status === 'COMPLETED' && 'Upload Complete!'}
            </span>
          </div>

          {uploadStatus.progress !== undefined && uploadStatus.progress < 100 && (
            <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${uploadStatus.progress}%` }}
              ></div>
            </div>
          )}

          {uploadStatus.status === 'COMPLETED' && (
            <div className="space-y-2">
              <p className="text-green-600">
                Successfully extracted {uploadStatus.extractedTransactions} transactions
              </p>
              {uploadStatus.confidenceScore && (
                <p className="text-sm text-gray-600">
                  Confidence: {(uploadStatus.confidenceScore * 100).toFixed(1)}%
                </p>
              )}
            </div>
          )}

          {uploadStatus.status === 'PROCESSING' && (
            <p className="text-gray-600">
              Our AI is analyzing your document and extracting transaction data...
            </p>
          )}
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <div className="text-red-800">{error}</div>
        </Alert>
      )}

      {!selectedFile ? (
        <Card 
          className={`p-8 border-2 border-dashed transition-colors ${
            dragActive 
              ? 'border-blue-400 bg-blue-50' 
              : 'border-gray-300 hover:border-gray-400'
          }`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <div className="text-center">
            <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-medium mb-2">Upload Your Statement</h4>
            <p className="text-gray-600 mb-4">
              Drag and drop your file here, or click to browse
            </p>
            
            <input
              type="file"
              id="file-upload"
              className="hidden"
              accept=".pdf,.csv,.xls,.xlsx"
              onChange={handleFileInputChange}
            />
            
            <Button 
              variant="outline" 
              onClick={() => document.getElementById('file-upload')?.click()}
            >
              Choose File
            </Button>
            
            <div className="mt-4 text-xs text-gray-500">
              Supported formats: PDF, CSV, Excel (max 10MB)
            </div>
          </div>
        </Card>
      ) : (
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {getFileIcon(selectedFile)}
              <div>
                <div className="font-medium">{selectedFile.name}</div>
                <div className="text-sm text-gray-500">
                  {formatFileSize(selectedFile.size)}
                </div>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRemoveFile}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </Card>
      )}

      {selectedFile && (
        <div className="space-y-4">
          <div>
            <label htmlFor="accountName" className="block text-sm font-medium text-gray-700 mb-1">
              Account Name (Optional)
            </label>
            <Input
              id="accountName"
              type="text"
              placeholder="e.g., My MTN Account"
              value={accountName}
              onChange={(e) => setAccountName(e.target.value)}
            />
            <p className="text-xs text-gray-500 mt-1">
              Help us organize your accounts by giving this one a name
            </p>
          </div>

          <Button
            onClick={handleUpload}
            disabled={loading}
            className="w-full"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <Upload className="h-4 w-4 mr-2" />
            )}
            Upload and Process
          </Button>
        </div>
      )}
    </div>
  );
};
