/**
 * Application Constants
 * Central location for all application constants
 */

/**
 * API Configuration
 */
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080',
  WEBSOCKET_URL: process.env.NEXT_PUBLIC_WEBSOCKET_URL || 'ws://localhost:8080/ws',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
} as const;

/**
 * Application Routes
 */
export const ROUTES = {
  // Public routes
  HOME: '/',
  LOGIN: '/login',
  REGISTER: '/register',
  FORGOT_PASSWORD: '/forgot-password',
  RESET_PASSWORD: '/reset-password',
  VERIFY_EMAIL: '/verify-email',
  
  // Protected routes
  DASHBOARD: '/dashboard',
  TRANSACTIONS: '/transactions',
  INVOICES: '/invoices',
  CLIENTS: '/clients',
  REPORTS: '/reports',
  SETTINGS: '/settings',
  PROFILE: '/profile',
  
  // Accounting
  ACCOUNTING_CHART_OF_ACCOUNTS: '/accounting/chart-of-accounts',
  ACCOUNTING_CATEGORIES: '/accounting/categories',
  JOURNAL_ENTRIES: '/journal',
  JOURNAL_ENTRY_DETAILS: '/journal/[id]',

  // Onboarding
  ONBOARDING: '/onboarding',
  ONBOARDING_PROFILE: '/onboarding/profile',
  ONBOARDING_ACCOUNTS: '/onboarding/accounts',
  ONBOARDING_COMPLETE: '/onboarding/complete',
} as const;

/**
 * Local Storage Keys
 */
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'intellifin_token',
  USER_DATA: 'intellifin_user',
  THEME: 'intellifin_theme',
  LANGUAGE: 'intellifin_language',
  ONBOARDING_STEP: 'intellifin_onboarding_step',
  CONVERSATION_HISTORY: 'intellifin_conversation_history',
} as const;

/**
 * Theme Configuration
 */
export const THEME = {
  COLORS: {
    PRIMARY: '#007BFF',
    PRIMARY_DARK: '#2A4E8D',
    SECONDARY: '#28A745',
    ACCENT: '#17A2B8',
    HIGHLIGHT: '#FFC107',
    ERROR: '#DC3545',
    WARNING: '#FD7E14',
    SUCCESS: '#28A745',
    INFO: '#17A2B8',
    
    // Neutral colors
    WHITE: '#FFFFFF',
    BLACK: '#000000',
    GRAY_50: '#F9FAFB',
    GRAY_100: '#F3F4F6',
    GRAY_200: '#E5E7EB',
    GRAY_300: '#D1D5DB',
    GRAY_400: '#9CA3AF',
    GRAY_500: '#6B7280',
    GRAY_600: '#4B5563',
    GRAY_700: '#374151',
    GRAY_800: '#1F2937',
    GRAY_900: '#111827',
  },
  
  FONTS: {
    PRIMARY: 'Inter, system-ui, sans-serif',
    MONO: 'Monaco, Consolas, monospace',
  },
  
  BREAKPOINTS: {
    SM: '640px',
    MD: '768px',
    LG: '1024px',
    XL: '1280px',
    '2XL': '1536px',
  },
} as const;

/**
 * Business Rules and Limits
 */
export const BUSINESS_RULES = {
  // Transaction limits
  MAX_TRANSACTION_AMOUNT: 1000000, // K1,000,000
  MIN_TRANSACTION_AMOUNT: 0.01,
  
  // Invoice limits
  MAX_INVOICE_AMOUNT: 10000000, // K10,000,000
  MAX_INVOICE_LINE_ITEMS: 50,
  INVOICE_DUE_DAYS_DEFAULT: 30,
  
  // File upload limits
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_FILE_TYPES: [
    'application/pdf',
    'image/jpeg',
    'image/png',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/csv',
  ],
  
  // Pagination
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  
  // Search
  MIN_SEARCH_LENGTH: 2,
  SEARCH_DEBOUNCE_MS: 300,
  
  // Session
  SESSION_TIMEOUT_MS: 30 * 60 * 1000, // 30 minutes
  TOKEN_REFRESH_THRESHOLD_MS: 5 * 60 * 1000, // 5 minutes before expiry
} as const;

/**
 * Supported Currencies
 */
export const CURRENCIES = [
  { code: 'ZMW', name: 'Zambian Kwacha', symbol: 'K' },
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'GBP', name: 'British Pound', symbol: '£' },
  { code: 'ZAR', name: 'South African Rand', symbol: 'R' },
] as const;

/**
 * Supported Languages
 */
export const LANGUAGES = [
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'ny', name: 'Chichewa', nativeName: 'Chichewa' },
  { code: 'bem', name: 'Bemba', nativeName: 'Bemba' },
] as const;

/**
 * Mobile Money Providers
 */
export const MOBILE_MONEY_PROVIDERS = [
  {
    id: 'mtn',
    name: 'MTN Mobile Money',
    code: 'MTN',
    logo: '/images/providers/mtn.png',
    color: '#FFCC00',
    supported: true,
  },
  {
    id: 'airtel',
    name: 'Airtel Money',
    code: 'AIRTEL',
    logo: '/images/providers/airtel.png',
    color: '#FF0000',
    supported: true,
  },
  {
    id: 'zamtel',
    name: 'Zamtel Kwacha',
    code: 'ZAMTEL',
    logo: '/images/providers/zamtel.png',
    color: '#00A651',
    supported: false,
    comingSoon: true,
  },
] as const;

/**
 * Bank Providers (via Stitch)
 */
export const BANK_PROVIDERS = [
  {
    id: 'zanaco',
    name: 'Zanaco Bank',
    code: 'ZANACO',
    logo: '/images/banks/zanaco.png',
    supported: true,
  },
  {
    id: 'fbn',
    name: 'First National Bank',
    code: 'FNB',
    logo: '/images/banks/fnb.png',
    supported: true,
  },
  {
    id: 'stanbic',
    name: 'Stanbic Bank',
    code: 'STANBIC',
    logo: '/images/banks/stanbic.png',
    supported: true,
  },
  {
    id: 'standard_chartered',
    name: 'Standard Chartered',
    code: 'SCB',
    logo: '/images/banks/scb.png',
    supported: false,
    comingSoon: true,
  },
] as const;

/**
 * Transaction Categories (Default)
 */
export const DEFAULT_CATEGORIES = {
  INCOME: [
    'Sales Revenue',
    'Service Income',
    'Interest Income',
    'Investment Income',
    'Other Income',
  ],
  EXPENSE: [
    'Office Supplies',
    'Marketing & Advertising',
    'Travel & Transport',
    'Utilities',
    'Rent',
    'Professional Services',
    'Insurance',
    'Telecommunications',
    'Fuel',
    'Maintenance & Repairs',
    'Bank Charges',
    'Other Expenses',
  ],
} as const;

/**
 * Invoice Statuses
 */
export const INVOICE_STATUSES = [
  { value: 'DRAFT', label: 'Draft', color: 'gray' },
  { value: 'PENDING_SUBMISSION', label: 'Pending Submission', color: 'yellow' },
  { value: 'SUBMITTED', label: 'Submitted', color: 'blue' },
  { value: 'PAID', label: 'Paid', color: 'green' },
  { value: 'OVERDUE', label: 'Overdue', color: 'red' },
  { value: 'CANCELLED', label: 'Cancelled', color: 'gray' },
] as const;

/**
 * Transaction Statuses
 */
export const TRANSACTION_STATUSES = [
  { value: 'PENDING_CLASSIFICATION', label: 'Pending Classification', color: 'yellow' },
  { value: 'CLASSIFIED', label: 'Classified', color: 'blue' },
  { value: 'RECONCILED', label: 'Reconciled', color: 'green' },
] as const;

/**
 * Account Connection Statuses
 */
export const CONNECTION_STATUSES = [
  { value: 'CONNECTED', label: 'Connected', color: 'green' },
  { value: 'DISCONNECTED', label: 'Disconnected', color: 'gray' },
  { value: 'ERROR', label: 'Error', color: 'red' },
] as const;

/**
 * ZRA Submission Statuses
 */
export const ZRA_STATUSES = [
  { value: 'PENDING', label: 'Pending', color: 'yellow' },
  { value: 'SUBMITTED', label: 'Submitted', color: 'green' },
  { value: 'FAILED', label: 'Failed', color: 'red' },
  { value: 'N/A', label: 'Not Applicable', color: 'gray' },
] as const;

/**
 * Onboarding Steps
 */
export const ONBOARDING_STEPS = [
  {
    id: 'STARTED',
    title: 'Welcome',
    description: 'Welcome to IntelliFin',
    completed: false,
  },
  {
    id: 'PROFILE_COMPLETE',
    title: 'Profile Setup',
    description: 'Complete your business profile',
    completed: false,
  },
  {
    id: 'ACCOUNTS_CONNECTED',
    title: 'Connect Accounts',
    description: 'Connect your financial accounts',
    completed: false,
  },
  {
    id: 'ALL_SET',
    title: 'All Set',
    description: 'You\'re ready to start using IntelliFin',
    completed: true,
  },
] as const;

/**
 * Error Messages
 */
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your internet connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  FORBIDDEN: 'Access denied.',
  NOT_FOUND: 'The requested resource was not found.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  SERVER_ERROR: 'Server error. Please try again later.',
  UNKNOWN_ERROR: 'An unexpected error occurred.',
  
  // Auth specific
  INVALID_CREDENTIALS: 'Invalid email or password.',
  EMAIL_ALREADY_EXISTS: 'An account with this email already exists.',
  WEAK_PASSWORD: 'Password is too weak. Please choose a stronger password.',
  
  // File upload
  FILE_TOO_LARGE: 'File is too large. Maximum size is 10MB.',
  INVALID_FILE_TYPE: 'Invalid file type. Please upload a supported file.',
  
  // Business rules
  AMOUNT_TOO_LARGE: 'Amount exceeds the maximum allowed limit.',
  AMOUNT_TOO_SMALL: 'Amount is below the minimum allowed limit.',
} as const;

/**
 * Success Messages
 */
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Successfully logged in.',
  LOGOUT_SUCCESS: 'Successfully logged out.',
  REGISTRATION_SUCCESS: 'Account created successfully.',
  PROFILE_UPDATED: 'Profile updated successfully.',
  TRANSACTION_CREATED: 'Transaction created successfully.',
  INVOICE_CREATED: 'Invoice created successfully.',
  ACCOUNT_CONNECTED: 'Account connected successfully.',
  FILE_UPLOADED: 'File uploaded successfully.',
  EMAIL_SENT: 'Email sent successfully.',
  PASSWORD_CHANGED: 'Password changed successfully.',
} as const;

/**
 * Feature Flags
 */
export const FEATURE_FLAGS = {
  ENABLE_DARK_MODE: process.env.NEXT_PUBLIC_ENABLE_DARK_MODE === 'true',
  ENABLE_ANALYTICS: process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true',
  ENABLE_CHAT_SUPPORT: process.env.NEXT_PUBLIC_ENABLE_CHAT_SUPPORT === 'true',
  ENABLE_NOTIFICATIONS: process.env.NEXT_PUBLIC_ENABLE_NOTIFICATIONS === 'true',
  ENABLE_BETA_FEATURES: process.env.NEXT_PUBLIC_ENABLE_BETA_FEATURES === 'true',
} as const;

/**
 * External Service URLs
 */
export const EXTERNAL_URLS = {
  SUPPORT_EMAIL: '<EMAIL>',
  SUPPORT_PHONE: '+260 XXX XXX XXX',
  PRIVACY_POLICY: 'https://intellifin.com/privacy',
  TERMS_OF_SERVICE: 'https://intellifin.com/terms',
  HELP_CENTER: 'https://help.intellifin.com',
  STATUS_PAGE: 'https://status.intellifin.com',
} as const;
