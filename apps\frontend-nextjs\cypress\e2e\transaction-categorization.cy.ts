describe('Transaction Categorization E2E', () => {
  beforeEach(() => {
    // Mock authentication
    cy.window().then((win) => {
      win.localStorage.setItem('auth-token', 'mock-jwt-token');
      win.localStorage.setItem('user', JSON.stringify({
        id: 'test-user-123',
        email: '<EMAIL>',
        name: 'Test User'
      }));
    });

    // Intercept API calls
    cy.intercept('GET', '/api/v1/transactions*', { fixture: 'transactions.json' }).as('getTransactions');
    cy.intercept('GET', '/api/v1/categories*', { fixture: 'categories.json' }).as('getCategories');
    cy.intercept('POST', '/api/v1/transactions', { fixture: 'new-transaction.json' }).as('createTransaction');
    cy.intercept('PUT', '/api/v1/transactions/*/categorize', { fixture: 'categorization-response.json' }).as('categorizeTransaction');
    cy.intercept('POST', '/api/v1/transactions/bulk-categorize', { fixture: 'bulk-categorization-response.json' }).as('bulkCategorize');

    // Visit the transactions page
    cy.visit('/dashboard/transactions');
  });

  it('should display transactions list with categorization status', () => {
    cy.wait('@getTransactions');
    cy.wait('@getCategories');

    // Check that transactions are displayed
    cy.get('[data-testid="transaction-list"]').should('be.visible');
    cy.get('[data-testid="transaction-row"]').should('have.length.at.least', 1);

    // Check for AI categorization badges
    cy.get('[data-testid="ai-categorization-badge"]').should('be.visible');
    cy.contains('AI: Transport').should('be.visible');
    cy.contains('85%').should('be.visible');
  });

  it('should accept AI categorization suggestion', () => {
    cy.wait('@getTransactions');
    cy.wait('@getCategories');

    // Find a transaction with AI suggestion
    cy.get('[data-testid="transaction-row"]').first().within(() => {
      // Click accept button
      cy.get('[title="Accept AI suggestion"]').click();
    });

    cy.wait('@categorizeTransaction');

    // Verify success message or updated status
    cy.contains('Transaction has been successfully categorized').should('be.visible');
  });

  it('should reject AI categorization and select manual category', () => {
    cy.wait('@getTransactions');
    cy.wait('@getCategories');

    // Find a transaction with AI suggestion
    cy.get('[data-testid="transaction-row"]').first().within(() => {
      // Click reject button
      cy.get('[title="Reject AI suggestion"]').click();

      // Category selector should appear
      cy.get('[data-testid="category-selector"]').should('be.visible');
      
      // Select a different category
      cy.get('[data-testid="category-selector"]').click();
    });

    // Select Food & Dining from dropdown
    cy.contains('Food & Dining').click();

    cy.wait('@categorizeTransaction');

    // Verify the transaction was updated
    cy.contains('Categorization Updated').should('be.visible');
  });

  it('should create new transaction and trigger AI categorization', () => {
    cy.wait('@getTransactions');

    // Click create transaction button
    cy.get('[data-testid="create-transaction-btn"]').click();

    // Fill out transaction form
    cy.get('[data-testid="transaction-form"]').within(() => {
      cy.get('input[name="description"]').type('Taxi ride to airport');
      cy.get('input[name="amount"]').type('75.00');
      cy.get('select[name="type"]').select('EXPENSE');
      cy.get('input[name="source"]').type('manual');
      
      // Submit form
      cy.get('button[type="submit"]').click();
    });

    cy.wait('@createTransaction');

    // Verify transaction was created and appears in list
    cy.contains('Taxi ride to airport').should('be.visible');
    cy.contains('Pending Classification').should('be.visible');
  });

  it('should perform bulk categorization', () => {
    cy.wait('@getTransactions');
    cy.wait('@getCategories');

    // Select multiple transactions
    cy.get('[data-testid="transaction-row"]').each(($row, index) => {
      if (index < 3) { // Select first 3 transactions
        cy.wrap($row).find('[data-testid="select-transaction"]').click();
      }
    });

    // Verify bulk actions are visible
    cy.get('[data-testid="bulk-actions"]').should('be.visible');
    cy.contains('3 selected').should('be.visible');

    // Select category for bulk categorization
    cy.get('[data-testid="bulk-category-selector"]').click();
    cy.contains('Transport').click();

    cy.wait('@bulkCategorize');

    // Verify bulk categorization completed
    cy.contains('Bulk Categorization Complete').should('be.visible');
    cy.contains('Processed 3 transactions').should('be.visible');
  });

  it('should filter transactions by categorization status', () => {
    cy.wait('@getTransactions');

    // Click pending only filter
    cy.get('[data-testid="pending-only-filter"]').click();

    // Verify only pending transactions are shown
    cy.get('[data-testid="transaction-row"]').each(($row) => {
      cy.wrap($row).should('contain', 'Pending Classification');
    });

    // Check statistics
    cy.get('[data-testid="categorization-stats"]').within(() => {
      cy.contains('pending').should('be.visible');
      cy.contains('AI suggestions').should('be.visible');
    });
  });

  it('should search transactions', () => {
    cy.wait('@getTransactions');

    // Type in search box
    cy.get('[data-testid="transaction-search"]').type('fuel');

    // Verify filtered results
    cy.get('[data-testid="transaction-row"]').should('have.length.at.least', 1);
    cy.get('[data-testid="transaction-row"]').each(($row) => {
      cy.wrap($row).should('contain.text', 'fuel');
    });
  });

  it('should display real-time WebSocket updates', () => {
    cy.wait('@getTransactions');

    // Mock WebSocket connection
    cy.window().then((win) => {
      // Simulate WebSocket message for transaction categorized
      const mockEvent = {
        eventType: 'TRANSACTION_CATEGORIZED',
        data: {
          transactionId: 'txn-123',
          suggestedCategory: {
            id: 'cat-transport',
            name: 'Transport'
          },
          confidence: 0.88,
          explanation: 'AI detected transport keywords'
        }
      };

      // Trigger the WebSocket event handler
      win.dispatchEvent(new CustomEvent('websocket-message', { detail: mockEvent }));
    });

    // Verify real-time update appeared
    cy.contains('AI Categorization Complete').should('be.visible');
    cy.contains('88% confidence').should('be.visible');
  });

  it('should handle AI categorization errors gracefully', () => {
    // Mock API error
    cy.intercept('PUT', '/api/v1/transactions/*/categorize', {
      statusCode: 500,
      body: { error: 'AI service unavailable' }
    }).as('categorizationError');

    cy.wait('@getTransactions');
    cy.wait('@getCategories');

    // Try to accept AI suggestion
    cy.get('[data-testid="transaction-row"]').first().within(() => {
      cy.get('[title="Accept AI suggestion"]').click();
    });

    cy.wait('@categorizationError');

    // Verify error message is displayed
    cy.contains('Failed to categorize transaction').should('be.visible');
    cy.contains('AI service unavailable').should('be.visible');
  });

  it('should show confidence levels with appropriate colors', () => {
    cy.wait('@getTransactions');

    // Check high confidence (green)
    cy.get('[data-testid="confidence-badge"]').contains('High').should('have.class', 'text-green-700');

    // Check medium confidence (yellow)
    cy.get('[data-testid="confidence-badge"]').contains('Medium').should('have.class', 'text-yellow-700');

    // Check low confidence (red)
    cy.get('[data-testid="confidence-badge"]').contains('Low').should('have.class', 'text-red-700');
  });

  it('should display categorization statistics correctly', () => {
    cy.wait('@getTransactions');

    cy.get('[data-testid="categorization-stats"]').within(() => {
      // Check total transactions
      cy.contains('total').should('be.visible');
      
      // Check pending count
      cy.contains('pending').should('be.visible');
      
      // Check AI suggestions count
      cy.contains('AI suggestions').should('be.visible');
      
      // Check classification percentage
      cy.contains('% classified').should('be.visible');
    });
  });

  it('should handle WebSocket connection status', () => {
    cy.wait('@getTransactions');

    // Check for WebSocket connection indicator
    cy.get('[data-testid="websocket-status"]').should('be.visible');
    
    // Should show connected status
    cy.contains('Real-time updates active').should('be.visible');
    
    // Mock disconnection
    cy.window().then((win) => {
      win.dispatchEvent(new CustomEvent('websocket-disconnect'));
    });
    
    // Should show disconnected status
    cy.contains('Real-time updates unavailable').should('be.visible');
  });

  it('should validate transaction form inputs', () => {
    cy.get('[data-testid="create-transaction-btn"]').click();

    // Try to submit empty form
    cy.get('[data-testid="transaction-form"]').within(() => {
      cy.get('button[type="submit"]').click();
    });

    // Verify validation errors
    cy.contains('Description is required').should('be.visible');
    cy.contains('Amount is required').should('be.visible');
    cy.contains('Type is required').should('be.visible');
  });

  it('should handle category creation for new AI suggestions', () => {
    cy.wait('@getTransactions');

    // Mock WebSocket message with new category
    cy.window().then((win) => {
      const mockEvent = {
        eventType: 'TRANSACTION_CATEGORIZED',
        data: {
          transactionId: 'txn-456',
          suggestedCategory: {
            id: 'cat-new',
            name: 'Professional Services'
          },
          confidence: 0.75,
          explanation: 'AI detected professional service keywords'
        }
      };

      win.dispatchEvent(new CustomEvent('websocket-message', { detail: mockEvent }));
    });

    // Verify new category appears in suggestions
    cy.contains('Professional Services').should('be.visible');
    cy.contains('75%').should('be.visible');
  });
});
