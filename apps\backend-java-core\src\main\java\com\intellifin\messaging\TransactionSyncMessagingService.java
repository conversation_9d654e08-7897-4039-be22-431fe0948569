package com.intellifin.messaging;

import com.intellifin.messaging.events.TransactionReceivedEvent;
import com.intellifin.messaging.events.TransactionCategorizedEvent;
import com.intellifin.messaging.events.TransactionSyncFailedEvent;
import com.intellifin.model.Transaction;
import com.intellifin.model.WebhookEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * Service for publishing transaction synchronization messaging events
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TransactionSyncMessagingService {

    private final StreamBridge streamBridge;

    /**
     * Publish transaction received event
     */
    public void publishTransactionReceived(Transaction transaction, WebhookEvent webhookEvent) {
        try {
            log.info("Publishing transaction received event for transaction: {}", transaction.getId());
            
            TransactionReceivedEvent event = TransactionReceivedEvent.builder()
                    .transactionId(transaction.getId())
                    .externalTransactionId(transaction.getExternalTransactionId())
                    .userId(transaction.getUser().getId())
                    .amount(transaction.getAmount())
                    .description(transaction.getDescription())
                    .transactionType(transaction.getType().name())
                    .source(transaction.getSource())
                    .webhookEventId(webhookEvent != null ? webhookEvent.getId() : null)
                    .financialAccountId(webhookEvent != null && webhookEvent.getFinancialAccount() != null ? 
                            webhookEvent.getFinancialAccount().getId() : null)
                    .timestamp(OffsetDateTime.now())
                    .build();
            
            Message<TransactionReceivedEvent> message = MessageBuilder
                    .withPayload(event)
                    .setHeader("eventType", "TransactionReceived")
                    .setHeader("transactionId", transaction.getId().toString())
                    .setHeader("userId", transaction.getUser().getId().toString())
                    .setHeader("source", transaction.getSource())
                    .build();

            boolean sent = streamBridge.send("transactionReceived-out-0", message);
            
            if (sent) {
                log.info("Successfully published transaction received event for transaction: {}", transaction.getId());
            } else {
                log.error("Failed to publish transaction received event for transaction: {}", transaction.getId());
            }
            
        } catch (Exception e) {
            log.error("Error publishing transaction received event for transaction: {}", transaction.getId(), e);
        }
    }

    /**
     * Publish transaction categorized event
     */
    public void publishTransactionCategorized(Transaction transaction, UUID categoryId) {
        try {
            log.info("Publishing transaction categorized event for transaction: {}", transaction.getId());
            
            TransactionCategorizedEvent event = TransactionCategorizedEvent.builder()
                    .transactionId(transaction.getId())
                    .userId(transaction.getUser().getId())
                    .categoryId(categoryId)
                    .amount(transaction.getAmount())
                    .transactionType(transaction.getType().name())
                    .confidence(transaction.getAiConfidence())
                    .source(transaction.getSource())
                    .timestamp(OffsetDateTime.now())
                    .build();
            
            Message<TransactionCategorizedEvent> message = MessageBuilder
                    .withPayload(event)
                    .setHeader("eventType", "TransactionCategorized")
                    .setHeader("transactionId", transaction.getId().toString())
                    .setHeader("userId", transaction.getUser().getId().toString())
                    .setHeader("categoryId", categoryId.toString())
                    .build();

            boolean sent = streamBridge.send("transactionCategorized-out-0", message);
            
            if (sent) {
                log.info("Successfully published transaction categorized event for transaction: {}", transaction.getId());
            } else {
                log.error("Failed to publish transaction categorized event for transaction: {}", transaction.getId());
            }
            
        } catch (Exception e) {
            log.error("Error publishing transaction categorized event for transaction: {}", transaction.getId(), e);
        }
    }

    /**
     * Publish transaction sync failed event
     */
    public void publishTransactionSyncFailed(String externalTransactionId, String errorMessage) {
        try {
            log.info("Publishing transaction sync failed event for external transaction: {}", externalTransactionId);
            
            TransactionSyncFailedEvent event = TransactionSyncFailedEvent.builder()
                    .externalTransactionId(externalTransactionId)
                    .errorMessage(errorMessage)
                    .retryCount(1)
                    .timestamp(OffsetDateTime.now())
                    .build();
            
            Message<TransactionSyncFailedEvent> message = MessageBuilder
                    .withPayload(event)
                    .setHeader("eventType", "TransactionSyncFailed")
                    .setHeader("externalTransactionId", externalTransactionId)
                    .setHeader("errorMessage", errorMessage)
                    .build();

            boolean sent = streamBridge.send("transactionSyncFailed-out-0", message);
            
            if (sent) {
                log.info("Successfully published transaction sync failed event for external transaction: {}", externalTransactionId);
            } else {
                log.error("Failed to publish transaction sync failed event for external transaction: {}", externalTransactionId);
            }
            
        } catch (Exception e) {
            log.error("Error publishing transaction sync failed event for external transaction: {}", externalTransactionId, e);
        }
    }

    /**
     * Publish journal entry created event
     */
    public void publishJournalEntryCreated(UUID transactionId, UUID journalEntryId) {
        try {
            log.info("Publishing journal entry created event for transaction: {}", transactionId);
            
            Message<Object> message = MessageBuilder
                    .withPayload(java.util.Map.of(
                            "transactionId", transactionId,
                            "journalEntryId", journalEntryId,
                            "timestamp", OffsetDateTime.now()
                    ))
                    .setHeader("eventType", "JournalEntryCreated")
                    .setHeader("transactionId", transactionId.toString())
                    .setHeader("journalEntryId", journalEntryId.toString())
                    .build();

            boolean sent = streamBridge.send("journalEntryCreated-out-0", message);
            
            if (sent) {
                log.info("Successfully published journal entry created event for transaction: {}", transactionId);
            } else {
                log.error("Failed to publish journal entry created event for transaction: {}", transactionId);
            }
            
        } catch (Exception e) {
            log.error("Error publishing journal entry created event for transaction: {}", transactionId, e);
        }
    }
}
