package com.intellifin.messaging.events;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Event published when a transaction needs AI categorization
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionCategorizationRequestedEvent {
    
    private UUID transactionId;
    private String description;
    private BigDecimal amount;
    private UUID userId;
    private LocalDateTime timestamp;
    private String transactionType; // INCOME or EXPENSE
    private String source;
}
