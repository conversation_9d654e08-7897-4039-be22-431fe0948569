package com.intellifin.config;

import com.intellifin.security.WebSocketAuthInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.simp.config.ChannelRegistration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;

/**
 * WebSocket configuration class that sets up STOMP messaging over WebSocket for real-time communication.
 * This configuration enables WebSocket message broker functionality with authentication, message routing,
 * and connection management for the Intellifin application.
 */
@Configuration
@EnableWebSocketMessageBroker
@RequiredArgsConstructor
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {

    private final WebSocketAuthInterceptor webSocketAuthInterceptor;

    /**
     * Configures the message broker for handling WebSocket messages.
     * Sets up destination prefixes for different types of messages including user-specific
     * and topic-based messaging patterns.
     *
     * @param config the MessageBrokerRegistry used to configure message broker settings
     */
    @Override
    public void configureMessageBroker(MessageBrokerRegistry config) {
        // Enable simple broker for sending messages to clients
        config.enableSimpleBroker("/user", "/topic");
        
        // Set application destination prefix for client messages
        config.setApplicationDestinationPrefixes("/app");
        
        // Set user destination prefix for private messages
        config.setUserDestinationPrefix("/user");
    }

    /**
     * Registers STOMP endpoints for WebSocket connections.
     * Configures endpoints with SockJS fallback support and sets allowed origins
     * for cross-origin resource sharing (CORS) compliance.
     *
     * @param registry the StompEndpointRegistry used to register WebSocket endpoints
     */
    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        // Register WebSocket endpoint with SockJS fallback for conversation
        registry.addEndpoint("/ws/conversation")
                .setAllowedOriginPatterns("http://localhost:3000", "https://*.intellifin.com")
                .withSockJS()
                .setHeartbeatTime(25000)
                .setDisconnectDelay(5000);

        // Register WebSocket endpoint without SockJS for native WebSocket clients
        registry.addEndpoint("/ws/conversation")
                .setAllowedOriginPatterns("http://localhost:3000", "https://*.intellifin.com");

        // Register WebSocket endpoint for transaction updates
        registry.addEndpoint("/ws/transaction-updates")
                .setAllowedOriginPatterns("http://localhost:3000", "https://*.intellifin.com")
                .withSockJS()
                .setHeartbeatTime(25000)
                .setDisconnectDelay(5000);

        // Register WebSocket endpoint without SockJS for transaction updates
        registry.addEndpoint("/ws/transaction-updates")
                .setAllowedOriginPatterns("http://localhost:3000", "https://*.intellifin.com");
    }

    /**
     * Configures the client inbound channel for processing incoming messages from clients.
     * Sets up authentication interceptors and thread pool configuration for handling
     * inbound message processing efficiently.
     *
     * @param registration the ChannelRegistration used to configure the inbound channel
     */
    @Override
    public void configureClientInboundChannel(ChannelRegistration registration) {
        // Add authentication interceptor for WebSocket connections
        registration.interceptors(webSocketAuthInterceptor);
        
        // Configure thread pool for handling inbound messages
        registration.taskExecutor()
                .corePoolSize(4)
                .maxPoolSize(8)
                .keepAliveSeconds(60);
    }

    /**
     * Configures the client outbound channel for sending messages to clients.
     * Sets up thread pool configuration to optimize the delivery of outbound messages
     * to connected WebSocket clients.
     *
     * @param registration the ChannelRegistration used to configure the outbound channel
     */
    @Override
    public void configureClientOutboundChannel(ChannelRegistration registration) {
        // Configure thread pool for handling outbound messages
        registration.taskExecutor()
                .corePoolSize(4)
                .maxPoolSize(8)
                .keepAliveSeconds(60);
    }
}