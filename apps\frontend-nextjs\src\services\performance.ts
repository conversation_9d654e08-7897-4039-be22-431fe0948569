/**
 * Performance monitoring service for tracking categorization operations
 */

interface PerformanceMetric {
  operation: string;
  duration: number;
  timestamp: number;
  success: boolean;
  error?: string;
  metadata?: Record<string, any>;
}

interface PerformanceStats {
  totalOperations: number;
  averageDuration: number;
  successRate: number;
  errorRate: number;
  slowestOperation: number;
  fastestOperation: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private maxMetrics = 1000; // Keep last 1000 metrics

  /**
   * Start timing an operation
   */
  startTiming(operation: string): () => void {
    const startTime = performance.now();
    
    return (success: boolean = true, error?: string, metadata?: Record<string, any>) => {
      const duration = performance.now() - startTime;
      this.recordMetric({
        operation,
        duration,
        timestamp: Date.now(),
        success,
        error,
        metadata
      });
    };
  }

  /**
   * Record a performance metric
   */
  private recordMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric);
    
    // Keep only the most recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    // Log slow operations
    if (metric.duration > 5000) { // 5 seconds
      console.warn(`Slow operation detected: ${metric.operation} took ${metric.duration.toFixed(2)}ms`);
    }

    // Log errors
    if (!metric.success && metric.error) {
      console.error(`Operation failed: ${metric.operation} - ${metric.error}`);
    }
  }

  /**
   * Get performance statistics for an operation
   */
  getStats(operation?: string): PerformanceStats {
    const filteredMetrics = operation 
      ? this.metrics.filter(m => m.operation === operation)
      : this.metrics;

    if (filteredMetrics.length === 0) {
      return {
        totalOperations: 0,
        averageDuration: 0,
        successRate: 0,
        errorRate: 0,
        slowestOperation: 0,
        fastestOperation: 0
      };
    }

    const durations = filteredMetrics.map(m => m.duration);
    const successCount = filteredMetrics.filter(m => m.success).length;

    return {
      totalOperations: filteredMetrics.length,
      averageDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
      successRate: (successCount / filteredMetrics.length) * 100,
      errorRate: ((filteredMetrics.length - successCount) / filteredMetrics.length) * 100,
      slowestOperation: Math.max(...durations),
      fastestOperation: Math.min(...durations)
    };
  }

  /**
   * Get recent metrics
   */
  getRecentMetrics(count: number = 10): PerformanceMetric[] {
    return this.metrics.slice(-count);
  }

  /**
   * Get metrics for a specific time range
   */
  getMetricsInRange(startTime: number, endTime: number): PerformanceMetric[] {
    return this.metrics.filter(m => 
      m.timestamp >= startTime && m.timestamp <= endTime
    );
  }

  /**
   * Clear all metrics
   */
  clear(): void {
    this.metrics = [];
  }

  /**
   * Export metrics for analysis
   */
  exportMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

/**
 * Decorator for monitoring async function performance
 */
export function monitorPerformance(operation: string) {
  return function <T extends (...args: any[]) => Promise<any>>(
    target: any,
    propertyName: string,
    descriptor: TypedPropertyDescriptor<T>
  ) {
    const method = descriptor.value!;

    descriptor.value = (async function (this: any, ...args: any[]) {
      const endTiming = performanceMonitor.startTiming(operation);
      
      try {
        const result = await method.apply(this, args);
        endTiming(true, undefined, { args: args.length });
        return result;
      } catch (error: any) {
        endTiming(false, error.message, { args: args.length });
        throw error;
      }
    }) as T;

    return descriptor;
  };
}

/**
 * Hook for monitoring categorization performance
 */
export function useCategorizationPerformance() {
  const getStats = (operation?: string) => performanceMonitor.getStats(operation);
  
  const getRecentMetrics = (count?: number) => performanceMonitor.getRecentMetrics(count);
  
  const monitorOperation = (operation: string) => performanceMonitor.startTiming(operation);

  return {
    getStats,
    getRecentMetrics,
    monitorOperation,
    performanceMonitor
  };
}

/**
 * Performance monitoring for specific categorization operations
 */
export const CategorizationOperations = {
  ACCEPT_AI_SUGGESTION: 'accept_ai_suggestion',
  REJECT_AI_SUGGESTION: 'reject_ai_suggestion',
  MANUAL_CATEGORIZE: 'manual_categorize',
  BULK_CATEGORIZE: 'bulk_categorize',
  FETCH_TRANSACTIONS: 'fetch_transactions',
  FETCH_CATEGORIES: 'fetch_categories',
  WEBSOCKET_CONNECT: 'websocket_connect',
  WEBSOCKET_MESSAGE: 'websocket_message'
} as const;

/**
 * Utility function to measure and log categorization performance
 */
export async function measureCategorizationPerformance<T>(
  operation: string,
  fn: () => Promise<T>,
  metadata?: Record<string, any>
): Promise<T> {
  const endTiming = performanceMonitor.startTiming(operation);
  
  try {
    const result = await fn();
    endTiming(true, undefined, metadata);
    return result;
  } catch (error: any) {
    endTiming(false, error.message, metadata);
    throw error;
  }
}

/**
 * Performance alert thresholds
 */
export const PerformanceThresholds = {
  SLOW_OPERATION: 5000, // 5 seconds
  VERY_SLOW_OPERATION: 10000, // 10 seconds
  HIGH_ERROR_RATE: 10, // 10%
  LOW_SUCCESS_RATE: 90 // 90%
} as const;

/**
 * Check if performance is within acceptable thresholds
 */
export function checkPerformanceHealth(operation?: string): {
  healthy: boolean;
  issues: string[];
  stats: PerformanceStats;
} {
  const stats = performanceMonitor.getStats(operation);
  const issues: string[] = [];
  
  if (stats.averageDuration > PerformanceThresholds.SLOW_OPERATION) {
    issues.push(`Average duration (${stats.averageDuration.toFixed(2)}ms) exceeds threshold`);
  }
  
  if (stats.errorRate > PerformanceThresholds.HIGH_ERROR_RATE) {
    issues.push(`Error rate (${stats.errorRate.toFixed(2)}%) is too high`);
  }
  
  if (stats.successRate < PerformanceThresholds.LOW_SUCCESS_RATE) {
    issues.push(`Success rate (${stats.successRate.toFixed(2)}%) is too low`);
  }
  
  return {
    healthy: issues.length === 0,
    issues,
    stats
  };
}
