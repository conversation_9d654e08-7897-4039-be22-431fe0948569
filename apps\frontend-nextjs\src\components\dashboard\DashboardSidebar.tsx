"use client"

import React from 'react';
import { usePathname, useRouter } from 'next/navigation';
import {
  Home,
  CreditCard,
  Receipt,
  FileText,
  BarChart3,
  Users,
  Settings,
  Wallet,
  TrendingUp,
  AlertCircle,
  BookOpen,
  Eye
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useFinancialStore } from '@/stores';
import { ROUTES } from '@/utils/constants';
import { formatCurrency } from '@/utils';

interface SidebarItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string | number;
  badgeColor?: 'red' | 'yellow' | 'green' | 'blue' | 'gray';
}

const navigation: SidebarItem[] = [
  { name: 'Dashboard', href: ROUTES.DASHBOARD, icon: Home },
  { name: 'Transactions', href: ROUTES.TRANSACTIONS, icon: CreditCard },
  { name: 'Invoices', href: ROUTES.INVOICES, icon: Receipt },
  { name: 'Clients', href: ROUTES.CLIENTS, icon: Users },
  { name: 'Reports', href: ROUTES.REPORTS, icon: BarChart3 },
];

const bottomNavigation: SidebarItem[] = [
  { name: 'Settings', href: ROUTES.SETTINGS, icon: Settings },
];

const accountingNavigation: SidebarItem[] = [
  { name: 'Chart of Accounts', href: ROUTES.ACCOUNTING_CHART_OF_ACCOUNTS, icon: BarChart3 },
  { name: 'Categories', href: ROUTES.ACCOUNTING_CATEGORIES, icon: FileText },
  { name: 'Journal Entries', href: ROUTES.JOURNAL_ENTRIES, icon: BookOpen },
];

const developmentNavigation: SidebarItem[] = [
  { name: 'Preview Components', href: '/previews', icon: Eye },
];

export function DashboardSidebar() {
  const pathname = usePathname();
  const router = useRouter();
  const { dashboardData, accounts } = useFinancialStore();

  // Calculate business vitals
  const totalBalance = accounts.reduce((sum, account) => sum + account.balance, 0);
  const pendingInvoicesCount = dashboardData?.pendingInvoices?.length || 0;
  const overdueInvoicesCount = dashboardData?.alerts?.filter(
    alert => alert.type === 'OVERDUE_INVOICE'
  ).length || 0;

  const getBadgeColor = (color: string) => {
    const colors = {
      red: 'bg-red-100 text-red-800',
      yellow: 'bg-yellow-100 text-yellow-800',
      green: 'bg-green-100 text-green-800',
      blue: 'bg-blue-100 text-blue-800',
      gray: 'bg-gray-100 text-gray-800',
    };
    return colors[color as keyof typeof colors] || colors.gray;
  };

  // Add badges to navigation items based on data
  const navigationWithBadges = navigation.map(item => {
    switch (item.href) {
      case ROUTES.INVOICES:
        if (overdueInvoicesCount > 0) {
          return { ...item, badge: overdueInvoicesCount, badgeColor: 'red' as const };
        }
        if (pendingInvoicesCount > 0) {
          return { ...item, badge: pendingInvoicesCount, badgeColor: 'yellow' as const };
        }
        return item;
      default:
        return item;
    }
  });

  return (
    <div className="w-64 bg-white border-r border-gray-200 flex flex-col">
      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-2">
        {navigationWithBadges.map((item) => {
          const isActive = pathname === item.href;
          return (
            <button
              key={item.name}
              onClick={() => router.push(item.href)}
              className={cn(
                'w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors',
                isActive
                  ? 'bg-primary/10 text-primary border border-primary/20'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              )}
            >
              <div className="flex items-center space-x-3">
                <item.icon className="h-4 w-4" />
                <span>{item.name}</span>
              </div>
              {item.badge && (
                <span
                  className={cn(
                    'px-2 py-1 text-xs font-medium rounded-full',
                    getBadgeColor(item.badgeColor || 'gray')
                  )}
                >
                  {item.badge}
                </span>
              )}
            </button>
          );
        })}

        {/* Accounting Section */}
        <div className="pt-6">
          <h3 className="px-3 mb-2 text-xs font-semibold text-gray-500 uppercase tracking-wide">
            Accounting
          </h3>
          <div className="space-y-2">
            {accountingNavigation.map((item) => {
              const isActive = pathname === item.href;
              return (
                <button
                  key={item.name}
                  onClick={() => router.push(item.href)}
                  className={cn(
                    'w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors',
                    isActive
                      ? 'bg-primary/10 text-primary border border-primary/20'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  )}
                >
                  <div className="flex items-center space-x-3">
                    <item.icon className="h-4 w-4" />
                    <span>{item.name}</span>
                  </div>
                </button>
              );
            })}
          </div>
        </div>

        {/* Development Section */}
        <div className="pt-6">
          <h3 className="px-3 mb-2 text-xs font-semibold text-gray-500 uppercase tracking-wide">
            Development
          </h3>
          <div className="space-y-2">
            {developmentNavigation.map((item) => {
              const isActive = pathname === item.href;
              return (
                <button
                  key={item.name}
                  onClick={() => router.push(item.href)}
                  className={cn(
                    'w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors',
                    isActive
                      ? 'bg-primary/10 text-primary border border-primary/20'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  )}
                >
                  <div className="flex items-center space-x-3">
                    <item.icon className="h-4 w-4" />
                    <span>{item.name}</span>
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      </nav>

      {/* Business Vitals Summary */}
      <div className="px-4 py-4 border-t border-gray-200">
        <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3">
          Quick Overview
        </h3>
        <div className="space-y-3">
          {/* Total Balance */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Wallet className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-600">Total Balance</span>
            </div>
            <span className="text-sm font-semibold text-gray-900">
              {formatCurrency(totalBalance)}
            </span>
          </div>

          {/* Monthly Revenue */}
          {dashboardData?.summary && (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-4 w-4 text-green-500" />
                <span className="text-sm text-gray-600">This Month</span>
              </div>
              <span className="text-sm font-semibold text-green-600">
                {formatCurrency(dashboardData.summary.totalIncome)}
              </span>
            </div>
          )}

          {/* Alerts */}
          {dashboardData?.alerts && dashboardData.alerts.length > 0 && (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-4 w-4 text-red-500" />
                <span className="text-sm text-gray-600">Alerts</span>
              </div>
              <span className="text-sm font-semibold text-red-600">
                {dashboardData.alerts.filter(alert => 
                  alert.severity === 'HIGH' || alert.severity === 'CRITICAL'
                ).length}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Bottom Navigation */}
      <div className="px-4 py-4 border-t border-gray-200">
        {bottomNavigation.map((item) => {
          const isActive = pathname === item.href;
          return (
            <button
              key={item.name}
              onClick={() => router.push(item.href)}
              className={cn(
                'w-full flex items-center space-x-3 px-3 py-2 text-sm font-medium rounded-lg transition-colors',
                isActive
                  ? 'bg-primary/10 text-primary border border-primary/20'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              )}
            >
              <item.icon className="h-4 w-4" />
              <span>{item.name}</span>
            </button>
          );
        })}
      </div>
    </div>
  );
}
