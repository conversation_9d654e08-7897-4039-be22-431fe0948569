package com.intellifin.service;

import com.intellifin.dto.webhook.MTNTransactionWebhookRequest;
import com.intellifin.model.FinancialAccount;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Base64;

/**
 * Service for webhook security validation
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WebhookSecurityService {

    private static final String HMAC_SHA256 = "HmacSHA256";
    private static final long WEBHOOK_TIMESTAMP_TOLERANCE_MINUTES = 5;

    /**
     * Verify MTN webhook signature
     */
    public boolean verifyMTNSignature(MTNTransactionWebhookRequest request, 
                                     String signature, 
                                     String timestamp, 
                                     FinancialAccount account) {
        try {
            // Skip signature verification if not provided (for testing)
            if (signature == null || signature.isEmpty()) {
                log.warn("No signature provided for MTN webhook verification - allowing for testing");
                return true;
            }

            // Verify timestamp is recent
            if (!verifyTimestamp(timestamp)) {
                log.error("Webhook timestamp is too old or invalid: {}", timestamp);
                return false;
            }

            // Get webhook secret for the account
            String webhookSecret = getWebhookSecret(account, "MTN");
            if (webhookSecret == null) {
                log.error("No webhook secret found for MTN account: {}", account.getId());
                return false;
            }

            // Create payload string for signature verification
            String payload = createMTNPayloadString(request, timestamp);
            
            // Calculate expected signature
            String expectedSignature = calculateHMACSignature(payload, webhookSecret);
            
            // Compare signatures
            boolean isValid = signature.equals(expectedSignature);
            
            if (!isValid) {
                log.error("Invalid MTN webhook signature. Expected: {}, Received: {}", expectedSignature, signature);
            }
            
            return isValid;
            
        } catch (Exception e) {
            log.error("Error verifying MTN webhook signature", e);
            return false;
        }
    }

    /**
     * Verify generic webhook signature
     */
    public boolean verifyGenericSignature(String payload, 
                                        String signature, 
                                        String timestamp, 
                                        String provider,
                                        FinancialAccount account) {
        try {
            // Skip signature verification if not provided (for testing)
            if (signature == null || signature.isEmpty()) {
                log.warn("No signature provided for {} webhook verification - allowing for testing", provider);
                return true;
            }

            // Verify timestamp is recent
            if (!verifyTimestamp(timestamp)) {
                log.error("Webhook timestamp is too old or invalid: {}", timestamp);
                return false;
            }

            // Get webhook secret for the account and provider
            String webhookSecret = getWebhookSecret(account, provider);
            if (webhookSecret == null) {
                log.error("No webhook secret found for {} account: {}", provider, account.getId());
                return false;
            }

            // Create payload string for signature verification
            String signaturePayload = payload + timestamp;
            
            // Calculate expected signature
            String expectedSignature = calculateHMACSignature(signaturePayload, webhookSecret);
            
            // Compare signatures
            boolean isValid = signature.equals(expectedSignature);
            
            if (!isValid) {
                log.error("Invalid {} webhook signature. Expected: {}, Received: {}", provider, expectedSignature, signature);
            }
            
            return isValid;
            
        } catch (Exception e) {
            log.error("Error verifying {} webhook signature", provider, e);
            return false;
        }
    }

    /**
     * Verify webhook timestamp is recent
     */
    private boolean verifyTimestamp(String timestamp) {
        if (timestamp == null || timestamp.isEmpty()) {
            return false;
        }

        try {
            OffsetDateTime webhookTime = OffsetDateTime.parse(timestamp);
            OffsetDateTime now = OffsetDateTime.now();
            
            long minutesDifference = ChronoUnit.MINUTES.between(webhookTime, now);
            
            return Math.abs(minutesDifference) <= WEBHOOK_TIMESTAMP_TOLERANCE_MINUTES;
            
        } catch (Exception e) {
            log.error("Error parsing webhook timestamp: {}", timestamp, e);
            return false;
        }
    }

    /**
     * Get webhook secret for account and provider
     */
    private String getWebhookSecret(FinancialAccount account, String provider) {
        // In a real implementation, this would retrieve the webhook secret
        // from the account's encrypted connection details or a separate webhook configuration
        // For now, return a mock secret for testing
        return "mock-webhook-secret-" + provider.toLowerCase();
    }

    /**
     * Create MTN payload string for signature calculation
     */
    private String createMTNPayloadString(MTNTransactionWebhookRequest request, String timestamp) {
        return String.format("%s%s%s%s%s",
                request.getTransactionId(),
                request.getAmount().toString(),
                request.getDescription(),
                request.getAccountId(),
                timestamp);
    }

    /**
     * Calculate HMAC-SHA256 signature
     */
    private String calculateHMACSignature(String payload, String secret) 
            throws NoSuchAlgorithmException, InvalidKeyException {
        
        Mac mac = Mac.getInstance(HMAC_SHA256);
        SecretKeySpec secretKeySpec = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), HMAC_SHA256);
        mac.init(secretKeySpec);
        
        byte[] hash = mac.doFinal(payload.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(hash);
    }

    /**
     * Validate webhook rate limiting
     */
    public boolean validateRateLimit(FinancialAccount account, String provider) {
        // In a real implementation, this would check rate limits
        // For now, always allow
        return true;
    }

    /**
     * Log security event
     */
    public void logSecurityEvent(String event, FinancialAccount account, String provider, String details) {
        log.info("Webhook security event: {} for account: {} provider: {} details: {}", 
                event, account.getId(), provider, details);
    }
}
