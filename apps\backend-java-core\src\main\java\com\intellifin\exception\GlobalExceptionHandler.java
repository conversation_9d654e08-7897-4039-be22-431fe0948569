package com.intellifin.exception;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.LockedException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<Map<String, Object>> handleValidationException(
            ValidationException ex, HttpServletRequest request) {
        
        log.warn("Validation error: {}", ex.getMessage());
        
        Map<String, Object> errorResponse = createErrorResponse(
                "Validation Error",
                ex.getMessage(),
                HttpStatus.BAD_REQUEST,
                request.getRequestURI()
        );
        
        return ResponseEntity.badRequest().body(errorResponse);
    }

    @ExceptionHandler(AuthenticationException.class)
    public ResponseEntity<Map<String, Object>> handleAuthenticationException(
            AuthenticationException ex, HttpServletRequest request) {
        
        log.warn("Authentication error: {}", ex.getMessage());
        
        Map<String, Object> errorResponse = createErrorResponse(
                "Authentication Error",
                ex.getMessage(),
                HttpStatus.UNAUTHORIZED,
                request.getRequestURI()
        );
        
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(errorResponse);
    }

    @ExceptionHandler({BadCredentialsException.class, DisabledException.class, LockedException.class})
    public ResponseEntity<Map<String, Object>> handleSecurityExceptions(
            Exception ex, HttpServletRequest request) {
        
        log.warn("Security exception: {}", ex.getMessage());
        
        String message;
        if (ex instanceof BadCredentialsException) {
            message = "Invalid email or password";
        } else if (ex instanceof DisabledException) {
            message = "Account is disabled. Please verify your email address.";
        } else if (ex instanceof LockedException) {
            message = "Account is locked. Please try again later or reset your password.";
        } else {
            message = "Authentication failed";
        }
        
        Map<String, Object> errorResponse = createErrorResponse(
                "Authentication Error",
                message,
                HttpStatus.UNAUTHORIZED,
                request.getRequestURI()
        );
        
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(errorResponse);
    }

    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<Map<String, Object>> handleAccessDeniedException(
            AccessDeniedException ex, HttpServletRequest request) {
        
        log.warn("Access denied: {}", ex.getMessage());
        
        Map<String, Object> errorResponse = createErrorResponse(
                "Access Denied",
                "You don't have permission to access this resource",
                HttpStatus.FORBIDDEN,
                request.getRequestURI()
        );
        
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(errorResponse);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Map<String, Object>> handleMethodArgumentNotValidException(
            MethodArgumentNotValidException ex, HttpServletRequest request) {
        
        log.warn("Validation failed: {}", ex.getMessage());
        
        List<Map<String, String>> details = new ArrayList<>();
        for (FieldError error : ex.getBindingResult().getFieldErrors()) {
            Map<String, String> detail = new HashMap<>();
            detail.put("field", error.getField());
            detail.put("message", error.getDefaultMessage());
            details.add(detail);
        }
        
        Map<String, Object> errorResponse = createErrorResponse(
                "Validation Error",
                "Invalid input data",
                HttpStatus.BAD_REQUEST,
                request.getRequestURI()
        );
        errorResponse.put("details", details);
        
        return ResponseEntity.badRequest().body(errorResponse);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<Map<String, Object>> handleConstraintViolationException(
            ConstraintViolationException ex, HttpServletRequest request) {
        
        log.warn("Constraint violation: {}", ex.getMessage());
        
        List<Map<String, String>> details = new ArrayList<>();
        for (ConstraintViolation<?> violation : ex.getConstraintViolations()) {
            Map<String, String> detail = new HashMap<>();
            detail.put("field", violation.getPropertyPath().toString());
            detail.put("message", violation.getMessage());
            details.add(detail);
        }
        
        Map<String, Object> errorResponse = createErrorResponse(
                "Validation Error",
                "Constraint violation",
                HttpStatus.BAD_REQUEST,
                request.getRequestURI()
        );
        errorResponse.put("details", details);
        
        return ResponseEntity.badRequest().body(errorResponse);
    }

    @ExceptionHandler(TransactionNotFoundException.class)
    public ResponseEntity<Map<String, Object>> handleTransactionNotFoundException(
            TransactionNotFoundException ex, HttpServletRequest request) {

        log.warn("Transaction not found: {}", ex.getMessage());

        Map<String, Object> errorResponse = createErrorResponse(
                "Transaction Not Found",
                ex.getMessage(),
                HttpStatus.NOT_FOUND,
                request.getRequestURI()
        );

        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
    }

    @ExceptionHandler(CategoryNotFoundException.class)
    public ResponseEntity<Map<String, Object>> handleCategoryNotFoundException(
            CategoryNotFoundException ex, HttpServletRequest request) {

        log.warn("Category not found: {}", ex.getMessage());

        Map<String, Object> errorResponse = createErrorResponse(
                "Category Not Found",
                ex.getMessage(),
                HttpStatus.NOT_FOUND,
                request.getRequestURI()
        );

        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
    }

    @ExceptionHandler(AIServiceUnavailableException.class)
    public ResponseEntity<Map<String, Object>> handleAIServiceUnavailableException(
            AIServiceUnavailableException ex, HttpServletRequest request) {

        log.error("AI service unavailable: {}", ex.getMessage());

        Map<String, Object> errorResponse = createErrorResponse(
                "AI Service Unavailable",
                "AI categorization service is temporarily unavailable. Transactions will be processed manually.",
                HttpStatus.SERVICE_UNAVAILABLE,
                request.getRequestURI()
        );
        errorResponse.put("details", ex.getMessage());

        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(errorResponse);
    }

    @ExceptionHandler(CategorizationException.class)
    public ResponseEntity<Map<String, Object>> handleCategorizationException(
            CategorizationException ex, HttpServletRequest request) {

        log.error("Categorization failed: {}", ex.getMessage());

        Map<String, Object> errorResponse = createErrorResponse(
                "Categorization Failed",
                ex.getMessage(),
                HttpStatus.UNPROCESSABLE_ENTITY,
                request.getRequestURI()
        );

        return ResponseEntity.status(HttpStatus.UNPROCESSABLE_ENTITY).body(errorResponse);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> handleGenericException(
            Exception ex, HttpServletRequest request) {

        log.error("Unexpected error: ", ex);

        Map<String, Object> errorResponse = createErrorResponse(
                "Internal Server Error",
                "An unexpected error occurred. Please try again later.",
                HttpStatus.INTERNAL_SERVER_ERROR,
                request.getRequestURI()
        );

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }

    private Map<String, Object> createErrorResponse(String error, String message, HttpStatus status, String path) {
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("error", error);
        errorResponse.put("message", message);
        errorResponse.put("status", status.value());
        errorResponse.put("timestamp", LocalDateTime.now().toString());
        errorResponse.put("path", path);
        return errorResponse;
    }
}
