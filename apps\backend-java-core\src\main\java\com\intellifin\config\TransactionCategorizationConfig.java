package com.intellifin.config;

import com.intellifin.messaging.TransactionCategorizationMessagingService;
import com.intellifin.service.TransactionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;

/**
 * Configuration for transaction categorization messaging
 */
@Configuration
@RequiredArgsConstructor
@Slf4j
public class TransactionCategorizationConfig {

    private final TransactionCategorizationMessagingService messagingService;
    private final TransactionService transactionService;

    /**
     * Wire up the messaging service with the transaction service after context is refreshed
     */
    @EventListener(ContextRefreshedEvent.class)
    public void configureMessagingService() {
        log.info("Configuring transaction categorization messaging service");
        messagingService.setTransactionService(transactionService);
        log.info("Transaction categorization messaging service configured successfully");
    }
}
