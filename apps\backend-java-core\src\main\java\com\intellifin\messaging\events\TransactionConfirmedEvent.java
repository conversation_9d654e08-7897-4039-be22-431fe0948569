package com.intellifin.messaging.events;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Event published when a transaction is confirmed/categorized and ready for journal entry creation
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionConfirmedEvent {
    
    private UUID transactionId;
    private UUID userId;
    private String description;
    private BigDecimal amount;
    private String transactionType; // INCOME or EXPENSE
    private UUID categoryId;
    private String categoryName;
    private UUID accountId;
    private String accountCode;
    private String accountName;
    private LocalDateTime transactionDate;
    private String source;
    private String referenceNumber;
    private LocalDateTime timestamp;
}
