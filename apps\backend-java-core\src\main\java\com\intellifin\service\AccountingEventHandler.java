package com.intellifin.service;

import com.intellifin.messaging.events.JournalEntryCreatedEvent;
import com.intellifin.messaging.events.TransactionConfirmedEvent;
import com.intellifin.model.*;
import com.intellifin.repository.AccountRepository;
import com.intellifin.repository.CategoryRepository;
import com.intellifin.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.context.annotation.Bean;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Consumer;

/**
 * Service for handling accounting-related events and automatically creating journal entries
 */
@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class AccountingEventHandler {

    private final JournalService journalService;
    private final AccountRepository accountRepository;
    private final CategoryRepository categoryRepository;
    private final UserRepository userRepository;
    private final StreamBridge streamBridge;

    /**
     * Consumer for transaction confirmed events - creates journal entries automatically
     */
    @Bean
    public Consumer<TransactionConfirmedEvent> transactionConfirmed() {
        return event -> {
            try {
                log.info("Processing transaction confirmed event for transaction: {}", event.getTransactionId());
                createJournalEntryFromTransaction(event);
            } catch (Exception e) {
                log.error("Error processing transaction confirmed event for transaction: {}", 
                         event.getTransactionId(), e);
                throw e; // Re-throw to trigger dead letter queue
            }
        };
    }

    /**
     * Create journal entry from confirmed transaction
     */
    private void createJournalEntryFromTransaction(TransactionConfirmedEvent event) {
        try {
            // Get user
            User user = userRepository.findById(event.getUserId())
                    .orElseThrow(() -> new RuntimeException("User not found: " + event.getUserId()));

            // Get the account from the category mapping
            Account transactionAccount = accountRepository.findById(event.getAccountId())
                    .orElseThrow(() -> new RuntimeException("Account not found: " + event.getAccountId()));

            // Determine the cash/bank account (we'll use a default cash account for now)
            // In a real implementation, this would be determined by the transaction source
            Account cashAccount = getCashAccountForTransaction(event, user);

            // Create journal entry
            JournalEntry journalEntry = JournalEntry.builder()
                    .user(user)
                    .transactionId(event.getTransactionId())
                    .description(event.getDescription())
                    .totalAmount(event.getAmount())
                    .entryDate(event.getTransactionDate().atOffset(OffsetDateTime.now().getOffset()))
                    .referenceNumber(event.getReferenceNumber())
                    .sourceType("TRANSACTION")
                    .sourceId(event.getTransactionId().toString())
                    .status(JournalEntry.JournalEntryStatus.DRAFT)
                    .build();

            // Create journal entry lines based on transaction type
            List<JournalEntryLine> lines = createJournalEntryLines(event, transactionAccount, cashAccount);
            
            for (JournalEntryLine line : lines) {
                journalEntry.addLine(line);
            }

            // Save and post the journal entry
            JournalEntry savedEntry = journalService.createJournalEntry(journalEntry);
            JournalEntry postedEntry = journalService.postJournalEntry(savedEntry.getId());

            // Publish journal entry created event
            publishJournalEntryCreatedEvent(postedEntry);

            log.info("Successfully created and posted journal entry {} for transaction {}", 
                    postedEntry.getEntryNumber(), event.getTransactionId());

        } catch (Exception e) {
            log.error("Failed to create journal entry for transaction: {}", event.getTransactionId(), e);
            throw new RuntimeException("Failed to create journal entry for transaction", e);
        }
    }

    /**
     * Create journal entry lines based on transaction type
     */
    private List<JournalEntryLine> createJournalEntryLines(TransactionConfirmedEvent event, 
                                                          Account transactionAccount, 
                                                          Account cashAccount) {
        List<JournalEntryLine> lines = new ArrayList<>();

        if ("INCOME".equals(event.getTransactionType())) {
            // Income transaction: Debit Cash, Credit Income Account
            
            // Debit Cash Account
            JournalEntryLine cashLine = JournalEntryLine.builder()
                    .account(cashAccount)
                    .debitAmount(event.getAmount())
                    .creditAmount(BigDecimal.ZERO)
                    .description("Cash received - " + event.getDescription())
                    .referenceNumber(event.getReferenceNumber())
                    .lineNumber(1)
                    .build();
            lines.add(cashLine);

            // Credit Income Account
            JournalEntryLine incomeLine = JournalEntryLine.builder()
                    .account(transactionAccount)
                    .debitAmount(BigDecimal.ZERO)
                    .creditAmount(event.getAmount())
                    .description(event.getDescription())
                    .referenceNumber(event.getReferenceNumber())
                    .lineNumber(2)
                    .build();
            lines.add(incomeLine);

        } else if ("EXPENSE".equals(event.getTransactionType())) {
            // Expense transaction: Debit Expense Account, Credit Cash
            
            // Debit Expense Account
            JournalEntryLine expenseLine = JournalEntryLine.builder()
                    .account(transactionAccount)
                    .debitAmount(event.getAmount())
                    .creditAmount(BigDecimal.ZERO)
                    .description(event.getDescription())
                    .referenceNumber(event.getReferenceNumber())
                    .lineNumber(1)
                    .build();
            lines.add(expenseLine);

            // Credit Cash Account
            JournalEntryLine cashLine = JournalEntryLine.builder()
                    .account(cashAccount)
                    .debitAmount(BigDecimal.ZERO)
                    .creditAmount(event.getAmount())
                    .description("Cash paid - " + event.getDescription())
                    .referenceNumber(event.getReferenceNumber())
                    .lineNumber(2)
                    .build();
            lines.add(cashLine);
        }

        return lines;
    }

    /**
     * Get the appropriate cash account for the transaction
     * This is a simplified implementation - in reality, this would be determined by the transaction source
     */
    private Account getCashAccountForTransaction(TransactionConfirmedEvent event, User user) {
        // Look for a cash account for this user, or use the system default
        Optional<Account> cashAccount = accountRepository.findByCodeAndUser("1100", user);
        
        if (cashAccount.isEmpty()) {
            // Fall back to system cash account
            cashAccount = accountRepository.findByCodeAndUserIsNull("1100");
        }
        
        return cashAccount.orElseThrow(() -> 
            new RuntimeException("No cash account found for user or system"));
    }

    /**
     * Publish journal entry created event
     */
    private void publishJournalEntryCreatedEvent(JournalEntry journalEntry) {
        try {
            List<JournalEntryCreatedEvent.JournalEntryLineEvent> lineEvents = journalEntry.getLines().stream()
                    .map(line -> JournalEntryCreatedEvent.JournalEntryLineEvent.builder()
                            .lineId(line.getId())
                            .accountId(line.getAccount().getId())
                            .accountCode(line.getAccount().getCode())
                            .accountName(line.getAccount().getName())
                            .debitAmount(line.getDebitAmount())
                            .creditAmount(line.getCreditAmount())
                            .description(line.getDescription())
                            .build())
                    .toList();

            JournalEntryCreatedEvent event = JournalEntryCreatedEvent.builder()
                    .journalEntryId(journalEntry.getId())
                    .entryNumber(journalEntry.getEntryNumber())
                    .userId(journalEntry.getUser().getId())
                    .transactionId(journalEntry.getTransactionId())
                    .description(journalEntry.getDescription())
                    .totalAmount(journalEntry.getTotalAmount())
                    .status(journalEntry.getStatus().name())
                    .entryDate(journalEntry.getEntryDate().toLocalDateTime())
                    .sourceType(journalEntry.getSourceType())
                    .sourceId(journalEntry.getSourceId())
                    .lines(lineEvents)
                    .timestamp(LocalDateTime.now())
                    .build();

            Message<JournalEntryCreatedEvent> message = MessageBuilder
                    .withPayload(event)
                    .setHeader("eventType", "JournalEntryCreated")
                    .setHeader("journalEntryId", event.getJournalEntryId().toString())
                    .setHeader("userId", event.getUserId().toString())
                    .build();

            boolean sent = streamBridge.send("journalEntryCreated-out-0", message);
            
            if (sent) {
                log.info("Successfully published journal entry created event for entry: {}", 
                        journalEntry.getEntryNumber());
            } else {
                log.error("Failed to publish journal entry created event for entry: {}", 
                         journalEntry.getEntryNumber());
            }

        } catch (Exception e) {
            log.error("Error publishing journal entry created event for entry: {}", 
                     journalEntry.getEntryNumber(), e);
            // Don't fail the journal entry creation if event publishing fails
        }
    }

    /**
     * Publish transaction confirmed event (for manual transactions)
     */
    public void publishTransactionConfirmedEvent(TransactionConfirmedEvent event) {
        try {
            Message<TransactionConfirmedEvent> message = MessageBuilder
                    .withPayload(event)
                    .setHeader("eventType", "TransactionConfirmed")
                    .setHeader("transactionId", event.getTransactionId().toString())
                    .setHeader("userId", event.getUserId().toString())
                    .build();

            boolean sent = streamBridge.send("transactionConfirmed-out-0", message);
            
            if (sent) {
                log.info("Successfully published transaction confirmed event for transaction: {}", 
                        event.getTransactionId());
            } else {
                log.error("Failed to publish transaction confirmed event for transaction: {}", 
                         event.getTransactionId());
            }

        } catch (Exception e) {
            log.error("Error publishing transaction confirmed event for transaction: {}", 
                     event.getTransactionId(), e);
            throw new RuntimeException("Failed to publish transaction confirmed event", e);
        }
    }
}
