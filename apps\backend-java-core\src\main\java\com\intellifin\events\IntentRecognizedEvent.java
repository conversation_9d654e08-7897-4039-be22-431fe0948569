package com.intellifin.events;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

public class IntentRecognizedEvent {
    private String eventId;
    private String userId;
    private String sessionId;
    private String command;
    private String intentName;
    private double confidence;
    private String description;
    private LocalDateTime timestamp;
    private Map<String, Object> metadata;

    public IntentRecognizedEvent(String userId, String sessionId, String command,
                               String intentName, double confidence, String description) {
        this.eventId = UUID.randomUUID().toString();
        this.userId = userId;
        this.sessionId = sessionId;
        this.command = command;
        this.intentName = intentName;
        this.confidence = confidence;
        this.description = description;
        this.timestamp = LocalDateTime.now();
    }

    // Getters and setters
    public String getEventId() { return eventId; }
    public String getUserId() { return userId; }
    public String getSessionId() { return sessionId; }
    public String getCommand() { return command; }
    public String getIntentName() { return intentName; }
    public double getConfidence() { return confidence; }
    public String getDescription() { return description; }
    public LocalDateTime getTimestamp() { return timestamp; }
    public Map<String, Object> getMetadata() { return metadata; }
    public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
}
