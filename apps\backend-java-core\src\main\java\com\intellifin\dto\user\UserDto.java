package com.intellifin.dto.user;

import com.intellifin.model.User;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDto {

    private UUID id;
    
    private String email;
    
    private String firstName;
    
    private String lastName;
    
    private String organizationName;
    
    private String tpin;
    
    private User.OnboardingStatus onboardingStatus;
    
    private Boolean emailVerified;
    
    private LocalDateTime lastLoginAt;
    
    private String preferences;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;

    // Helper method to get full name
    public String getFullName() {
        return firstName + " " + lastName;
    }

    // Static factory method to create from User entity
    public static UserDto fromUser(User user) {
        return UserDto.builder()
                .id(user.getId())
                .email(user.getEmail())
                .firstName(user.getFirstName())
                .lastName(user.getLastName())
                .organizationName(user.getOrganizationName())
                .tpin(user.getTpin())
                .onboardingStatus(user.getOnboardingStatus())
                .emailVerified(user.getEmailVerified())
                .lastLoginAt(user.getLastLoginAt())
                .preferences(user.getPreferences() != null ? user.getPreferences().toString() : null)
                .createdAt(user.getCreatedAt())
                .updatedAt(user.getUpdatedAt())

                .build();
    }
}
