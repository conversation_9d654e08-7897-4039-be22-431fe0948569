'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { BookOpen, ExternalLink, AlertCircle, CheckCircle, FileEdit } from 'lucide-react';
import { JournalEntry } from '@intellifin/data-models';
import { formatCurrency, formatDate } from '@/utils/formatting';
import { ROUTES } from '@/utils/constants';

interface JournalEntryPreviewProps {
  entry: JournalEntry;
  showLines?: boolean;
  maxLines?: number;
  className?: string;
}

export function JournalEntryPreview({ 
  entry, 
  showLines = true, 
  maxLines = 3,
  className = '' 
}: JournalEntryPreviewProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return <FileEdit className="w-4 h-4" />;
      case 'POSTED':
        return <CheckCircle className="w-4 h-4" />;
      case 'REVERSED':
        return <AlertCircle className="w-4 h-4" />;
      default:
        return <BookOpen className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'POSTED':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'REVERSED':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const totalDebits = entry.lines?.reduce((sum, line) => sum + (line.debitAmount || 0), 0) || 0;
  const totalCredits = entry.lines?.reduce((sum, line) => sum + (line.creditAmount || 0), 0) || 0;
  const isBalanced = Math.abs(totalDebits - totalCredits) < 0.01;

  const displayLines = showLines ? (entry.lines?.slice(0, maxLines) || []) : [];
  const hasMoreLines = (entry.lines?.length || 0) > maxLines;

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={className}
    >
      <Card className="border-l-4 border-l-blue-500 hover:shadow-md transition-shadow">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <BookOpen className="w-5 h-5 text-blue-600" />
              <CardTitle className="text-lg">
                Journal Entry {entry.entryNumber}
              </CardTitle>
            </div>
            <div className="flex items-center space-x-2">
              <Badge 
                variant="outline" 
                className={`${getStatusColor(entry.status)} flex items-center space-x-1`}
              >
                {getStatusIcon(entry.status)}
                <span>{entry.status}</span>
              </Badge>
              {!isBalanced && (
                <Badge variant="destructive" className="flex items-center space-x-1">
                  <AlertCircle className="w-3 h-3" />
                  <span>Unbalanced</span>
                </Badge>
              )}
            </div>
          </div>
          
          <div className="text-sm text-gray-600 space-y-1">
            <p><strong>Date:</strong> {formatDate(entry.entryDate)}</p>
            {entry.description && (
              <p><strong>Description:</strong> {entry.description}</p>
            )}
            {(entry as any).reference && (
              <p><strong>Reference:</strong> {(entry as any).reference}</p>
            )}
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          {/* Balance Summary */}
          <div className="grid grid-cols-2 gap-4 mb-4 p-3 bg-gray-50 rounded-lg">
            <div className="text-center">
              <p className="text-xs text-gray-500 uppercase tracking-wide">Total Debits</p>
              <p className="text-lg font-semibold text-green-600">
                {formatCurrency(totalDebits)}
              </p>
            </div>
            <div className="text-center">
              <p className="text-xs text-gray-500 uppercase tracking-wide">Total Credits</p>
              <p className="text-lg font-semibold text-red-600">
                {formatCurrency(totalCredits)}
              </p>
            </div>
          </div>

          {/* Journal Lines Preview */}
          {showLines && displayLines.length > 0 && (
            <div className="space-y-2 mb-4">
              <h4 className="text-sm font-medium text-gray-700">Journal Lines:</h4>
              <div className="space-y-1">
                {displayLines.map((line, index) => (
                  <div 
                    key={line.id || index} 
                    className="flex justify-between items-center text-sm p-2 bg-white border rounded"
                  >
                    <div className="flex-1">
                      <span className="font-medium">{(line as any).accountName || `Account ${line.accountId}`}</span>
                      {line.description && (
                        <span className="text-gray-500 ml-2">- {line.description}</span>
                      )}
                    </div>
                    <div className="flex space-x-4 text-right">
                      <span className={`w-20 ${line.debitAmount ? 'text-green-600 font-medium' : 'text-gray-300'}`}>
                        {line.debitAmount ? formatCurrency(line.debitAmount) : '—'}
                      </span>
                      <span className={`w-20 ${line.creditAmount ? 'text-red-600 font-medium' : 'text-gray-300'}`}>
                        {line.creditAmount ? formatCurrency(line.creditAmount) : '—'}
                      </span>
                    </div>
                  </div>
                ))}
                
                {hasMoreLines && (
                  <div className="text-center py-2">
                    <span className="text-sm text-gray-500">
                      +{(entry.lines?.length || 0) - maxLines} more lines
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Progressive Disclosure CTA */}
          <div className="flex justify-between items-center pt-3 border-t">
            <div className="text-xs text-gray-500">
              {entry.lines?.length || 0} lines • Created {formatDate(entry.createdAt)}
            </div>
            <Button 
              variant="outline" 
              size="sm"
              className="flex items-center space-x-2 hover:bg-blue-50 hover:border-blue-300"
              onClick={() => {
                window.location.href = `${ROUTES.JOURNAL_ENTRIES}?entry=${entry.id}`;
              }}
            >
              <span>View Full Entry</span>
              <ExternalLink className="w-4 h-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

/**
 * Compact version for inline chat responses
 */
export function JournalEntryInlinePreview({ entry }: { entry: JournalEntry }) {
  const isBalanced = (entry as any).isBalanced;
  
  return (
    <div className="inline-flex items-center space-x-2 bg-blue-50 border border-blue-200 rounded-lg px-3 py-2 text-sm">
      <BookOpen className="w-4 h-4 text-blue-600" />
      <span className="font-medium">Entry {entry.entryNumber}</span>
      <Badge 
        variant="outline" 
        className={`text-xs ${entry.status === 'POSTED' ? 'bg-green-100 text-green-700' : 'bg-yellow-100 text-yellow-700'}`}
      >
        {entry.status}
      </Badge>
      {!isBalanced && (
        <Badge variant="destructive" className="text-xs">
          Unbalanced
        </Badge>
      )}
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-6 px-2 text-blue-600 hover:bg-blue-100"
        onClick={() => {
          window.location.href = `${ROUTES.JOURNAL_ENTRIES}?entry=${entry.id}`;
        }}
      >
        View <ExternalLink className="w-3 h-3 ml-1" />
      </Button>
    </div>
  );
}
