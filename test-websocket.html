<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test</title>
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@stomp/stompjs@7/bundles/stomp.umd.min.js"></script>
</head>
<body>
    <h1>IntelliFin WebSocket Test</h1>
    <div id="status">Disconnected</div>
    <div id="messages"></div>
    <input type="text" id="messageInput" placeholder="Enter message">
    <button onclick="sendMessage()">Send</button>
    <button onclick="connect()">Connect</button>
    <button onclick="disconnect()">Disconnect</button>

    <script>
        let stompClient = null;
        const token = 'eyJhbGciOiJIUzUxMiJ9.*******************************************************************************************************************************************************************************************************************************************************************************.riCy7iI14xID57UBSlBv9Jfia_C5SHf9EN4k1FuvW0B7wNeAVwRnP6a0wNlEZ0lkZnuRqo5xtDZlWdyoJ9yqeQ';

        function connect() {
            console.log('Attempting to connect...');
            const socket = new SockJS('http://localhost:8080/ws/conversation');
            stompClient = Stomp.over(socket);
            
            stompClient.connect({
                'Authorization': `Bearer ${token}`,
                'X-Auth-Token': token,
                'token': token
            }, function (frame) {
                console.log('Connected: ' + frame);
                document.getElementById('status').innerText = 'Connected';
                
                stompClient.subscribe('/user/queue/conversation', function (message) {
                    console.log('Received: ' + message.body);
                    showMessage(JSON.parse(message.body));
                });
            }, function (error) {
                console.log('Error: ' + error);
                document.getElementById('status').innerText = 'Error: ' + error;
            });
        }

        function disconnect() {
            if (stompClient !== null) {
                stompClient.disconnect();
            }
            document.getElementById('status').innerText = 'Disconnected';
            console.log("Disconnected");
        }

        function sendMessage() {
            const message = document.getElementById('messageInput').value;
            stompClient.send("/app/conversation/command", {}, JSON.stringify({
                'command': message,
                'sessionId': 'test-session',
                'expectResponse': true,
                'saveToHistory': true
            }));
        }

        function showMessage(message) {
            const messages = document.getElementById('messages');
            const messageElement = document.createElement('div');
            messageElement.appendChild(document.createTextNode(message.content || JSON.stringify(message)));
            messages.appendChild(messageElement);
        }
    </script>
</body>
</html>
