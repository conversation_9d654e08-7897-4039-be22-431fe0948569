package com.intellifin.security;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import io.jsonwebtoken.security.SignatureException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;

@Component
@Slf4j
public class JwtTokenUtil {

    private static final String AUTHORITIES_KEY = "authorities";
    private static final String USER_ID_KEY = "userId";
    private static final String EMAIL_KEY = "email";
    private static final String FULL_NAME_KEY = "fullName";

    @Value("${jwt.secret}")
    private String secret;

    @Value("${jwt.expiration:86400}") // 24 hours default
    private Long expiration;

    private SecretKey getSigningKey() {
        return Keys.hmacShaKeyFor(secret.getBytes());
    }

    public String generateToken(UserDetails userDetails, UUID userId, String email, String fullName) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(USER_ID_KEY, userId.toString());
        claims.put(EMAIL_KEY, email);
        claims.put(FULL_NAME_KEY, fullName);
        claims.put(AUTHORITIES_KEY, userDetails.getAuthorities().stream()
                .map(authority -> authority.getAuthority())
                .toList());
        
        return createToken(claims, userDetails.getUsername());
    }

    public String generateToken(UserDetails userDetails, UUID userId, String email, String fullName, Long customExpiration) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(USER_ID_KEY, userId.toString());
        claims.put(EMAIL_KEY, email);
        claims.put(FULL_NAME_KEY, fullName);
        claims.put(AUTHORITIES_KEY, userDetails.getAuthorities().stream()
                .map(authority -> authority.getAuthority())
                .toList());
        
        return createToken(claims, userDetails.getUsername(), customExpiration);
    }

    private String createToken(Map<String, Object> claims, String subject) {
        return createToken(claims, subject, expiration);
    }

    private String createToken(Map<String, Object> claims, String subject, Long tokenExpiration) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + tokenExpiration * 1000);

        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact();
    }

    public String getUsernameFromToken(String token) {
        return getClaimFromToken(token, Claims::getSubject);
    }

    public UUID getUserIdFromToken(String token) {
        String userIdStr = getClaimFromToken(token, claims -> claims.get(USER_ID_KEY, String.class));
        return userIdStr != null ? UUID.fromString(userIdStr) : null;
    }

    public String getEmailFromToken(String token) {
        return getClaimFromToken(token, claims -> claims.get(EMAIL_KEY, String.class));
    }

    public String getFullNameFromToken(String token) {
        return getClaimFromToken(token, claims -> claims.get(FULL_NAME_KEY, String.class));
    }

    public Date getExpirationDateFromToken(String token) {
        return getClaimFromToken(token, Claims::getExpiration);
    }

    public LocalDateTime getExpirationLocalDateTimeFromToken(String token) {
        Date expiration = getExpirationDateFromToken(token);
        return expiration.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public <T> T getClaimFromToken(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = getAllClaimsFromToken(token);
        return claimsResolver.apply(claims);
    }

    private Claims getAllClaimsFromToken(String token) {
        try {
            return Jwts.parser()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
        } catch (ExpiredJwtException e) {


            log.warn("JWT token is expired: {}", e.getMessage());
            throw e;
        } catch (UnsupportedJwtException e) {
            log.error("JWT token is unsupported: {}", e.getMessage());
            throw e;
        } catch (MalformedJwtException e) {
            log.error("JWT token is malformed: {}", e.getMessage());
            throw e;
        } catch (SecurityException e) {
            log.error("JWT signature validation failed: {}", e.getMessage());
            throw e;
        } catch (IllegalArgumentException e) {
            log.error("JWT token compact of handler are invalid: {}", e.getMessage());
            throw e;
        }
    }

    public Boolean isTokenExpired(String token) {
        try {
            final Date expiration = getExpirationDateFromToken(token);
            return expiration.before(new Date());
        } catch (ExpiredJwtException e) {
            return true;
        }
    }

    public Boolean validateToken(String token, UserDetails userDetails) {
        try {
            final String username = getUsernameFromToken(token);
            return (username.equals(userDetails.getUsername()) && !isTokenExpired(token));
        } catch (JwtException | IllegalArgumentException e) {
            log.warn("JWT token validation failed: {}", e.getMessage());
            return false;
        }
    }

    public boolean validateToken(String authToken) {
        try {
            Jwts.parser().setSigningKey(getSigningKey()).build().parseClaimsJws(authToken);
            return true;
        } catch (SignatureException ex) {


            log.error("Invalid JWT signature");
        } catch (MalformedJwtException ex) {
            log.error("Invalid JWT token");
        } catch (ExpiredJwtException ex) {
            log.error("Expired JWT token");
        } catch (UnsupportedJwtException ex) {
            log.error("Unsupported JWT token");
        } catch (IllegalArgumentException ex) {
            log.error("JWT claims string is empty.");
        }
        return false;
    }

    public Long getExpirationTime() {
        return expiration;
    }

    public LocalDateTime getExpirationDateTime() {
        return LocalDateTime.now().plusSeconds(expiration);
    }
}
