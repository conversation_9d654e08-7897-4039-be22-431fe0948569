"""
Intent recognition service using LangChain and AI models
"""

import json
import time
import re
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

from langchain_core.language_models.base import BaseLanguageModel

from ..config.ai_config import get_ai_config, INTENT_RECOGNITION_CONFIG
from ..models.schemas import (
    IntentRecognitionRequest, 
    IntentRecognitionResponse,
    Intent, 
    Entity, 
    Fallback,
    IntentType,
    EntityType,
    FallbackType
)
from ..models.prompts import (
    INTENT_RECOGNITION_PROMPT, 
    CLARIFICATION_PROMPT,
    CONFIDENCE_THRESHOLDS,
    ZAMBIAN_BUSINESS_TERMS
)


class IntentRecognitionService:
    """Service for recognizing user intents and extracting entities"""
    
    def __init__(self):
        self.ai_config = get_ai_config()
        self.model = None  # Lazy initialization
        self.confidence_thresholds = CONFIDENCE_THRESHOLDS["intent_recognition"]

    def _get_model(self):
        """Get AI model with lazy initialization"""
        if self.model is None:
            self.model = self.ai_config.get_model()
        return self.model
    
    async def recognize_intent(self, request: IntentRecognitionRequest) -> IntentRecognitionResponse:
        """Recognize intent from user command"""
        start_time = time.time()
        
        try:
            # Preprocess command
            processed_command = self._preprocess_command(request.command)
            
            # Build context string
            context_str = self._build_context_string(request.context)
            
            # Create prompt
            prompt = INTENT_RECOGNITION_PROMPT.format(
                command=processed_command,
                context=context_str
            )
            
            # Get AI response
            ai_response = await self._get_ai_response(prompt)
            
            # Parse AI response
            parsed_response = self._parse_ai_response(ai_response, request.command)
            
            # Calculate processing time
            processing_time = int((time.time() - start_time) * 1000)
            
            # Build response
            response = IntentRecognitionResponse(
                intent=parsed_response["intent"],
                entities=parsed_response["entities"],
                suggestions=parsed_response.get("suggestions"),
                fallback=parsed_response.get("fallback"),
                processing_time_ms=processing_time
            )
            
            return response
            
        except Exception as e:
            # Return fallback response on error
            processing_time = int((time.time() - start_time) * 1000)
            return self._create_fallback_response(
                request.command,
                f"Error processing command: {str(e)}",
                processing_time
            )

    def _validate_and_enhance_intent(self, intent_name: str, command: str, confidence: float) -> str:
        """Validate and enhance intent recognition using rule-based patterns"""
        command_lower = command.lower()

        # Rule-based validation for core MVP intents
        if intent_name == "UNCLEAR" or confidence < 0.7:
            # Check for CREATE_INVOICE patterns
            if any(keyword in command_lower for keyword in ["invoice", "bill", "charge", "create invoice"]):
                if any(keyword in command_lower for keyword in ["for", "to", "client", "customer"]):
                    return "CREATE_INVOICE"

            # Check for SHOW_SUMMARY patterns
            if any(keyword in command_lower for keyword in ["summary", "profit", "balance", "total", "overview"]):
                if any(keyword in command_lower for keyword in ["show", "display", "view", "what", "how much"]):
                    return "SHOW_SUMMARY"

            # Check for CATEGORIZE_TRANSACTION patterns
            if any(keyword in command_lower for keyword in ["categorize", "category", "classify", "tag"]):
                if any(keyword in command_lower for keyword in ["transaction", "expense", "payment", "this"]):
                    return "CATEGORIZE_TRANSACTION"

        return intent_name

    def _adjust_confidence_for_core_intents(self, intent_name: str, command: str, confidence: float) -> float:
        """Adjust confidence scores for core MVP intents based on keyword matching"""
        command_lower = command.lower()

        if intent_name == "CREATE_INVOICE":
            # High confidence keywords for invoice creation
            high_conf_keywords = ["create invoice", "bill client", "invoice for", "charge customer"]
            if any(keyword in command_lower for keyword in high_conf_keywords):
                return min(0.95, confidence + 0.1)

        elif intent_name == "SHOW_SUMMARY":
            # High confidence keywords for summary/balance
            high_conf_keywords = ["show balance", "my profit", "financial summary", "total income"]
            if any(keyword in command_lower for keyword in high_conf_keywords):
                return min(0.95, confidence + 0.1)

        elif intent_name == "CATEGORIZE_TRANSACTION":
            # High confidence keywords for categorization
            high_conf_keywords = ["categorize this", "what category", "classify expense", "tag transaction"]
            if any(keyword in command_lower for keyword in high_conf_keywords):
                return min(0.95, confidence + 0.1)

        return confidence
    
    def _preprocess_command(self, command: str) -> str:
        """Preprocess user command for better recognition"""
        # Convert to lowercase for processing
        processed = command.lower().strip()
        
        # Normalize Zambian business terms
        for term, normalized in ZAMBIAN_BUSINESS_TERMS.items():
            processed = processed.replace(term, normalized)
        
        # Normalize currency mentions
        processed = re.sub(r'\bzmw\b', 'kwacha', processed)
        processed = re.sub(r'\bk(\d)', r'kwacha \1', processed)
        
        return processed
    
    def _build_context_string(self, context: Optional[Dict[str, Any]]) -> str:
        """Build context string from request context"""
        if not context:
            return "No additional context provided."
        
        context_parts = []
        
        if "previous_commands" in context:
            prev_commands = context["previous_commands"][-3:]  # Last 3 commands
            if prev_commands:
                context_parts.append(f"Previous commands: {', '.join(prev_commands)}")
        
        if "user_preferences" in context:
            prefs = context["user_preferences"]
            if prefs:
                context_parts.append(f"User preferences: {json.dumps(prefs)}")
        
        return " | ".join(context_parts) if context_parts else "No additional context provided."
    
    async def _get_ai_response(self, prompt: str) -> str:
        """Get response from AI model"""
        try:
            # Get model with lazy initialization
            model = self._get_model()
            # For now, use synchronous call as async might not be available
            response = model.invoke(prompt)
            
            # Extract content from response
            if hasattr(response, 'content'):
                return response.content
            else:
                return str(response)
                
        except Exception as e:
            raise Exception(f"AI model error: {str(e)}")
    
    def _parse_ai_response(self, ai_response: str, original_command: str) -> Dict[str, Any]:
        """Parse AI response into structured format with enhanced validation"""
        try:
            # Try to extract JSON from response
            json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
            if json_match:
                response_data = json.loads(json_match.group())
            else:
                raise ValueError("No JSON found in AI response")

            # Validate and structure the response
            intent_name = response_data.get("intent", "UNCLEAR")
            confidence = float(response_data.get("confidence", 0.0))
            description = response_data.get("description", "Intent not clearly identified")

            # Enhanced validation for core MVP intents
            intent_name = self._validate_and_enhance_intent(intent_name, original_command, confidence)

            # Adjust confidence based on intent validation
            if intent_name in ["CREATE_INVOICE", "SHOW_SUMMARY", "CATEGORIZE_TRANSACTION"]:
                # Boost confidence for core MVP intents if they're well-matched
                confidence = self._adjust_confidence_for_core_intents(intent_name, original_command, confidence)

            # Create intent object
            intent = Intent(
                name=IntentType(intent_name) if intent_name in IntentType.__members__ else IntentType.UNCLEAR,
                confidence=confidence,
                description=description
            )
            
            # Parse entities
            entities = self._parse_entities(response_data.get("entities", {}), original_command)
            
            # Handle clarification if needed
            fallback = None
            suggestions = response_data.get("suggestions", [])
            
            if (confidence < self.confidence_thresholds["low"] or 
                response_data.get("needs_clarification", False)):
                fallback = self._create_clarification_fallback(
                    original_command,
                    response_data.get("clarification_questions", [])
                )
            
            return {
                "intent": intent,
                "entities": entities,
                "suggestions": suggestions,
                "fallback": fallback
            }
            
        except Exception as e:
            # Return unclear intent on parsing error
            return {
                "intent": Intent(
                    name=IntentType.UNCLEAR,
                    confidence=0.1,
                    description=f"Failed to parse AI response: {str(e)}"
                ),
                "entities": {},
                "suggestions": ["Could you please rephrase your request?"],
                "fallback": Fallback(
                    type=FallbackType.ERROR,
                    message="I'm having trouble understanding your request. Could you please try again?",
                    options=["Show me recent transactions", "Create a new invoice", "Help me with something else"]
                )
            }
    
    def _parse_entities(self, entities_data: Dict[str, Any], original_command: str) -> Dict[EntityType, List[Entity]]:
        """Parse entities from AI response"""
        parsed_entities = {}
        
        for entity_type_str, entity_list in entities_data.items():
            try:
                # Map string to EntityType enum
                if entity_type_str in EntityType.__members__:
                    entity_type = EntityType(entity_type_str)
                else:
                    entity_type = EntityType.OTHER
                
                # Parse entity list
                if isinstance(entity_list, list) and entity_list:
                    entities = []
                    for entity_data in entity_list:
                        if isinstance(entity_data, dict):
                            entity = Entity(
                                value=entity_data.get("value"),
                                confidence=float(entity_data.get("confidence", 0.5)),
                                start_index=int(entity_data.get("start_index", 0)),
                                end_index=int(entity_data.get("end_index", len(original_command))),
                                entity_type=entity_type
                            )
                            entities.append(entity)
                    
                    if entities:
                        parsed_entities[entity_type] = entities
                        
            except Exception as e:
                # Skip invalid entities
                continue
        
        return parsed_entities
    
    def _create_clarification_fallback(self, command: str, questions: List[str]) -> Fallback:
        """Create clarification fallback response"""
        if not questions:
            questions = [
                "Could you please be more specific about what you'd like to do?",
                "Are you looking to view data, create something, or get help with a task?"
            ]
        
        return Fallback(
            type=FallbackType.CLARIFICATION,
            message="I need a bit more information to help you properly.",
            options=questions[:3]  # Limit to 3 options
        )
    
    def _create_fallback_response(self, command: str, error_message: str, processing_time: int) -> IntentRecognitionResponse:
        """Create fallback response for errors"""
        return IntentRecognitionResponse(
            intent=Intent(
                name=IntentType.UNCLEAR,
                confidence=0.0,
                description="Unable to process command due to error"
            ),
            entities={},
            suggestions=[
                "Show me recent transactions",
                "Create a new invoice", 
                "Help me understand what you can do"
            ],
            fallback=Fallback(
                type=FallbackType.ERROR,
                message="I'm having trouble processing your request right now. Please try again.",
                options=[
                    "Show me recent transactions",
                    "Create a new invoice",
                    "Get help"
                ]
            ),
            processing_time_ms=processing_time
        )
    
    def get_supported_intents(self) -> List[Dict[str, str]]:
        """Get list of supported intents with descriptions"""
        return [
            {
                "intent": "VIEW_TRANSACTIONS",
                "description": "View transaction history and financial data",
                "examples": ["show me recent transactions", "what did I spend last week"]
            },
            {
                "intent": "CREATE_INVOICE", 
                "description": "Create invoices for clients",
                "examples": ["create invoice for John", "bill client for services"]
            },
            {
                "intent": "VIEW_FINANCIAL_SUMMARY",
                "description": "View profit/loss and financial summaries", 
                "examples": ["show my profit", "how much did I make this month"]
            },
            {
                "intent": "CATEGORIZE_TRANSACTION",
                "description": "Categorize transactions and expenses",
                "examples": ["categorize this expense", "what category is this"]
            },
            {
                "intent": "CONNECT_ACCOUNT",
                "description": "Connect financial accounts and services",
                "examples": ["connect my MTN account", "add bank account"]
            },
            {
                "intent": "GET_HELP",
                "description": "Get help and assistance",
                "examples": ["help me", "what can you do", "how does this work"]
            }
        ]
