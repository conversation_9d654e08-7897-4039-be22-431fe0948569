/**
 * Journal Toast Service
 * Handles journal-specific toast notifications following Progressive Disclosure pattern
 */

import { BookOpen, AlertCircle, CheckCircle, FileEdit } from 'lucide-react';
import { ROUTES } from '@/utils/constants';

export interface JournalToastNotification {
  id: string;
  type: 'success' | 'error' | 'info' | 'warning';
  title: string;
  message: string;
  icon?: React.ComponentType<{ className?: string }>;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

class JournalToastService {
  private toastCallbacks: ((toast: Omit<JournalToastNotification, 'id'>) => void)[] = [];

  /**
   * Register a callback to handle toast notifications
   */
  onToast(callback: (toast: Omit<JournalToastNotification, 'id'>) => void) {
    this.toastCallbacks.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.toastCallbacks.indexOf(callback);
      if (index > -1) {
        this.toastCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Emit a toast notification
   */
  private emit(toast: Omit<JournalToastNotification, 'id'>) {
    this.toastCallbacks.forEach(callback => callback(toast));
  }

  /**
   * Show notification for unbalanced journal entry detected
   * Progressive Disclosure: Direct action to fix the issue
   */
  showUnbalancedEntryDetected(entryId: string, entryNumber: string) {
    this.emit({
      type: 'warning',
      title: 'Unbalanced Entry Detected',
      message: `Journal Entry ${entryNumber} has unbalanced debits and credits`,
      icon: AlertCircle,
      duration: 8000,
      action: {
        label: 'Fix Now',
        onClick: () => {
          window.location.href = `${ROUTES.JOURNAL_ENTRIES}?status=unbalanced&entry=${entryId}`;
        }
      }
    });
  }

  /**
   * Show notification for draft entries requiring attention
   * Progressive Disclosure: Direct action to review drafts
   */
  showDraftEntriesReminder(count: number) {
    this.emit({
      type: 'info',
      title: 'Draft Entries Pending',
      message: `You have ${count} draft journal ${count === 1 ? 'entry' : 'entries'} waiting to be posted`,
      icon: FileEdit,
      duration: 6000,
      action: {
        label: 'Review Drafts',
        onClick: () => {
          window.location.href = `${ROUTES.JOURNAL_ENTRIES}?status=draft`;
        }
      }
    });
  }

  /**
   * Show notification for successful journal entry posting
   */
  showEntryPosted(entryNumber: string) {
    this.emit({
      type: 'success',
      title: 'Entry Posted Successfully',
      message: `Journal Entry ${entryNumber} has been posted to the ledger`,
      icon: CheckCircle,
      duration: 4000,
      action: {
        label: 'View Journal',
        onClick: () => {
          window.location.href = ROUTES.JOURNAL_ENTRIES;
        }
      }
    });
  }

  /**
   * Show notification for journal entry creation from transaction
   */
  showEntryCreatedFromTransaction(entryNumber: string, transactionDescription: string) {
    this.emit({
      type: 'success',
      title: 'Journal Entry Created',
      message: `Entry ${entryNumber} created for: ${transactionDescription}`,
      icon: BookOpen,
      duration: 5000,
      action: {
        label: 'View Entry',
        onClick: () => {
          window.location.href = ROUTES.JOURNAL_ENTRIES;
        }
      }
    });
  }

  /**
   * Show notification for journal entry reversal
   */
  showEntryReversed(originalEntryNumber: string, reversalEntryNumber: string) {
    this.emit({
      type: 'info',
      title: 'Entry Reversed',
      message: `Entry ${originalEntryNumber} reversed with entry ${reversalEntryNumber}`,
      icon: AlertCircle,
      duration: 5000,
      action: {
        label: 'View Journal',
        onClick: () => {
          window.location.href = ROUTES.JOURNAL_ENTRIES;
        }
      }
    });
  }

  /**
   * Show notification for journal entry validation errors
   */
  showValidationError(entryNumber: string, errors: string[]) {
    this.emit({
      type: 'error',
      title: 'Journal Entry Validation Failed',
      message: `Entry ${entryNumber}: ${errors.join(', ')}`,
      icon: AlertCircle,
      duration: 8000,
      action: {
        label: 'Fix Issues',
        onClick: () => {
          window.location.href = `${ROUTES.JOURNAL_ENTRIES}?status=unbalanced`;
        }
      }
    });
  }

  /**
   * Show notification for account balance warnings
   */
  showAccountBalanceWarning(accountName: string, balance: number) {
    this.emit({
      type: 'warning',
      title: 'Account Balance Alert',
      message: `${accountName} balance is ${balance < 0 ? 'negative' : 'unusually high'}: ${Math.abs(balance).toLocaleString('en-ZM', { style: 'currency', currency: 'ZMW' })}`,
      icon: AlertCircle,
      duration: 6000,
      action: {
        label: 'Review Journal',
        onClick: () => {
          window.location.href = ROUTES.JOURNAL_ENTRIES;
        }
      }
    });
  }

  /**
   * Show notification for end-of-period journal entry reminders
   */
  showPeriodEndReminder(periodName: string, draftCount: number) {
    this.emit({
      type: 'info',
      title: 'Period End Reminder',
      message: `${periodName} is ending soon. You have ${draftCount} draft entries to review`,
      icon: BookOpen,
      duration: 10000,
      action: {
        label: 'Review Entries',
        onClick: () => {
          window.location.href = `${ROUTES.JOURNAL_ENTRIES}?status=draft`;
        }
      }
    });
  }
}

// Export singleton instance
export const journalToastService = new JournalToastService();
