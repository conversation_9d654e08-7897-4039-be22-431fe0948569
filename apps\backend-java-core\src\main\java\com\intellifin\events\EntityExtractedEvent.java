package com.intellifin.events;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

public class EntityExtractedEvent {
    private String eventId;
    private String userId;
    private String sessionId;
    private String command;
    private Map<String, Object> entities;
    private LocalDateTime timestamp;

    public EntityExtractedEvent(String userId, String sessionId, String command, Map<String, Object> entities) {
        this.eventId = UUID.randomUUID().toString();
        this.userId = userId;
        this.sessionId = sessionId;
        this.command = command;
        this.entities = entities;
        this.timestamp = LocalDateTime.now();
    }

    // Getters
    public String getEventId() { return eventId; }
    public String getUserId() { return userId; }
    public String getSessionId() { return sessionId; }
    public String getCommand() { return command; }
    public Map<String, Object> getEntities() { return entities; }
    public LocalDateTime getTimestamp() { return timestamp; }
}
