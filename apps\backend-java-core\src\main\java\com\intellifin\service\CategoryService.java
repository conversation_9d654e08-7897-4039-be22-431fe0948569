package com.intellifin.service;

import com.intellifin.model.Account;
import com.intellifin.model.Category;
import com.intellifin.model.User;
import com.intellifin.repository.AccountRepository;
import com.intellifin.repository.CategoryRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional
public class CategoryService {

    private final CategoryRepository categoryRepository;
    private final AccountRepository accountRepository;
    private final AuthService authService;

    public List<Category> getCategoriesForCurrentUser() {
        User currentUser = authService.getCurrentUser();
        return categoryRepository.findByUser(currentUser);
    }

    public Optional<Category> getCategoryById(UUID id) {
        return categoryRepository.findById(id);
    }

    public Category createCategory(Category category) {
        User currentUser = authService.getCurrentUser();
        category.setUser(currentUser);
        //TODO: Add validation logic
        return categoryRepository.save(category);
    }

    public Category updateCategory(UUID id, Category categoryDetails) {
        Category category = categoryRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Category not found with id: " + id));

        category.setName(categoryDetails.getName());
        category.setDescription(categoryDetails.getDescription());
        category.setActive(categoryDetails.getIsActive());
        category.setColor(categoryDetails.getColor());
        category.setIcon(categoryDetails.getIcon());
        // Note: Mapping to account is handled separately

        return categoryRepository.save(category);
    }

    public void deleteCategory(UUID id) {
        categoryRepository.deleteById(id);
    }

    public Category mapCategoryToAccount(UUID categoryId, UUID accountId) {
        Category category = categoryRepository.findById(categoryId)
                .orElseThrow(() -> new RuntimeException("Category not found with id: " + categoryId));
        Account account = accountRepository.findById(accountId)
                .orElseThrow(() -> new RuntimeException("Account not found with id: " + accountId));

        // Validation: Ensure category type matches account type (e.g., INCOME category -> INCOME account)
        if (!category.getType().toString().equals(account.getType().toString())) {
            throw new IllegalStateException("Category type '" + category.getType() + "' does not match account type '" + account.getType() + "'");
        }

        category.setAccount(account);
        return categoryRepository.save(category);
    }
}
