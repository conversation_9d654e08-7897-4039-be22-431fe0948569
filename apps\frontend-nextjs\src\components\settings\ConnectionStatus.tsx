'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MTNConnectionStatus } from '@/hooks/useAccountConnection';
import { 
  RefreshCw, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Smartphone,
  Calendar
} from 'lucide-react';

interface ConnectionStatusProps {
  account: MTNConnectionStatus;
  onRefresh?: () => void;
}

export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({ 
  account, 
  onRefresh 
}) => {
  const formatLastSync = (lastSync?: string) => {
    if (!lastSync) return 'Never';
    
    const date = new Date(lastSync);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minutes ago`;
    if (diffHours < 24) return `${diffHours} hours ago`;
    if (diffDays === 1) return 'Yesterday';
    return `${diffDays} days ago`;
  };

  const getStatusDetails = () => {
    switch (account.status) {
      case 'CONNECTED':
        return {
          icon: <CheckCircle className="h-4 w-4 text-green-500" />,
          color: 'bg-green-100 text-green-800',
          message: 'Connected and syncing',
          showRefresh: true
        };
      case 'CONNECTING':
        return {
          icon: <Clock className="h-4 w-4 text-yellow-500" />,
          color: 'bg-yellow-100 text-yellow-800',
          message: 'Connecting...',
          showRefresh: false
        };
      case 'ERROR':
        return {
          icon: <AlertTriangle className="h-4 w-4 text-red-500" />,
          color: 'bg-red-100 text-red-800',
          message: account.errorMessage || 'Connection error',
          showRefresh: true
        };
      default:
        return {
          icon: <Smartphone className="h-4 w-4 text-gray-500" />,
          color: 'bg-gray-100 text-gray-800',
          message: 'Disconnected',
          showRefresh: false
        };
    }
  };

  const statusDetails = getStatusDetails();

  return (
    <div className="flex items-center space-x-3">
      {/* Status Badge */}
      <div className="flex items-center space-x-2">
        {statusDetails.icon}
        <Badge className={statusDetails.color}>
          {account.status}
        </Badge>
      </div>

      {/* Account Info */}
      {account.accountInfo && (
        <div className="text-sm text-gray-600">
          {account.accountInfo.balance !== undefined && (
            <span className="font-medium">
              {account.accountInfo.currency || 'ZMW'} {account.accountInfo.balance.toFixed(2)}
            </span>
          )}
        </div>
      )}

      {/* Last Sync */}
      {account.status === 'CONNECTED' && (
        <div className="flex items-center space-x-1 text-xs text-gray-500">
          <Calendar className="h-3 w-3" />
          <span>Last sync: {formatLastSync(account.lastSync)}</span>
        </div>
      )}

      {/* Refresh Button */}
      {statusDetails.showRefresh && onRefresh && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onRefresh}
          className="h-8 w-8 p-0"
        >
          <RefreshCw className="h-4 w-4" />
        </Button>
      )}

      {/* Error Message */}
      {account.status === 'ERROR' && account.errorMessage && (
        <div className="text-xs text-red-600 max-w-xs truncate" title={account.errorMessage}>
          {account.errorMessage}
        </div>
      )}
    </div>
  );
};
