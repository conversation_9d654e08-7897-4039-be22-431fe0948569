package com.intellifin.service;

import com.intellifin.model.DocumentUpload;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import java.time.OffsetDateTime;
import java.util.Map;

/**
 * Service for handling document processing messaging events
 * Uses Spring Cloud Stream for messaging abstraction
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DocumentProcessingMessagingService {

    private final StreamBridge streamBridge;

    /**
     * Publish document upload started event
     */
    public void publishDocumentUploadStarted(DocumentUpload upload) {
        try {
            log.info("Publishing document upload started event for upload: {}", upload.getUploadId());

            Map<String, Object> eventData = Map.of(
                "uploadId", upload.getUploadId(),
                "userId", upload.getUser().getId().toString(),
                "fileName", upload.getFileName(),
                "fileType", upload.getFileType().toString(),
                "fileSizeBytes", upload.getFileSizeBytes(),
                "timestamp", OffsetDateTime.now().toString()
            );

            Message<Map<String, Object>> message = MessageBuilder
                    .withPayload(eventData)
                    .setHeader("eventType", "DocumentUploadStarted")
                    .setHeader("uploadId", upload.getUploadId())
                    .setHeader("userId", upload.getUser().getId().toString())
                    .build();

            boolean sent = streamBridge.send("documentUploadStarted-out-0", message);

            if (sent) {
                log.info("Successfully published document upload started event for upload: {}", upload.getUploadId());
            } else {
                log.error("Failed to publish document upload started event for upload: {}", upload.getUploadId());
            }

        } catch (Exception e) {
            log.error("Error publishing document upload started event for upload: {}", upload.getUploadId(), e);
        }
    }

    /**
     * Publish document processing requested event
     */
    public void publishDocumentProcessingRequested(DocumentUpload upload) {
        try {
            log.info("Publishing document processing requested event for upload: {}", upload.getUploadId());

            Map<String, Object> eventData = Map.of(
                "uploadId", upload.getUploadId(),
                "userId", upload.getUser().getId().toString(),
                "fileName", upload.getFileName(),
                "fileType", upload.getFileType().toString(),
                "filePath", upload.getFilePath() != null ? upload.getFilePath() : "",
                "accountType", upload.getFinancialAccount() != null ? 
                    upload.getFinancialAccount().getAccountType().toString() : "UNKNOWN",
                "timestamp", OffsetDateTime.now().toString()
            );

            Message<Map<String, Object>> message = MessageBuilder
                    .withPayload(eventData)
                    .setHeader("eventType", "DocumentProcessingRequested")
                    .setHeader("uploadId", upload.getUploadId())
                    .setHeader("userId", upload.getUser().getId().toString())
                    .setHeader("fileType", upload.getFileType().toString())
                    .build();

            boolean sent = streamBridge.send("documentProcessingRequested-out-0", message);

            if (sent) {
                log.info("Successfully published document processing requested event for upload: {}", upload.getUploadId());
            } else {
                log.error("Failed to publish document processing requested event for upload: {}", upload.getUploadId());
            }

        } catch (Exception e) {
            log.error("Error publishing document processing requested event for upload: {}", upload.getUploadId(), e);
        }
    }

    /**
     * Publish document parsing completed event
     */
    public void publishDocumentParsingCompleted(String uploadId, int extractedTransactions, double confidence, long processingTimeMs) {
        try {
            log.info("Publishing document parsing completed event for upload: {}", uploadId);

            Map<String, Object> eventData = Map.of(
                "uploadId", uploadId,
                "extractedTransactions", extractedTransactions,
                "confidence", confidence,
                "processingTimeMs", processingTimeMs,
                "timestamp", OffsetDateTime.now().toString()
            );

            Message<Map<String, Object>> message = MessageBuilder
                    .withPayload(eventData)
                    .setHeader("eventType", "DocumentParsingCompleted")
                    .setHeader("uploadId", uploadId)
                    .build();

            boolean sent = streamBridge.send("documentParsingCompleted-out-0", message);

            if (sent) {
                log.info("Successfully published document parsing completed event for upload: {}", uploadId);
            } else {
                log.error("Failed to publish document parsing completed event for upload: {}", uploadId);
            }

        } catch (Exception e) {
            log.error("Error publishing document parsing completed event for upload: {}", uploadId, e);
        }
    }

    /**
     * Publish document processing failed event
     */
    public void publishDocumentProcessingFailed(String uploadId, String error, int retryCount) {
        try {
            log.info("Publishing document processing failed event for upload: {}", uploadId);

            Map<String, Object> eventData = Map.of(
                "uploadId", uploadId,
                "error", error,
                "retryCount", retryCount,
                "timestamp", OffsetDateTime.now().toString()
            );

            Message<Map<String, Object>> message = MessageBuilder
                    .withPayload(eventData)
                    .setHeader("eventType", "DocumentProcessingFailed")
                    .setHeader("uploadId", uploadId)
                    .setHeader("error", error)
                    .build();

            boolean sent = streamBridge.send("documentProcessingFailed-out-0", message);

            if (sent) {
                log.info("Successfully published document processing failed event for upload: {}", uploadId);
            } else {
                log.error("Failed to publish document processing failed event for upload: {}", uploadId);
            }

        } catch (Exception e) {
            log.error("Error publishing document processing failed event for upload: {}", uploadId, e);
        }
    }

    /**
     * Publish document processing progress event
     */
    public void publishDocumentProcessingProgress(String uploadId, int progressPercentage, String stage) {
        try {
            log.debug("Publishing document processing progress event for upload: {} - {}%", uploadId, progressPercentage);

            Map<String, Object> eventData = Map.of(
                "uploadId", uploadId,
                "progressPercentage", progressPercentage,
                "stage", stage,
                "timestamp", OffsetDateTime.now().toString()
            );

            Message<Map<String, Object>> message = MessageBuilder
                    .withPayload(eventData)
                    .setHeader("eventType", "DocumentProcessingProgress")
                    .setHeader("uploadId", uploadId)
                    .setHeader("progressPercentage", progressPercentage)
                    .build();

            boolean sent = streamBridge.send("documentProcessingProgress-out-0", message);

            if (!sent) {
                log.warn("Failed to publish document processing progress event for upload: {}", uploadId);
            }

        } catch (Exception e) {
            log.error("Error publishing document processing progress event for upload: {}", uploadId, e);
        }
    }
}
