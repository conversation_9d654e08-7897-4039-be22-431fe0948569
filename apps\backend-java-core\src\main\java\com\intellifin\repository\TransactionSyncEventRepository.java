package com.intellifin.repository;

import com.intellifin.model.TransactionSyncEvent;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Repository for TransactionSyncEvent entities
 */
@Repository
public interface TransactionSyncEventRepository extends JpaRepository<TransactionSyncEvent, UUID> {

    /**
     * Find sync events by sync status ID
     */
    Page<TransactionSyncEvent> findBySyncStatusId(UUID syncStatusId, Pageable pageable);

    /**
     * Find sync events by webhook event ID
     */
    List<TransactionSyncEvent> findByWebhookEventId(UUID webhookEventId);

    /**
     * Find sync events by transaction ID
     */
    List<TransactionSyncEvent> findByTransactionId(UUID transactionId);

    /**
     * Find sync events by external transaction ID
     */
    List<TransactionSyncEvent> findByExternalTransactionId(String externalTransactionId);

    /**
     * Find sync events by operation status
     */
    List<TransactionSyncEvent> findByOperationStatus(TransactionSyncEvent.OperationStatus status);

    /**
     * Find failed sync events that can be retried
     */
    @Query("SELECT e FROM TransactionSyncEvent e WHERE e.operationStatus = 'FAILED' AND e.retryCount < 3")
    List<TransactionSyncEvent> findRetryableFailedEvents();

    /**
     * Find sync events by sync operation and status
     */
    List<TransactionSyncEvent> findBySyncOperationAndOperationStatus(
            TransactionSyncEvent.SyncOperation operation, 
            TransactionSyncEvent.OperationStatus status);

    /**
     * Find sync events created within a time range
     */
    List<TransactionSyncEvent> findByCreatedAtBetween(OffsetDateTime startTime, OffsetDateTime endTime);

    /**
     * Count sync events by status for a sync status
     */
    @Query("SELECT COUNT(e) FROM TransactionSyncEvent e WHERE e.syncStatus.id = :syncStatusId AND e.operationStatus = :status")
    Long countBySyncStatusIdAndStatus(@Param("syncStatusId") UUID syncStatusId, @Param("status") TransactionSyncEvent.OperationStatus status);

    /**
     * Find recent sync events for a financial account
     */
    @Query("SELECT e FROM TransactionSyncEvent e WHERE e.syncStatus.financialAccount.id = :accountId " +
           "AND e.createdAt >= :since ORDER BY e.createdAt DESC")
    List<TransactionSyncEvent> findRecentEventsByAccountId(@Param("accountId") UUID accountId, @Param("since") OffsetDateTime since);

    /**
     * Find sync events with processing time above threshold
     */
    @Query("SELECT e FROM TransactionSyncEvent e WHERE e.processingTimeMs > :thresholdMs")
    List<TransactionSyncEvent> findSlowProcessingEvents(@Param("thresholdMs") Long thresholdMs);

    /**
     * Delete old successful sync events
     */
    @Query("DELETE FROM TransactionSyncEvent e WHERE e.operationStatus = 'SUCCESS' AND e.createdAt < :cutoffDate")
    void deleteOldSuccessfulEvents(@Param("cutoffDate") OffsetDateTime cutoffDate);
}
