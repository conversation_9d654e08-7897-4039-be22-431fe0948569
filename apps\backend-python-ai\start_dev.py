#!/usr/bin/env python3
"""
Development startup script for IntelliFin AI Service
"""

import os
import sys
import subprocess
import asyncio
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import fastapi
        import uvicorn
        import pydantic
        import langchain
        print("✅ Core dependencies found")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Please run: poetry install")
        return False

def check_environment():
    """Check environment configuration"""
    print("🔍 Checking environment configuration...")
    
    # Check for Ollama (local development)
    try:
        import requests
        response = requests.get("http://localhost:11434/api/version", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama is running locally")
        else:
            print("⚠️  Ollama is not responding properly")
    except Exception:
        print("⚠️  Ollama is not running (will use mock responses)")
    
    # Check for Google API key (production)
    google_api_key = os.getenv("GOOGLE_API_KEY")
    if google_api_key:
        print("✅ Google API key configured")
    else:
        print("ℹ️  Google API key not configured (using local model)")
    
    return True

async def test_services():
    """Test AI services"""
    print("\n🧠 Testing AI services...")
    
    try:
        from src.services.intent_recognition import IntentRecognitionService
        from src.services.entity_extraction import EntityExtractionService
        from src.models.schemas import IntentRecognitionRequest
        
        # Test intent recognition
        intent_service = IntentRecognitionService()
        entity_service = EntityExtractionService()
        
        # Simple test
        request = IntentRecognitionRequest(
            command="Show me recent transactions",
            user_id="test_user",
            session_id="test_session"
        )
        
        print("Testing intent recognition...")
        response = await intent_service.recognize_intent(request)
        print(f"✅ Intent: {response.intent.name.value} (confidence: {response.intent.confidence:.2f})")
        
        # Test entity extraction
        print("Testing entity extraction...")
        entities = entity_service.extract_entities("I paid K2500 to ZESCO yesterday")
        if entities:
            print(f"✅ Found {len(entities)} entity types")
        else:
            print("ℹ️  No entities extracted (this is normal for simple tests)")
        
        return True
        
    except Exception as e:
        print(f"❌ Service test failed: {e}")
        return False

def start_server():
    """Start the FastAPI server"""
    print("\n🚀 Starting IntelliFin AI Service...")
    print("Server will be available at: http://localhost:8002")
    print("API documentation: http://localhost:8002/docs")
    print("Press Ctrl+C to stop\n")
    
    # Start uvicorn server
    subprocess.run([
        sys.executable, "-m", "uvicorn",
        "src.main:app",
        "--host", "0.0.0.0",
        "--port", "8002",
        "--reload",
        "--log-level", "info"
    ])

async def main():
    """Main startup function"""
    print("🎯 IntelliFin AI Service - Development Startup")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        return
    
    # Check environment
    if not check_environment():
        return
    
    # Test services
    if not await test_services():
        print("⚠️  Service tests failed, but starting server anyway...")
    
    # Start server
    start_server()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Shutting down IntelliFin AI Service...")
    except Exception as e:
        print(f"❌ Startup failed: {e}")
        sys.exit(1)
