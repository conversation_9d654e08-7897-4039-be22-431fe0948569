export interface Account {
  id: string;
  code: string; // e.g., "1100", "2000"
  name: string; // e.g., "Cash and Cash Equivalents"
  description?: string;
  type: 'ASSET' | 'LIABILITY' | 'EQUITY' | 'INCOME' | 'EXPENSE';
  subType: string; // e.g., "CURRENT_ASSET", "FIXED_ASSET"
  parentAccountId?: string;
  isSystemDefined: boolean;
  isActive: boolean;
  normalBalance: 'DEBIT' | 'CREDIT';
  currentBalance: number;
  userId?: string; // null for system accounts
  createdAt: string;
  updatedAt: string;
}

export interface Category {
  id: string;
  userId?: string;
  name: string;
  description?: string;
  type: 'INCOME' | 'EXPENSE';
  accountId: string; // Required mapping to Account
  isSystemDefined: boolean;
  isActive: boolean;
  color: string;
  icon: string;
  createdAt: string;
  updatedAt: string;
  account?: Account; // Populated in responses
}

export interface AccountHierarchy {
  account: Account;
  children: AccountHierarchy[];
  totalBalance: number;
}

export interface JournalEntry {
  id: string;
  entryNumber: string;
  userId: string;
  transactionId?: string;
  description: string;
  totalAmount: number;
  status: 'DRAFT' | 'POSTED' | 'REVERSED';
  entryDate: string;
  referenceNumber?: string;
  sourceType?: string; // e.g., "TRANSACTION", "MANUAL", "INVOICE", "ADJUSTMENT"
  sourceId?: string;
  lines: JournalEntryLine[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  postedAt?: string;
  postedBy?: string;
  reversedAt?: string;
  reversedBy?: string;
  reversalReason?: string;
  reversalEntryId?: string;
}

export interface JournalEntryLine {
  id: string;
  journalEntryId: string;
  accountId: string;
  debitAmount: number;
  creditAmount: number;
  description: string;
  referenceNumber?: string;
  lineNumber?: number;
  createdAt: string;
  updatedAt: string;
  account?: Account; // Populated in responses
}

export interface JournalEntryRequest {
  description: string;
  entryDate: string;
  referenceNumber?: string;
  sourceType?: string;
  sourceId?: string;
  lines: JournalEntryLineRequest[];
}

export interface JournalEntryLineRequest {
  accountId: string;
  debitAmount?: number;
  creditAmount?: number;
  description: string;
  referenceNumber?: string;
}

export interface JournalEntryValidation {
  isValid: boolean;
  errors: string[];
  totalDebits: number;
  totalCredits: number;
  isBalanced: boolean;
}

// Journal Entry Validation Response from API
export interface JournalEntryValidationResponse {
  isValid: boolean;
  isBalanced: boolean;
  totalDebits: number;
  totalCredits: number;
  difference: number;
  message: string;
}

export interface AccountBalance {
  accountId: string;
  code: string;
  name: string;
  type: 'ASSET' | 'LIABILITY' | 'EQUITY' | 'INCOME' | 'EXPENSE';
  normalBalance: 'DEBIT' | 'CREDIT';
  currentBalance: number;
  transactionCount: number;
  lastTransactionDate?: string;
}

export interface JournalEntrySummary {
  id: string;
  entryNumber: string;
  description: string;
  totalAmount: number;
  status: 'DRAFT' | 'POSTED' | 'REVERSED';
  entryDate: string;
  createdAt: string;
  createdByEmail: string;
  lineCount: number;
  totalDebits: number;
  totalCredits: number;
  isBalanced: boolean;
}
