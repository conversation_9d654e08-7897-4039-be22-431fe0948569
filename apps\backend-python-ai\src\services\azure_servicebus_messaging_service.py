"""
Azure Service Bus implementation of the messaging service
"""
import asyncio
import json
import logging
from typing import Dict, Callable
from azure.servicebus.aio import ServiceBusClient, ServiceBusMessage
from azure.servicebus import ServiceBusReceiveMode
from .messaging_service import MessagingService, MessageEvent

logger = logging.getLogger(__name__)


class AzureServiceBusMessagingService(MessagingService):
    """Azure Service Bus implementation of messaging service"""
    
    def __init__(self, connection_string: str):
        self.connection_string = connection_string
        self.client = None
        self.senders = {}
        self.receivers = {}
        self.consumers = {}
        
        # Topic names for transaction categorization
        self.topics = {
            "transaction.categorization.requested": "transaction-categorization-requested",
            "transaction.categorized": "transaction-categorized",
            "categorization.accepted": "categorization-accepted",
            "categorization.rejected": "categorization-rejected",
            "bulk.categorization.completed": "bulk-categorization-completed"
        }
    
    async def start(self) -> None:
        """Start the Azure Service Bus connection"""
        try:
            self.client = ServiceBusClient.from_connection_string(
                self.connection_string,
                logging_enable=True
            )
            logger.info("Connected to Azure Service Bus")
            
        except Exception as e:
            logger.error(f"Failed to connect to Azure Service Bus: {str(e)}")
            raise
    
    async def stop(self) -> None:
        """Stop the Azure Service Bus connection"""
        try:
            # Close all receivers
            for receiver in self.receivers.values():
                await receiver.close()
            
            # Close all senders
            for sender in self.senders.values():
                await sender.close()
            
            # Close the client
            if self.client:
                await self.client.close()
                
            logger.info("Disconnected from Azure Service Bus")
            
        except Exception as e:
            logger.error(f"Error closing Azure Service Bus connection: {str(e)}")
    
    async def publish_event(self, topic: str, event: MessageEvent) -> bool:
        """Publish an event to the specified topic"""
        try:
            topic_name = self.topics.get(topic)
            if not topic_name:
                logger.error(f"Unknown topic: {topic}")
                return False
            
            # Get or create sender
            if topic_name not in self.senders:
                self.senders[topic_name] = self.client.get_topic_sender(topic_name)
            
            sender = self.senders[topic_name]
            
            message_body = json.dumps({
                "event_type": event.event_type,
                "correlation_id": event.correlation_id,
                "timestamp": event.timestamp,
                "payload": event.payload
            })
            
            message = ServiceBusMessage(
                body=message_body,
                content_type="application/json",
                correlation_id=event.correlation_id,
                subject=event.event_type,
                application_properties={
                    "event_type": event.event_type,
                    "timestamp": event.timestamp
                }
            )
            
            await sender.send_messages(message)
            logger.info(f"Published event {event.event_type} to {topic_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to publish event to {topic}: {str(e)}")
            await self._handle_publish_error(topic, event, e)
            return False
    
    async def subscribe(self, topic: str, handler: Callable[[MessageEvent], None]) -> None:
        """Subscribe to events on the specified topic"""
        try:
            topic_name = self.topics.get(topic)
            if not topic_name:
                logger.error(f"Unknown topic: {topic}")
                return
            
            subscription_name = f"{topic_name}-ai-subscription"
            
            # Get or create receiver
            if topic_name not in self.receivers:
                self.receivers[topic_name] = self.client.get_subscription_receiver(
                    topic_name=topic_name,
                    subscription_name=subscription_name,
                    receive_mode=ServiceBusReceiveMode.PEEK_LOCK,
                    max_wait_time=30
                )
            
            receiver = self.receivers[topic_name]
            self.consumers[topic] = handler
            
            # Start consuming messages
            asyncio.create_task(self._consume_messages(receiver, handler, topic))
            logger.info(f"Subscribed to topic: {topic_name}")
            
        except Exception as e:
            logger.error(f"Failed to subscribe to {topic}: {str(e)}")
            raise
    
    async def _consume_messages(self, receiver, handler: Callable[[MessageEvent], None], topic: str) -> None:
        """Consume messages from a receiver"""
        try:
            async with receiver:
                async for message in receiver:
                    try:
                        event_data = json.loads(str(message))
                        event = MessageEvent(
                            event_type=event_data["event_type"],
                            correlation_id=event_data["correlation_id"],
                            timestamp=event_data["timestamp"],
                            payload=event_data["payload"]
                        )
                        
                        await handler(event)
                        await receiver.complete_message(message)
                        logger.info(f"Successfully processed message: {event.correlation_id}")
                        
                    except json.JSONDecodeError as e:
                        logger.error(f"Failed to decode message: {str(e)}")
                        await receiver.dead_letter_message(
                            message, 
                            reason="InvalidMessageFormat",
                            error_description=str(e)
                        )
                        
                    except Exception as e:
                        logger.error(f"Error processing message: {str(e)}")
                        
                        # Check delivery count and dead letter if exceeded
                        if message.delivery_count >= 3:
                            await receiver.dead_letter_message(
                                message,
                                reason="MaxDeliveryCountExceeded",
                                error_description=str(e)
                            )
                            logger.warning(f"Message dead lettered after {message.delivery_count} attempts")
                        else:
                            # Abandon message for retry
                            await receiver.abandon_message(message)
                            logger.info(f"Message abandoned for retry (attempt {message.delivery_count})")
                            
        except Exception as e:
            logger.error(f"Error in message consumption loop for {topic}: {str(e)}")
    
    async def _handle_publish_error(self, topic: str, event: MessageEvent, error: Exception) -> None:
        """Handle publish errors by logging and potentially retrying"""
        try:
            error_info = {
                "topic": topic,
                "event_type": event.event_type,
                "correlation_id": event.correlation_id,
                "error": str(error),
                "timestamp": event.timestamp
            }
            
            logger.error(f"Publish error details: {json.dumps(error_info)}")
            
            # In a production environment, you might want to:
            # 1. Store failed messages in a database for manual retry
            # 2. Send alerts to monitoring systems
            # 3. Implement exponential backoff retry logic
            
        except Exception as log_error:
            logger.error(f"Failed to log publish error: {str(log_error)}")
    
    async def get_topic_stats(self, topic: str) -> Dict:
        """Get statistics for a specific topic (if available)"""
        try:
            topic_name = self.topics.get(topic)
            if not topic_name:
                return {}
            
            # Azure Service Bus doesn't provide easy programmatic access to topic stats
            # This would typically require Azure Management SDK
            return {
                "topic": topic_name,
                "status": "connected" if self.client else "disconnected"
            }
            
        except Exception as e:
            logger.error(f"Failed to get topic stats for {topic}: {str(e)}")
            return {}
    
    async def create_subscription_if_not_exists(self, topic_name: str, subscription_name: str) -> None:
        """Create subscription if it doesn't exist (requires management permissions)"""
        try:
            # This would require Azure Service Bus Management SDK
            # For now, we assume subscriptions are created via infrastructure
            logger.info(f"Subscription {subscription_name} for topic {topic_name} should be created via infrastructure")
            
        except Exception as e:
            logger.error(f"Failed to create subscription {subscription_name}: {str(e)}")
