// WebSocket Message Types
export interface WebSocketMessage {
  messageId?: string;
  sessionId?: string;
  conversationId?: string;
  content: string;
  type: 'COMMAND' | 'RESPONSE' | 'ERROR' | 'PROGRESS' | 'SYSTEM';
  role: 'USER' | 'ASSISTANT' | 'SYSTEM';
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  intentRecognized?: string;
  confidenceScore?: number;
  processingTimeMs?: number;
  errorMessage?: string;
  timestamp: string;
  isComplete: boolean;
  requiresFollowUp?: boolean;
  followUpPrompt?: string;
  data?: any; // Additional structured data (e.g., transaction list, invoice draft)
}

// Conversation Command Types
export interface ConversationCommand {
  command: string;
  sessionId?: string;
  expectResponse?: boolean;
  saveToHistory?: boolean;
  context?: Record<string, any>;
}

export interface TypingIndicator {
  typing: boolean;
  sessionId?: string;
}

// Intent Recognition Types (Enhanced for Story 2.2)
export interface Intent {
  name: string;
  confidence: number;
  description: string;
}

export interface Entity {
  value: string;
  confidence: number;
  startIndex: number;
  endIndex: number;
  entityType: string;
}

export interface RecognizedIntent {
  intent: string;
  confidence: number;
  entities: Record<string, any>;
  parameters: Record<string, any>;
}

// Enhanced Intent Recognition Result
export interface IntentRecognitionResult {
  intent: Intent;
  entities: Record<string, Entity[]>;
  suggestions?: string[];
  fallback?: {
    type: 'CLARIFICATION' | 'ALTERNATIVE' | 'ERROR';
    message: string;
    options?: string[];
  };
  processingTimeMs: number;
}

// Conversation Context Types
export interface ConversationContext {
  sessionId: string;
  conversationId?: string;
  userId: string;
  currentIntent?: string;
  awaitingInput?: boolean;
  lastActivity: string;
  metadata: Record<string, any>;
}

// Message Display Types
export interface DisplayMessage {
  id: string;
  content: string;
  role: 'USER' | 'ASSISTANT' | 'SYSTEM';
  type: 'COMMAND' | 'RESPONSE' | 'ERROR' | 'PROGRESS' | 'SYSTEM';
  timestamp: Date;
  isComplete: boolean;
  requiresFollowUp?: boolean;
  followUpPrompt?: string;
  intentRecognized?: string;
  confidenceScore?: number;
  intent?: Intent;
  entities?: Record<string, Entity[]>;
  data?: any;
  isLoading?: boolean;
  error?: string;
}

// Conversation State Types
export interface ConversationState {
  messages: DisplayMessage[];
  isConnected: boolean;
  isConnecting: boolean;
  connectionError: string | null;
  currentSessionId: string | null;
  isTyping: boolean;
  awaitingResponse: boolean;
  lastActivity: Date | null;
}

// Command Suggestions
export interface CommandSuggestion {
  text: string;
  description: string;
  category: 'TRANSACTION' | 'INVOICE' | 'REPORT' | 'ACCOUNT' | 'GENERAL';
  icon?: string;
}

// AI Response Data Types
export interface TransactionListResponse {
  transactions: Transaction[];
  summary: {
    total: number;
    totalIncome: number;
    totalExpenses: number;
    period: string;
  };
  suggestions: string[];
}

export interface InvoiceDraftResponse {
  invoice: Partial<Invoice>;
  client?: Client;
  validationErrors?: string[];
  suggestions: string[];
  nextSteps: string[];
}

export interface FinancialSummaryResponse {
  summary: FinancialSummary;
  insights: string[];
  recommendations: string[];
  chartData?: any;
}

export interface AccountConnectionResponse {
  account: FinancialAccount;
  connectionStatus: 'SUCCESS' | 'PENDING' | 'FAILED';
  message: string;
  nextSteps?: string[];
}

// Error Types
export interface ConversationError {
  code: string;
  message: string;
  details?: Record<string, any>;
  recoverable: boolean;
  suggestions?: string[];
}

// WebSocket Connection States
export type ConnectionState = 
  | 'DISCONNECTED'
  | 'CONNECTING'
  | 'CONNECTED'
  | 'RECONNECTING'
  | 'ERROR';

// Message Processing States
export type MessageStatus = 
  | 'PENDING'
  | 'PROCESSING'
  | 'COMPLETED'
  | 'FAILED'
  | 'TIMEOUT';

// Intent Categories
export type IntentCategory = 
  | 'TRANSACTION_MANAGEMENT'
  | 'INVOICE_CREATION'
  | 'FINANCIAL_REPORTING'
  | 'ACCOUNT_MANAGEMENT'
  | 'HELP_SUPPORT'
  | 'GENERAL_QUERY';

// Conversation Events
export interface ConversationEvent {
  type: 'MESSAGE_SENT' | 'MESSAGE_RECEIVED' | 'CONNECTION_CHANGED' | 'ERROR_OCCURRED' | 'TYPING_CHANGED';
  payload: any;
  timestamp: Date;
}

// Re-export from api.ts for convenience
export type { Transaction, Invoice, Client, FinancialAccount, FinancialSummary } from './api';
