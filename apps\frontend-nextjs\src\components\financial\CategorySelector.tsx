"use client"

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from '@/components/ui/dropdown-menu';
import { ChevronDown, Search, Plus, Tag } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Category } from '@/types/api';
import { useFinancialStore } from '@/stores';

interface CategorySelectorProps {
  selectedCategoryId?: string;
  transactionType: 'INCOME' | 'EXPENSE';
  onCategorySelect: (categoryId: string, categoryName: string) => void;
  disabled?: boolean;
  placeholder?: string;
  showCreateNew?: boolean;
  className?: string;
}

/**
 * Dropdown selector for transaction categories with search and filtering
 */
export const CategorySelector: React.FC<CategorySelectorProps> = ({
  selectedCategoryId,
  transactionType,
  onCategorySelect,
  disabled = false,
  placeholder = "Select category",
  showCreateNew = true,
  className
}) => {
  const { categories, fetchCategories } = useFinancialStore();
  const [searchTerm, setSearchTerm] = useState('');
  const [isOpen, setIsOpen] = useState(false);

  // Fetch categories on mount
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  // Filter categories by type and search term
  const filteredCategories = categories.filter(category => {
    const matchesType = category.type === transactionType;
    const matchesSearch = searchTerm === '' || 
      category.name.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesType && matchesSearch && category.isActive;
  });

  // Group categories by system vs user-defined
  const systemCategories = filteredCategories.filter(cat => cat.isSystemDefined);
  const userCategories = filteredCategories.filter(cat => !cat.isSystemDefined);

  // Find selected category
  const selectedCategory = categories.find(cat => cat.id === selectedCategoryId);

  const handleCategorySelect = (category: Category) => {
    onCategorySelect(category.id, category.name);
    setIsOpen(false);
    setSearchTerm('');
  };

  const getCategoryIcon = (iconName?: string) => {
    // Map icon names to actual icons - simplified for now
    return <Tag className="w-4 h-4" />;
  };

  const getCategoryColor = (color?: string) => {
    return color || '#6B7280';
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          disabled={disabled}
          className={cn(
            "justify-between min-w-[200px]",
            !selectedCategory && "text-muted-foreground",
            className
          )}
        >
          <div className="flex items-center gap-2">
            {selectedCategory ? (
              <>
                <div 
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: getCategoryColor(selectedCategory.color) }}
                />
                <span>{selectedCategory.name}</span>
              </>
            ) : (
              <span>{placeholder}</span>
            )}
          </div>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent className="w-[300px] p-0">
        {/* Search input */}
        <div className="p-2 border-b">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search categories..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>
        </div>

        <div className="max-h-[300px] overflow-y-auto">
          {/* System Categories */}
          {systemCategories.length > 0 && (
            <>
              <DropdownMenuLabel className="text-xs text-muted-foreground px-2 py-1">
                System Categories
              </DropdownMenuLabel>
              {systemCategories.map((category) => (
                <DropdownMenuItem
                  key={category.id}
                  onClick={() => handleCategorySelect(category)}
                  className="flex items-center gap-2 px-2 py-2 cursor-pointer"
                >
                  <div 
                    className="w-3 h-3 rounded-full flex-shrink-0"
                    style={{ backgroundColor: getCategoryColor(category.color) }}
                  />
                  <div className="flex-1">
                    <div className="font-medium">{category.name}</div>
                    {category.description && (
                      <div className="text-xs text-muted-foreground">
                        {category.description}
                      </div>
                    )}
                  </div>
                  {selectedCategoryId === category.id && (
                    <Badge variant="secondary" className="text-xs">
                      Selected
                    </Badge>
                  )}
                </DropdownMenuItem>
              ))}
            </>
          )}

          {/* User Categories */}
          {userCategories.length > 0 && (
            <>
              {systemCategories.length > 0 && <DropdownMenuSeparator />}
              <DropdownMenuLabel className="text-xs text-muted-foreground px-2 py-1">
                Your Categories
              </DropdownMenuLabel>
              {userCategories.map((category) => (
                <DropdownMenuItem
                  key={category.id}
                  onClick={() => handleCategorySelect(category)}
                  className="flex items-center gap-2 px-2 py-2 cursor-pointer"
                >
                  <div 
                    className="w-3 h-3 rounded-full flex-shrink-0"
                    style={{ backgroundColor: getCategoryColor(category.color) }}
                  />
                  <div className="flex-1">
                    <div className="font-medium">{category.name}</div>
                    {category.description && (
                      <div className="text-xs text-muted-foreground">
                        {category.description}
                      </div>
                    )}
                  </div>
                  {selectedCategoryId === category.id && (
                    <Badge variant="secondary" className="text-xs">
                      Selected
                    </Badge>
                  )}
                </DropdownMenuItem>
              ))}
            </>
          )}

          {/* No results */}
          {filteredCategories.length === 0 && (
            <div className="p-4 text-center text-muted-foreground">
              <Tag className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <div className="text-sm">No categories found</div>
              {searchTerm && (
                <div className="text-xs">
                  Try adjusting your search term
                </div>
              )}
            </div>
          )}

          {/* Create new category option */}
          {showCreateNew && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => {
                  // TODO: Open create category modal
                  console.log('Create new category');
                }}
                className="flex items-center gap-2 px-2 py-2 cursor-pointer text-blue-600"
              >
                <Plus className="w-4 h-4" />
                <span>Create new category</span>
              </DropdownMenuItem>
            </>
          )}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

/**
 * Simplified category display component
 */
export const CategoryDisplay: React.FC<{
  category?: Category;
  showDescription?: boolean;
  size?: 'sm' | 'md' | 'lg';
}> = ({ category, showDescription = false, size = 'md' }) => {
  if (!category) {
    return (
      <div className="flex items-center gap-2 text-muted-foreground">
        <div className="w-3 h-3 rounded-full bg-gray-300" />
        <span className="text-sm">Uncategorized</span>
      </div>
    );
  }

  const sizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  return (
    <div className="flex items-center gap-2">
      <div 
        className={cn(
          "rounded-full flex-shrink-0",
          size === 'sm' ? 'w-2 h-2' : size === 'md' ? 'w-3 h-3' : 'w-4 h-4'
        )}
        style={{ backgroundColor: category.color || '#6B7280' }}
      />
      <div>
        <div className={cn("font-medium", sizeClasses[size])}>
          {category.name}
        </div>
        {showDescription && category.description && (
          <div className="text-xs text-muted-foreground">
            {category.description}
          </div>
        )}
      </div>
    </div>
  );
};
