package com.intellifin.config;

import com.intellifin.model.ConversationMessage;
import com.intellifin.service.MessageHandler;

public class AzureServiceBusMessagingService implements MessageHandler {
    private final String connectionString;

    public AzureServiceBusMessagingService(String connectionString) {
        this.connectionString = connectionString;
    }

    @Override
    public void handle(ConversationMessage message) {
        // TODO: Implement Azure Service Bus publishing
        System.out.println("Azure Service Bus: Publishing to " + message.getConversation().getId() + ": " + message.getContent());
    }
}
