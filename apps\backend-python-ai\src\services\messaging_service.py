"""
Messaging service abstraction for transaction categorization events
"""
import json
import logging
import os
from abc import ABC, abstractmethod
from urllib.parse import urlparse
from dataclasses import dataclass, asdict
from datetime import datetime
from typing import Dict, Any, Callable, Optional
from uuid import UUID

from ..config.settings import get_settings

logger = logging.getLogger(__name__)


@dataclass
class MessageEvent:
    """Base message event structure"""
    event_type: str
    correlation_id: str
    timestamp: str
    payload: Dict[str, Any]


class MessagingService(ABC):
    """Abstract messaging service interface"""
    
    @abstractmethod
    async def publish_event(self, topic: str, event: MessageEvent) -> bool:
        """Publish an event to the specified topic"""
        pass
    
    @abstractmethod
    async def subscribe(self, topic: str, handler: Callable[[MessageEvent], None]) -> None:
        """Subscribe to events on the specified topic"""
        pass
    
    @abstractmethod
    async def start(self) -> None:
        """Start the messaging service"""
        pass
    
    @abstractmethod
    async def stop(self) -> None:
        """Stop the messaging service"""
        pass


class MessagingServiceFactory:
    """Factory for creating messaging service instances based on environment"""
    
    @staticmethod
    def create_messaging_service() -> MessagingService:
        """Create appropriate messaging service based on environment variables"""
        settings = get_settings()
        messaging_provider = settings.messaging_service_provider.lower()
        
        if messaging_provider == 'azure_servicebus':
            from .azure_servicebus_messaging_service import AzureServiceBusMessagingService
            connection_string = settings.azure_service_bus_connection_string
            if not connection_string:
                raise ValueError("AZURE_SERVICEBUS_CONNECTION_STRING environment variable is required")
            return AzureServiceBusMessagingService(connection_string)
        
        elif messaging_provider == 'rabbitmq':
            from .rabbitmq_messaging_service import RabbitMQMessagingService
            url = urlparse(settings.rabbitmq_url)
            
            host = url.hostname or 'localhost'
            port = url.port or 5672
            username = url.username or 'guest'
            password = url.password or 'guest'
            virtual_host = url.path.lstrip('/') or '/'
            return RabbitMQMessagingService(host, port, username, password, virtual_host)
        
        else:
            raise ValueError(f"Unsupported messaging provider: {messaging_provider}")


# Transaction categorization specific event types
@dataclass
class TransactionCategorizationRequestedEvent:
    """Event received when a transaction needs categorization"""
    transaction_id: str
    description: str
    amount: float
    user_id: str
    timestamp: str
    transaction_type: str  # INCOME or EXPENSE
    source: str


@dataclass
class TransactionCategorizedEvent:
    """Event to publish when AI has categorized a transaction"""
    transaction_id: str
    suggested_category_id: str
    suggested_category_name: str
    confidence: float
    explanation: str
    processing_time: int  # milliseconds
    timestamp: str


@dataclass
class CategorizationAcceptedEvent:
    """Event received when user accepts AI categorization"""
    transaction_id: str
    category_id: str
    user_accepted: bool
    feedback: Optional[str]
    timestamp: str


@dataclass
class CategorizationRejectedEvent:
    """Event received when user rejects AI categorization"""
    transaction_id: str
    rejected_category_id: str
    new_category_id: Optional[str]
    reason: Optional[str]
    timestamp: str


@dataclass
class BulkCategorizationCompletedEvent:
    """Event to publish when bulk categorization is completed"""
    batch_id: str
    processed_count: int
    success_count: int
    failure_count: int
    timestamp: str


class TransactionCategorizationEventHandler:
    """Handler for transaction categorization events"""
    
    def __init__(self, messaging_service: MessagingService):
        self.messaging_service = messaging_service
        self.categorization_service = None  # Will be injected
    
    def set_categorization_service(self, categorization_service):
        """Set the categorization service dependency"""
        self.categorization_service = categorization_service
    
    async def handle_categorization_request(self, event: MessageEvent) -> None:
        """Handle incoming categorization request"""
        try:
            logger.info(f"Processing categorization request: {event.correlation_id}")
            
            # Parse the event payload
            request_data = TransactionCategorizationRequestedEvent(**event.payload)
            
            if not self.categorization_service:
                raise ValueError("Categorization service not initialized")
            
            # Process the categorization
            result = await self.categorization_service.categorize_transaction(
                description=request_data.description,
                amount=request_data.amount,
                transaction_type=request_data.transaction_type,
                user_id=request_data.user_id
            )
            
            # Create response event
            response_event = TransactionCategorizedEvent(
                transaction_id=request_data.transaction_id,
                suggested_category_id=result['category_id'],
                suggested_category_name=result['category_name'],
                confidence=result['confidence'],
                explanation=result['explanation'],
                processing_time=result['processing_time'],
                timestamp=datetime.utcnow().isoformat()
            )
            
            # Publish the result
            await self.publish_categorization_result(response_event)
            
            logger.info(f"Successfully processed categorization request: {event.correlation_id}")
            
        except Exception as e:
            logger.error(f"Error processing categorization request {event.correlation_id}: {str(e)}")
            raise
    
    async def publish_categorization_result(self, event: TransactionCategorizedEvent) -> None:
        """Publish categorization result"""
        message_event = MessageEvent(
            event_type="TransactionCategorized",
            correlation_id=event.transaction_id,
            timestamp=event.timestamp,
            payload=asdict(event)
        )
        
        await self.messaging_service.publish_event("transaction.categorized", message_event)
    
    async def handle_categorization_feedback(self, event: MessageEvent) -> None:
        """Handle categorization feedback for learning"""
        try:
            logger.info(f"Processing categorization feedback: {event.correlation_id}")
            
            if event.event_type == "CategorizationAccepted":
                feedback_data = CategorizationAcceptedEvent(**event.payload)
                if self.categorization_service:
                    await self.categorization_service.record_positive_feedback(
                        transaction_id=feedback_data.transaction_id,
                        category_id=feedback_data.category_id,
                        feedback=feedback_data.feedback
                    )
            
            elif event.event_type == "CategorizationRejected":
                feedback_data = CategorizationRejectedEvent(**event.payload)
                if self.categorization_service:
                    await self.categorization_service.record_negative_feedback(
                        transaction_id=feedback_data.transaction_id,
                        rejected_category_id=feedback_data.rejected_category_id,
                        correct_category_id=feedback_data.new_category_id,
                        reason=feedback_data.reason
                    )
            
            logger.info(f"Successfully processed categorization feedback: {event.correlation_id}")
            
        except Exception as e:
            logger.error(f"Error processing categorization feedback {event.correlation_id}: {str(e)}")
            raise
