"""
Unit tests for the transaction categorization service
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from src.services.categorization import TransactionCategorizationService, CategorySuggestion


class TestTransactionCategorizationService:
    """Test cases for TransactionCategorizationService"""

    @pytest.fixture
    def mock_ai_config(self):
        """Mock AI configuration"""
        config = Mock()
        config.get_model.return_value = Mock()
        config.get_model_info.return_value = {
            "production_model": {"name": "gemini-pro"}
        }
        config.has_google_api_key = True
        return config

    @pytest.fixture
    def categorization_service(self, mock_ai_config):
        """Create categorization service with mocked dependencies"""
        with patch('src.services.categorization.get_ai_config', return_value=mock_ai_config):
            service = TransactionCategorizationService()
            return service

    @pytest.mark.asyncio
    async def test_categorize_transaction_success(self, categorization_service):
        """Test successful transaction categorization"""
        # Mock AI model response
        mock_response = Mock()
        mock_response.content = '''
        {
            "category_name": "Transport",
            "category_type": "EXPENSE",
            "confidence": 0.85,
            "explanation": "Transaction appears to be fuel purchase based on description",
            "keywords": ["fuel", "petrol", "station"]
        }
        '''
        
        categorization_service.model.ainvoke = AsyncMock(return_value=mock_response)
        
        # Test categorization
        result = await categorization_service.categorize_transaction(
            description="Fuel purchase at Shell station",
            amount=150.0,
            transaction_type="EXPENSE",
            user_id="test-user-123"
        )
        
        # Assertions
        assert result["category_name"] == "Transport"
        assert result["category_type"] == "EXPENSE"
        assert result["confidence"] >= 0.8  # Should be adjusted upward due to keywords
        assert result["explanation"] == "Transaction appears to be fuel purchase based on description"
        assert "fuel" in result["keywords"]
        assert result["processing_time"] > 0
        assert result["model_used"] == "gemini-pro"

    @pytest.mark.asyncio
    async def test_categorize_transaction_with_custom_categories(self, categorization_service):
        """Test categorization with custom user categories"""
        mock_response = Mock()
        mock_response.content = '''
        {
            "category_name": "Business Travel",
            "category_type": "EXPENSE",
            "confidence": 0.75,
            "explanation": "Custom category for business-related travel expenses",
            "keywords": ["travel", "business"]
        }
        '''
        
        categorization_service.model.ainvoke = AsyncMock(return_value=mock_response)
        
        custom_categories = ["Business Travel", "Office Supplies", "Client Entertainment"]
        
        result = await categorization_service.categorize_transaction(
            description="Business trip to Lusaka",
            amount=500.0,
            transaction_type="EXPENSE",
            user_id="test-user-123",
            custom_categories=custom_categories
        )
        
        assert result["category_name"] == "Business Travel"
        assert result["confidence"] > 0.7

    @pytest.mark.asyncio
    async def test_categorize_transaction_ai_failure_fallback(self, categorization_service):
        """Test fallback categorization when AI fails"""
        # Mock AI model to raise exception
        categorization_service.model.ainvoke = AsyncMock(side_effect=Exception("AI service unavailable"))
        
        result = await categorization_service.categorize_transaction(
            description="Taxi fare",
            amount=25.0,
            transaction_type="EXPENSE",
            user_id="test-user-123"
        )
        
        # Should fall back to keyword-based categorization
        assert result["category_name"] == "Transport"
        assert result["confidence"] == 0.6
        assert result["model_used"] == "fallback"

    @pytest.mark.asyncio
    async def test_categorize_transaction_invalid_json_fallback(self, categorization_service):
        """Test fallback when AI returns invalid JSON"""
        mock_response = Mock()
        mock_response.content = "Invalid JSON response"
        
        categorization_service.model.ainvoke = AsyncMock(return_value=mock_response)
        
        result = await categorization_service.categorize_transaction(
            description="Restaurant bill",
            amount=75.0,
            transaction_type="EXPENSE",
            user_id="test-user-123"
        )
        
        # Should fall back to keyword-based categorization
        assert result["category_name"] == "Food & Dining"
        assert result["model_used"] == "fallback"

    def test_adjust_confidence_with_keywords(self, categorization_service):
        """Test confidence adjustment based on keywords"""
        # Test high confidence keywords
        adjusted = categorization_service._adjust_confidence(
            base_confidence=0.7,
            description="Fuel purchase at Engen station",
            category="Transport",
            amount=100.0
        )
        assert adjusted > 0.7  # Should be boosted

        # Test generic description penalty
        adjusted = categorization_service._adjust_confidence(
            base_confidence=0.8,
            description="Payment",
            category="Miscellaneous",
            amount=50.0
        )
        assert adjusted < 0.8  # Should be reduced

    def test_fallback_categorization_keywords(self, categorization_service):
        """Test keyword-based fallback categorization"""
        # Test transport keywords
        result = categorization_service._fallback_categorization(
            description="Taxi to airport",
            transaction_type="EXPENSE",
            amount=80.0
        )
        assert result["category_name"] == "Transport"
        assert result["confidence"] == 0.6

        # Test food keywords
        result = categorization_service._fallback_categorization(
            description="Lunch at restaurant",
            transaction_type="EXPENSE",
            amount=45.0
        )
        assert result["category_name"] == "Food & Dining"

        # Test income categorization
        result = categorization_service._fallback_categorization(
            description="Payment from client",
            transaction_type="INCOME",
            amount=1000.0
        )
        assert result["category_name"] == "Sales Revenue"

    @pytest.mark.asyncio
    async def test_bulk_categorize_transactions(self, categorization_service):
        """Test bulk transaction categorization"""
        # Mock successful categorization
        categorization_service.categorize_transaction = AsyncMock(return_value={
            "category_id": "cat-123",
            "category_name": "Transport",
            "confidence": 0.8,
            "explanation": "Test categorization",
            "keywords": ["test"],
            "processing_time": 100,
            "model_used": "test"
        })
        
        transactions = [
            {"id": "txn-1", "description": "Fuel", "amount": 100, "type": "EXPENSE"},
            {"id": "txn-2", "description": "Taxi", "amount": 50, "type": "EXPENSE"}
        ]
        
        result = await categorization_service.bulk_categorize_transactions(
            transactions=transactions,
            user_id="test-user-123"
        )
        
        assert result["processed_count"] == 2
        assert result["success_count"] == 2
        assert result["failure_count"] == 0
        assert len(result["results"]) == 2
        assert result["batch_id"] is not None

    @pytest.mark.asyncio
    async def test_bulk_categorize_with_failures(self, categorization_service):
        """Test bulk categorization with some failures"""
        # Mock categorization with one failure
        def mock_categorize(description, amount, transaction_type, user_id):
            if description == "Fail":
                raise Exception("Categorization failed")
            return {
                "category_id": "cat-123",
                "category_name": "Transport",
                "confidence": 0.8,
                "explanation": "Test categorization",
                "keywords": ["test"],
                "processing_time": 100,
                "model_used": "test"
            }
        
        categorization_service.categorize_transaction = AsyncMock(side_effect=mock_categorize)
        
        transactions = [
            {"id": "txn-1", "description": "Fuel", "amount": 100, "type": "EXPENSE"},
            {"id": "txn-2", "description": "Fail", "amount": 50, "type": "EXPENSE"}
        ]
        
        result = await categorization_service.bulk_categorize_transactions(
            transactions=transactions,
            user_id="test-user-123"
        )
        
        assert result["processed_count"] == 2
        assert result["success_count"] == 1
        assert result["failure_count"] == 1

    @pytest.mark.asyncio
    async def test_record_positive_feedback(self, categorization_service):
        """Test recording positive feedback"""
        # Should not raise exception
        await categorization_service.record_positive_feedback(
            transaction_id="txn-123",
            category_id="cat-456",
            feedback="Good categorization"
        )

    @pytest.mark.asyncio
    async def test_record_negative_feedback(self, categorization_service):
        """Test recording negative feedback"""
        # Should not raise exception
        await categorization_service.record_negative_feedback(
            transaction_id="txn-123",
            rejected_category_id="cat-456",
            correct_category_id="cat-789",
            reason="Wrong category"
        )

    def test_default_categories_structure(self, categorization_service):
        """Test that default categories are properly structured"""
        assert "EXPENSE" in categorization_service.default_categories
        assert "INCOME" in categorization_service.default_categories
        
        expense_categories = categorization_service.default_categories["EXPENSE"]
        income_categories = categorization_service.default_categories["INCOME"]
        
        # Check some expected categories
        assert "Transport" in expense_categories
        assert "Food & Dining" in expense_categories
        assert "Utilities" in expense_categories
        assert "Sales Revenue" in income_categories
        assert "Service Revenue" in income_categories

    @pytest.mark.asyncio
    async def test_model_retry_mechanism(self, categorization_service):
        """Test AI model retry mechanism"""
        # Mock model to fail twice then succeed
        call_count = 0
        
        def mock_invoke(prompt):
            nonlocal call_count
            call_count += 1
            if call_count <= 2:
                raise Exception("Temporary failure")
            
            mock_response = Mock()
            mock_response.content = '''
            {
                "category_name": "Transport",
                "category_type": "EXPENSE",
                "confidence": 0.8,
                "explanation": "Success after retry",
                "keywords": ["test"]
            }
            '''
            return mock_response
        
        categorization_service.model.ainvoke = AsyncMock(side_effect=mock_invoke)
        
        result = await categorization_service.categorize_transaction(
            description="Test transaction",
            amount=100.0,
            transaction_type="EXPENSE",
            user_id="test-user-123"
        )
        
        assert call_count == 3  # Failed twice, succeeded on third attempt
        assert result["category_name"] == "Transport"

    @pytest.mark.asyncio
    async def test_zambian_context_categorization(self, categorization_service):
        """Test categorization with Zambian business context"""
        mock_response = Mock()
        mock_response.content = '''
        {
            "category_name": "Telecommunications",
            "category_type": "EXPENSE",
            "confidence": 0.9,
            "explanation": "Airtel is a major telecom provider in Zambia",
            "keywords": ["airtel", "telecommunications"]
        }
        '''
        
        categorization_service.model.ainvoke = AsyncMock(return_value=mock_response)
        
        result = await categorization_service.categorize_transaction(
            description="Airtel data bundle purchase",
            amount=50.0,
            transaction_type="EXPENSE",
            user_id="test-user-123"
        )
        
        assert result["category_name"] == "Telecommunications"
        assert "airtel" in result["keywords"]
        assert "Zambia" in categorization_service.categorization_prompt.template
