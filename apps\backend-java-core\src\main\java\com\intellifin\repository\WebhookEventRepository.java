package com.intellifin.repository;

import com.intellifin.model.WebhookEvent;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository for WebhookEvent entities
 */
@Repository
public interface WebhookEventRepository extends JpaRepository<WebhookEvent, UUID> {

    /**
     * Find webhook event by webhook ID and source
     */
    Optional<WebhookEvent> findByWebhookIdAndSource(String webhookId, String source);

    /**
     * Find webhook events by source and processing status
     */
    List<WebhookEvent> findBySourceAndProcessingStatus(String source, WebhookEvent.ProcessingStatus status);

    /**
     * Find webhook events by financial account ID
     */
    Page<WebhookEvent> findByFinancialAccountId(UUID financialAccountId, Pageable pageable);

    /**
     * Find webhook events by processing status
     */
    List<WebhookEvent> findByProcessingStatus(WebhookEvent.ProcessingStatus status);

    /**
     * Find failed webhook events that can be retried
     */
    @Query("SELECT w FROM WebhookEvent w WHERE w.processingStatus = 'FAILED' AND w.retryCount < 3")
    List<WebhookEvent> findRetryableFailedEvents();

    /**
     * Find webhook events created within a time range
     */
    List<WebhookEvent> findByCreatedAtBetween(OffsetDateTime startTime, OffsetDateTime endTime);

    /**
     * Find webhook events by transaction ID
     */
    List<WebhookEvent> findByTransactionId(String transactionId);

    /**
     * Count webhook events by source and status
     */
    @Query("SELECT COUNT(w) FROM WebhookEvent w WHERE w.source = :source AND w.processingStatus = :status")
    Long countBySourceAndStatus(@Param("source") String source, @Param("status") WebhookEvent.ProcessingStatus status);

    /**
     * Find webhook events that need processing (pending or failed with retry count < 3)
     */
    @Query("SELECT w FROM WebhookEvent w WHERE w.processingStatus = 'PENDING' OR " +
           "(w.processingStatus = 'FAILED' AND w.retryCount < 3) ORDER BY w.createdAt ASC")
    List<WebhookEvent> findEventsNeedingProcessing();

    /**
     * Find duplicate webhook events by webhook ID
     */
    @Query("SELECT w FROM WebhookEvent w WHERE w.webhookId = :webhookId AND w.id != :excludeId")
    List<WebhookEvent> findDuplicatesByWebhookId(@Param("webhookId") String webhookId, @Param("excludeId") UUID excludeId);

    /**
     * Delete old processed webhook events
     */
    @Query("DELETE FROM WebhookEvent w WHERE w.processingStatus = 'PROCESSED' AND w.createdAt < :cutoffDate")
    void deleteOldProcessedEvents(@Param("cutoffDate") OffsetDateTime cutoffDate);
}
