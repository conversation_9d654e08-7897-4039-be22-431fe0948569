"use client"

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Check, X, Brain, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AICategorizationBadgeProps {
  confidence?: number;
  categoryName?: string;
  explanation?: string;
  status: 'pending' | 'suggested' | 'accepted' | 'rejected' | 'manual';
  onAccept?: () => void;
  onReject?: () => void;
  isLoading?: boolean;
  className?: string;
}

/**
 * Badge component to display AI categorization status and actions
 */
export const AICategorizationBadge: React.FC<AICategorizationBadgeProps> = ({
  confidence,
  categoryName,
  explanation,
  status,
  onAccept,
  onReject,
  isLoading = false,
  className
}) => {
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-50 border-green-200';
    if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-red-600 bg-red-50 border-red-200';
  };

  const getStatusBadge = () => {
    switch (status) {
      case 'pending':
        return (
          <Badge variant="outline" className="text-gray-600 bg-gray-50">
            <AlertCircle className="w-3 h-3 mr-1" />
            Pending Classification
          </Badge>
        );
      
      case 'suggested':
        return (
          <div className="flex items-center gap-2">
            <Badge 
              variant="outline" 
              className={cn(
                "flex items-center gap-1",
                confidence ? getConfidenceColor(confidence) : "text-blue-600 bg-blue-50 border-blue-200"
              )}
            >
              <Brain className="w-3 h-3" />
              AI: {categoryName}
              {confidence && (
                <span className="text-xs ml-1">
                  ({Math.round(confidence * 100)}%)
                </span>
              )}
            </Badge>
            
            {onAccept && onReject && (
              <div className="flex items-center gap-1">
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={onAccept}
                  disabled={isLoading}
                  className="h-6 w-6 p-0 text-green-600 hover:text-green-700 hover:bg-green-50"
                  title="Accept AI suggestion"
                >
                  <Check className="w-3 h-3" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={onReject}
                  disabled={isLoading}
                  className="h-6 w-6 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                  title="Reject AI suggestion"
                >
                  <X className="w-3 h-3" />
                </Button>
              </div>
            )}
          </div>
        );
      
      case 'accepted':
        return (
          <Badge variant="outline" className="text-green-600 bg-green-50 border-green-200">
            <Check className="w-3 h-3 mr-1" />
            AI Accepted: {categoryName}
          </Badge>
        );
      
      case 'rejected':
        return (
          <Badge variant="outline" className="text-orange-600 bg-orange-50 border-orange-200">
            <X className="w-3 h-3 mr-1" />
            AI Rejected
          </Badge>
        );
      
      case 'manual':
        return (
          <Badge variant="outline" className="text-blue-600 bg-blue-50 border-blue-200">
            Manual: {categoryName}
          </Badge>
        );
      
      default:
        return null;
    }
  };

  return (
    <div className={cn("flex flex-col gap-1", className)}>
      {getStatusBadge()}
      
      {explanation && status === 'suggested' && (
        <div className="text-xs text-gray-500 max-w-xs">
          {explanation}
        </div>
      )}
    </div>
  );
};

/**
 * Simplified badge for displaying just the confidence level
 */
export const ConfidenceBadge: React.FC<{
  confidence: number;
  size?: 'sm' | 'md';
  showPercentage?: boolean;
}> = ({ confidence, size = 'sm', showPercentage = true }) => {
  const getConfidenceLevel = (confidence: number) => {
    if (confidence >= 0.8) return { label: 'High', color: 'green' };
    if (confidence >= 0.6) return { label: 'Medium', color: 'yellow' };
    return { label: 'Low', color: 'red' };
  };

  const { label, color } = getConfidenceLevel(confidence);
  
  const colorClasses = {
    green: 'text-green-700 bg-green-100 border-green-300',
    yellow: 'text-yellow-700 bg-yellow-100 border-yellow-300',
    red: 'text-red-700 bg-red-100 border-red-300'
  };

  return (
    <Badge 
      variant="outline" 
      className={cn(
        colorClasses[color as keyof typeof colorClasses],
        size === 'sm' ? 'text-xs px-1.5 py-0.5' : 'text-sm px-2 py-1'
      )}
    >
      {label}
      {showPercentage && (
        <span className="ml-1">
          ({Math.round(confidence * 100)}%)
        </span>
      )}
    </Badge>
  );
};

/**
 * Loading badge for when categorization is in progress
 */
export const CategorizationLoadingBadge: React.FC<{
  message?: string;
}> = ({ message = "Categorizing..." }) => {
  return (
    <Badge variant="outline" className="text-blue-600 bg-blue-50 border-blue-200 animate-pulse">
      <div className="w-3 h-3 mr-1 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
      {message}
    </Badge>
  );
};
