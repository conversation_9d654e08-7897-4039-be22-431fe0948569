package com.intellifin.dto;

import com.intellifin.model.Category;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO for Category entity
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CategoryDto {
    
    private UUID id;
    private UUID userId;
    private String name;
    private String description;
    private Category.CategoryType type;
    private Boolean isSystemDefined;
    private Boolean isActive;
    private String color;
    private String icon;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    /**
     * Convert Category entity to DTO
     */
    public static CategoryDto fromEntity(Category category) {
        if (category == null) {
            return null;
        }
        
        return CategoryDto.builder()
                .id(category.getId())
                .userId(category.getUserId())
                .name(category.getName())
                .description(category.getDescription())
                .type(category.getType())
                .isSystemDefined(category.getIsSystemDefined())
                .isActive(category.getIsActive())
                .color(category.getColor())
                .icon(category.getIcon())
                .createdAt(category.getCreatedAt() != null ? category.getCreatedAt().toLocalDateTime() : null)
                .updatedAt(category.getUpdatedAt() != null ? category.getUpdatedAt().toLocalDateTime() : null)
                .build();
    }
    
    /**
     * Convert DTO to Category entity (for creation/updates)
     */
    public Category toEntity() {
        return Category.builder()
                .id(this.id)
                .name(this.name)
                .description(this.description)
                .type(this.type)
                .isSystemDefined(this.isSystemDefined)
                .isActive(this.isActive)
                .color(this.color)
                .icon(this.icon)
                .build();
    }
}
