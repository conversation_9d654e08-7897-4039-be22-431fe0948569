package com.intellifin.service;

import com.intellifin.model.*;
import com.intellifin.repository.AccountRepository;
import com.intellifin.repository.JournalEntryLineRepository;
import com.intellifin.repository.JournalEntryRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Service for managing journal entries and double-entry bookkeeping
 */
@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class JournalService {

    private final JournalEntryRepository journalEntryRepository;
    private final JournalEntryLineRepository journalEntryLineRepository;
    private final AccountRepository accountRepository;
    private final AuthService authService;

    /**
     * Create a new journal entry
     */
    public JournalEntry createJournalEntry(JournalEntry journalEntry) {
        User currentUser = authService.getCurrentUser();

        journalEntry.setUser(currentUser);
        journalEntry.setCreatedBy(currentUser);
        journalEntry.setStatus(JournalEntry.JournalEntryStatus.DRAFT);
        
        if (journalEntry.getEntryDate() == null) {
            journalEntry.setEntryDate(OffsetDateTime.now());
        }

        // Validate the journal entry
        validateJournalEntry(journalEntry);

        // Save the journal entry
        JournalEntry savedEntry = journalEntryRepository.save(journalEntry);
        
        // Set line numbers and save lines
        int lineNumber = 1;
        for (JournalEntryLine line : journalEntry.getLines()) {
            line.setJournalEntry(savedEntry);
            line.setLineNumber(lineNumber++);
            validateJournalEntryLine(line);
        }

        log.info("Created journal entry {} for user {}", savedEntry.getEntryNumber(), currentUser.getEmail());
        return savedEntry;
    }

    /**
     * Get journal entry by ID
     */
    @Transactional(readOnly = true)
    public Optional<JournalEntry> getJournalEntryById(UUID id) {
        return journalEntryRepository.findById(id);
    }

    /**
     * Get journal entries for current user
     */
    @Transactional(readOnly = true)
    public List<JournalEntry> getJournalEntriesForCurrentUser() {
        User currentUser = authService.getCurrentUser();
        return journalEntryRepository.findByUserOrderByCreatedAtDesc(currentUser);
    }

    /**
     * Get journal entries for current user with pagination
     */
    @Transactional(readOnly = true)
    public Page<JournalEntry> getJournalEntriesForCurrentUser(Pageable pageable) {
        User currentUser = authService.getCurrentUser();
        return journalEntryRepository.findByUserOrderByCreatedAtDesc(currentUser, pageable);
    }

    /**
     * Update a journal entry (only if in DRAFT status)
     */
    public JournalEntry updateJournalEntry(UUID id, JournalEntry updatedEntry) {
        JournalEntry existingEntry = journalEntryRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Journal entry not found with id: " + id));

        if (existingEntry.getStatus() != JournalEntry.JournalEntryStatus.DRAFT) {
            throw new IllegalStateException("Cannot update journal entry that is not in DRAFT status");
        }

        // Update allowed fields
        existingEntry.setDescription(updatedEntry.getDescription());
        existingEntry.setEntryDate(updatedEntry.getEntryDate());
        existingEntry.setReferenceNumber(updatedEntry.getReferenceNumber());
        existingEntry.setTotalAmount(updatedEntry.getTotalAmount());

        // Clear existing lines and add new ones
        existingEntry.getLines().clear();
        int lineNumber = 1;
        for (JournalEntryLine line : updatedEntry.getLines()) {
            line.setJournalEntry(existingEntry);
            line.setLineNumber(lineNumber++);
            validateJournalEntryLine(line);
            existingEntry.addLine(line);
        }

        validateJournalEntry(existingEntry);
        return journalEntryRepository.save(existingEntry);
    }

    /**
     * Post a journal entry (change status from DRAFT to POSTED)
     */
    public JournalEntry postJournalEntry(UUID id) {
        JournalEntry journalEntry = journalEntryRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Journal entry not found with id: " + id));

        if (!journalEntry.canBePosted()) {
            throw new IllegalStateException("Journal entry cannot be posted: " + getPostingValidationErrors(journalEntry));
        }

        User currentUser = authService.getCurrentUser();

        journalEntry.setStatus(JournalEntry.JournalEntryStatus.POSTED);
        journalEntry.setPostedAt(OffsetDateTime.now());
        journalEntry.setPostedBy(currentUser);

        JournalEntry postedEntry = journalEntryRepository.save(journalEntry);
        log.info("Posted journal entry {} by user {}", postedEntry.getEntryNumber(), currentUser.getEmail());
        
        return postedEntry;
    }

    /**
     * Reverse a journal entry
     */
    public JournalEntry reverseJournalEntry(UUID id, String reason) {
        JournalEntry originalEntry = journalEntryRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Journal entry not found with id: " + id));

        if (!originalEntry.canBeReversed()) {
            throw new IllegalStateException("Journal entry cannot be reversed");
        }

        User currentUser = authService.getCurrentUser();

        // Create reversal entry
        JournalEntry reversalEntry = JournalEntry.builder()
                .user(originalEntry.getUser())
                .description("REVERSAL: " + originalEntry.getDescription())
                .totalAmount(originalEntry.getTotalAmount())
                .entryDate(OffsetDateTime.now())
                .referenceNumber(originalEntry.getReferenceNumber())
                .sourceType("REVERSAL")
                .sourceId(originalEntry.getId().toString())
                .createdBy(currentUser)
                .status(JournalEntry.JournalEntryStatus.POSTED)
                .postedAt(OffsetDateTime.now())
                .postedBy(currentUser)
                .build();

        // Create reversed lines (swap debits and credits)
        for (JournalEntryLine originalLine : originalEntry.getLines()) {
            JournalEntryLine reversalLine = JournalEntryLine.builder()
                    .journalEntry(reversalEntry)
                    .account(originalLine.getAccount())
                    .debitAmount(originalLine.getCreditAmount())  // Swap
                    .creditAmount(originalLine.getDebitAmount()) // Swap
                    .description("REVERSAL: " + originalLine.getDescription())
                    .referenceNumber(originalLine.getReferenceNumber())
                    .build();
            reversalEntry.addLine(reversalLine);
        }

        // Save reversal entry
        JournalEntry savedReversalEntry = journalEntryRepository.save(reversalEntry);

        // Update original entry
        originalEntry.setStatus(JournalEntry.JournalEntryStatus.REVERSED);
        originalEntry.setReversedAt(OffsetDateTime.now());
        originalEntry.setReversedBy(currentUser);
        originalEntry.setReversalReason(reason);
        originalEntry.setReversalEntry(savedReversalEntry);
        journalEntryRepository.save(originalEntry);

        log.info("Reversed journal entry {} by user {}", originalEntry.getEntryNumber(), currentUser.getEmail());
        return savedReversalEntry;
    }

    /**
     * Delete a journal entry (only if in DRAFT status)
     */
    public void deleteJournalEntry(UUID id) {
        JournalEntry journalEntry = journalEntryRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Journal entry not found with id: " + id));

        if (journalEntry.getStatus() != JournalEntry.JournalEntryStatus.DRAFT) {
            throw new IllegalStateException("Cannot delete journal entry that is not in DRAFT status");
        }

        journalEntryRepository.delete(journalEntry);
        log.info("Deleted journal entry {} ", journalEntry.getEntryNumber());
    }

    /**
     * Calculate account balance based on posted journal entries
     */
    @Transactional(readOnly = true)
    public BigDecimal calculateAccountBalance(UUID accountId) {
        Account account = accountRepository.findById(accountId)
                .orElseThrow(() -> new RuntimeException("Account not found with id: " + accountId));
        
        BigDecimal balance = journalEntryLineRepository.calculateAccountBalance(account);
        
        // Adjust for normal balance type
        if (account.getNormalBalance() == Account.NormalBalance.CREDIT) {
            balance = balance.negate();
        }
        
        return balance;
    }

    /**
     * Validate journal entry
     */
    private void validateJournalEntry(JournalEntry journalEntry) {
        if (journalEntry.getLines().isEmpty()) {
            throw new IllegalArgumentException("Journal entry must have at least one line");
        }

        if (journalEntry.getTotalAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Total amount must be greater than zero");
        }

        // Validate that total amount matches the sum of debits (or credits)
        BigDecimal totalDebits = journalEntry.getTotalDebits();
        if (totalDebits.compareTo(journalEntry.getTotalAmount()) != 0) {
            throw new IllegalArgumentException("Total amount must equal sum of debits");
        }

        // Validate balance
        if (!journalEntry.isBalanced()) {
            throw new IllegalArgumentException("Journal entry must be balanced (debits must equal credits)");
        }
    }

    /**
     * Validate journal entry line
     */
    private void validateJournalEntryLine(JournalEntryLine line) {
        if (!line.isValid()) {
            throw new IllegalArgumentException("Journal entry line must have either debit OR credit amount (not both, not neither)");
        }

        if (line.getAccount() == null) {
            throw new IllegalArgumentException("Journal entry line must have an account");
        }

        if (line.getDescription() == null || line.getDescription().trim().isEmpty()) {
            throw new IllegalArgumentException("Journal entry line must have a description");
        }
    }

    /**
     * Get posting validation errors
     */
    private String getPostingValidationErrors(JournalEntry journalEntry) {
        StringBuilder errors = new StringBuilder();
        
        if (journalEntry.getStatus() != JournalEntry.JournalEntryStatus.DRAFT) {
            errors.append("Entry is not in DRAFT status. ");
        }
        
        if (!journalEntry.isBalanced()) {
            errors.append("Entry is not balanced. ");
        }
        
        if (journalEntry.getLines().isEmpty()) {
            errors.append("Entry has no lines. ");
        }
        
        if (journalEntry.getTotalAmount().compareTo(BigDecimal.ZERO) <= 0) {
            errors.append("Total amount must be greater than zero. ");
        }
        
        return errors.toString();
    }
}
