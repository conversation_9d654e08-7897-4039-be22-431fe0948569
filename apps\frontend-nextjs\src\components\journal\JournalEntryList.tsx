'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Eye, Edit, Trash2, CheckCircle, XCircle, RotateCcw } from 'lucide-react';
import { JournalEntry, JournalEntrySummary } from '@intellifin/data-models';
import { useJournalEntries } from '@/hooks/useJournalEntries';
import { formatCurrency, formatDate } from '@/utils/formatting';

interface JournalEntryListProps {
  entries?: JournalEntry[];
  onViewEntry?: (entry: JournalEntry) => void;
  onEditEntry?: (entry: JournalEntry) => void;
  onDeleteEntry?: (entryId: string) => void;
  onPostEntry?: (entryId: string) => void;
  onReverseEntry?: (entryId: string, reason: string) => void;
}

export function JournalEntryList({
  entries: propEntries,
  onViewEntry,
  onEditEntry,
  onDeleteEntry,
  onPostEntry,
  onReverseEntry
}: JournalEntryListProps) {
  const { entries: hookEntries, loading, error, refreshEntries } = useJournalEntries();
  const entries = propEntries || hookEntries;
  const [selectedEntry, setSelectedEntry] = useState<string | null>(null);

  useEffect(() => {
    if (!propEntries) {
      refreshEntries();
    }
  }, [refreshEntries, propEntries]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return <Badge variant="secondary">Draft</Badge>;
      case 'POSTED':
        return <Badge variant="default">Posted</Badge>;
      case 'REVERSED':
        return <Badge variant="destructive">Reversed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getBalanceBadge = (isBalanced: boolean) => {
    return isBalanced ? (
      <Badge variant="default" className="bg-green-100 text-green-800">
        <CheckCircle className="w-3 h-3 mr-1" />
        Balanced
      </Badge>
    ) : (
      <Badge variant="destructive">
        <XCircle className="w-3 h-3 mr-1" />
        Unbalanced
      </Badge>
    );
  };

  const handleAction = (action: string, entry: JournalEntry) => {
    switch (action) {
      case 'view':
        onViewEntry?.(entry);
        break;
      case 'edit':
        onEditEntry?.(entry);
        break;
      case 'delete':
        if (confirm('Are you sure you want to delete this journal entry?')) {
          onDeleteEntry?.(entry.id);
        }
        break;
      case 'post':
        if (confirm('Are you sure you want to post this journal entry? This action cannot be undone.')) {
          onPostEntry?.(entry.id);
        }
        break;
      case 'reverse':
        const reason = prompt('Please enter a reason for reversing this journal entry:');
        if (reason) {
          onReverseEntry?.(entry.id, reason);
        }
        break;
    }
  };

  if (loading && !propEntries) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2">Loading journal entries...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error && !propEntries) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-red-600">
            Error loading journal entries: {error}
          </div>
          <Button onClick={refreshEntries} className="mt-2">
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {entries.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          No journal entries found. Create your first journal entry to get started.
        </div>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Entry #</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Balance</TableHead>
              <TableHead>Lines</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {entries.map((entry) => {
              const totalDebits = entry.lines?.reduce((sum, line) => sum + (line.debitAmount || 0), 0) || 0;
              const totalCredits = entry.lines?.reduce((sum, line) => sum + (line.creditAmount || 0), 0) || 0;
              const isBalanced = totalDebits === totalCredits;
              
              return (
                <TableRow key={entry.id}>
                  <TableCell className="font-mono text-sm">
                    {entry.entryNumber}
                  </TableCell>
                  <TableCell>
                    {formatDate(entry.entryDate)}
                  </TableCell>
                  <TableCell className="max-w-xs truncate">
                    {entry.description}
                  </TableCell>
                  <TableCell className="font-mono">
                    {formatCurrency(entry.totalAmount)}
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(entry.status)}
                  </TableCell>
                  <TableCell>
                    {getBalanceBadge(isBalanced)}
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {entry.lines?.length || 0} lines
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleAction('view', entry)}
                        title="View Details"
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      
                      {entry.status === 'DRAFT' && (
                        <>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleAction('edit', entry)}
                            title="Edit Entry"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleAction('delete', entry)}
                            title="Delete Entry"
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                          {isBalanced && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleAction('post', entry)}
                              title="Post Entry"
                              className="text-green-600 hover:text-green-700"
                            >
                              <CheckCircle className="w-4 h-4" />
                            </Button>
                          )}
                        </>
                      )}
                      
                      {entry.status === 'POSTED' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleAction('reverse', entry)}
                          title="Reverse Entry"
                          className="text-orange-600 hover:text-orange-700"
                        >
                          <RotateCcw className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      )}
    </div>
  );
}
