package com.intellifin.messaging.events;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Event received when bulk categorization is completed
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BulkCategorizationCompletedEvent {
    
    private String batchId;
    private Integer processedCount;
    private Integer successCount;
    private Integer failureCount;
    private LocalDateTime timestamp;
}
