# Story 2.2: Automated Double-Entry Journal System

**Epic:** Core Financial Engine & Accounting Integrity  
**Status:** Ready for Development  
**Priority:** Critical  
**Story Points:** 13

## User Story

**As a** business owner  
**I want** all financial transactions to automatically create balanced journal entries  
**So that** my books maintain proper double-entry accounting integrity and audit compliance

## Acceptance Criteria

- [ ] JournalEntry data model supports double-entry bookkeeping with debit/credit validation
- [ ] All confirmed financial transactions automatically generate balanced journal entries (debits = credits)
- [ ] JournalService subscribes to financial events via messaging abstraction layer
- [ ] Transaction categorization creates appropriate journal entries based on Account mapping
- [ ] Manual transaction entry includes journal entry creation
- [ ] Invoice payments generate proper journal entries (Accounts Receivable, Cash, Revenue)
- [ ] Expense transactions create journal entries (Expense Account, Cash/Accounts Payable)
- [ ] System validates journal entry balance before saving (prevents unbalanced entries)
- [ ] Journal entries include proper audit trail (user, timestamp, source transaction)
- [ ] Journal entry reversal functionality for corrections
- [ ] Real-time account balance updates based on journal entries
- [ ] Journal entry search and filtering capabilities
- [ ] Integration with existing transaction categorization system maintains journal integrity

## Technical Implementation

### Backend Changes
- `src/main/java/com/intellifin/model/JournalEntry.java` - Journal entry entity with debit/credit validation
- `src/main/java/com/intellifin/model/JournalEntryLine.java` - Individual debit/credit lines
- `src/main/java/com/intellifin/repository/JournalEntryRepository.java` - Journal entry data access
- `src/main/java/com/intellifin/service/JournalService.java` - Double-entry business logic
- `src/main/java/com/intellifin/service/AccountingEventHandler.java` - Event-driven journal creation
- `src/main/java/com/intellifin/controller/JournalController.java` - Journal entry API endpoints
- `src/main/java/com/intellifin/validation/JournalEntryValidator.java` - Balance validation
- `src/main/resources/db/migration/` - Database migrations for journal tables

### Frontend Changes
- `src/components/financial/JournalEntryView.tsx` - Journal entry display
- `src/components/financial/JournalEntryList.tsx` - Journal entries listing
- `src/components/financial/AccountBalanceDisplay.tsx` - Real-time balance updates
- `src/hooks/useJournalEntries.ts` - Journal entry data management
- `src/stores/journalStore.ts` - Journal entry state management

### Database Changes
- `journal_entries` table with transaction references and audit fields
- `journal_entry_lines` table for individual debit/credit entries
- `account_balances` materialized view for real-time balance calculations
- Triggers for automatic balance updates on journal entry changes
- Indexes for performance on journal entry queries

### Event Integration
- Subscribe to `TransactionConfirmedEvent` for transaction journal entries
- Subscribe to `InvoicePaidEvent` for invoice payment journal entries
- Subscribe to `ExpenseRecordedEvent` for expense journal entries
- Publish `JournalEntryCreatedEvent` for downstream processing
- Handle event failures with dead-letter queue processing

## API Contracts

```typescript
interface JournalEntry {
  id: string;
  transactionId?: string; // Source transaction reference
  invoiceId?: string; // Source invoice reference
  entryNumber: string; // Sequential journal entry number
  description: string;
  entryDate: string;
  totalAmount: number; // Total debit amount (must equal total credit)
  status: 'DRAFT' | 'POSTED' | 'REVERSED';
  userId: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  lines: JournalEntryLine[];
}

interface JournalEntryLine {
  id: string;
  journalEntryId: string;
  accountId: string;
  description: string;
  debitAmount: number; // 0 if credit entry
  creditAmount: number; // 0 if debit entry
  lineNumber: number; // Order within journal entry
  account?: Account; // Populated in responses
}

interface JournalAPI {
  GET /api/v1/journal-entries: {
    query: { 
      userId: string; 
      accountId?: string; 
      fromDate?: string; 
      toDate?: string; 
      status?: string;
      limit?: number; 
      offset?: number; 
    };
    response: { 
      journalEntries: JournalEntry[]; 
      total: number; 
      totalDebits: number;
      totalCredits: number;
    };
    errors: { 401: "Unauthorized", 500: "Server error" };
  };
  
  POST /api/v1/journal-entries: {
    body: {
      description: string;
      entryDate: string;
      lines: Omit<JournalEntryLine, 'id' | 'journalEntryId' | 'account'>[];
    };
    response: JournalEntry;
    errors: { 
      400: "Unbalanced entry or invalid data", 
      409: "Entry number conflict" 
    };
  };
  
  GET /api/v1/journal-entries/{id}: {
    response: JournalEntry;
    errors: { 404: "Journal entry not found" };
  };
  
  POST /api/v1/journal-entries/{id}/reverse: {
    body: { reason: string; reversalDate: string; };
    response: { 
      originalEntry: JournalEntry; 
      reversalEntry: JournalEntry; 
    };
    errors: { 
      400: "Entry already reversed", 
      404: "Journal entry not found" 
    };
  };
  
  GET /api/v1/accounts/{id}/journal-entries: {
    query: { fromDate?: string; toDate?: string; limit?: number; };
    response: { 
      journalEntries: JournalEntry[]; 
      runningBalance: number;
    };
  };
  
  POST /api/v1/journal-entries/validate: {
    body: {
      lines: Omit<JournalEntryLine, 'id' | 'journalEntryId' | 'account'>[];
    };
    response: { 
      isBalanced: boolean; 
      totalDebits: number; 
      totalCredits: number; 
      errors: string[]; 
    };
  };
}
```

## Standard Journal Entry Templates

### Transaction Categorization (Expense)
```
Dr. [Expense Account from Category mapping]     $100.00
    Cr. Cash and Cash Equivalents                   $100.00
```

### Transaction Categorization (Income)
```
Dr. Cash and Cash Equivalents                   $500.00
    Cr. [Revenue Account from Category mapping]         $500.00
```

### Invoice Payment Received
```
Dr. Cash and Cash Equivalents                   $1,000.00
    Cr. Accounts Receivable                             $1,000.00
```

### Invoice Creation
```
Dr. Accounts Receivable                         $1,000.00
    Cr. Service Revenue                                 $1,000.00
```

### Expense Payment
```
Dr. [Expense Account]                           $200.00
    Cr. Cash and Cash Equivalents                      $200.00
```

## Error Handling

- **Unbalanced Entries:** Prevent saving with clear validation messages showing debit/credit totals
- **Invalid Account Mapping:** Show available accounts when Category lacks Account mapping
- **Concurrent Modifications:** Optimistic locking prevents conflicting journal entry updates
- **Event Processing Failures:** Dead-letter queue handling for failed journal entry creation
- **Account Balance Calculation Errors:** Graceful handling with balance recalculation utilities
- **Journal Entry Reversal Errors:** Validate reversal eligibility and prevent duplicate reversals

## Definition of Done

- [ ] JournalEntry data model supports complete double-entry bookkeeping
- [ ] All financial transaction events automatically create balanced journal entries
- [ ] Journal entry validation prevents unbalanced entries from being saved
- [ ] Event-driven journal creation works reliably via messaging abstraction layer
- [ ] Account balances update in real-time based on journal entries
- [ ] Journal entry reversal functionality works correctly
- [ ] API endpoints support full journal entry lifecycle
- [ ] Integration with transaction categorization maintains journal integrity
- [ ] Journal entry search and filtering performs efficiently
- [ ] Audit trail captures all journal entry modifications
- [ ] Performance meets requirements (< 2 seconds for journal entry creation)
- [ ] Tests cover journal entry creation, validation, and reversal scenarios
- [ ] No breaking changes to existing transaction categorization system
- [ ] Documentation includes double-entry bookkeeping guide

## Dependencies

- [Story 2.1: Chart of Accounts & Category Mapping](story-2.1-chart-of-accounts.md) - Required for Account mapping
- [Story 3.1: Transaction Categorization](../epic-03/story-3.1-transaction-categorization.md) - Must integrate with journal creation
- Database migration framework
- Messaging abstraction layer (Spring Cloud Stream)

## Notes

This story implements the core double-entry bookkeeping engine that ensures all financial transactions maintain accounting integrity. Every financial event must result in balanced journal entries where total debits equal total credits.

The integration with the existing transaction categorization system is critical - when users categorize transactions, the system must automatically create the appropriate journal entries based on the Category-to-Account mapping established in Story 2.1.

This foundational accounting engine enables proper financial reporting, audit compliance, and business intelligence while maintaining the user-friendly transaction categorization interface.

---

**Related Stories:**
- [Story 2.1: Chart of Accounts & Category Mapping](story-2.1-chart-of-accounts.md)
- [Story 3.1: Transaction Categorization](../epic-03/story-3.1-transaction-categorization.md)

**Epic:** [Core Financial Engine & Accounting Integrity](../../epics-and-stories.md#epic-2-core-financial-engine--accounting-integrity)
