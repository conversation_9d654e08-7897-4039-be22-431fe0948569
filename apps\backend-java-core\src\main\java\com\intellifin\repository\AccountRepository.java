package com.intellifin.repository;

import com.intellifin.model.Account;
import com.intellifin.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface AccountRepository extends JpaRepository<Account, UUID> {
    Optional<Account> findByCode(String code);

    /**
     * Find account by code and user
     */
    Optional<Account> findByCodeAndUser(String code, User user);

    /**
     * Find system account by code (user is null)
     */
    Optional<Account> findByCodeAndUserIsNull(String code);
}
