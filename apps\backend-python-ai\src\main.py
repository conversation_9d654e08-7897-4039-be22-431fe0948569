"""
IntelliFin AI Service
FastAPI application for natural language processing and transaction categorization
"""

import time
import os
from pathlib import Path
from contextlib import asynccontextmanager

from typing import Dict, Any, Optional

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from src.config.settings import get_settings
from src.models.schemas import (
    HealthResponse,
    IntentRecognitionRequest,
    IntentRecognitionResponse,
    CommandProcessingRequest,
    CommandProcessingResponse,
    ErrorResponse
)
from src.models.vertex_schemas import VertexAIResponse
from src.models.exceptions import InvalidAIResponseError
from src.services.intent_recognition import IntentRecognitionService
from src.services.entity_extraction import EntityExtractionService
from src.services.entity_extractor import EntityExtractor
from src.services.categorization import TransactionCategorizationService
from src.services.messaging_service import (
    MessagingServiceFactory,
    TransactionCategorizationEventHandler
)

from src.config.ai_config import get_ai_config, test_model_connection

# Global startup time
startup_time = time.time()

# Global service instances
intent_service: Optional[IntentRecognitionService] = None
entity_service: Optional[EntityExtractionService] = None
entity_extractor: Optional[EntityExtractor] = None
categorization_service: Optional[TransactionCategorizationService] = None
messaging_service = None
event_handler: Optional[TransactionCategorizationEventHandler] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global intent_service, entity_service, entity_extractor, categorization_service, messaging_service, event_handler

    # Startup
    print("🚀 IntelliFin AI Service starting up...")

    # Initialize AI services
    try:
        # HACK: Temporarily change CWD to project root to fix file path issues in services
        original_cwd = Path.cwd()
        # This file is in .../apps/backend-python-ai/src, so 4 levels up is project root
        project_root = Path(__file__).resolve().parent.parent.parent.parent
        
        try:
            os.chdir(project_root)
            intent_service = IntentRecognitionService()
            entity_service = EntityExtractionService()
            entity_extractor = EntityExtractor()
            categorization_service = TransactionCategorizationService()
            print("✅ AI services initialized")
        finally:
            # Always change back to the original directory
            os.chdir(original_cwd)


        # Initialize messaging service
        messaging_service = MessagingServiceFactory.create_messaging_service()
        await messaging_service.start()

        # Initialize event handler
        event_handler = TransactionCategorizationEventHandler(messaging_service)
        event_handler.set_categorization_service(categorization_service)

        # Subscribe to categorization requests
        await messaging_service.subscribe(
            "transaction.categorization.requested",
            event_handler.handle_categorization_request
        )

        # Subscribe to feedback events
        await messaging_service.subscribe(
            "categorization.accepted",
            event_handler.handle_categorization_feedback
        )

        await messaging_service.subscribe(
            "categorization.rejected",
            event_handler.handle_categorization_feedback
        )

        print("✅ Messaging service initialized and subscribed to events")

    except Exception as e:
        print(f"❌ Failed to initialize AI services: {e}")

    print("✅ IntelliFin AI Service ready!")

    yield

    # Shutdown
    print("🛑 IntelliFin AI Service shutting down...")

    # Cleanup messaging service
    if messaging_service:
        await messaging_service.stop()

    # Cleanup here
    intent_service = None
    entity_service = None
    entity_extractor = None
    categorization_service = None
    messaging_service = None
    event_handler = None

    print("✅ IntelliFin AI Service shutdown complete!")


# Create FastAPI application
app = FastAPI(
    title="IntelliFin AI Service",
    description="AI service for natural language processing and transaction categorization",
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs",
    redoc_url="/redoc",
)

# Get settings
settings = get_settings()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:8080"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include categorization routes
from src.api.categorization_endpoints import include_categorization_routes
include_categorization_routes(app)

@app.get("/health", response_model=HealthResponse)
async def health_check() -> HealthResponse:
    """Health check endpoint"""
    try:
        # Check Redis connection
        # redis_status = await check_redis_connection()
        
        # Check AI model availability
        # ai_model_status = await check_ai_model()
        
        return HealthResponse(
            status="healthy",
            service="intellifin-ai-service",
            timestamp=time.time(),
            version="1.0.0",
            uptime=time.time() - startup_time,
            environment=settings.environment,
        )
    except Exception as e:
        raise HTTPException(
            status_code=503,
            detail={
                "status": "unhealthy",
                "service": "intellifin-ai-service",
                "timestamp": time.time(),
                "error": str(e),
                "environment": settings.environment,
            }
        )

@app.get("/info")
async def service_info() -> Dict[str, Any]:
    """Service information endpoint"""
    return {
        "service": "intellifin-ai-service",
        "version": "1.0.0",
        "description": "AI service for IntelliFin platform",
        "environment": settings.environment,
        "python_version": "3.10+",
        "framework": "FastAPI",
        "ai_models": {
            "local": "Ollama Llama 3.1 8B",
            "production": "Google Gemini Pro",
        },
        "capabilities": [
            "transaction_categorization",
            "intent_recognition",
            "entity_extraction",
            "financial_explanations",
        ],
    }

@app.post("/api/v1/ai/intent", response_model=VertexAIResponse)
async def recognize_intent(request: IntentRecognitionRequest):
    """Recognize intent from user command"""
    if not entity_extractor:
        raise HTTPException(status_code=503, detail="Entity extractor service not available")

    try:
        # Note: This is now a synchronous call since the underlying library is sync
        response = entity_extractor.get_structured_intent_from_text(request.command)
        return response
    except InvalidAIResponseError as e:
        raise HTTPException(status_code=500, detail=f"Failed to process AI response: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Intent recognition failed: {str(e)}")



@app.post("/api/v1/process-command", response_model=CommandProcessingResponse)
async def process_command(request: CommandProcessingRequest):
    """Process complete command with intent recognition and entity extraction"""
    if not intent_service or not entity_service:
        raise HTTPException(status_code=503, detail="AI services not available")

    try:
        # Recognize intent
        intent_request = IntentRecognitionRequest(
            command=request.command,
            user_id=request.user_id,
            session_id=request.session_id,
            context=request.context
        )

        intent_response = await intent_service.recognize_intent(intent_request)

        # Extract additional entities if needed
        additional_entities = entity_service.extract_entities(request.command, request.context)

        # Merge entities
        all_entities = intent_response.entities.copy()
        for entity_type, entities in additional_entities.items():
            if entity_type in all_entities:
                all_entities[entity_type].extend(entities)
            else:
                all_entities[entity_type] = entities

        # Generate response based on intent
        response_text = _generate_response_text(intent_response.intent, all_entities)

        return CommandProcessingResponse(
            intent=intent_response.intent,
            entities=all_entities,
            response=response_text,
            requires_follow_up=bool(intent_response.fallback),
            follow_up_prompt=intent_response.fallback.message if intent_response.fallback else None,
            suggestions=intent_response.suggestions,
            metadata={
                "confidence": intent_response.intent.confidence,
                "processing_time_ms": intent_response.processing_time_ms
            },
            processing_time_ms=intent_response.processing_time_ms
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Command processing failed: {str(e)}")


@app.get("/api/v1/ai/intents")
async def get_supported_intents():
    """Get list of supported intents"""
    if not intent_service:
        raise HTTPException(status_code=503, detail="Intent recognition service not available")

    return {
        "supported_intents": intent_service.get_supported_intents(),
        "total_count": len(intent_service.get_supported_intents())
    }


@app.get("/api/v1/ai/models")
async def get_model_info():
    """Get AI model information"""
    ai_config = get_ai_config()
    return ai_config.get_model_info()


@app.get("/api/v1/ai/test")
async def test_ai_models():
    """Test AI model connections"""
    return test_model_connection()


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "IntelliFin AI Service",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health",
        "info": "/info",
        "endpoints": {
            "intent_recognition": "/api/v1/ai/intent",
            "command_processing": "/api/v1/process-command",
            "supported_intents": "/api/v1/ai/intents",
            "model_info": "/api/v1/ai/models",
            "test_models": "/api/v1/ai/test",
            "categorization": "/api/v1/categorization/categorize",
            "bulk_categorization": "/api/v1/categorization/bulk-categorize",
            "categorization_health": "/api/v1/categorization/health"
        }
    }


def _generate_response_text(intent, entities) -> str:
    """Generate response text based on intent and entities - Enhanced for Story 2.2"""
    intent_name = intent.name.value
    confidence = intent.confidence

    # Core MVP intents (Story 2.2)
    if intent_name == "CREATE_INVOICE":
        client_name = _extract_entity_value(entities, "CLIENT_NAME")
        amount = _extract_entity_value(entities, "AMOUNT")

        if client_name and amount:
            return f"I'll help you create an invoice for {client_name} for {amount}. Let me prepare that for you."
        elif client_name:
            return f"I'll help you create an invoice for {client_name}. What amount should I include?"
        elif amount:
            return f"I'll help you create an invoice for {amount}. Which client is this for?"
        else:
            return "I'll help you create an invoice. Please provide the client name and amount."

    elif intent_name == "SHOW_SUMMARY":
        date_range = _extract_entity_value(entities, "DATE_RANGE")

        if date_range:
            return f"I'll show you your financial summary for {date_range}. Gathering your data now..."
        else:
            return "I'll show you your financial summary. Gathering your latest financial data..."

    elif intent_name == "CATEGORIZE_TRANSACTION":
        amount = _extract_entity_value(entities, "AMOUNT")
        category = _extract_entity_value(entities, "CATEGORY")

        if amount and category:
            return f"I'll categorize the {amount} transaction as {category}."
        elif amount:
            return f"I'll help you categorize the {amount} transaction. What category should this be?"
        else:
            return "I'll help you categorize a transaction. Please specify which transaction and the category."

    elif intent_name == "CONNECT_ACCOUNT":
        account_type = _extract_entity_value(entities, "ACCOUNT_TYPE")
        if account_type:
            return f"I'll help you connect your {account_type}. Let me guide you through the setup process."
        else:
            return "I'll help you connect a new financial account. Which service would you like to connect (MTN Money, Airtel Money, Bank Account)?"

    elif intent_name == "GET_HELP":
        return "I'm here to help! I can assist with:\n• Creating invoices\n• Showing financial summaries\n• Categorizing transactions\n• Viewing transaction history\n• Connecting accounts\n\nWhat would you like to do?"

    else:
        # Handle unclear or low confidence intents
        if confidence < 0.5:
            return "I'm not quite sure what you'd like to do. Could you please rephrase your request? I can help with invoices, financial summaries, transaction categorization, and more."
        else:
            return "I understand you want to do something, but I need a bit more information. Could you please be more specific?"


def _extract_entity_value(entities: dict, entity_type: str) -> Optional[str]:
    """Extract the first entity value of a given type"""
    entity_list = entities.get(entity_type, [])
    if entity_list and len(entity_list) > 0:
        return entity_list[0].value
    return None

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request, exc):
    return JSONResponse(
        status_code=404,
        content={
            "error": "Not Found",
            "message": "The requested endpoint was not found",
            "timestamp": time.time(),
            "path": str(request.url.path),
        }
    )

@app.exception_handler(500)
async def internal_error_handler(request, exc):
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal Server Error",
            "message": "An unexpected error occurred",
            "timestamp": time.time(),
            "path": str(request.url.path),
        }
    )

if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "src.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True if settings.environment == "development" else False,
        log_level="debug" if settings.environment == "development" else "info",
    )
