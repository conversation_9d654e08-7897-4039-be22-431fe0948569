"use client";

import React from 'react';
import { useCategories } from '../../hooks/useCategories';
import AccountSelector from './AccountSelector';

const CategoryAccountMapping = () => {
  const { categories, loading, error, mapCategory } = useCategories();

  if (loading) return <div>Loading categories...</div>;
  if (error) return <div className="text-red-500">{error}</div>;

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Category to Account Mapping</h1>
      <div className="space-y-4">
        {categories.map(category => (
          <div key={category.id} className="flex items-center justify-between p-2 border rounded">
            <span>{category.name}</span>
            <AccountSelector
              value={category.accountId}
              onChange={(accountId) => mapCategory(category.id, accountId)}
              accountType={category.type === 'INCOME' ? 'INCOME' : 'EXPENSE'}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default CategoryAccountMapping;
