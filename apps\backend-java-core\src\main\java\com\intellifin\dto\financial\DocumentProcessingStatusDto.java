package com.intellifin.dto.financial;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentProcessingStatusDto {

    private String uploadId;
    private ProcessingStatus status;
    private Integer progress;
    private Integer extractedTransactions;
    private String errorMessage;
    private OffsetDateTime processedAt;
    private BigDecimal confidenceScore;

    public enum ProcessingStatus {
        UPLOADING,
        PROCESSING,
        COMPLETED,
        FAILED
    }
}
