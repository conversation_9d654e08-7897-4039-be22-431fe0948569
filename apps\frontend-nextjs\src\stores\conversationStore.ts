import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import { Client, IMessage } from '@stomp/stompjs';
import SockJS from 'sockjs-client';
import {
  DisplayMessage,
  ConversationState,
  WebSocketMessage,
  CommandSuggestion,
  ConnectionState
} from '@/types/conversation';
import { conversationalCommandService } from '@/services/conversational-commands';
import { useAuthStore } from './authStore';

interface ConversationStore extends ConversationState {
  // WebSocket client management
  stompClient: Client | null;

  // WebSocket connection
  connectionState: ConnectionState;
  reconnectAttempts: number;

  // WebSocket connection methods
  connect: () => void;
  disconnect: () => void;

  // Actions
  addMessage: (message: DisplayMessage) => void;
  updateMessage: (messageId: string, updates: Partial<DisplayMessage>) => void;
  removeMessage: (messageId: string) => void;
  clearMessages: () => void;

  // WebSocket actions
  setConnectionState: (state: ConnectionState) => void;
  setConnectionError: (error: string | null) => void;
  incrementReconnectAttempts: () => void;
  resetReconnectAttempts: () => void;
  
  // Typing and activity
  setTyping: (isTyping: boolean) => void;
  setAwaitingResponse: (awaiting: boolean) => void;
  updateLastActivity: () => void;
  
  // Session management
  setSessionId: (sessionId: string | null) => void;
  generateNewSession: () => string;
  
  // Message processing
  processWebSocketMessage: (wsMessage: WebSocketMessage) => void;
  addUserMessage: (content: string) => DisplayMessage;
  
  // Suggestions and commands
  getCommandSuggestions: () => CommandSuggestion[];

  // WebSocket methods (managed internally by the store)
  sendWebSocketCommand: (command: string, sessionId?: string) => void;
  sendWebSocketTyping: (isTyping: boolean, sessionId?: string) => void;

  // Utilities
  getLastMessage: () => DisplayMessage | null;
  getMessageById: (id: string) => DisplayMessage | null;
  getUnreadCount: () => number;
  markAllAsRead: () => void;
}

const defaultSuggestions: CommandSuggestion[] = [
  {
    text: "What's my account balance?",
    description: "Check current balance across all accounts",
    category: 'ACCOUNT',
    icon: '💰'
  },
  {
    text: "Show me recent transactions",
    description: "View latest transaction activity",
    category: 'TRANSACTION',
    icon: '📊'
  },
  {
    text: "Create a new invoice",
    description: "Generate an invoice for a client",
    category: 'INVOICE',
    icon: '📄'
  },
  {
    text: "Help me categorize expenses",
    description: "Review and categorize pending transactions",
    category: 'TRANSACTION',
    icon: '🏷️'
  },
  {
    text: "Show my profit this month",
    description: "View monthly financial summary",
    category: 'REPORT',
    icon: '📈'
  },
  {
    text: "What can you help me with?",
    description: "Learn about available features",
    category: 'GENERAL',
    icon: '❓'
  }
];

export const useConversationStore = create<ConversationStore>()(
  devtools(
    subscribeWithSelector((set, get) => ({
      // Initial state
      messages: [],
      isConnected: false,
      isConnecting: false,
      connectionError: null,
      currentSessionId: null,
      isTyping: false,
      awaitingResponse: false,
      lastActivity: null,
      connectionState: 'DISCONNECTED',
      reconnectAttempts: 0,
      stompClient: null,

      // Actions
      addMessage: (message: DisplayMessage) => {
        set((state) => ({
          messages: [...state.messages, message],
          lastActivity: new Date(),
        }));
      },

      updateMessage: (messageId: string, updates: Partial<DisplayMessage>) => {
        set((state) => ({
          messages: state.messages.map((msg) =>
            msg.id === messageId ? { ...msg, ...updates } : msg
          ),
        }));
      },

      removeMessage: (messageId: string) => {
        set((state) => ({
          messages: state.messages.filter((msg) => msg.id !== messageId),
        }));
      },

      clearMessages: () => {
        set({
          messages: [],
          lastActivity: new Date(),
        });
      },

      // WebSocket actions
      setConnectionState: (connectionState: ConnectionState) => {
        set({
          connectionState,
          isConnected: connectionState === 'CONNECTED',
          isConnecting: connectionState === 'CONNECTING' || connectionState === 'RECONNECTING',
        });
      },

      setConnectionError: (error: string | null) => {
        set({ connectionError: error });
      },

      incrementReconnectAttempts: () => {
        set((state) => ({
          reconnectAttempts: state.reconnectAttempts + 1,
        }));
      },

      resetReconnectAttempts: () => {
        set({ reconnectAttempts: 0 });
      },

      // WebSocket connection management
      connect: () => {
        const state = get();

        console.log('🔄 ConversationStore - Starting WebSocket connection...');

        // Don't connect if already connected or connecting
        if (state.stompClient?.connected || state.isConnecting) {
          console.log('⚠️ Already connected or connecting, skipping...');
          return;
        }

        // Get auth token
        const authState = useAuthStore.getState();
        if (!authState.isAuthenticated || !authState.token) {
          console.error('❌ No auth token found for WebSocket connection');
          set({ connectionError: 'Authentication required' });
          return;
        }

        console.log('✅ Auth token found, establishing WebSocket connection...');

        // Set connecting state
        set({
          isConnecting: true,
          connectionState: 'CONNECTING',
          connectionError: null
        });

        // Create WebSocket connection
        const socket = new SockJS(`${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080'}/ws/conversation`);
        const client = new Client({
          webSocketFactory: () => socket,
          connectHeaders: {
            Authorization: `Bearer ${authState.token}`,
            'X-Auth-Token': authState.token,
            token: authState.token
          },
          debug: (str) => {
            console.log('🔧 STOMP Debug:', str);
          },
          onConnect: (frame) => {
            console.log('🎉 WebSocket connected successfully!', frame);

            // Update connection state
            set({
              isConnected: true,
              isConnecting: false,
              connectionState: 'CONNECTED',
              connectionError: null,
              reconnectAttempts: 0,
              stompClient: client
            });

            // Subscribe to conversation responses
            client.subscribe('/user/queue/conversation', (message: IMessage) => {
              console.log('📨 Received WebSocket message:', message.body);
              try {
                const wsMessage: WebSocketMessage = JSON.parse(message.body);
                get().processWebSocketMessage(wsMessage);

                // Reset awaiting response when message is complete
                if (wsMessage.isComplete) {
                  set({ awaitingResponse: false });
                }
              } catch (error) {
                console.error('❌ Error parsing WebSocket message:', error);
              }
            });

            // Subscribe to error messages
            client.subscribe('/user/queue/errors', (error: IMessage) => {
              console.error('❌ WebSocket error received:', error.body);
              set({
                connectionError: error.body,
                awaitingResponse: false
              });
            });
          },
          onStompError: (frame) => {
            console.error('❌ STOMP error:', frame);
            set({
              isConnected: false,
              isConnecting: false,
              connectionState: 'DISCONNECTED',
              connectionError: frame.body || 'Connection error',
              stompClient: null
            });
          },
          onWebSocketError: (error) => {
            console.error('❌ WebSocket error:', error);
            set({
              isConnected: false,
              isConnecting: false,
              connectionState: 'DISCONNECTED',
              connectionError: 'WebSocket connection failed',
              stompClient: null
            });
          },
          onDisconnect: () => {
            console.log('🔌 WebSocket disconnected');
            set({
              isConnected: false,
              isConnecting: false,
              connectionState: 'DISCONNECTED',
              stompClient: null
            });
          }
        });

        // Activate the client
        client.activate();
      },

      disconnect: () => {
        const state = get();
        console.log('🔌 ConversationStore - Disconnecting WebSocket...');

        if (state.stompClient) {
          state.stompClient.deactivate();
        }

        set({
          isConnected: false,
          isConnecting: false,
          connectionState: 'DISCONNECTED',
          stompClient: null,
          connectionError: null
        });
      },

      // Typing and activity
      setTyping: (isTyping: boolean) => {
        set({ isTyping });
      },

      setAwaitingResponse: (awaiting: boolean) => {
        set({ awaitingResponse: awaiting });
      },

      updateLastActivity: () => {
        set({ lastActivity: new Date() });
      },

      // Session management
      setSessionId: (sessionId: string | null) => {
        set({ currentSessionId: sessionId });
      },

      generateNewSession: (): string => {
        const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        set({ currentSessionId: sessionId });
        return sessionId;
      },

      // Message processing
      processWebSocketMessage: (wsMessage: WebSocketMessage) => {
        const displayMessage: DisplayMessage = {
          id: wsMessage.messageId || `ws_${Date.now()}_${Math.random()}`,
          content: wsMessage.content,
          role: wsMessage.role,
          type: wsMessage.type,
          timestamp: new Date(wsMessage.timestamp),
          isComplete: wsMessage.isComplete,
          requiresFollowUp: wsMessage.requiresFollowUp,
          followUpPrompt: wsMessage.followUpPrompt,
          intentRecognized: wsMessage.intentRecognized,
          confidenceScore: wsMessage.confidenceScore,
          data: wsMessage.data,
          isLoading: wsMessage.status === 'PROCESSING',
          error: wsMessage.errorMessage,
        };

        // Check if message already exists (for updates)
        const existingMessage = get().getMessageById(displayMessage.id);
        
        if (existingMessage) {
          get().updateMessage(displayMessage.id, displayMessage);
        } else {
          get().addMessage(displayMessage);
        }

        // Update awaiting response state
        // Clear awaiting response for any complete message or error
        if ((wsMessage.role === 'ASSISTANT' && wsMessage.isComplete) || 
            wsMessage.type === 'ERROR' || 
            wsMessage.errorMessage) {
          set({ awaitingResponse: false });
        }
      },

      // WebSocket command methods
      sendWebSocketCommand: (command: string, sessionId?: string) => {
        const state = get();

        console.log('🚀 sendWebSocketCommand called:', {
          command,
          sessionId,
          isConnected: state.isConnected,
          hasClient: !!state.stompClient,
          clientConnected: state.stompClient?.connected
        });

        if (!state.stompClient?.connected) {
          console.error('❌ WebSocket not connected, cannot send command');
          throw new Error('WebSocket not connected');
        }

        try {
          const message = {
            command,
            sessionId: sessionId || state.currentSessionId,
            expectResponse: true,
            saveToHistory: true,
          };

          console.log('📤 Sending WebSocket message:', message);

          state.stompClient.publish({
            destination: '/app/conversation/command',
            body: JSON.stringify(message),
          });

          console.log('✅ WebSocket command sent successfully');
        } catch (error) {
          console.error('❌ Error sending WebSocket command:', error);
          throw error;
        }
      },

      sendWebSocketTyping: (isTyping: boolean, sessionId?: string) => {
        const state = get();

        if (!state.stompClient?.connected) {
          console.warn('⚠️ WebSocket not connected, cannot send typing indicator');
          return;
        }

        try {
          const message = {
            isTyping,
            sessionId: sessionId || state.currentSessionId,
          };

          state.stompClient.publish({
            destination: '/app/conversation/typing',
            body: JSON.stringify(message),
          });

          console.log('📝 Typing indicator sent:', isTyping);
        } catch (error) {
          console.error('❌ Error sending typing indicator:', error);
        }
      },

      addUserMessage: (content: string): DisplayMessage => {
        const message: DisplayMessage = {
          id: `user_${Date.now()}_${Math.random()}`,
          content,
          role: 'USER',
          type: 'COMMAND',
          timestamp: new Date(),
          isComplete: true,
        };

        // Add user message and set awaiting response
        set((state) => ({
          messages: [...state.messages, message],
          awaitingResponse: true,
          lastActivity: new Date(),
        }));

        // Try to send command via WebSocket first
        const currentState = get();

        console.log('🔍 addUserMessage - Attempting WebSocket command:', {
          command: content,
          isConnected: currentState.isConnected,
          connectionState: currentState.connectionState,
          hasStompClient: !!currentState.stompClient,
          clientConnected: currentState.stompClient?.connected
        });

        if (currentState.isConnected && currentState.stompClient?.connected) {
          console.log('🚀 Sending command via WebSocket...');
          try {
            // Use the store's own sendWebSocketCommand method
            get().sendWebSocketCommand(content, currentState.currentSessionId || undefined);
            console.log('✅ WebSocket command sent successfully');
            // Response will come through WebSocket message processing
            return message;
          } catch (error) {
            console.error('❌ WebSocket command failed:', error);
            console.log('⚠️ Falling back to HTTP API due to WebSocket error');
          }
        } else {
          console.log('⚠️ WebSocket not connected, using HTTP API fallback');
          console.log('Debug info:', {
            isConnected: currentState.isConnected,
            connectionState: currentState.connectionState,
            hasStompClient: !!currentState.stompClient,
            clientConnected: currentState.stompClient?.connected
          });

          // Fallback to HTTP API (should be minimal usage)
          conversationalCommandService.processCommand(content, 'user-id')
            .then(result => {
              if (result.success && result.response) {
                // Create AI response message
                const aiMessage: DisplayMessage = {
                  id: `ai_${Date.now()}_${Math.random()}`,
                  content: result.response,
                  role: 'ASSISTANT',
                  type: 'RESPONSE',
                  timestamp: new Date(),
                  isComplete: true,
                  intentRecognized: result.intent,
                  confidenceScore: result.confidence,
                };

                // Add AI message and reset awaiting response
                set((state) => ({
                  messages: [...state.messages, aiMessage],
                  awaitingResponse: false,
                  lastActivity: new Date(),
                }));
              } else {
                // Reset awaiting response on error
                set({ awaitingResponse: false });
              }
            })
            .catch(error => {
              console.error('Error processing command via HTTP:', error);
              set({ awaitingResponse: false });
            });
        }

        return message;
      },

      // Suggestions and commands
      getCommandSuggestions: (): CommandSuggestion[] => {
        const state = get();
        
        // Return contextual suggestions based on conversation state
        if (state.messages.length === 0) {
          return defaultSuggestions;
        }

        // Could add logic here to provide contextual suggestions
        // based on the last message or conversation history
        return defaultSuggestions;
      },

      // Utilities
      getLastMessage: (): DisplayMessage | null => {
        const messages = get().messages;
        return messages.length > 0 ? messages[messages.length - 1] : null;
      },

      getMessageById: (id: string): DisplayMessage | null => {
        return get().messages.find((msg) => msg.id === id) || null;
      },

      getUnreadCount: (): number => {
        // For now, return 0. Could implement read/unread tracking later
        return 0;
      },

      markAllAsRead: () => {
        // Placeholder for read/unread functionality
      },
    })),
    {
      name: 'conversation-store',
    }
  )
);

// Selectors for common use cases
export const conversationSelectors = {
  isConnected: (state: ConversationStore) => state.isConnected,
  isConnecting: (state: ConversationStore) => state.isConnecting,
  hasError: (state: ConversationStore) => !!state.connectionError,
  hasMessages: (state: ConversationStore) => state.messages.length > 0,
  isAwaitingResponse: (state: ConversationStore) => state.awaitingResponse,
  lastMessage: (state: ConversationStore) => state.getLastMessage(),
  messageCount: (state: ConversationStore) => state.messages.length,
  canSendMessage: (state: ConversationStore) => 
    state.isConnected && !state.awaitingResponse,
  connectionStatus: (state: ConversationStore) => ({
    state: state.connectionState,
    error: state.connectionError,
    attempts: state.reconnectAttempts,
  }),
};
