package com.intellifin.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * DTO for transaction categorization responses
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CategorizationResponseDto {
    
    private Boolean success;
    private TransactionDto transaction;
    private AILearningDto aiLearning;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AILearningDto {
        private String feedback; // 'positive' or 'negative'
        private BigDecimal confidence;
    }
}
