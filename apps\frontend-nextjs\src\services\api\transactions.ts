import { apiClient } from './client';
import {
  Transaction,
  CreateTransactionRequest,
  UpdateTransactionRequest,
  TransactionQueryParams,
  PaginatedResponse,
  Category,
  CreateCategoryRequest,
  CategorizationRequest,
  CategorizationResponse,
  BulkCategorizationRequest,
  BulkCategorizationResponse
} from '@/types/api';

/**
 * Transactions API Service
 * Handles all transaction-related API calls
 */
export class TransactionsService {
  /**
   * Get paginated list of transactions
   */
  static async getTransactions(params?: TransactionQueryParams): Promise<PaginatedResponse<Transaction>> {
    const queryString = params ? new URLSearchParams(params as any).toString() : '';
    const url = `/api/v1/transactions${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.get<PaginatedResponse<Transaction>>(url);
    return response.data;
  }

  /**
   * Get single transaction by ID
   */
  static async getTransaction(id: string): Promise<Transaction> {
    const response = await apiClient.get<Transaction>(`/api/v1/transactions/${id}`);
    return response.data;
  }

  /**
   * Create new transaction
   */
  static async createTransaction(transaction: CreateTransactionRequest): Promise<Transaction> {
    const response = await apiClient.post<Transaction>('/api/v1/transactions', transaction);
    return response.data;
  }

  /**
   * Update existing transaction
   */
  static async updateTransaction(id: string, updates: UpdateTransactionRequest): Promise<Transaction> {
    const response = await apiClient.put<Transaction>(`/api/v1/transactions/${id}`, updates);
    return response.data;
  }

  /**
   * Delete transaction
   */
  static async deleteTransaction(id: string): Promise<void> {
    await apiClient.delete(`/api/v1/transactions/${id}`);
  }

  /**
   * Bulk update transactions (e.g., categorize multiple)
   */
  static async bulkUpdateTransactions(
    transactionIds: string[],
    updates: UpdateTransactionRequest
  ): Promise<Transaction[]> {
    const response = await apiClient.patch<Transaction[]>('/api/v1/transactions/bulk', {
      transactionIds,
      updates
    });
    return response.data;
  }

  /**
   * Categorize transaction (accept/reject AI suggestion or manual categorization)
   */
  static async categorizeTransaction(
    id: string,
    request: CategorizationRequest
  ): Promise<CategorizationResponse> {
    const response = await apiClient.put<CategorizationResponse>(
      `/api/v1/transactions/${id}/categorize`,
      request
    );
    return response.data;
  }

  /**
   * Bulk categorize transactions
   */
  static async bulkCategorizeTransactions(
    request: BulkCategorizationRequest
  ): Promise<BulkCategorizationResponse> {
    const response = await apiClient.post<BulkCategorizationResponse>(
      '/api/v1/transactions/bulk-categorize',
      request
    );
    return response.data;
  }

  /**
   * Get transactions pending categorization
   */
  static async getPendingCategorizationTransactions(params?: {
    page?: number;
    limit?: number;
  }): Promise<PaginatedResponse<Transaction>> {
    const queryString = params ? new URLSearchParams(params as any).toString() : '';
    const url = `/api/v1/transactions/pending-categorization${queryString ? `?${queryString}` : ''}`;

    const response = await apiClient.get<PaginatedResponse<Transaction>>(url);
    return response.data;
  }

  /**
   * Request AI categorization for a transaction
   */
  static async requestAICategorization(id: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await apiClient.post(`/api/v1/transactions/${id}/request-categorization`);
    return response.data;t<{
      suggestedCategory: Category;
      confidence: number;
      reasoning: string;
    }>(`/api/v1/transactions/${id}/categorize`);
    return response.data;
  }

  /**
   * Approve AI categorization
   */
  static async approveCategorization(id: string, categoryId: string): Promise<Transaction> {
    const response = await apiClient.post<Transaction>(`/api/v1/transactions/${id}/approve-category`, {
      categoryId
    });
    return response.data;
  }

  /**
   * Get transaction statistics for a period
   */
  static async getTransactionStats(params?: {
    startDate?: string;
    endDate?: string;
    accountId?: string;
  }): Promise<{
    totalIncome: number;
    totalExpenses: number;
    netAmount: number;
    transactionCount: number;
    averageTransaction: number;
    categoryBreakdown: Array<{
      categoryId: string;
      categoryName: string;
      amount: number;
      count: number;
    }>;
  }> {
    const queryString = params ? new URLSearchParams(params as any).toString() : '';
    const url = `/api/v1/transactions/stats${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.get(url);
    return response.data;
  }

  /**
   * Export transactions to CSV/Excel
   */
  static async exportTransactions(params?: TransactionQueryParams & {
    format?: 'csv' | 'excel';
  }): Promise<Blob> {
    const queryString = params ? new URLSearchParams(params as any).toString() : '';
    const url = `/api/v1/transactions/export${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.axios.get(url, {
      responseType: 'blob'
    });
    return response.data;
  }

  /**
   * Import transactions from file
   */
  static async importTransactions(file: File, accountId: string): Promise<{
    imported: number;
    skipped: number;
    errors: Array<{ row: number; error: string }>;
  }> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('accountId', accountId);

    const response = await apiClient.axios.post('/api/v1/transactions/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data.data;
  }

  /**
   * Search transactions
   */
  static async searchTransactions(query: string, params?: {
    limit?: number;
    accountId?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<Transaction[]> {
    const searchParams = new URLSearchParams({
      q: query,
      ...params
    } as any);
    
    const response = await apiClient.get<Transaction[]>(`/api/v1/transactions/search?${searchParams}`);
    return response.data;
  }

  /**
   * Get recent transactions (quick access)
   */
  static async getRecentTransactions(limit: number = 10): Promise<Transaction[]> {
    const response = await apiClient.get<Transaction[]>(`/api/v1/transactions/recent?limit=${limit}`);
    return response.data;
  }

  /**
   * Get pending transactions (need categorization)
   */
  static async getPendingTransactions(): Promise<Transaction[]> {
    const response = await apiClient.get<Transaction[]>('/api/v1/transactions/pending');
    return response.data;
  }

  /**
   * Mark transaction as reconciled
   */
  static async reconcileTransaction(id: string): Promise<Transaction> {
    const response = await apiClient.post<Transaction>(`/api/v1/transactions/${id}/reconcile`);
    return response.data;
  }

  /**
   * Bulk reconcile transactions
   */
  static async bulkReconcileTransactions(transactionIds: string[]): Promise<Transaction[]> {
    const response = await apiClient.post<Transaction[]>('/api/v1/transactions/bulk-reconcile', {
      transactionIds
    });
    return response.data;
  }
}

/**
 * Categories API Service
 * Handles transaction categories
 */
export class CategoriesService {
  /**
   * Get all categories for current user
   */
  static async getCategories(): Promise<Category[]> {
    const response = await apiClient.get<Category[]>('/api/v1/categories');
    return response.data;
  }

  /**
   * Get categories by type
   */
  static async getCategoriesByType(type: 'INCOME' | 'EXPENSE'): Promise<Category[]> {
    const response = await apiClient.get<Category[]>(`/api/v1/categories?type=${type}`);
    return response.data;
  }

  /**
   * Create new category
   */
  static async createCategory(category: CreateCategoryRequest): Promise<Category> {
    const response = await apiClient.post<Category>('/api/v1/categories', category);
    return response.data;
  }

  /**
   * Update category
   */
  static async updateCategory(id: string, updates: { name?: string }): Promise<Category> {
    const response = await apiClient.put<Category>(`/api/v1/categories/${id}`, updates);
    return response.data;
  }

  /**
   * Delete category
   */
  static async deleteCategory(id: string): Promise<void> {
    await apiClient.delete(`/api/v1/categories/${id}`);
  }

  /**
   * Get category usage statistics
   */
  static async getCategoryStats(categoryId: string, params?: {
    startDate?: string;
    endDate?: string;
  }): Promise<{
    totalAmount: number;
    transactionCount: number;
    averageAmount: number;
    trend: 'INCREASING' | 'DECREASING' | 'STABLE';
  }> {
    const queryString = params ? new URLSearchParams(params as any).toString() : '';
    const url = `/api/v1/categories/${categoryId}/stats${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.get(url);
    return response.data;
  }
}

// Export default instances
export const transactionsService = TransactionsService;
export const categoriesService = CategoriesService;
