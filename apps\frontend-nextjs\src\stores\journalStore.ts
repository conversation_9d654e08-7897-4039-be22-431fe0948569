import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { JournalEntry, JournalEntryValidationResponse, AccountBalance } from '@intellifin/data-models';
import { apiClient } from '@/services/api/client';
import { journalToastService } from '@/services/journalToastService';

interface JournalState {
  // State
  entries: JournalEntry[];
  selectedEntry: JournalEntry | null;
  accountBalances: Record<string, number>;
  loading: boolean;
  error: string | null;
  
  // Actions
  setEntries: (entries: JournalEntry[]) => void;
  setSelectedEntry: (entry: JournalEntry | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // API Actions
  fetchEntries: () => Promise<void>;
  fetchEntry: (id: string) => Promise<JournalEntry | null>;
  createEntry: (entry: Omit<JournalEntry, 'id' | 'entryNumber' | 'createdAt' | 'updatedAt'>) => Promise<JournalEntry>;
  updateEntry: (id: string, entry: Partial<JournalEntry>) => Promise<JournalEntry>;
  deleteEntry: (id: string) => Promise<void>;
  postEntry: (id: string) => Promise<JournalEntry>;
  reverseEntry: (id: string, reason: string) => Promise<JournalEntry>;
  validateEntry: (entry: JournalEntry) => Promise<JournalEntryValidationResponse>;
  fetchAccountBalance: (accountId: string) => Promise<number>;
  
  // Computed
  getDraftEntries: () => JournalEntry[];
  getPostedEntries: () => JournalEntry[];
  getReversedEntries: () => JournalEntry[];
  getUnbalancedEntries: () => JournalEntry[];
}

export const useJournalStore = create<JournalState>()(
  devtools(
    (set, get) => ({
      // Initial state
      entries: [],
      selectedEntry: null,
      accountBalances: {},
      loading: false,
      error: null,

      // Basic setters
      setEntries: (entries) => set({ entries }),
      setSelectedEntry: (entry) => set({ selectedEntry: entry }),
      setLoading: (loading) => set({ loading }),
      setError: (error) => set({ error }),

      // API Actions
      fetchEntries: async () => {
        set({ loading: true, error: null });
        try {
          const response = await apiClient.get('/api/v1/journal/entries');
          const entries = response.data;
          set({ entries, loading: false });

          // Progressive Disclosure: Check for actionable items and show notifications
          const draftEntries = entries.filter((entry: JournalEntry) => entry.status === 'DRAFT');
          const unbalancedEntries = entries.filter((entry: JournalEntry) => !(entry as any).isBalanced);

          // Show draft entries reminder if there are drafts (but not too frequently)
          if (draftEntries.length > 0 && Math.random() < 0.3) {
            setTimeout(() => {
              journalToastService.showDraftEntriesReminder(draftEntries.length);
            }, 2000);
          }

          // Show unbalanced entry alert if any exist
          if (unbalancedEntries.length > 0) {
            const firstUnbalanced = unbalancedEntries[0];
            setTimeout(() => {
              journalToastService.showUnbalancedEntryDetected(
                firstUnbalanced.id,
                firstUnbalanced.entryNumber
              );
            }, 1000);
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch journal entries';
          set({ error: errorMessage, loading: false });
        }
      },

      fetchEntry: async (id: string) => {
        set({ loading: true, error: null });
        try {
          const response = await apiClient.get(`/api/v1/journal/entries/${id}`);
          const entry = response.data;
          set({ selectedEntry: entry, loading: false });
          return entry;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch journal entry';
          set({ error: errorMessage, loading: false });
          return null;
        }
      },

      createEntry: async (entryData) => {
        set({ loading: true, error: null });
        try {
          const response = await apiClient.post('/api/v1/journal/entries', entryData);
          const newEntry = response.data;

          set((state) => ({
            entries: [newEntry, ...state.entries],
            loading: false
          }));

          return newEntry;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to create journal entry';
          set({ error: errorMessage, loading: false });
          throw error;
        }
      },

      updateEntry: async (id: string, entryData) => {
        set({ loading: true, error: null });
        try {
          const response = await apiClient.put(`/api/v1/journal/entries/${id}`, entryData);
          const updatedEntry = response.data;

          set((state) => ({
            entries: state.entries.map(entry =>
              entry.id === id ? updatedEntry : entry
            ),
            selectedEntry: state.selectedEntry?.id === id ? updatedEntry : state.selectedEntry,
            loading: false
          }));

          return updatedEntry;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to update journal entry';
          set({ error: errorMessage, loading: false });
          throw error;
        }
      },

      deleteEntry: async (id: string) => {
        set({ loading: true, error: null });
        try {
          await apiClient.delete(`/api/v1/journal/entries/${id}`);

          set((state) => ({
            entries: state.entries.filter(entry => entry.id !== id),
            selectedEntry: state.selectedEntry?.id === id ? null : state.selectedEntry,
            loading: false
          }));
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to delete journal entry';
          set({ error: errorMessage, loading: false });
          throw error;
        }
      },

      postEntry: async (id: string) => {
        set({ loading: true, error: null });
        try {
          const response = await apiClient.post(`/api/v1/journal/entries/${id}/post`);
          const postedEntry = response.data;

          set((state) => ({
            entries: state.entries.map(entry =>
              entry.id === id ? postedEntry : entry
            ),
            selectedEntry: state.selectedEntry?.id === id ? postedEntry : state.selectedEntry,
            loading: false
          }));

          // Progressive Disclosure: Show success notification with action
          journalToastService.showEntryPosted(postedEntry.entryNumber);

          return postedEntry;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to post journal entry';
          set({ error: errorMessage, loading: false });
          throw error;
        }
      },

      reverseEntry: async (id: string, reason: string) => {
        set({ loading: true, error: null });
        try {
          const response = await apiClient.post(`/api/v1/journal/entries/${id}/reverse`, null, {
            params: { reason }
          });
          const reversalEntry = response.data;

          // Find the original entry to get its number
          const originalEntry = get().entries.find(entry => entry.id === id);

          set((state) => ({
            entries: [
              reversalEntry,
              ...state.entries.map(entry =>
                entry.id === id ? { ...entry, status: 'REVERSED' as const } : entry
              )
            ],
            loading: false
          }));

          // Progressive Disclosure: Show reversal notification with action
          if (originalEntry) {
            journalToastService.showEntryReversed(
              originalEntry.entryNumber,
              reversalEntry.entryNumber
            );
          }

          return reversalEntry;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to reverse journal entry';
          set({ error: errorMessage, loading: false });
          throw error;
        }
      },

      validateEntry: async (entry: JournalEntry) => {
        try {
          const response = await apiClient.post('/api/v1/journal/entries/validate', entry);
          return response.data;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to validate journal entry';
          set({ error: errorMessage });
          throw error;
        }
      },

      fetchAccountBalance: async (accountId: string) => {
        try {
          const response = await apiClient.get(`/api/v1/journal/accounts/${accountId}/balance`);
          const balance = response.data;

          set((state) => ({
            accountBalances: {
              ...state.accountBalances,
              [accountId]: balance
            }
          }));

          return balance;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch account balance';
          set({ error: errorMessage });
          throw error;
        }
      },

      // Computed getters
      getDraftEntries: () => {
        return get().entries.filter(entry => entry.status === 'DRAFT');
      },

      getPostedEntries: () => {
        return get().entries.filter(entry => entry.status === 'POSTED');
      },

      getReversedEntries: () => {
        return get().entries.filter(entry => entry.status === 'REVERSED');
      },

      getUnbalancedEntries: () => {
        return get().entries.filter(entry => {
          const totalDebits = entry.lines?.reduce((sum, line) => sum + (line.debitAmount || 0), 0) || 0;
          const totalCredits = entry.lines?.reduce((sum, line) => sum + (line.creditAmount || 0), 0) || 0;
          return totalDebits !== totalCredits;
        });
      }
    }),
    {
      name: 'journal-store',
      partialize: (state) => ({
        // Only persist non-sensitive data
        accountBalances: state.accountBalances
      })
    }
  )
);
