package com.intellifin.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import jakarta.persistence.*;
import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * Entity representing individual transaction sync events
 */
@Entity
@Table(name = "transaction_sync_events")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionSyncEvent {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "sync_status_id", nullable = false)
    private SyncStatus syncStatus;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "webhook_event_id")
    private WebhookEvent webhookEvent;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "transaction_id")
    private Transaction transaction;

    @Column(name = "external_transaction_id")
    private String externalTransactionId;

    @Enumerated(EnumType.STRING)
    @Column(name = "sync_operation", nullable = false)
    private SyncOperation syncOperation;

    @Enumerated(EnumType.STRING)
    @Column(name = "operation_status", nullable = false)
    private OperationStatus operationStatus;

    @Column(name = "error_details", columnDefinition = "text")
    private String errorDetails;

    @Column(name = "processing_time_ms")
    private Long processingTimeMs;

    @Column(name = "retry_count")
    private Integer retryCount = 0;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false)
    private OffsetDateTime createdAt;

    public enum SyncOperation {
        CREATE,
        UPDATE,
        DELETE,
        CATEGORIZE
    }

    public enum OperationStatus {
        PENDING,
        SUCCESS,
        FAILED,
        SKIPPED
    }

    /**
     * Mark operation as successful
     */
    public void markAsSuccessful(Long processingTimeMs) {
        this.operationStatus = OperationStatus.SUCCESS;
        this.processingTimeMs = processingTimeMs;
        this.errorDetails = null;
    }

    /**
     * Mark operation as failed
     */
    public void markAsFailed(String errorDetails) {
        this.operationStatus = OperationStatus.FAILED;
        this.errorDetails = errorDetails;
        this.retryCount = this.retryCount != null ? this.retryCount + 1 : 1;
    }

    /**
     * Check if operation can be retried
     */
    public boolean canRetry() {
        return this.retryCount != null && this.retryCount < 3 && 
               this.operationStatus == OperationStatus.FAILED;
    }
}
