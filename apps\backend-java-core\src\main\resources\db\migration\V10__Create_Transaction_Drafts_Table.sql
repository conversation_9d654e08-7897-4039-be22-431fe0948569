-- V10__Create_Transaction_Drafts_Table.sql
-- Create table for saving incomplete manual transaction entries

-- Transaction drafts table for saving incomplete manual entries
CREATE TABLE transaction_drafts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    draft_name <PERSON><PERSON><PERSON><PERSON>(255), -- Optional name for the draft
    description VARCHAR(255) NOT NULL,
    amount DECIMAL(15,2) NOT NULL CHECK (amount > 0),
    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('INCOME', 'EXPENSE')),
    transaction_date DATE NOT NULL,
    category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    notes TEXT,
    suggested_category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    ai_confidence DECIMAL(3,2), -- AI confidence score (0.00 to 1.00)
    ai_explanation TEXT, -- AI explanation for suggestion
    form_data JSONB, -- Store complete form state as JSO<PERSON>
    is_auto_saved BOOLEAN DEFAULT true, -- Whether this was auto-saved or manually saved
    expires_at TIMESTAMPTZ, -- When this draft expires (optional)
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Enhanced transactions table to track manual entry source
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS entry_source VARCHAR(20) DEFAULT 'EXTERNAL' 
    CHECK (entry_source IN ('EXTERNAL', 'MANUAL', 'WEBHOOK', 'IMPORT'));

ALTER TABLE transactions ADD COLUMN IF NOT EXISTS draft_id UUID REFERENCES transaction_drafts(id) ON DELETE SET NULL;

-- Create indexes for performance
CREATE INDEX idx_transaction_drafts_user_id ON transaction_drafts(user_id);
CREATE INDEX idx_transaction_drafts_created_at ON transaction_drafts(created_at);
CREATE INDEX idx_transaction_drafts_updated_at ON transaction_drafts(updated_at);
CREATE INDEX idx_transaction_drafts_expires_at ON transaction_drafts(expires_at);
CREATE INDEX idx_transaction_drafts_category_id ON transaction_drafts(category_id);
CREATE INDEX idx_transaction_drafts_suggested_category_id ON transaction_drafts(suggested_category_id);

CREATE INDEX idx_transactions_entry_source ON transactions(entry_source);
CREATE INDEX idx_transactions_draft_id ON transactions(draft_id);

-- Create trigger to automatically update updated_at
CREATE TRIGGER trigger_transaction_drafts_updated_at
    BEFORE UPDATE ON transaction_drafts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to clean up expired drafts
CREATE OR REPLACE FUNCTION cleanup_expired_drafts()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM transaction_drafts 
    WHERE expires_at IS NOT NULL AND expires_at < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to auto-expire old drafts (30 days)
CREATE OR REPLACE FUNCTION auto_expire_old_drafts()
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER;
BEGIN
    UPDATE transaction_drafts 
    SET expires_at = NOW() + INTERVAL '7 days'
    WHERE expires_at IS NULL 
      AND created_at < NOW() - INTERVAL '30 days'
      AND is_auto_saved = true;
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql;
