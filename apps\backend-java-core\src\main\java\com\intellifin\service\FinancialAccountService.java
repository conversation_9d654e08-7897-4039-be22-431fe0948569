package com.intellifin.service;

import com.intellifin.dto.financial.*;
import com.intellifin.exception.ValidationException;
import com.intellifin.model.FinancialAccount;
import com.intellifin.model.User;
import com.intellifin.repository.FinancialAccountRepository;
import com.intellifin.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class FinancialAccountService {

    private final FinancialAccountRepository financialAccountRepository;
    private final UserRepository userRepository;
    private final MTNIntegrationService mtnIntegrationService;
    private final DocumentUploadService documentUploadService;

    /**
     * Get all financial accounts for a user
     */
    public List<MTNConnectionStatusDto> getUserFinancialAccounts(UUID userId) {
        log.debug("Getting financial accounts for user: {}", userId);
        
        List<FinancialAccount> accounts = financialAccountRepository.findByUserIdOrderByCreatedAtDesc(userId);
        
        return accounts.stream()
                .map(this::mapToConnectionStatusDto)
                .collect(Collectors.toList());
    }

    /**
     * Get MTN Mobile Money accounts for a user
     */
    public List<MTNConnectionStatusDto> getUserMTNAccounts(UUID userId) {
        log.debug("Getting MTN accounts for user: {}", userId);
        
        List<FinancialAccount> mtnAccounts = financialAccountRepository.findMTNAccountsByUserId(userId);
        
        return mtnAccounts.stream()
                .map(this::mapToConnectionStatusDto)
                .collect(Collectors.toList());
    }

    /**
     * Get financial account status
     */
    public MTNConnectionStatusDto getAccountStatus(UUID accountId) {
        log.debug("Getting status for account: {}", accountId);
        
        FinancialAccount account = financialAccountRepository.findById(accountId)
                .orElseThrow(() -> new ValidationException("Account not found"));
        
        return mapToConnectionStatusDto(account);
    }

    /**
     * Initiate MTN connection
     */
    @Transactional
    public MTNOAuthResponseDto initiateMTNConnection(MTNConnectionRequestDto request) {
        log.info("Initiating MTN connection for user: {}", request.getUserId());
        
        UUID userId = UUID.fromString(request.getUserId());
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ValidationException("User not found"));

        // Check if account already exists
        if (financialAccountRepository.findByUserIdAndPhoneNumber(userId, request.getPhoneNumber()).isPresent()) {
            throw new ValidationException("MTN account with this phone number already exists");
        }

        // Create financial account
        FinancialAccount account = FinancialAccount.builder()
                .user(user)
                .accountType(FinancialAccount.AccountType.MTN_MOBILE_MONEY)
                .accountName(request.getAccountName())
                .phoneNumber(request.getPhoneNumber())
                .provider("MTN")
                .connectionStatus(FinancialAccount.ConnectionStatus.CONNECTING)
                .build();

        account = financialAccountRepository.save(account);
        log.debug("Created financial account: {}", account.getId());

        // Initiate OAuth flow with MTN
        return mtnIntegrationService.initiateOAuthFlow(account);
    }

    /**
     * Handle MTN OAuth callback
     */
    @Transactional
    public void handleMTNCallback(String code, String state) {
        log.info("Handling MTN OAuth callback with state: {}", state);
        
        mtnIntegrationService.handleOAuthCallback(code, state);
    }

    /**
     * Disconnect financial account
     */
    @Transactional
    public void disconnectAccount(UUID accountId) {
        log.info("Disconnecting account: {}", accountId);
        
        FinancialAccount account = financialAccountRepository.findById(accountId)
                .orElseThrow(() -> new ValidationException("Account not found"));

        if (account.isMTNMobileMoney()) {
            mtnIntegrationService.disconnectAccount(account);
        }

        account.markAsDisconnected();
        financialAccountRepository.save(account);
        
        log.info("Successfully disconnected account: {}", accountId);
    }

    /**
     * Initiate document upload
     */
    @Transactional
    public DocumentUploadResponseDto initiateDocumentUpload(DocumentUploadRequestDto request) {
        log.info("Initiating document upload for user: {}", request.getUserId());
        
        return documentUploadService.initiateUpload(request);
    }

    /**
     * Get document upload status
     */
    public DocumentProcessingStatusDto getDocumentUploadStatus(String uploadId) {
        log.debug("Getting document upload status: {}", uploadId);
        
        return documentUploadService.getUploadStatus(uploadId);
    }

    /**
     * Confirm document upload completion
     */
    @Transactional
    public void confirmDocumentUpload(String uploadId) {
        log.info("Confirming document upload: {}", uploadId);
        
        documentUploadService.confirmUpload(uploadId);
    }

    /**
     * Check if user has any connected accounts
     */
    public boolean hasConnectedAccounts(UUID userId) {
        return financialAccountRepository.hasConnectedAccounts(userId);
    }

    /**
     * Map FinancialAccount to MTNConnectionStatusDto
     */
    private MTNConnectionStatusDto mapToConnectionStatusDto(FinancialAccount account) {
        MTNConnectionStatusDto.AccountInfo accountInfo = null;
        
        if (account.isConnected()) {
            accountInfo = MTNConnectionStatusDto.AccountInfo.builder()
                    .phoneNumber(account.getPhoneNumber())
                    .accountName(account.getAccountName())
                    .balance(account.getBalance())
                    .currency(account.getCurrency())
                    .build();
        }

        return MTNConnectionStatusDto.builder()
                .accountId(account.getId().toString())
                .status(mapConnectionStatus(account.getConnectionStatus()))
                .lastSync(account.getLastSyncAt())
                .errorMessage(account.getErrorMessage())
                .accountInfo(accountInfo)
                .build();
    }

    /**
     * Map internal connection status to DTO status
     */
    private MTNConnectionStatusDto.ConnectionStatus mapConnectionStatus(FinancialAccount.ConnectionStatus status) {
        return switch (status) {
            case CONNECTING -> MTNConnectionStatusDto.ConnectionStatus.CONNECTING;
            case CONNECTED -> MTNConnectionStatusDto.ConnectionStatus.CONNECTED;
            case DISCONNECTED -> MTNConnectionStatusDto.ConnectionStatus.DISCONNECTED;
            case ERROR -> MTNConnectionStatusDto.ConnectionStatus.ERROR;
        };
    }
}
