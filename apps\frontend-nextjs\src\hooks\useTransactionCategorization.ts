import { useState, useCallback } from 'react';
import { useFinancialStore } from '@/stores';
import {
  CategorizationRequest,
  CategorizationResponse,
  BulkCategorizationRequest,
  BulkCategorizationResponse,
  Transaction
} from '@/types/api';
import {
  measureCategorizationPerformance,
  CategorizationOperations
} from '@/services/performance';

interface CategorizationError {
  message: string;
  code?: string;
  details?: any;
}

/**
 * Hook for transaction categorization functionality
 */
export const useTransactionCategorization = () => {
  const { 
    categorizeTransaction, 
    bulkCategorizeTransactions, 
    fetchPendingCategorizationTransactions,
    requestAICategorization,
    transactions,
    isLoading,
    errors 
  } = useFinancialStore();

  const [categorizationLoading, setCategorizationLoading] = useState<Record<string, boolean>>({});
  const [bulkCategorizationLoading, setBulkCategorizationLoading] = useState(false);
  const [categorizationErrors, setCategorizationErrors] = useState<Record<string, CategorizationError>>({});
  const [bulkCategorizationError, setBulkCategorizationError] = useState<CategorizationError | null>(null);

  /**
   * Accept AI categorization suggestion
   */
  const acceptAISuggestion = useCallback(async (
    transactionId: string,
    categoryId: string,
    explanation?: string
  ): Promise<CategorizationResponse> => {
    setCategorizationLoading(prev => ({ ...prev, [transactionId]: true }));
    setCategorizationErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[transactionId];
      return newErrors;
    });

    try {
      const request: CategorizationRequest = {
        transactionId,
        categoryId,
        isAISuggestion: true,
        explanation
      };

      const response = await measureCategorizationPerformance(
        CategorizationOperations.ACCEPT_AI_SUGGESTION,
        () => categorizeTransaction(transactionId, request),
        { transactionId, categoryId }
      );
      return response;
    } catch (error: any) {
      const categorizationError: CategorizationError = {
        message: error.message || 'Failed to accept AI suggestion',
        code: error.code,
        details: error.response?.data
      };

      setCategorizationErrors(prev => ({
        ...prev,
        [transactionId]: categorizationError
      }));

      throw categorizationError;
    } finally {
      setCategorizationLoading(prev => ({ ...prev, [transactionId]: false }));
    }
  }, [categorizeTransaction]);

  /**
   * Reject AI categorization suggestion and set manual category
   */
  const rejectAISuggestion = useCallback(async (
    transactionId: string,
    newCategoryId: string,
    reason?: string
  ): Promise<CategorizationResponse> => {
    setCategorizationLoading(prev => ({ ...prev, [transactionId]: true }));
    
    try {
      const request: CategorizationRequest = {
        transactionId,
        categoryId: newCategoryId,
        isAISuggestion: false,
        explanation: reason
      };
      
      const response = await categorizeTransaction(transactionId, request);
      return response;
    } finally {
      setCategorizationLoading(prev => ({ ...prev, [transactionId]: false }));
    }
  }, [categorizeTransaction]);

  /**
   * Manually categorize a transaction
   */
  const manualCategorize = useCallback(async (
    transactionId: string,
    categoryId: string,
    explanation?: string
  ): Promise<CategorizationResponse> => {
    setCategorizationLoading(prev => ({ ...prev, [transactionId]: true }));
    
    try {
      const request: CategorizationRequest = {
        transactionId,
        categoryId,
        isAISuggestion: false,
        explanation
      };
      
      const response = await categorizeTransaction(transactionId, request);
      return response;
    } finally {
      setCategorizationLoading(prev => ({ ...prev, [transactionId]: false }));
    }
  }, [categorizeTransaction]);

  /**
   * Bulk categorize multiple transactions
   */
  const bulkCategorize = useCallback(async (
    categorizationRequests: CategorizationRequest[]
  ): Promise<BulkCategorizationResponse> => {
    setBulkCategorizationLoading(true);
    setBulkCategorizationError(null);

    try {
      const request: BulkCategorizationRequest = {
        transactions: categorizationRequests
      };

      const response = await bulkCategorizeTransactions(request);
      return response;
    } catch (error: any) {
      const bulkError: CategorizationError = {
        message: error.message || 'Failed to perform bulk categorization',
        code: error.code,
        details: error.response?.data
      };

      setBulkCategorizationError(bulkError);
      throw bulkError;
    } finally {
      setBulkCategorizationLoading(false);
    }
  }, [bulkCategorizeTransactions]);

  /**
   * Request AI categorization for a transaction
   */
  const triggerAICategorization = useCallback(async (
    transactionId: string
  ): Promise<{ success: boolean; message: string }> => {
    setCategorizationLoading(prev => ({ ...prev, [transactionId]: true }));

    try {
      const response = await requestAICategorization(transactionId);
      return response;
    } finally {
      setCategorizationLoading(prev => ({ ...prev, [transactionId]: false }));
    }
  }, [requestAICategorization]);

  /**
   * Get transactions that need categorization
   */
  const getPendingTransactions = useCallback(() => {
    return transactions.filter(t => t.status === 'PENDING_CLASSIFICATION');
  }, [transactions]);

  /**
   * Get transactions with AI suggestions
   */
  const getTransactionsWithAISuggestions = useCallback(() => {
    return transactions.filter(t => t.aiCategoryId && t.aiConfidence);
  }, [transactions]);

  /**
   * Get high confidence AI suggestions
   */
  const getHighConfidenceAISuggestions = useCallback((threshold: number = 0.8) => {
    return transactions.filter(t => 
      t.aiCategoryId && 
      t.aiConfidence && 
      t.aiConfidence >= threshold
    );
  }, [transactions]);

  /**
   * Get low confidence AI suggestions that need review
   */
  const getLowConfidenceAISuggestions = useCallback((threshold: number = 0.6) => {
    return transactions.filter(t => 
      t.aiCategoryId && 
      t.aiConfidence && 
      t.aiConfidence < threshold
    );
  }, [transactions]);

  /**
   * Check if a transaction is being categorized
   */
  const isTransactionBeingCategorized = useCallback((transactionId: string) => {
    return categorizationLoading[transactionId] || false;
  }, [categorizationLoading]);

  /**
   * Get categorization statistics
   */
  const getCategorizationStats = useCallback(() => {
    const total = transactions.length;
    const pending = getPendingTransactions().length;
    const withAISuggestions = getTransactionsWithAISuggestions().length;
    const highConfidence = getHighConfidenceAISuggestions().length;
    const lowConfidence = getLowConfidenceAISuggestions().length;
    const classified = transactions.filter(t => t.status === 'CLASSIFIED').length;
    
    return {
      total,
      pending,
      withAISuggestions,
      highConfidence,
      lowConfidence,
      classified,
      percentageClassified: total > 0 ? Math.round((classified / total) * 100) : 0
    };
  }, [transactions, getPendingTransactions, getTransactionsWithAISuggestions, getHighConfidenceAISuggestions, getLowConfidenceAISuggestions]);

  return {
    // Actions
    acceptAISuggestion,
    rejectAISuggestion,
    manualCategorize,
    bulkCategorize,
    triggerAICategorization,
    fetchPendingCategorizationTransactions,
    
    // Data
    transactions,
    getPendingTransactions,
    getTransactionsWithAISuggestions,
    getHighConfidenceAISuggestions,
    getLowConfidenceAISuggestions,
    getCategorizationStats,
    
    // Loading states
    isTransactionBeingCategorized,
    bulkCategorizationLoading,
    isLoading: isLoading.transactions,

    // Errors
    error: errors.transactions,
    categorizationErrors,
    bulkCategorizationError,

    // Error helpers
    getTransactionError: (transactionId: string) => categorizationErrors[transactionId],
    clearTransactionError: (transactionId: string) => {
      setCategorizationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[transactionId];
        return newErrors;
      });
    },
    clearBulkError: () => setBulkCategorizationError(null)
  };
};
