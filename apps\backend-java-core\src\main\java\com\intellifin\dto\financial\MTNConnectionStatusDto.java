package com.intellifin.dto.financial;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MTNConnectionStatusDto {

    private String accountId;
    private ConnectionStatus status;
    private OffsetDateTime lastSync;
    private String errorMessage;
    private AccountInfo accountInfo;

    public enum ConnectionStatus {
        CONNECTING,
        CONNECTED,
        DISCONNECTED,
        ERROR
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AccountInfo {
        private String phoneNumber;
        private String accountName;
        private BigDecimal balance;
        private String currency;
    }
}
