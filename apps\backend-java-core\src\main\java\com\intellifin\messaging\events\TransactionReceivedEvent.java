package com.intellifin.messaging.events;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * Event published when a transaction is received from a webhook
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionReceivedEvent {

    private UUID transactionId;
    private String externalTransactionId;
    private UUID userId;
    private UUID financialAccountId;
    private UUID webhookEventId;
    private BigDecimal amount;
    private String description;
    private String transactionType;
    private String source;
    private String eventType;
    private OffsetDateTime timestamp;
}
