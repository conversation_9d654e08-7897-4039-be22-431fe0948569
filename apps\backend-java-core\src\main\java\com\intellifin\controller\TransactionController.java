package com.intellifin.controller;

import com.intellifin.dto.*;
import com.intellifin.model.Transaction;
import com.intellifin.service.TransactionService;
import com.intellifin.service.ManualTransactionService;
import com.intellifin.validation.TransactionValidator;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * REST controller for transaction management
 */
@RestController
@RequestMapping("/api/v1/transactions")
@RequiredArgsConstructor
@Slf4j
public class TransactionController {

    private final TransactionService transactionService;
    private final ManualTransactionService manualTransactionService;

    /**
     * Get paginated transactions with filters
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getTransactions(
            @RequestHeader("X-User-ID") UUID userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int limit,
            @RequestParam(required = false) Transaction.TransactionStatus status,
            @RequestParam(required = false) Transaction.TransactionType type,
            @RequestParam(required = false) UUID categoryId,
            @RequestParam(required = false) UUID accountId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false) String search) {
        
        log.info("Getting transactions for user: {} with filters", userId);
        
        try {
            Pageable pageable = PageRequest.of(page, Math.min(limit, 100)); // Max 100 per page
            
            Page<TransactionDto> transactions = transactionService.getTransactions(
                    userId, status, type, categoryId, accountId, startDate, endDate, search, pageable);
            
            Map<String, Object> response = Map.of(
                    "data", transactions.getContent(),
                    "pagination", Map.of(
                            "page", transactions.getNumber(),
                            "totalPages", transactions.getTotalPages(),
                            "total", transactions.getTotalElements(),
                            "hasNext", transactions.hasNext(),
                            "hasPrevious", transactions.hasPrevious()
                    )
            );
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error fetching transactions for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to fetch transactions", "message", e.getMessage()));
        }
    }

    /**
     * Get a single transaction by ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getTransaction(
            @RequestHeader("X-User-ID") UUID userId,
            @PathVariable UUID id) {
        
        log.info("Getting transaction: {} for user: {}", id, userId);
        
        try {
            return transactionService.getTransaction(id, userId)
                    .map(transaction -> ResponseEntity.ok(transaction))
                    .orElse(ResponseEntity.notFound().build());
                    
        } catch (Exception e) {
            log.error("Error fetching transaction: {} for user: {}", id, userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to fetch transaction", "message", e.getMessage()));
        }
    }

    /**
     * Create a new transaction
     */
    @PostMapping
    public ResponseEntity<?> createTransaction(
            @RequestHeader("X-User-ID") UUID userId,
            @Valid @RequestBody TransactionDto transactionDto) {
        
        log.info("Creating transaction for user: {}", userId);
        
        try {
            transactionDto.setUserId(userId);
            TransactionDto createdTransaction = transactionService.createTransaction(transactionDto);
            
            return ResponseEntity.status(HttpStatus.CREATED).body(createdTransaction);
            
        } catch (Exception e) {
            log.error("Error creating transaction for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("error", "Failed to create transaction", "message", e.getMessage()));
        }
    }

    /**
     * Update an existing transaction
     */
    @PutMapping("/{id}")
    public ResponseEntity<?> updateTransaction(
            @RequestHeader("X-User-ID") UUID userId,
            @PathVariable UUID id,
            @Valid @RequestBody TransactionDto updates) {
        
        log.info("Updating transaction: {} for user: {}", id, userId);
        
        try {
            TransactionDto updatedTransaction = transactionService.updateTransaction(id, userId, updates);
            return ResponseEntity.ok(updatedTransaction);
            
        } catch (RuntimeException e) {
            log.error("Error updating transaction: {} for user: {}", id, userId, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("error", "Transaction not found", "message", e.getMessage()));
        } catch (Exception e) {
            log.error("Error updating transaction: {} for user: {}", id, userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to update transaction", "message", e.getMessage()));
        }
    }

    /**
     * Delete a transaction
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteTransaction(
            @RequestHeader("X-User-ID") UUID userId,
            @PathVariable UUID id) {
        
        log.info("Deleting transaction: {} for user: {}", id, userId);
        
        try {
            transactionService.deleteTransaction(id, userId);
            return ResponseEntity.noContent().build();
            
        } catch (RuntimeException e) {
            log.error("Error deleting transaction: {} for user: {}", id, userId, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("error", "Transaction not found", "message", e.getMessage()));
        } catch (Exception e) {
            log.error("Error deleting transaction: {} for user: {}", id, userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to delete transaction", "message", e.getMessage()));
        }
    }

    /**
     * Categorize a transaction (accept/reject AI suggestion or manual categorization)
     */
    @PutMapping("/{id}/categorize")
    public ResponseEntity<?> categorizeTransaction(
            @RequestHeader("X-User-ID") UUID userId,
            @PathVariable UUID id,
            @Valid @RequestBody CategorizationRequestDto request) {
        
        log.info("Categorizing transaction: {} for user: {}", id, userId);
        
        try {
            request.setTransactionId(id);
            CategorizationResponseDto response = transactionService.categorizeTransaction(id, userId, request);
            
            return ResponseEntity.ok(response);
            
        } catch (RuntimeException e) {
            log.error("Error categorizing transaction: {} for user: {}", id, userId, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("error", "Failed to categorize transaction", "message", e.getMessage()));
        } catch (Exception e) {
            log.error("Error categorizing transaction: {} for user: {}", id, userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to categorize transaction", "message", e.getMessage()));
        }
    }

    /**
     * Bulk categorize transactions
     */
    @PostMapping("/bulk-categorize")
    public ResponseEntity<?> bulkCategorizeTransactions(
            @RequestHeader("X-User-ID") UUID userId,
            @Valid @RequestBody BulkCategorizationRequestDto request) {
        
        log.info("Bulk categorizing {} transactions for user: {}", request.getTransactions().size(), userId);
        
        try {
            BulkCategorizationResponseDto response = transactionService.bulkCategorizeTransactions(userId, request);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error in bulk categorization for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to bulk categorize transactions", "message", e.getMessage()));
        }
    }

    /**
     * Get recent transactions
     */
    @GetMapping("/recent")
    public ResponseEntity<?> getRecentTransactions(
            @RequestHeader("X-User-ID") UUID userId,
            @RequestParam(defaultValue = "10") int limit) {
        
        log.info("Getting recent transactions for user: {}", userId);
        
        try {
            Pageable pageable = PageRequest.of(0, Math.min(limit, 50)); // Max 50 recent
            
            Page<TransactionDto> transactions = transactionService.getTransactions(
                    userId, null, null, null, null, null, null, null, pageable);
            
            return ResponseEntity.ok(Map.of(
                    "data", transactions.getContent(),
                    "total", transactions.getTotalElements()
            ));
            
        } catch (Exception e) {
            log.error("Error fetching recent transactions for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to fetch recent transactions", "message", e.getMessage()));
        }
    }

    /**
     * Get transactions pending categorization
     */
    @GetMapping("/pending-categorization")
    public ResponseEntity<?> getPendingCategorizationTransactions(
            @RequestHeader("X-User-ID") UUID userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int limit) {
        
        log.info("Getting pending categorization transactions for user: {}", userId);
        
        try {
            Pageable pageable = PageRequest.of(page, Math.min(limit, 100));
            
            Page<TransactionDto> transactions = transactionService.getTransactions(
                    userId, Transaction.TransactionStatus.PENDING_CLASSIFICATION, 
                    null, null, null, null, null, null, pageable);
            
            Map<String, Object> response = Map.of(
                    "data", transactions.getContent(),
                    "pagination", Map.of(
                            "page", transactions.getNumber(),
                            "totalPages", transactions.getTotalPages(),
                            "total", transactions.getTotalElements()
                    )
            );
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error fetching pending categorization transactions for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to fetch pending transactions", "message", e.getMessage()));
        }
    }

    // ===== MANUAL TRANSACTION ENDPOINTS =====

    /**
     * Create a manual transaction
     */
    @PostMapping("/manual")
    public ResponseEntity<?> createManualTransaction(
            @RequestHeader("X-User-ID") UUID userId,
            @Valid @RequestBody ManualTransactionDto transactionDto) {

        log.info("Creating manual transaction for user: {}", userId);

        try {
            transactionDto.setUserId(userId);
            ManualTransactionDto createdTransaction = manualTransactionService.createManualTransaction(transactionDto);

            return ResponseEntity.status(HttpStatus.CREATED).body(createdTransaction);

        } catch (IllegalArgumentException e) {
            log.warn("Validation error creating manual transaction for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("error", "Validation failed", "message", e.getMessage()));
        } catch (Exception e) {
            log.error("Error creating manual transaction for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to create manual transaction", "message", e.getMessage()));
        }
    }

    /**
     * Validate transaction data
     */
    @PostMapping("/validate")
    public ResponseEntity<?> validateTransaction(
            @RequestHeader("X-User-ID") UUID userId,
            @Valid @RequestBody ManualTransactionDto transactionDto) {

        log.debug("Validating transaction data for user: {}", userId);

        try {
            transactionDto.setUserId(userId);
            TransactionValidator.ValidationResult result = manualTransactionService.validateTransaction(transactionDto);

            return ResponseEntity.ok(Map.of(
                    "isValid", result.isValid(),
                    "errors", result.getErrors(),
                    "warnings", result.getWarnings()
            ));

        } catch (Exception e) {
            log.error("Error validating transaction for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to validate transaction", "message", e.getMessage()));
        }
    }

    // ===== DRAFT ENDPOINTS =====

    /**
     * Save transaction draft
     */
    @PostMapping("/drafts")
    public ResponseEntity<?> saveDraft(
            @RequestHeader("X-User-ID") UUID userId,
            @Valid @RequestBody TransactionDraftDto draftDto) {

        log.info("Saving transaction draft for user: {}", userId);

        try {
            draftDto.setUserId(userId);
            TransactionDraftDto savedDraft = manualTransactionService.saveDraft(draftDto);

            return ResponseEntity.status(HttpStatus.CREATED).body(savedDraft);

        } catch (Exception e) {
            log.error("Error saving draft for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to save draft", "message", e.getMessage()));
        }
    }

    /**
     * Auto-save draft (for real-time saving)
     */
    @PostMapping("/drafts/auto-save")
    public ResponseEntity<?> autoSaveDraft(
            @RequestHeader("X-User-ID") UUID userId,
            @Valid @RequestBody TransactionDraftDto draftDto) {

        log.debug("Auto-saving draft for user: {}", userId);

        try {
            TransactionDraftDto savedDraft = manualTransactionService.autoSaveDraft(userId, draftDto);

            return ResponseEntity.ok(savedDraft);

        } catch (Exception e) {
            log.error("Error auto-saving draft for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to auto-save draft", "message", e.getMessage()));
        }
    }

    /**
     * Get user's drafts
     */
    @GetMapping("/drafts")
    public ResponseEntity<?> getDrafts(
            @RequestHeader("X-User-ID") UUID userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        log.debug("Getting drafts for user: {}", userId);

        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<TransactionDraftDto> drafts = manualTransactionService.getDrafts(userId, pageable);

            return ResponseEntity.ok(drafts);

        } catch (Exception e) {
            log.error("Error getting drafts for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to get drafts", "message", e.getMessage()));
        }
    }

    /**
     * Get specific draft
     */
    @GetMapping("/drafts/{draftId}")
    public ResponseEntity<?> getDraft(
            @RequestHeader("X-User-ID") UUID userId,
            @PathVariable UUID draftId) {

        log.debug("Getting draft: {} for user: {}", draftId, userId);

        try {
            Optional<TransactionDraftDto> draft = manualTransactionService.getDraft(draftId, userId);

            if (draft.isPresent()) {
                return ResponseEntity.ok(draft.get());
            } else {
                return ResponseEntity.notFound().build();
            }

        } catch (Exception e) {
            log.error("Error getting draft: {} for user: {}", draftId, userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to get draft", "message", e.getMessage()));
        }
    }

    /**
     * Delete draft
     */
    @DeleteMapping("/drafts/{draftId}")
    public ResponseEntity<?> deleteDraft(
            @RequestHeader("X-User-ID") UUID userId,
            @PathVariable UUID draftId) {

        log.info("Deleting draft: {} for user: {}", draftId, userId);

        try {
            manualTransactionService.deleteDraft(draftId, userId);

            return ResponseEntity.noContent().build();

        } catch (IllegalArgumentException e) {
            log.warn("Draft not found or access denied: {} for user: {}", draftId, userId);
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Error deleting draft: {} for user: {}", draftId, userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to delete draft", "message", e.getMessage()));
        }
    }

    /**
     * Convert draft to transaction
     */
    @PostMapping("/drafts/{draftId}/convert")
    public ResponseEntity<?> convertDraftToTransaction(
            @RequestHeader("X-User-ID") UUID userId,
            @PathVariable UUID draftId) {

        log.info("Converting draft to transaction: {} for user: {}", draftId, userId);

        try {
            ManualTransactionDto transaction = manualTransactionService.convertDraftToTransaction(draftId, userId);

            return ResponseEntity.status(HttpStatus.CREATED).body(transaction);

        } catch (IllegalArgumentException e) {
            log.warn("Error converting draft: {} for user: {}", draftId, userId, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("error", "Conversion failed", "message", e.getMessage()));
        } catch (Exception e) {
            log.error("Error converting draft: {} for user: {}", draftId, userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to convert draft", "message", e.getMessage()));
        }
    }
}
