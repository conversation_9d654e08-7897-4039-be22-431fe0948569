# Story 5.2: ZRA Invoice Submission

**Epic:** Invoice Management & ZRA Compliance  
**Status:** Ready for Development  
**Priority:** High  
**Story Points:** 12

## User Story

**As a** user  
**I want** to submit invoices to ZRA for compliance  
**So that** my invoices are legally valid

## Acceptance Criteria

- [ ] User can submit invoices to ZRA via conversational interface
- [ ] ZRA submission includes all required compliance fields
- [ ] System handles ZRA API authentication and security
- [ ] Invoice status updates reflect ZRA submission status
- [ ] **Balanced JournalEntry creation** for invoice transactions
- [ ] **Proper Account mapping** through Categories for invoice entries
- [ ] **Audit trail maintenance** for all invoice and journal entry changes
- [ ] **Error handling for unbalanced entries** with clear validation messages
- [ ] ZRA submission failures are handled gracefully with retry mechanisms
- [ ] Compliance validation occurs before submission
- [ ] User receives confirmation of successful submission

## Technical Implementation

### Backend Changes
- `src/main/java/com/intellifin/zra/` - ZRA API integration with Spring Cloud Stream event handling
- `src/main/java/com/intellifin/controller/ZRAController.java` - ZRA submission endpoints
- `src/main/java/com/intellifin/service/ZRASubmissionService.java` - ZRA submission business logic
- `src/main/java/com/intellifin/validation/ZRAComplianceValidator.java` - ZRA compliance validation
- `src/main/java/com/intellifin/dto/ZRASubmissionDto.java` - ZRA submission data transfer objects

### AI Service Changes
- `src/services/zra_compliance.py` - AI-powered compliance checking with MessagingService integration
- `src/services/invoice_validation.py` - Invoice validation for ZRA requirements

### Frontend Changes
- `src/components/invoices/ZRASubmissionDialog.tsx` - ZRA submission interface
- `src/components/invoices/ComplianceStatus.tsx` - Compliance status display
- `src/components/invoices/ZRAStatusTracker.tsx` - Submission status tracking
- `src/hooks/useZRASubmission.ts` - ZRA submission state management

### Database Changes
- `zra_submissions` table for tracking submission history
- `compliance_checks` table for validation results
- Enhanced `invoices` table with ZRA-specific fields
- Indexes for performance on ZRA submission queries

## API Contracts

```typescript
interface ZRAAPI {
  POST /api/v1/zra/submit-invoice: {
    body: { 
      invoiceId: string,
      submissionType: 'ORIGINAL' | 'AMENDMENT' | 'CANCELLATION',
      urgentSubmission?: boolean
    }
    response: { 
      submissionId: string,
      zraReference: string,
      status: 'SUBMITTED' | 'ACCEPTED' | 'REJECTED',
      journalEntries: JournalEntry[]
    }
    errors: {
      400: "Invalid invoice data",
      422: "Compliance validation failed",
      503: "ZRA service unavailable"
    }
  }
  
  GET /api/v1/zra/submission-status/{submissionId}: {
    response: {
      submissionId: string,
      zraReference: string,
      status: 'PENDING' | 'ACCEPTED' | 'REJECTED' | 'CANCELLED',
      submissionDate: string,
      responseDate?: string,
      errorDetails?: ZRAError[]
    }
  }
  
  POST /api/v1/zra/validate-compliance: {
    body: { invoiceId: string }
    response: {
      isCompliant: boolean,
      validationResults: ComplianceCheck[],
      requiredFields: string[],
      warnings: ComplianceWarning[]
    }
  }
}

interface ZRAError {
  code: string;
  message: string;
  field?: string;
  severity: 'ERROR' | 'WARNING';
}

interface ComplianceCheck {
  rule: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  message: string;
  field?: string;
}
```

## ZRA Compliance Requirements

- **Invoice Number:** Must follow ZRA numbering format
- **Tax Information:** VAT calculations must be accurate
- **Business Registration:** Valid ZRA business registration number
- **Customer Details:** Complete customer information for B2B transactions
- **Currency:** Proper currency codes and exchange rates
- **Digital Signature:** Invoice must be digitally signed
- **Timing:** Submission within required timeframes

## Error Handling

- **ZRA API failures:** Retry with exponential backoff via messaging queues
- **Compliance validation errors:** Show detailed field-level validation messages
- **Network connectivity issues:** Queue submissions for retry when connection restored
- **Authentication failures:** Refresh ZRA API tokens automatically
- **Journal entry creation failures:** Handle accounting errors with proper rollback
- **Submission timeouts:** Provide status updates and retry options

## Definition of Done

- [ ] ZRA submission works end-to-end with proper authentication
- [ ] Compliance validation prevents invalid submissions
- [ ] Journal entries are created automatically for submitted invoices
- [ ] Error handling is comprehensive with retry mechanisms
- [ ] Status tracking provides real-time updates
- [ ] Integration with conversational interface works smoothly
- [ ] Performance meets requirements (< 10 seconds for submission)
- [ ] Tests cover submission scenarios, compliance validation, and error handling
- [ ] No breaking changes to existing invoice management system
- [ ] Documentation includes ZRA setup and troubleshooting guide

## Dependencies

- [Story 5.1: Conversational Invoice Creation](story-5.1-invoice-draft-creation.md) - Required for invoice creation
- [Story 2.2: Automated Double-Entry Journal System](../epic-02/story-2.2-double-entry-journal.md) - Required for journal entry creation
- ZRA API access and credentials
- Digital signature infrastructure
- Messaging abstraction layer for retry mechanisms

## Notes

This story implements the critical ZRA compliance functionality that makes invoices legally valid in Zambia. The integration with the journal entry system ensures that submitted invoices are properly recorded in the accounting system.

The submission process must be robust and handle various failure scenarios gracefully, as ZRA compliance is mandatory for business operations.

---

**Related Stories:**
- [Story 5.1: Conversational Invoice Creation](story-5.1-invoice-draft-creation.md)
- [Story 2.2: Automated Double-Entry Journal System](../epic-02/story-2.2-double-entry-journal.md)

**Epic:** [Invoice Management & ZRA Compliance](../../epics-and-stories.md#epic-5-invoice-management--zra-compliance)
