{"data": [{"id": "cat-transport", "userId": null, "name": "Transport", "description": "Transportation expenses including fuel, taxi, bus fares", "type": "EXPENSE", "isSystemDefined": true, "isActive": true, "color": "#3B82F6", "icon": "car", "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z"}, {"id": "cat-food", "userId": null, "name": "Food & Dining", "description": "Restaurant meals, food purchases, dining expenses", "type": "EXPENSE", "isSystemDefined": true, "isActive": true, "color": "#EF4444", "icon": "utensils", "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z"}, {"id": "cat-office", "userId": null, "name": "Office Supplies", "description": "Office equipment, stationery, business supplies", "type": "EXPENSE", "isSystemDefined": true, "isActive": true, "color": "#6B7280", "icon": "briefcase", "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z"}, {"id": "cat-utilities", "userId": null, "name": "Utilities", "description": "Electricity, water, internet, phone bills", "type": "EXPENSE", "isSystemDefined": true, "isActive": true, "color": "#F59E0B", "icon": "zap", "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z"}, {"id": "cat-sales", "userId": null, "name": "Sales Revenue", "description": "Revenue from sales of products or services", "type": "INCOME", "isSystemDefined": true, "isActive": true, "color": "#10B981", "icon": "trending-up", "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z"}, {"id": "cat-consulting", "userId": null, "name": "Consulting", "description": "Consulting and professional service revenue", "type": "INCOME", "isSystemDefined": true, "isActive": true, "color": "#8B5CF6", "icon": "user-check", "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z"}, {"id": "cat-user-1", "userId": "test-user-123", "name": "Business Travel", "description": "Custom category for business-related travel", "type": "EXPENSE", "isSystemDefined": false, "isActive": true, "color": "#EC4899", "icon": "plane", "createdAt": "2024-01-05T10:00:00Z", "updatedAt": "2024-01-05T10:00:00Z"}, {"id": "cat-user-2", "userId": "test-user-123", "name": "Client Entertainment", "description": "Entertainment expenses for client meetings", "type": "EXPENSE", "isSystemDefined": false, "isActive": true, "color": "#F472B6", "icon": "music", "createdAt": "2024-01-06T15:30:00Z", "updatedAt": "2024-01-06T15:30:00Z"}], "total": 8}