'use client';

import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Alert } from '@/components/ui/alert';
import { useMTNConnection } from '@/hooks/useMTNConnection';
import { Smartphone, ArrowRight, AlertCircle, CheckCircle, ExternalLink } from 'lucide-react';

interface MTNConnectionFlowProps {
  userId: string;
  onSuccess: () => void;
  onCancel: () => void;
}

export const MTNConnectionFlow: React.FC<MTNConnectionFlowProps> = ({
  userId,
  onSuccess,
  onCancel
}) => {
  const [step, setStep] = useState<'form' | 'oauth' | 'success'>('form');
  const [formData, setFormData] = useState({
    accountName: '',
    phoneNumber: ''
  });

  const {
    initiateConnection,
    loading,
    error,
    oauthUrl,
    connectionStatus
  } = useMTNConnection();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.accountName.trim() || !formData.phoneNumber.trim()) {
      return;
    }

    const sanitizedPhoneNumber = formData.phoneNumber.replace(/[^0-9+]/g, '');

    const success = await initiateConnection({
      userId,
      accountName: formData.accountName.trim(),
      phoneNumber: sanitizedPhoneNumber
    });

    if (success && oauthUrl) {
      setStep('oauth');
    }
  };

  const handleOAuthRedirect = () => {
    if (oauthUrl) {
      // Open OAuth URL in a popup window
      const popup = window.open(
        oauthUrl,
        'mtn-oauth',
        'width=600,height=700,scrollbars=yes,resizable=yes'
      );

      // Listen for the popup to close or receive a message
      const checkClosed = setInterval(() => {
        if (popup?.closed) {
          clearInterval(checkClosed);
          // Check connection status after popup closes
          setTimeout(() => {
            if (connectionStatus === 'CONNECTED') {
              setStep('success');
            }
          }, 1000);
        }
      }, 1000);

      // Listen for messages from the popup
      const messageListener = (event: MessageEvent) => {
        if (event.origin !== window.location.origin) return;
        
        if (event.data.type === 'MTN_OAUTH_SUCCESS') {
          popup?.close();
          setStep('success');
          window.removeEventListener('message', messageListener);
        } else if (event.data.type === 'MTN_OAUTH_ERROR') {
          popup?.close();
          window.removeEventListener('message', messageListener);
        }
      };

      window.addEventListener('message', messageListener);
    }
  };

  const handleSuccess = () => {
    onSuccess();
  };

  const formatPhoneNumber = (value: string) => {
    // Remove all non-digits
    const digits = value.replace(/\D/g, '');
    
    // Format as +260 XX XXX XXXX for Zambian numbers
    if (digits.startsWith('260')) {
      const formatted = digits.replace(/(\d{3})(\d{2})(\d{3})(\d{4})/, '+$1 $2 $3 $4');
      return formatted;
    } else if (digits.startsWith('0')) {
      // Convert local format (0XX) to international (+260XX)
      const international = '260' + digits.substring(1);
      return formatPhoneNumber(international);
    }
    
    return value;
  };

  const handlePhoneNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhoneNumber(e.target.value);
    setFormData(prev => ({ ...prev, phoneNumber: formatted }));
  };

  if (step === 'success') {
    return (
      <Card className="p-6 max-w-md mx-auto">
        <div className="text-center">
          <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Connection Successful!</h3>
          <p className="text-gray-600 mb-6">
            Your MTN Mobile Money account has been successfully connected.
          </p>
          <Button onClick={handleSuccess} className="w-full">
            Continue
          </Button>
        </div>
      </Card>
    );
  }

  if (step === 'oauth') {
    return (
      <Card className="p-6 max-w-md mx-auto">
        <div className="text-center">
          <Smartphone className="h-12 w-12 text-blue-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Authorize Connection</h3>
          <p className="text-gray-600 mb-6">
            Click the button below to securely authorize IntelliFin to access your MTN Mobile Money account.
          </p>
          
          {error && (
            <Alert className="mb-4 border-red-200 bg-red-50">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <div className="text-red-800">{error}</div>
            </Alert>
          )}

          <div className="space-y-3">
            <Button 
              onClick={handleOAuthRedirect}
              disabled={!oauthUrl || loading}
              className="w-full"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              Authorize with MTN
            </Button>
            
            <Button 
              variant="outline" 
              onClick={onCancel}
              className="w-full"
            >
              Cancel
            </Button>
          </div>

          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <p className="text-xs text-blue-800">
              You will be redirected to MTN's secure login page. Your credentials are never shared with IntelliFin.
            </p>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6 max-w-md mx-auto">
      <div className="text-center mb-6">
        <Smartphone className="h-12 w-12 text-blue-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">Connect MTN Mobile Money</h3>
        <p className="text-gray-600">
          Enter your account details to connect your MTN Mobile Money account
        </p>
      </div>

      {error && (
        <Alert className="mb-4 border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <div className="text-red-800">{error}</div>
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="accountName" className="block text-sm font-medium text-gray-700 mb-1">
            Account Name
          </label>
          <Input
            id="accountName"
            type="text"
            placeholder="e.g., My MTN Account"
            value={formData.accountName}
            onChange={(e) => setFormData(prev => ({ ...prev, accountName: e.target.value }))}
            required
          />
          <p className="text-xs text-gray-500 mt-1">
            A friendly name to identify this account
          </p>
        </div>

        <div>
          <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-1">
            Phone Number
          </label>
          <Input
            id="phoneNumber"
            type="tel"
            placeholder="+260 XX XXX XXXX"
            value={formData.phoneNumber}
            onChange={handlePhoneNumberChange}
            required
          />
          <p className="text-xs text-gray-500 mt-1">
            Your MTN Mobile Money registered phone number
          </p>
        </div>

        <div className="flex space-x-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            className="flex-1"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={loading || !formData.accountName.trim() || !formData.phoneNumber.trim()}
            className="flex-1"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <ArrowRight className="h-4 w-4 mr-2" />
            )}
            Continue
          </Button>
        </div>
      </form>

      <div className="mt-6 p-3 bg-gray-50 rounded-lg">
        <p className="text-xs text-gray-600">
          <strong>Secure Connection:</strong> We use bank-level security to protect your data. 
          Your login credentials are never stored by IntelliFin.
        </p>
      </div>
    </Card>
  );
};
