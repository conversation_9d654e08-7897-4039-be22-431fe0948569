package com.intellifin.validation;

import com.intellifin.dto.ManualTransactionDto;
import com.intellifin.model.Category;
import com.intellifin.model.Transaction;
import com.intellifin.repository.CategoryRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * Validator for manual transaction entries
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class TransactionValidator {

    private final CategoryRepository categoryRepository;

    // Validation patterns
    private static final Pattern DESCRIPTION_PATTERN = Pattern.compile("^[a-zA-Z0-9\\s\\-_.,()]+$");
    private static final BigDecimal MAX_AMOUNT = new BigDecimal("999999999.99");
    private static final BigDecimal MIN_AMOUNT = new BigDecimal("0.01");

    /**
     * Validate manual transaction for creation
     */
    public ValidationResult validateForCreation(ManualTransactionDto dto) {
        log.debug("Validating manual transaction for creation: {}", dto.getDescription());
        
        ValidationResult result = new ValidationResult();
        
        // Skip validation if explicitly requested
        if (Boolean.TRUE.equals(dto.getSkipValidation())) {
            log.debug("Skipping validation as requested");
            return result;
        }
        
        // Basic field validation
        validateBasicFields(dto, result);
        
        // Business rule validation
        validateBusinessRules(dto, result);
        
        // Category validation
        validateCategory(dto, result);
        
        // Date validation
        validateDate(dto, result);
        
        // Amount validation
        validateAmount(dto, result);
        
        log.debug("Validation completed. Errors: {}, Warnings: {}", 
                result.getErrors().size(), result.getWarnings().size());
        
        return result;
    }

    /**
     * Validate transaction for update
     */
    public ValidationResult validateForUpdate(ManualTransactionDto dto) {
        log.debug("Validating manual transaction for update: {}", dto.getId());
        
        ValidationResult result = validateForCreation(dto);
        
        // Additional update-specific validations
        if (dto.getId() == null) {
            result.addError("id", "Transaction ID is required for updates", "MISSING_ID");
        }
        
        return result;
    }

    /**
     * Validate basic required fields
     */
    private void validateBasicFields(ManualTransactionDto dto, ValidationResult result) {
        // Description validation
        if (dto.getDescription() == null || dto.getDescription().trim().isEmpty()) {
            result.addError("description", "Description is required", "REQUIRED_FIELD");
        } else if (dto.getDescription().length() < 3) {
            result.addError("description", "Description must be at least 3 characters", "MIN_LENGTH");
        } else if (dto.getDescription().length() > 255) {
            result.addError("description", "Description must not exceed 255 characters", "MAX_LENGTH");
        } else if (!DESCRIPTION_PATTERN.matcher(dto.getDescription()).matches()) {
            result.addError("description", "Description contains invalid characters", "INVALID_CHARACTERS");
        }
        
        // Amount validation
        if (dto.getAmount() == null) {
            result.addError("amount", "Amount is required", "REQUIRED_FIELD");
        }
        
        // Type validation
        if (dto.getType() == null) {
            result.addError("type", "Transaction type is required", "REQUIRED_FIELD");
        }
        
        // Date validation
        if (dto.getDate() == null) {
            result.addError("date", "Transaction date is required", "REQUIRED_FIELD");
        }
        
        // User ID validation
        if (dto.getUserId() == null) {
            result.addError("userId", "User ID is required", "REQUIRED_FIELD");
        }
    }

    /**
     * Validate business rules
     */
    private void validateBusinessRules(ManualTransactionDto dto, ValidationResult result) {
        // Check for duplicate description on same date (warning only)
        if (dto.getDescription() != null && dto.getDate() != null) {
            // This would typically check against existing transactions
            // For now, just add a warning for very generic descriptions
            if (isGenericDescription(dto.getDescription())) {
                result.addWarning("description", 
                    "Description is very generic. Consider adding more details.", 
                    "Consider adding merchant name, location, or purpose");
            }
        }
        
        // Check for unusual amounts
        if (dto.getAmount() != null) {
            if (dto.getAmount().compareTo(new BigDecimal("10000")) > 0) {
                result.addWarning("amount", 
                    "Large transaction amount detected", 
                    "Please verify this amount is correct");
            }
            
            if (dto.getAmount().scale() > 2) {
                result.addError("amount", "Amount cannot have more than 2 decimal places", "INVALID_PRECISION");
            }
        }
        
        // Notes validation
        if (dto.getNotes() != null && dto.getNotes().length() > 1000) {
            result.addError("notes", "Notes must not exceed 1000 characters", "MAX_LENGTH");
        }
    }

    /**
     * Validate category assignment
     */
    private void validateCategory(ManualTransactionDto dto, ValidationResult result) {
        if (dto.getCategoryId() != null) {
            Optional<Category> categoryOpt = categoryRepository.findById(dto.getCategoryId());
            
            if (categoryOpt.isEmpty()) {
                result.addError("categoryId", "Selected category does not exist", "INVALID_CATEGORY");
            } else {
                Category category = categoryOpt.get();
                
                // Check if category type matches transaction type
                if (!isCategoryTypeCompatible(category, dto.getType())) {
                    result.addError("categoryId", 
                        "Category type does not match transaction type", 
                        "INCOMPATIBLE_CATEGORY_TYPE");
                }
                
                // Check if category is active
                if (!category.isActive()) {
                    result.addError("categoryId", "Selected category is inactive", "INACTIVE_CATEGORY");
                }
                
                // Check if category has proper account mapping for journal entries
                if (!category.hasAccountMapping()) {
                    result.addWarning("categoryId", 
                        "Category does not have account mapping configured", 
                        "Journal entries may not be created automatically");
                }
            }
        } else if (dto.getSuggestedCategoryId() != null) {
            // Validate AI suggested category
            Optional<Category> suggestedCategoryOpt = categoryRepository.findById(dto.getSuggestedCategoryId());
            
            if (suggestedCategoryOpt.isEmpty()) {
                result.addWarning("suggestedCategoryId", 
                    "AI suggested category is no longer available", 
                    "Please select a different category");
            }
        }
    }

    /**
     * Validate transaction date
     */
    private void validateDate(ManualTransactionDto dto, ValidationResult result) {
        if (dto.getDate() != null) {
            LocalDate today = LocalDate.now();
            
            // Future date check
            if (dto.getDate().isAfter(today)) {
                result.addError("date", "Transaction date cannot be in the future", "FUTURE_DATE");
            }
            
            // Too far in past check (2 years)
            if (dto.getDate().isBefore(today.minusYears(2))) {
                result.addWarning("date", 
                    "Transaction date is more than 2 years ago", 
                    "Please verify this date is correct");
            }
            
            // Weekend/holiday check for business transactions
            if (dto.getType() == Transaction.TransactionType.INCOME && isWeekend(dto.getDate())) {
                result.addWarning("date", 
                    "Income transaction on weekend", 
                    "Please verify this date is correct");
            }
        }
    }

    /**
     * Validate amount
     */
    private void validateAmount(ManualTransactionDto dto, ValidationResult result) {
        if (dto.getAmount() != null) {
            // Range validation
            if (dto.getAmount().compareTo(MIN_AMOUNT) < 0) {
                result.addError("amount", "Amount must be greater than 0.01", "MIN_AMOUNT");
            }
            
            if (dto.getAmount().compareTo(MAX_AMOUNT) > 0) {
                result.addError("amount", "Amount exceeds maximum limit", "MAX_AMOUNT");
            }
            
            // Precision validation
            if (dto.getAmount().scale() > 2) {
                result.addError("amount", "Amount cannot have more than 2 decimal places", "INVALID_PRECISION");
            }
        }
    }

    /**
     * Check if description is too generic
     */
    private boolean isGenericDescription(String description) {
        String lower = description.toLowerCase().trim();
        return lower.equals("payment") || 
               lower.equals("purchase") || 
               lower.equals("transaction") ||
               lower.equals("expense") ||
               lower.equals("income") ||
               lower.length() < 5;
    }

    /**
     * Check if category type is compatible with transaction type
     */
    private boolean isCategoryTypeCompatible(Category category, Transaction.TransactionType transactionType) {
        if (category.getType() == null || transactionType == null) {
            return true; // Allow if type is not specified
        }
        
        return (category.getType() == Category.CategoryType.EXPENSE && transactionType == Transaction.TransactionType.EXPENSE) ||
               (category.getType() == Category.CategoryType.INCOME && transactionType == Transaction.TransactionType.INCOME);
    }

    /**
     * Check if date is weekend
     */
    private boolean isWeekend(LocalDate date) {
        return date.getDayOfWeek().getValue() >= 6; // Saturday = 6, Sunday = 7
    }

    /**
     * Validation result container
     */
    public static class ValidationResult {
        private final List<ValidationError> errors = new ArrayList<>();
        private final List<ValidationWarning> warnings = new ArrayList<>();
        
        public void addError(String field, String message, String code) {
            errors.add(new ValidationError(field, message, code));
        }
        
        public void addWarning(String field, String message, String suggestion) {
            warnings.add(new ValidationWarning(field, message, suggestion));
        }
        
        public boolean isValid() {
            return errors.isEmpty();
        }
        
        public List<ValidationError> getErrors() {
            return errors;
        }
        
        public List<ValidationWarning> getWarnings() {
            return warnings;
        }
    }

    /**
     * Validation error
     */
    public static class ValidationError {
        private final String field;
        private final String message;
        private final String code;
        
        public ValidationError(String field, String message, String code) {
            this.field = field;
            this.message = message;
            this.code = code;
        }
        
        // Getters
        public String getField() { return field; }
        public String getMessage() { return message; }
        public String getCode() { return code; }
    }

    /**
     * Validation warning
     */
    public static class ValidationWarning {
        private final String field;
        private final String message;
        private final String suggestion;
        
        public ValidationWarning(String field, String message, String suggestion) {
            this.field = field;
            this.message = message;
            this.suggestion = suggestion;
        }
        
        // Getters
        public String getField() { return field; }
        public String getMessage() { return message; }
        public String getSuggestion() { return suggestion; }
    }
}
