package com.intellifin.service;

import com.intellifin.dto.conversation.ConversationCommandRequest;
import com.intellifin.dto.conversation.ConversationResponse;
import com.intellifin.model.Conversation;
import com.intellifin.model.ConversationMessage;
import com.intellifin.model.User;
import com.intellifin.repository.ConversationMessageRepository;
import com.intellifin.repository.ConversationRepository;
import com.intellifin.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ConversationService {

    private final ConversationRepository conversationRepository;
    private final ConversationMessageRepository messageRepository;
    private final UserRepository userRepository;
    private final SimpMessagingTemplate messagingTemplate;
    private final AiCommandProcessingService aiCommandProcessingService;
    private final IntentEventPublisher intentEventPublisher;

    public ConversationResponse processCommand(ConversationCommandRequest request, UUID userId) {
        log.info("Processing command for user: {}, session: {}", userId, request.getSessionId());
        
        try {
            // Get or create conversation
            Conversation conversation = getOrCreateConversation(request, userId);
            
            // Ensure conversation is saved and flushed
            conversation = conversationRepository.saveAndFlush(conversation);
            
            // Create user message
            ConversationMessage userMessage = createUserMessage(conversation, request);
            userMessage = messageRepository.saveAndFlush(userMessage);
            
            // Send immediate acknowledgment
            ConversationResponse ackResponse = ConversationResponse.progress(
                userMessage.getId(),
                request.getSessionId(),
                conversation.getId(),
                "Processing your request..."
            );
            
            sendWebSocketMessage(userId, ackResponse);
            
            // Process command asynchronously
            processCommandAsync(userMessage, request, userId);
            
            return ackResponse;
            
        } catch (Exception e) {
            log.error("Error processing command for user: {}", userId, e);
            return ConversationResponse.error(
                null,
                request.getSessionId(),
                null,
                "Failed to process command: " + e.getMessage()
            );
        }
    }

    private Conversation getOrCreateConversation(ConversationCommandRequest request, UUID userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));

        if (request.hasSessionId()) {
            Optional<Conversation> existing = conversationRepository.findActiveBySessionId(request.getSessionId());
            if (existing.isPresent()) {
                existing.get().updateActivity();
                return conversationRepository.save(existing.get());
            }
        }

        // Create new conversation
        String sessionId = request.hasSessionId() ? request.getSessionId() : generateSessionId();
        String title = generateConversationTitle(request.getCommand());
        
        Conversation conversation = Conversation.builder()
                .user(user)
                .sessionId(sessionId)
                .title(title)
                .status(Conversation.ConversationStatus.ACTIVE)
                .lastActivityAt(LocalDateTime.now())
                .build();

        return conversationRepository.save(conversation);
    }

    private ConversationMessage createUserMessage(Conversation conversation, ConversationCommandRequest request) {
        return ConversationMessage.builder()
                .conversation(conversation)
                .type(ConversationMessage.MessageType.COMMAND)
                .role(ConversationMessage.MessageRole.USER)
                .content(request.getCommand())
                .status(ConversationMessage.MessageStatus.PENDING)
                .metadata("{}")  // Ensure valid JSON for JSONB field
                .build();
    }

    private void processCommandAsync(ConversationMessage userMessage, ConversationCommandRequest request, UUID userId) {
        // Store IDs to avoid lazy loading issues
        final UUID messageId = userMessage.getId();
        final UUID conversationId = userMessage.getConversation().getId();
        final String sessionId = request.getSessionId();
        
        CompletableFuture.runAsync(() -> {
            processCommandInNewTransaction(messageId, conversationId, sessionId, request.getCommand(), userId);
        });
    }
    
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void processCommandInNewTransaction(UUID messageId, UUID conversationId, String sessionId, String command, UUID userId) {
        try {
            long startTime = System.currentTimeMillis();
            
            // Reload entities in new transaction
            ConversationMessage userMessage = messageRepository.findById(messageId)
                    .orElseThrow(() -> new RuntimeException("Message not found"));
            Conversation conversation = conversationRepository.findById(conversationId)
                    .orElseThrow(() -> new RuntimeException("Conversation not found"));
            
            // Mark message as processing
            userMessage.markAsProcessing();
            messageRepository.save(userMessage);
            
            // Send processing update
            ConversationResponse processingResponse = ConversationResponse.progress(
                messageId,
                sessionId,
                conversationId,
                "Analyzing your request..."
            );
            sendWebSocketMessage(userId, processingResponse);
            
            // Process with AI service
            AiCommandProcessingService.CommandProcessingResult result = 
                aiCommandProcessingService.processCommand(command, conversation);
            
            long processingTime = System.currentTimeMillis() - startTime;
            
            // Update user message with results
            userMessage.setProcessingResult(
                result.getIntent(),
                result.getConfidence(),
                processingTime
            );
            userMessage.markAsCompleted();
            messageRepository.save(userMessage);
            
            // Create assistant response message
            ConversationMessage assistantMessage = ConversationMessage.builder()
                    .conversation(conversation)
                    .type(ConversationMessage.MessageType.RESPONSE)
                    .role(ConversationMessage.MessageRole.ASSISTANT)
                    .content(result.getResponse())
                    .intentRecognized(result.getIntent())
                    .confidenceScore(result.getConfidence())
                    .processingTimeMs(processingTime)
                    .status(ConversationMessage.MessageStatus.COMPLETED)
                    .metadata("{}")  // Ensure valid JSON for JSONB field
                    .build();
            
            assistantMessage = messageRepository.save(assistantMessage);
            
            // Send final response
            ConversationResponse finalResponse = ConversationResponse.success(
                assistantMessage.getId(),
                sessionId,
                conversationId,
                result.getResponse(),
                result.getIntent(),
                result.getConfidence(),
                processingTime
            );
            
            if (result.requiresFollowUp()) {
                finalResponse.setRequiresFollowUp(true);
                finalResponse.setFollowUpPrompt(result.getFollowUpPrompt());
            }
            
            sendWebSocketMessage(userId, finalResponse);
            
            log.info("Command processed successfully for user: {}, intent: {}, confidence: {}", 
                    userId, result.getIntent(), result.getConfidence());
            
        } catch (Exception e) {
            log.error("Error in async command processing for user: {}", userId, e);
            
            try {
                // Reload message to update status
                ConversationMessage userMessage = messageRepository.findById(messageId).orElse(null);
                if (userMessage != null) {
                    // Truncate error message if too long
                    String errorMsg = e.getMessage();
                    if (errorMsg != null && errorMsg.length() > 250) {
                        errorMsg = errorMsg.substring(0, 250) + "...";
                    }
                    userMessage.markAsFailed(errorMsg);
                    messageRepository.save(userMessage);
                }
            } catch (Exception saveError) {
                log.error("Failed to save error status", saveError);
            }
            
            // Send error response with more descriptive message
            String errorMessage = "I apologize, but I encountered an error processing your request.";
            
            // Provide more specific error messages based on the exception
            if (e.getMessage() != null) {
                if (e.getMessage().contains("Connection refused") || e.getMessage().contains("I/O error")) {
                    errorMessage = "I'm having trouble connecting to the AI service. Please try again in a moment.";
                } else if (e.getMessage().contains("timeout")) {
                    errorMessage = "The request took too long to process. Please try again with a simpler query.";
                } else if (e.getMessage().contains("WebSocket")) {
                    errorMessage = "There was a connection issue. Please refresh the page and try again.";
                }
            }
            
            ConversationResponse errorResponse = ConversationResponse.error(
                messageId,
                sessionId,
                conversationId,
                errorMessage
            );
            
            sendWebSocketMessage(userId, errorResponse);
        }
    }

    private void sendWebSocketMessage(UUID userId, ConversationResponse response) {
        try {
            messagingTemplate.convertAndSendToUser(
                userId.toString(),
                "/queue/conversation",
                response
            );
        } catch (Exception e) {
            log.error("Failed to send WebSocket message to user: {}", userId, e);
        }
    }

    public List<Conversation> getUserConversations(UUID userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        
        return conversationRepository.findActiveByUser(user);
    }

    public Optional<Conversation> getConversation(UUID conversationId, UUID userId) {
        return conversationRepository.findById(conversationId)
                .filter(conv -> conv.getUser().getId().equals(userId));
    }

    public List<ConversationMessage> getConversationMessages(UUID conversationId, UUID userId) {
        Optional<Conversation> conversation = getConversation(conversationId, userId);
        if (conversation.isPresent()) {
            return messageRepository.findByConversationOrderByCreatedAtAsc(conversation.get());
        }
        return List.of();
    }

    public void archiveConversation(UUID conversationId, UUID userId) {
        Optional<Conversation> conversation = getConversation(conversationId, userId);
        if (conversation.isPresent()) {
            conversation.get().archive();
            conversationRepository.save(conversation.get());
        }
    }

    public void deleteConversation(UUID conversationId, UUID userId) {
        Optional<Conversation> conversation = getConversation(conversationId, userId);
        if (conversation.isPresent()) {
            conversation.get().delete();
            conversationRepository.save(conversation.get());
        }
    }

    private String generateSessionId() {
        return "session_" + UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }

    private String generateConversationTitle(String command) {
        // Simple title generation - could be enhanced with AI
        String title = command.length() > 50 ? command.substring(0, 47) + "..." : command;
        return title.replaceAll("[^a-zA-Z0-9\\s]", "").trim();
    }

    /**
     * Update conversation with intent information (Story 2.2)
     */
    public void updateConversationWithIntent(String sessionId, String intentName,
                                           double confidence, String description) {
        try {
            // Find conversation by session ID
            Optional<Conversation> conversationOpt = conversationRepository.findBySessionId(sessionId);
            if (conversationOpt.isPresent()) {
                Conversation conversation = conversationOpt.get();

                // Create proper JSON context for intent
                String intentContext = String.format(
                    "{\"intent\": \"%s\", \"confidence\": %.2f, \"description\": \"%s\"}",
                    intentName, confidence, description.replace("\"", "\\\""));
                conversation.setContext(intentContext);
                conversationRepository.save(conversation);

                log.info("Updated conversation {} with intent: {} (confidence: {})",
                        conversation.getId(), intentName, confidence);
            }
        } catch (Exception e) {
            log.error("Error updating conversation with intent: {}", e.getMessage());
        }
    }

    /**
     * Update conversation with entity information (Story 2.2)
     */
    public void updateConversationWithEntities(String sessionId, java.util.Map<String, Object> entities) {
        try {
            // Find conversation by session ID
            Optional<Conversation> conversationOpt = conversationRepository.findBySessionId(sessionId);
            if (conversationOpt.isPresent()) {
                Conversation conversation = conversationOpt.get();

                // Update conversation context with entities
                String entitiesJson = entities.toString(); // TODO: Use proper JSON serialization
                String currentContext = conversation.getContext() != null ? conversation.getContext() : "{}";
                String updatedContext = currentContext.replace("}", String.format(", \"entities\": %s}", entitiesJson));
                conversation.setContext(updatedContext);
                conversationRepository.save(conversation);

                log.info("Updated conversation {} with {} entities",
                        conversation.getId(), entities.size());
            }
        } catch (Exception e) {
            log.error("Error updating conversation with entities: {}", e.getMessage());
        }
    }

    /**
     * Update conversation with response information (Story 2.2)
     */
    public void updateConversationWithResponse(String sessionId, String response,
                                             boolean success, long processingTimeMs) {
        try {
            // Find conversation by session ID
            Optional<Conversation> conversationOpt = conversationRepository.findBySessionId(sessionId);
            if (conversationOpt.isPresent()) {
                Conversation conversation = conversationOpt.get();

                // Create assistant message
                ConversationMessage assistantMessage = ConversationMessage.builder()
                    .conversation(conversation)
                    .role(ConversationMessage.MessageRole.ASSISTANT)
                    .type(ConversationMessage.MessageType.RESPONSE)
                    .content(response)
                    .metadata(String.format(
                        "{\"success\": %b, \"processingTimeMs\": %d}", success, processingTimeMs))
                    .build();

                messageRepository.save(assistantMessage);

                log.info("Updated conversation {} with response (success: {}, time: {}ms)",
                        conversation.getId(), success, processingTimeMs);
            }
        } catch (Exception e) {
            log.error("Error updating conversation with response: {}", e.getMessage());
        }
    }

    /**
     * Update conversation with error information (Story 2.2)
     */
    public void updateConversationWithError(String sessionId, String errorMessage, String errorType) {
        try {
            // Find conversation by session ID
            Optional<Conversation> conversationOpt = conversationRepository.findBySessionId(sessionId);
            if (conversationOpt.isPresent()) {
                Conversation conversation = conversationOpt.get();

                // Create error message
                ConversationMessage errorMsg = ConversationMessage.builder()
                    .conversation(conversation)
                    .role(ConversationMessage.MessageRole.SYSTEM)
                    .type(ConversationMessage.MessageType.ERROR)
                    .content("I encountered an error processing your request: " + errorMessage)
                    .metadata(String.format(
                        "{\"error\": true, \"errorType\": \"%s\", \"errorMessage\": \"%s\"}",
                        errorType, errorMessage.replace("\"", "\\\"")))
                    .build();

                messageRepository.save(errorMsg);

                log.error("Updated conversation {} with error: {} (type: {})",
                         conversation.getId(), errorMessage, errorType);
            }
        } catch (Exception e) {
            log.error("Error updating conversation with error: {}", e.getMessage());
        }
    }
}
