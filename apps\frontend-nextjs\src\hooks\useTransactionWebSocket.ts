import { useEffect, useRef, useState, useCallback } from 'react';
import { Client, IMessage } from '@stomp/stompjs';
import SockJS from 'sockjs-client';
import { useAuthStore, useFinancialStore } from '@/stores';
import { 
  TransactionCategorized, 
  CategorizationAccepted, 
  CategorizationRejected,
  BulkCategorizationCompleted 
} from '@/types/api';

interface TransactionWebSocketMessage {
  messageId?: string;
  userId: string;
  eventType: 'TRANSACTION_CATEGORIZED' | 'CATEGORIZATION_ACCEPTED' | 'CATEGORIZATION_REJECTED' | 'BULK_CATEGORIZATION_COMPLETED';
  data: TransactionCategorized | CategorizationAccepted | CategorizationRejected | BulkCategorizationCompleted;
  timestamp: string;
}

interface UseTransactionWebSocketReturn {
  isConnected: boolean;
  connectionError: string | null;
  isConnecting: boolean;
  subscribeToTransactionUpdates: () => void;
  unsubscribeFromTransactionUpdates: () => void;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080';

/**
 * WebSocket hook specifically for transaction categorization real-time updates
 */
export function useTransactionWebSocket(): UseTransactionWebSocketReturn {
  const { token, isAuthenticated, user } = useAuthStore();
  const { updateTransaction, fetchTransactions } = useFinancialStore();
  
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  
  const clientRef = useRef<Client | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const subscriptionRef = useRef<any>(null);
  const maxReconnectAttempts = 5;

  const connect = useCallback(() => {
    if (!isAuthenticated || !token || !user) {
      console.log('Transaction WebSocket: Not authenticated, skipping connection');
      return;
    }

    console.log('Transaction WebSocket: Attempting to connect...');
    setIsConnecting(true);
    setConnectionError(null);

    try {
      const socket = new SockJS(`${API_BASE_URL}/ws/transaction-updates`);
      
      const client = new Client({
        webSocketFactory: () => socket,
        connectHeaders: {
          'Authorization': `Bearer ${token}`,
          'X-User-ID': user.id
        },
        debug: (str) => {
          console.log('Transaction WebSocket Debug:', str);
        },
        reconnectDelay: 5000,
        heartbeatIncoming: 4000,
        heartbeatOutgoing: 4000,
        onConnect: (frame) => {
          console.log('🎉 Transaction WebSocket connected successfully!', frame);
          setIsConnected(true);
          setIsConnecting(false);
          setConnectionError(null);
          reconnectAttempts.current = 0;
        },
        onStompError: (frame) => {
          console.error('❌ Transaction WebSocket STOMP error:', frame);
          setConnectionError(`STOMP Error: ${frame.headers['message']}`);
          setIsConnected(false);
          setIsConnecting(false);
        },
        onWebSocketError: (error) => {
          console.error('❌ Transaction WebSocket error:', error);
          setConnectionError('WebSocket connection failed');
          setIsConnected(false);
          setIsConnecting(false);
        },
        onDisconnect: () => {
          console.log('🔌 Transaction WebSocket disconnected');
          setIsConnected(false);
          setIsConnecting(false);
          
          // Attempt to reconnect if not manually disconnected
          if (reconnectAttempts.current < maxReconnectAttempts) {
            reconnectAttempts.current++;
            console.log(`🔄 Transaction WebSocket reconnect attempt ${reconnectAttempts.current}/${maxReconnectAttempts}`);
            
            reconnectTimeoutRef.current = setTimeout(() => {
              connect();
            }, 5000 * reconnectAttempts.current); // Exponential backoff
          } else {
            setConnectionError('Max reconnection attempts reached');
          }
        }
      });

      clientRef.current = client;
      client.activate();

    } catch (error) {
      console.error('❌ Transaction WebSocket connection error:', error);
      setConnectionError('Failed to establish WebSocket connection');
      setIsConnecting(false);
    }
  }, [isAuthenticated, token, user]);

  const disconnect = useCallback(() => {
    console.log('🔌 Disconnecting Transaction WebSocket...');
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (subscriptionRef.current) {
      subscriptionRef.current.unsubscribe();
      subscriptionRef.current = null;
    }

    if (clientRef.current) {
      clientRef.current.deactivate();
      clientRef.current = null;
    }

    setIsConnected(false);
    setIsConnecting(false);
    setConnectionError(null);
    reconnectAttempts.current = 0;
  }, []);

  const subscribeToTransactionUpdates = useCallback(() => {
    if (!clientRef.current?.connected || !user) {
      console.warn('Transaction WebSocket not connected or user not available');
      return;
    }

    console.log('📡 Subscribing to transaction updates...');

    try {
      // Subscribe to user-specific transaction updates
      subscriptionRef.current = clientRef.current.subscribe(
        `/user/${user.id}/queue/transaction-updates`,
        (message: IMessage) => {
          try {
            const messageData: TransactionWebSocketMessage = JSON.parse(message.body);
            console.log('📨 Received transaction update:', messageData);
            
            handleTransactionUpdate(messageData);
          } catch (error) {
            console.error('❌ Error parsing transaction WebSocket message:', error);
          }
        }
      );

      console.log('✅ Subscribed to transaction updates');
    } catch (error) {
      console.error('❌ Error subscribing to transaction updates:', error);
    }
  }, [user]);

  const unsubscribeFromTransactionUpdates = useCallback(() => {
    if (subscriptionRef.current) {
      console.log('📡 Unsubscribing from transaction updates...');
      subscriptionRef.current.unsubscribe();
      subscriptionRef.current = null;
    }
  }, []);

  const handleTransactionUpdate = useCallback((message: TransactionWebSocketMessage) => {
    console.log('🔄 Processing transaction update:', message.eventType);

    switch (message.eventType) {
      case 'TRANSACTION_CATEGORIZED':
        handleTransactionCategorized(message.data as TransactionCategorized);
        break;
      
      case 'CATEGORIZATION_ACCEPTED':
        handleCategorizationAccepted(message.data as CategorizationAccepted);
        break;
      
      case 'CATEGORIZATION_REJECTED':
        handleCategorizationRejected(message.data as CategorizationRejected);
        break;
      
      case 'BULK_CATEGORIZATION_COMPLETED':
        handleBulkCategorizationCompleted(message.data as BulkCategorizationCompleted);
        break;
      
      default:
        console.warn('Unknown transaction event type:', message.eventType);
    }
  }, []);

  const handleTransactionCategorized = useCallback((data: TransactionCategorized) => {
    console.log('🤖 AI categorized transaction:', data.transactionId);
    
    // Update the transaction in the store with AI suggestion
    const updates = {
      aiCategoryId: data.suggestedCategory.id,
      aiConfidence: data.confidence,
      aiExplanation: data.explanation,
      status: 'PENDING_CLASSIFICATION' as const
    };
    
    updateTransaction(data.transactionId, updates);
    
    // Show notification
    showNotification(
      'AI Categorization Complete',
      `Transaction categorized as "${data.suggestedCategory.name}" with ${Math.round(data.confidence * 100)}% confidence`,
      'info'
    );
  }, [updateTransaction]);

  const handleCategorizationAccepted = useCallback((data: CategorizationAccepted) => {
    console.log('✅ Categorization accepted:', data.transactionId);
    
    // Update transaction status
    const updates = {
      categoryId: data.categoryId,
      status: 'CLASSIFIED' as const
    };
    
    updateTransaction(data.transactionId, updates);
    
    showNotification(
      'Categorization Accepted',
      'Transaction has been successfully categorized',
      'success'
    );
  }, [updateTransaction]);

  const handleCategorizationRejected = useCallback((data: CategorizationRejected) => {
    console.log('❌ Categorization rejected:', data.transactionId);
    
    // Update transaction with new category if provided
    const updates: any = {
      status: 'CLASSIFIED' as const
    };
    
    if (data.newCategoryId) {
      updates.categoryId = data.newCategoryId;
    }
    
    updateTransaction(data.transactionId, updates);
    
    showNotification(
      'Categorization Updated',
      'Transaction categorization has been updated',
      'info'
    );
  }, [updateTransaction]);

  const handleBulkCategorizationCompleted = useCallback((data: BulkCategorizationCompleted) => {
    console.log('📦 Bulk categorization completed:', data.batchId);
    
    // Refresh transactions to get latest state
    fetchTransactions();
    
    showNotification(
      'Bulk Categorization Complete',
      `Processed ${data.processedCount} transactions (${data.successCount} successful, ${data.failureCount} failed)`,
      data.failureCount > 0 ? 'warning' : 'success'
    );
  }, [fetchTransactions]);

  const showNotification = (title: string, message: string, type: 'success' | 'info' | 'warning' | 'error') => {
    // TODO: Integrate with a proper notification system
    console.log(`${type.toUpperCase()}: ${title} - ${message}`);
    
    // For now, we could use browser notifications if permission is granted
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(title, {
        body: message,
        icon: '/favicon.ico'
      });
    }
  };

  // Auto-connect when authenticated
  useEffect(() => {
    if (isAuthenticated && token && user && !isConnected && !isConnecting) {
      connect();
    } else if (!isAuthenticated && isConnected) {
      disconnect();
    }

    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, [isAuthenticated, token, user, isConnected, isConnecting, connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    isConnected,
    connectionError,
    isConnecting,
    subscribeToTransactionUpdates,
    unsubscribeFromTransactionUpdates
  };
}
