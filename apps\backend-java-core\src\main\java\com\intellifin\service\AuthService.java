package com.intellifin.service;

import com.intellifin.dto.auth.AuthResponse;
import com.intellifin.dto.auth.LoginRequest;
import com.intellifin.dto.auth.RegisterRequest;
import com.intellifin.dto.user.UserDto;
import com.intellifin.exception.AuthenticationException;
import com.intellifin.exception.EmailAlreadyExistsException;
import com.intellifin.exception.InvalidCredentialsException;
import com.intellifin.exception.TokenVerificationException;
import com.intellifin.exception.ValidationException;
import com.intellifin.model.EmailVerificationToken;
import com.intellifin.model.PasswordResetToken;
import com.intellifin.model.Role;
import com.intellifin.model.User;
import com.intellifin.repository.EmailVerificationTokenRepository;
import com.intellifin.repository.PasswordResetTokenRepository;
import com.intellifin.repository.UserRepository;
import com.intellifin.security.JwtTokenUtil;
import com.intellifin.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;

import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class AuthService {


    private final UserRepository userRepository;
    private final EmailVerificationTokenRepository emailVerificationTokenRepository;
    private final PasswordResetTokenRepository passwordResetTokenRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtTokenUtil jwtTokenUtil;
    private final AuthenticationManager authenticationManager;
    private final EmailService emailService;

    private static final int TOKEN_LENGTH = 32;
    private static final int EMAIL_VERIFICATION_EXPIRY_HOURS = 24;
    private static final int PASSWORD_RESET_EXPIRY_HOURS = 1;

    @Transactional
    public AuthResponse register(RegisterRequest request) {
        log.info("Registering new user: {}", request.getEmail());
        if (userRepository.existsByEmail(request.getEmail().toLowerCase())) {
            log.warn("Registration failed: email {} already exists.", request.getEmail());
            throw new EmailAlreadyExistsException("Email address already in use.");
        }

        User user = new User();
        user.setEmail(request.getEmail().toLowerCase());
        user.setPasswordHash(passwordEncoder.encode(request.getPassword()));
        user.setFirstName(request.getFirstName());
        user.setLastName(request.getLastName());
        user.setOrganizationName(request.getOrganizationName());
        user.setTpin(request.getTpin());
        user.setRole(Role.USER);
        user.setEmailVerified(false);
        user.setAccountLocked(false);
        user.setFailedLoginAttempts(0);
        user.setOnboardingStatus(User.OnboardingStatus.STARTED);


        userRepository.save(user);
        log.debug("User saved to database with ID: {}", user.getId());
        sendVerificationEmail(user);

        return generateAuthResponse(user);
    }

    private AuthResponse generateAuthResponse(User user) {
        UserPrincipal userPrincipal = UserPrincipal.create(user);
        String token = jwtTokenUtil.generateToken(userPrincipal, user.getId(), user.getEmail(), user.getFullName());
        String refreshToken = jwtTokenUtil.generateToken(userPrincipal, user.getId(), user.getEmail(), user.getFullName(), 86400L * 7); // 7 days

        return AuthResponse.builder()
                .token(token)
                .refreshToken(refreshToken)
                .user(UserDto.fromUser(user))
                .build();
    }

    public AuthResponse login(LoginRequest request) {
        log.info("Attempting to authenticate user: {}", request.getEmail());
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                        request.getEmail().toLowerCase(),
                        request.getPassword()
                )
        );

        SecurityContextHolder.getContext().setAuthentication(authentication);
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        User user = userRepository.findById(userPrincipal.getId())
                .orElseThrow(() -> new InvalidCredentialsException("User not found after authentication."));

        
        log.info("User {} authenticated successfully.", user.getEmail());
        if (request.getRememberMe() != null && request.getRememberMe()) {
            log.debug("User {} requested to be remembered.", user.getEmail());
        }

        return generateAuthResponse(user);
    }

    public AuthResponse refreshToken(String authHeader) {
        log.info("Refreshing token");
        final String bearer = "Bearer ";
        if (authHeader == null || !authHeader.startsWith(bearer)) {
            throw new InvalidCredentialsException("Invalid Authorization header");
        }
        String token = authHeader.substring(bearer.length());
        String username = jwtTokenUtil.getUsernameFromToken(token);
        User user = userRepository.findByEmail(username)
                .orElseThrow(() -> new InvalidCredentialsException("User not found from token."));
        log.debug("Token refresh for user: {}", user.getEmail());
        return generateAuthResponse(user);
    }

    public void logout(String authHeader) {
        log.info("User logging out.");
        // Implement token invalidation if using a denylist
    }

    @Transactional
    public void verifyEmail(String token) {
        log.info("Verifying email with token");
        EmailVerificationToken verificationToken = emailVerificationTokenRepository.findByToken(token)
                .orElseThrow(() -> new TokenVerificationException("Invalid or expired verification token."));

        if (verificationToken.getExpiresAt().isBefore(LocalDateTime.now())) {
            emailVerificationTokenRepository.delete(verificationToken);
            throw new TokenVerificationException("Verification token has expired.");
        }

        User user = verificationToken.getUser();
        user.setEmailVerified(true);
        userRepository.save(user);
        emailVerificationTokenRepository.delete(verificationToken);
        log.info("Email verified for user: {}", user.getEmail());
    }

    @Transactional
    public void resendEmailVerification(String email) {
        log.info("Resending verification email to {}", email);
        User user = userRepository.findByEmail(email.toLowerCase())
                .orElseThrow(() -> new InvalidCredentialsException("User not found with this email."));

        if (user.getEmailVerified()) {
            log.warn("User {} email is already verified.", email);
            throw new IllegalStateException("Email is already verified.");
        }


        // Invalidate old tokens
        emailVerificationTokenRepository.deleteAll(emailVerificationTokenRepository.findByUser(user));

        
        sendVerificationEmail(user);
        log.info("New verification email sent to {}", user.getEmail());
    }

    private void sendVerificationEmail(User user) {
        log.debug("Sending verification email to {}", user.getEmail());
        String token = UUID.randomUUID().toString();
        EmailVerificationToken verificationToken = new EmailVerificationToken();
        verificationToken.setToken(token);
        verificationToken.setUser(user);
        verificationToken.setExpiresAt(LocalDateTime.now().plusHours(24));
        emailVerificationTokenRepository.save(verificationToken);

        // This would be an async call in a real application
        emailService.sendEmailVerification(user.getEmail(), user.getFullName(), token);
        log.debug("Email verification token {} created for user {}", token, user.getEmail());
    }

    @Transactional
    public void requestPasswordReset(String email) {
        log.info("Requesting password reset for email: {}", email);
        Optional<User> userOpt = userRepository.findByEmail(email.toLowerCase());
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            String token = UUID.randomUUID().toString();
            PasswordResetToken resetToken = new PasswordResetToken();
            resetToken.setToken(token);
            resetToken.setUser(user);
            resetToken.setExpiresAt(LocalDateTime.now().plusHours(1));
            passwordResetTokenRepository.save(resetToken);
            emailService.sendPasswordReset(user.getEmail(), user.getFullName(), token);
            log.debug("Password reset token created for user {}", user.getEmail());
        } else {
            log.warn("Password reset requested for non-existent email: {}", email);
            // Do nothing to prevent email enumeration
        }
    }

    @Transactional
    public void resetPassword(String token, String newPassword) {
        log.info("Resetting password with token");
        PasswordResetToken resetToken = passwordResetTokenRepository.findByToken(token)
                .orElseThrow(() -> new TokenVerificationException("Invalid or expired password reset token."));

        if (resetToken.getExpiresAt().isBefore(LocalDateTime.now())) {
            passwordResetTokenRepository.delete(resetToken);
            throw new TokenVerificationException("Password reset token has expired.");
        }

        User user = resetToken.getUser();
        user.setPasswordHash(passwordEncoder.encode(newPassword));
        user.setAccountLocked(false);
        user.setFailedLoginAttempts(0);
        user.setLockedUntil(null);
        userRepository.save(user);
        passwordResetTokenRepository.delete(resetToken);
        log.info("Password has been reset for user {}", user.getEmail());
    }

    private void handleFailedLogin(User user) {
        log.warn("Failed login attempt for user: {}", user.getEmail());
        user.incrementFailedLoginAttempts();
        if (user.getFailedLoginAttempts() >= 5) {
            user.setAccountLocked(true);
            user.setLockedUntil(LocalDateTime.now().plusMinutes(15));
            log.warn("User account for {} has been locked.", user.getEmail());
        }
        userRepository.save(user);
    }

    public User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated() || authentication.getPrincipal() instanceof String) {
            throw new AuthenticationException("User is not authenticated");
        }
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        return userRepository.findById(userPrincipal.getId())
                .orElseThrow(() -> new InvalidCredentialsException("User not found from security context."));
    }
}
