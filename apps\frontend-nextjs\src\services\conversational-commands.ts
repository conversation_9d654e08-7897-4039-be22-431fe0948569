import { DisplayMessage, WebSocketMessage, CommandSuggestion } from '@/types/conversation';

export interface CommandProcessingResult {
  success: boolean;
  response?: string;
  data?: any;
  intent?: string;
  confidence?: number;
  requiresFollowUp?: boolean;
  followUpPrompt?: string;
  error?: string;
}

export class ConversationalCommandService {
  private static instance: ConversationalCommandService;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  static getInstance(): ConversationalCommandService {
    if (!ConversationalCommandService.instance) {
      ConversationalCommandService.instance = new ConversationalCommandService();
    }
    return ConversationalCommandService.instance;
  }

  private constructor() {
    // Initialize connection
    this.connect();
  }

  async connect(): Promise<boolean> {
    try {
      // TODO: Implement WebSocket connection
      // For now, simulate connection
      await new Promise(resolve => setTimeout(resolve, 1000));
      this.isConnected = true;
      this.reconnectAttempts = 0;
      console.log('Connected to conversational command service');
      return true;
    } catch (error) {
      console.error('Failed to connect to conversational command service:', error);
      this.isConnected = false;
      
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.reconnectAttempts++;
        setTimeout(() => this.connect(), this.reconnectDelay * this.reconnectAttempts);
      }
      
      return false;
    }
  }

  disconnect(): void {
    this.isConnected = false;
    console.log('Disconnected from conversational command service');
  }

  isServiceConnected(): boolean {
    return this.isConnected;
  }

  async processCommand(command: string, userId: string): Promise<CommandProcessingResult> {
    if (!this.isConnected) {
      return {
        success: false,
        error: 'Service not connected'
      };
    }

    try {
      // Call the AI service for intent recognition and processing
      const response = await fetch('/api/ai/process-command', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          command,
          user_id: userId,
          session_id: this.generateSessionId(),
          context: this.getContextData()
        })
      });

      if (!response.ok) {
        throw new Error(`AI service error: ${response.status}`);
      }

      const aiResult = await response.json();

      return {
        success: true,
        response: aiResult.response,
        intent: aiResult.intent.name,
        confidence: aiResult.intent.confidence,
        requiresFollowUp: aiResult.requires_follow_up,
        followUpPrompt: aiResult.follow_up_prompt,
        data: {
          entities: aiResult.entities,
          metadata: aiResult.metadata
        }
      };
    } catch (error) {
      console.error('Error processing command:', error);

      // Fallback to mock processing if AI service is unavailable
      console.log('Falling back to mock processing...');
      const result = this.mockCommandProcessing(command);

      return {
        success: true,
        ...result
      };
    }
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getContextData(): any {
    // Get context from conversation store or other sources
    return {
      timestamp: new Date().toISOString(),
      user_agent: navigator.userAgent,
      // Add more context as needed
    };
  }

  private mockCommandProcessing(command: string): Partial<CommandProcessingResult> {
    const commandLower = command.toLowerCase();
    
    // Transaction-related commands
    if (commandLower.includes('transaction') || commandLower.includes('recent')) {
      return {
        response: "I've retrieved your recent transactions. You can see them in the results area.",
        intent: 'VIEW_TRANSACTIONS',
        confidence: 0.95,
        data: {
          type: 'transactions',
          count: 25,
          dateRange: 'last_30_days'
        }
      };
    }
    
    // Balance-related commands
    if (commandLower.includes('balance') || commandLower.includes('account')) {
      return {
        response: "Here are your current account balances across all connected accounts.",
        intent: 'VIEW_BALANCES',
        confidence: 0.92,
        data: {
          type: 'accounts',
          totalBalance: 15750.50
        }
      };
    }
    
    // Invoice-related commands
    if (commandLower.includes('invoice')) {
      if (commandLower.includes('create') || commandLower.includes('new')) {
        return {
          response: "I'll help you create a new invoice. What client is this for?",
          intent: 'CREATE_INVOICE',
          confidence: 0.88,
          requiresFollowUp: true,
          followUpPrompt: "Please provide the client name or select from your existing clients."
        };
      } else {
        return {
          response: "Here are your recent invoices and their current status.",
          intent: 'VIEW_INVOICES',
          confidence: 0.90,
          data: {
            type: 'invoices',
            pending: 3,
            overdue: 1
          }
        };
      }
    }
    
    // Profit/summary commands
    if (commandLower.includes('profit') || commandLower.includes('summary') || commandLower.includes('month')) {
      return {
        response: "Here's your financial summary for this month, including income, expenses, and net profit.",
        intent: 'VIEW_FINANCIAL_SUMMARY',
        confidence: 0.93,
        data: {
          type: 'summary',
          period: 'current_month'
        }
      };
    }
    
    // Categorization commands
    if (commandLower.includes('categorize') || commandLower.includes('category')) {
      return {
        response: "I found 12 uncategorized transactions. Let me help you categorize them.",
        intent: 'CATEGORIZE_TRANSACTIONS',
        confidence: 0.87,
        requiresFollowUp: true,
        followUpPrompt: "Would you like me to suggest categories based on transaction descriptions?"
      };
    }
    
    // Help commands
    if (commandLower.includes('help') || commandLower.includes('what can you')) {
      return {
        response: "I can help you with transactions, invoices, account balances, financial reports, and expense categorization. What would you like to know about?",
        intent: 'HELP_REQUEST',
        confidence: 0.99,
        data: {
          type: 'help',
          availableCommands: [
            'View transactions',
            'Check balances',
            'Create invoices',
            'Generate reports',
            'Categorize expenses'
          ]
        }
      };
    }
    
    // Default response for unrecognized commands
    return {
      response: "I'm not sure I understand that request. Could you please rephrase it or ask me something about your transactions, invoices, or account balances?",
      intent: 'UNKNOWN',
      confidence: 0.1,
      requiresFollowUp: true,
      followUpPrompt: "Try asking about transactions, balances, invoices, or financial summaries."
    };
  }

  getCommandSuggestions(context?: any): CommandSuggestion[] {
    // Return contextual suggestions based on current state
    const baseSuggestions: CommandSuggestion[] = [
      {
        text: "What's my account balance?",
        description: "Check current balance across all accounts",
        category: 'ACCOUNT',
        icon: '💰'
      },
      {
        text: "Show me recent transactions",
        description: "View latest transaction activity",
        category: 'TRANSACTION',
        icon: '📊'
      },
      {
        text: "Create a new invoice",
        description: "Generate an invoice for a client",
        category: 'INVOICE',
        icon: '📄'
      },
      {
        text: "Help me categorize expenses",
        description: "Review and categorize pending transactions",
        category: 'TRANSACTION',
        icon: '🏷️'
      },
      {
        text: "Show my profit this month",
        description: "View monthly financial summary",
        category: 'REPORT',
        icon: '📈'
      },
      {
        text: "What can you help me with?",
        description: "Learn about available features",
        category: 'GENERAL',
        icon: '❓'
      }
    ];

    return baseSuggestions;
  }

  // Event handlers for WebSocket messages (to be implemented)
  onMessage(callback: (message: WebSocketMessage) => void): void {
    // TODO: Implement WebSocket message handling
    console.log('Message handler registered');
  }

  onConnectionChange(callback: (connected: boolean) => void): void {
    // TODO: Implement connection state change handling
    console.log('Connection change handler registered');
  }

  onError(callback: (error: string) => void): void {
    // TODO: Implement error handling
    console.log('Error handler registered');
  }
}

// Export singleton instance
export const conversationalCommandService = ConversationalCommandService.getInstance();
