package com.intellifin.repository;

import com.intellifin.model.Category;
import com.intellifin.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface CategoryRepository extends JpaRepository<Category, UUID> {
    List<Category> findByUser(User user);
    Optional<Category> findByIsSystemDefinedTrueAndNameAndIsActiveTrue(String name);
}
