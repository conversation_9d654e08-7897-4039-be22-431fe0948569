package com.intellifin.controller;

import com.intellifin.dto.auth.AuthResponse;
import com.intellifin.dto.auth.LoginRequest;
import com.intellifin.dto.auth.RegisterRequest;
import com.intellifin.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;



import java.util.Map;

/**
 * Controller for handling user authentication and registration processes.
 * Provides endpoints for registering new users, logging in, verifying emails,
 * and managing password resets.
 */
@RestController
@RequestMapping("/api/v1/auth")
@RequiredArgsConstructor
@Slf4j
public class AuthController {


    // Service layer dependency for handling authentication logic.
    private final AuthService authService;

    /**
     * Registers a new user in the system.
     *
     * @param request The registration request containing user details (e.g., email, password).
     * @param httpRequest The HTTP request, used here to extract the client's IP address for logging.
     * @return A ResponseEntity containing the authentication response with JWT tokens.
     */
    @PostMapping("/register")
    @Operation(summary = "Register a new user", description = "Create a new user account with email verification")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "User registered successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input data"),
            @ApiResponse(responseCode = "409", description = "User already exists")
    })
    public ResponseEntity<AuthResponse> register(
            @Valid @RequestBody RegisterRequest request,
            HttpServletRequest httpRequest) {

        log.info("Registration attempt from IP: {} for email: {}",
                getClientIpAddress(httpRequest), request.getEmail());

        AuthResponse response = authService.register(request);

        log.info("User registered successfully: {}", request.getEmail());
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    /**
     * Authenticates a user and provides JWT tokens upon successful login.
     *
     * @param request The login request containing user credentials.
     * @param httpRequest The HTTP request, used to extract the client's IP address for logging.
     * @return A ResponseEntity containing the authentication response with JWT tokens.
     */
    @PostMapping("/login")
    @Operation(summary = "Authenticate user", description = "Login with email and password")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Login successful"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "423", description = "Account locked")
    })
    public ResponseEntity<AuthResponse> login(
            @Valid @RequestBody LoginRequest request,
            HttpServletRequest httpRequest) {

        log.info("Login attempt from IP: {} for email: {}",
                getClientIpAddress(httpRequest), request.getEmail());

        AuthResponse response = authService.login(request);

        log.info("User logged in successfully: {}", request.getEmail());
        return ResponseEntity.ok(response);
    }

    /**
     * Verifies a user's email address using a verification token.
     *
     * @param token The email verification token sent to the user.
     * @return A ResponseEntity with a success message.
     */
    @PostMapping("/verify-email")
    @Operation(summary = "Verify email address", description = "Verify user email with verification token")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Email verified successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid or expired token")
    })
    public ResponseEntity<Map<String, String>> verifyEmail(@RequestParam String token) {
        log.info("Email verification attempt with token");

        authService.verifyEmail(token);

        return ResponseEntity.ok(Map.of(
                "message", "Email verified successfully",
                "status", "success"
        ));
    }

    /**
     * Resends the email verification link to a user's email address.
     *
     * @param email The email address to which the verification link will be resent.
     * @return A ResponseEntity with a success message.
     */
    @PostMapping("/resend-verification")
    @Operation(summary = "Resend email verification", description = "Resend verification email to user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Verification email sent"),
            @ApiResponse(responseCode = "400", description = "Invalid email or already verified")
    })
    public ResponseEntity<Map<String, String>> resendEmailVerification(@RequestParam String email) {
        log.info("Resend verification request for email: {}", email);

        authService.resendEmailVerification(email);

        return ResponseEntity.ok(Map.of(
                "message", "Verification email sent successfully",
                "status", "success"
        ));
    }

    /**
     * Initiates the password reset process by sending a reset link to the user's email.
     *
     * @param email The user's email address.
     * @return A ResponseEntity with a generic success message to prevent email enumeration attacks.
     */
    @PostMapping("/forgot-password")
    @Operation(summary = "Request password reset", description = "Send password reset email to user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Password reset email sent"),
            @ApiResponse(responseCode = "400", description = "Invalid email")
    })
    public ResponseEntity<Map<String, String>> forgotPassword(@RequestParam String email) {
        log.info("Password reset request for email: {}", email);

        authService.requestPasswordReset(email);

        // Always return a success response to prevent attackers from discovering which emails are registered.
        return ResponseEntity.ok(Map.of(
                "message", "If an account with that email exists, a password reset link has been sent",
                "status", "success"
        ));
    }

    /**
     * Resets the user's password using a valid reset token and a new password.
     *
     * @param token The password reset token received by the user.
     * @param newPassword The new password to set for the user's account.
     * @return A ResponseEntity with a success message.
     */
    @PostMapping("/reset-password")
    @Operation(summary = "Reset password", description = "Reset user password with reset token")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Password reset successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid or expired token")
    })
    public ResponseEntity<Map<String, String>> resetPassword(
            @RequestParam String token,
            @RequestParam String newPassword) {

        log.info("Password reset attempt with token");

        authService.resetPassword(token, newPassword);

        return ResponseEntity.ok(Map.of(
                "message", "Password reset successfully",
                "status", "success"
        ));
    }

    /**
     * Validates the provided JWT Authorization token.
     * Note: The actual validation is performed by a security filter (e.g., JwtAuthenticationFilter).
     * If the request reaches this endpoint, the token is considered valid.
     *
     * @param authHeader The Authorization header containing the JWT token (e.g., "Bearer ...").
     * @return A ResponseEntity indicating the token is valid.
     */
    @GetMapping("/validate-token")
    @Operation(summary = "Validate JWT token", description = "Check if JWT token is valid")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Token is valid"),
            @ApiResponse(responseCode = "401", description = "Token is invalid or expired")
    })
    public ResponseEntity<Map<String, Object>> validateToken(
            @RequestHeader("Authorization") String authHeader) {

        // This endpoint is primarily protected by the JWT security filter.
        // If the request gets here, the token has already been successfully validated.
        return ResponseEntity.ok(Map.of(
                "valid", true,
                "message", "Token is valid",
                "timestamp", System.currentTimeMillis()
        ));
    }

    /**
     * Refreshes the JWT token for authenticated users.
     * Generates a new token with extended expiration time.
     *
     * @param authHeader The Authorization header containing the current JWT token.
     * @return A ResponseEntity containing the new authentication response with refreshed token.
     */
    @PostMapping("/refresh")
    @Operation(summary = "Refresh JWT token", description = "Generate a new JWT token with extended expiration")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Token refreshed successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid or expired token")
    })
    public ResponseEntity<AuthResponse> refreshToken(
            @RequestHeader("Authorization") String authHeader) {

        log.info("Token refresh request received");

        AuthResponse response = authService.refreshToken(authHeader);

        log.info("Token refreshed successfully");
        return ResponseEntity.ok(response);
    }

    /**
     * Logs out the user by invalidating their current session.
     * This endpoint can be used to perform server-side logout operations.
     *
     * @param authHeader The Authorization header containing the JWT token.
     * @return A ResponseEntity with a success message.
     */
    @PostMapping("/logout")
    @Operation(summary = "Logout user", description = "Invalidate user session and token")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Logout successful"),
            @ApiResponse(responseCode = "401", description = "Invalid token")
    })
    public ResponseEntity<Map<String, String>> logout(
            @RequestHeader("Authorization") String authHeader) {

        log.info("Logout request received");

        authService.logout(authHeader);

        return ResponseEntity.ok(Map.of(
                "message", "Logout successful",
                "status", "success"
        ));
    }

    /**
     * Initiates Google OAuth authentication flow.
     * Redirects the user to Google's OAuth consent screen.
     *
     * @return A ResponseEntity with redirect information or direct redirect.
     */
    @GetMapping("/oauth/google")
    @Operation(summary = "Initiate Google OAuth", description = "Start Google OAuth authentication flow")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "302", description = "Redirect to Google OAuth"),
            @ApiResponse(responseCode = "500", description = "OAuth configuration error")
    })
    public ResponseEntity<Map<String, String>> initiateGoogleOAuth() {
        log.info("Initiating Google OAuth flow");

        // TODO: Implement Google OAuth initiation
        // For now, return a placeholder response
        return ResponseEntity.ok(Map.of(
                "message", "Google OAuth not yet implemented",
                "status", "pending"
        ));
    }

    /**
     * Handles Google OAuth callback after user consent.
     * Processes the authorization code and creates/authenticates the user.
     *
     * @param code The authorization code from Google.
     * @param state The state parameter for CSRF protection.
     * @return A ResponseEntity containing the authentication response.
     */
    @PostMapping("/oauth/google/callback")
    @Operation(summary = "Handle Google OAuth callback", description = "Process Google OAuth authorization code")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OAuth authentication successful"),
            @ApiResponse(responseCode = "400", description = "Invalid authorization code"),
            @ApiResponse(responseCode = "401", description = "OAuth authentication failed")
    })
    public ResponseEntity<AuthResponse> handleGoogleOAuthCallback(
            @RequestParam String code,
            @RequestParam(required = false) String state) {

        log.info("Handling Google OAuth callback");

        // TODO: Implement Google OAuth callback handling
        // For now, return a placeholder response
        throw new UnsupportedOperationException("Google OAuth callback not yet implemented");
    }

    /**
     * Initiates Apple OAuth authentication flow.
     * Redirects the user to Apple's OAuth consent screen.
     *
     * @return A ResponseEntity with redirect information or direct redirect.
     */
    @GetMapping("/oauth/apple")
    @Operation(summary = "Initiate Apple OAuth", description = "Start Apple OAuth authentication flow")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "302", description = "Redirect to Apple OAuth"),
            @ApiResponse(responseCode = "500", description = "OAuth configuration error")
    })
    public ResponseEntity<Map<String, String>> initiateAppleOAuth() {
        log.info("Initiating Apple OAuth flow");

        // TODO: Implement Apple OAuth initiation
        // For now, return a placeholder response
        return ResponseEntity.ok(Map.of(
                "message", "Apple OAuth not yet implemented",
                "status", "pending"
        ));
    }

    /**
     * Handles Apple OAuth callback after user consent.
     * Processes the authorization code and creates/authenticates the user.
     *
     * @param code The authorization code from Apple.
     * @param state The state parameter for CSRF protection.
     * @return A ResponseEntity containing the authentication response.
     */
    @PostMapping("/oauth/apple/callback")
    @Operation(summary = "Handle Apple OAuth callback", description = "Process Apple OAuth authorization code")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OAuth authentication successful"),
            @ApiResponse(responseCode = "400", description = "Invalid authorization code"),
            @ApiResponse(responseCode = "401", description = "OAuth authentication failed")
    })
    public ResponseEntity<AuthResponse> handleAppleOAuthCallback(
            @RequestParam String code,
            @RequestParam(required = false) String state) {

        log.info("Handling Apple OAuth callback");

        // TODO: Implement Apple OAuth callback handling
        // For now, return a placeholder response
        throw new UnsupportedOperationException("Apple OAuth callback not yet implemented");
    }

    /**
     * Extracts the client's IP address from the HttpServletRequest.
     * It checks for 'X-Forwarded-For' and 'X-Real-IP' headers, which are common
     * in proxy/load balancer setups, before falling back to the remote address.
     *
     * @param request The incoming HTTP request.
     * @return The client's IP address as a String.
     */
    private String getClientIpAddress(HttpServletRequest request) {
        // Check for the X-Forwarded-For header, which can contain a comma-separated list of IPs.
        // The first IP is the original client.
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }

        // Check for the X-Real-IP header, another common header for the original IP.
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }

        // Fallback to the direct remote address if no proxy headers are present.
        return request.getRemoteAddr();
    }
}
