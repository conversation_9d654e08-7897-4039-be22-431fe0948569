// API Response Types
export interface ApiResponse<T = any> {
  data: T;
  message: string;
  success: boolean;
  timestamp: string;
}

export interface ApiError {
  message: string;
  code: string;
  details?: Record<string, any>;
  timestamp: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Authentication Types
export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterRequest {
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  organizationName?: string;
  tpin?: string;
}

export interface AuthResponse {
  token: string;
  tokenType: string;
  expiresIn: number;
  user: User;
  message: string;
}

export interface TokenValidationResponse {
  valid: boolean;
  message: string;
  timestamp: number;
}

// User Types
export interface User {
  id: string;
  email: string;
  passwordHash: string;
  firstName: string;
  lastName: string;
  organizationName: string;
  tpin?: string;
  onboardingStatus: 'STARTED' | 'PROFILE_COMPLETE' | 'ACCOUNTS_CONNECTED' | 'ALL_SET';
  preferences: Record<string, any>;
  emailVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface UserProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  organizationName: string;
  tpin?: string;
  onboardingStatus: 'STARTED' | 'PROFILE_COMPLETE' | 'ACCOUNTS_CONNECTED' | 'ALL_SET';
  preferences: Record<string, any>;
  emailVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface UpdateUserRequest {
  firstName?: string;
  lastName?: string;
  organizationName?: string;
  tpin?: string;
  preferences?: Record<string, any>;
}

// Transaction Types
export interface Transaction {
  id: string;
  userId: string;
  financialAccountId: string;
  date: string; // ISO 8601 date string
  description: string;
  amount: number;
  type: 'INCOME' | 'EXPENSE';
  categoryId?: string;
  aiCategoryId?: string;
  aiConfidence?: number;
  aiExplanation?: string;
  status: 'PENDING_CLASSIFICATION' | 'CLASSIFIED' | 'RECONCILED';
  source: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateTransactionRequest {
  financialAccountId: string;
  date: string;
  description: string;
  amount: number;
  type: 'INCOME' | 'EXPENSE';
  categoryId?: string;
  source: string;
}

export interface UpdateTransactionRequest {
  description?: string;
  amount?: number;
  type?: 'INCOME' | 'EXPENSE';
  categoryId?: string;
  status?: 'PENDING_CLASSIFICATION' | 'CLASSIFIED' | 'RECONCILED';
}

export interface CategorizationRequest {
  transactionId: string;
  categoryId: string;
  isAISuggestion: boolean;
  explanation?: string;
}

export interface CategorizationResponse {
  success: boolean;
  transaction: Transaction;
  aiLearning?: {
    feedback: 'positive' | 'negative';
    confidence: number;
  };
}

export interface BulkCategorizationRequest {
  transactions: CategorizationRequest[];
}

export interface BulkCategorizationResponse {
  results: CategorizationResponse[];
}

// Transaction Categorization Events for messaging
export interface TransactionCategorizationRequested {
  transactionId: string;
  description: string;
  amount: number;
  userId: string;
  timestamp: string;
}

export interface TransactionCategorized {
  transactionId: string;
  suggestedCategory: Category;
  confidence: number;
  explanation: string;
  processingTime: number;
}

export interface CategorizationAccepted {
  transactionId: string;
  categoryId: string;
  userAccepted: boolean;
  feedback?: string;
}

export interface CategorizationRejected {
  transactionId: string;
  rejectedCategoryId: string;
  newCategoryId?: string;
  reason?: string;
}

export interface BulkCategorizationCompleted {
  batchId: string;
  processedCount: number;
  successCount: number;
  failureCount: number;
}

// Category Types
export interface Category {
  id: string;
  userId?: string;
  name: string;
  type: 'INCOME' | 'EXPENSE';
  isSystemDefined: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCategoryRequest {
  name: string;
  type: 'INCOME' | 'EXPENSE';
}

// Financial Account Types
export interface FinancialAccount {
  id: string;
  userId: string;
  provider: string;
  accountName: string;
  accountNumberLast4: string;
  balance: number;
  currency: string;
  connectionStatus: 'CONNECTED' | 'DISCONNECTED' | 'ERROR';
  createdAt: string;
  updatedAt: string;
}

export interface ConnectAccountRequest {
  provider: string;
  accountName: string;
  credentials: Record<string, any>; // Provider-specific credentials
}

// Client Types
export interface Client {
  id: string;
  userId: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  tpin?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateClientRequest {
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  tpin?: string;
}

export interface UpdateClientRequest {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  tpin?: string;
}

// Invoice Types
export interface Invoice {
  id: string;
  userId: string;
  clientId: string;
  invoiceNumber: string;
  issueDate: string;
  dueDate: string;
  subTotal: number;
  taxAmount: number;
  totalAmount: number;
  currency: string;
  status: 'DRAFT' | 'PENDING_SUBMISSION' | 'SUBMITTED' | 'PAID' | 'OVERDUE' | 'CANCELLED';
  zraStatus: 'PENDING' | 'SUBMITTED' | 'FAILED' | 'N/A';
  zraInvoiceId?: string;
  paymentTerms?: string;
  notes?: string;
  lineItems: InvoiceLineItem[];
  client: Client;
  createdAt: string;
  updatedAt: string;
}

export interface InvoiceLineItem {
  id: string;
  invoiceId: string;
  description: string;
  quantity: number;
  unitPrice: number;
  amount: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateInvoiceRequest {
  clientId: string;
  issueDate: string;
  dueDate: string;
  currency: string;
  paymentTerms?: string;
  notes?: string;
  lineItems: CreateInvoiceLineItemRequest[];
}

export interface CreateInvoiceLineItemRequest {
  description: string;
  quantity: number;
  unitPrice: number;
}

export interface UpdateInvoiceRequest {
  clientId?: string;
  issueDate?: string;
  dueDate?: string;
  paymentTerms?: string;
  notes?: string;
  lineItems?: CreateInvoiceLineItemRequest[];
}

// Financial Summary Types
export interface FinancialSummary {
  totalIncome: number;
  totalExpenses: number;
  netProfit: number;
  period: {
    startDate: string;
    endDate: string;
  };
  currency: string;
  categoryBreakdown: CategorySummary[];
  accountBalances: AccountBalance[];
}

export interface CategorySummary {
  categoryId: string;
  categoryName: string;
  amount: number;
  type: 'INCOME' | 'EXPENSE';
  transactionCount: number;
}

export interface AccountBalance {
  accountId: string;
  accountName: string;
  provider: string;
  balance: number;
  currency: string;
  lastUpdated: string;
}

// Query Parameters
export interface TransactionQueryParams {
  page?: number;
  limit?: number;
  startDate?: string;
  endDate?: string;
  type?: 'INCOME' | 'EXPENSE';
  categoryId?: string;
  accountId?: string;
  status?: 'PENDING_CLASSIFICATION' | 'CLASSIFIED' | 'RECONCILED';
  search?: string;
}

export interface InvoiceQueryParams {
  page?: number;
  limit?: number;
  status?: 'DRAFT' | 'PENDING_SUBMISSION' | 'SUBMITTED' | 'PAID' | 'OVERDUE' | 'CANCELLED';
  clientId?: string;
  startDate?: string;
  endDate?: string;
}
