package com.intellifin.repository;

import com.intellifin.model.Transaction;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for Transaction entity
 */
@Repository
public interface TransactionRepository extends JpaRepository<Transaction, UUID> {

    // Basic queries by user with optimized fetching
    @EntityGraph(attributePaths = {"category", "aiCategory"})
    Page<Transaction> findByUserIdOrderByDateDescCreatedAtDesc(UUID userId, Pageable pageable);

    @EntityGraph(attributePaths = {"category", "aiCategory"})
    List<Transaction> findByUserIdOrderByDateDescCreatedAtDesc(UUID userId);

    // Queries by status
    Page<Transaction> findByUserIdAndStatusOrderByDateDescCreatedAtDesc(
            UUID userId, Transaction.TransactionStatus status, Pageable pageable);

    List<Transaction> findByUserIdAndStatus(UUID userId, Transaction.TransactionStatus status);

    // Queries by date range
    Page<Transaction> findByUserIdAndDateBetweenOrderByDateDescCreatedAtDesc(
            UUID userId, LocalDate startDate, LocalDate endDate, Pageable pageable);

    // Queries by type
    Page<Transaction> findByUserIdAndTypeOrderByDateDescCreatedAtDesc(
            UUID userId, Transaction.TransactionType type, Pageable pageable);

    // Queries by category
    Page<Transaction> findByUserIdAndCategoryIdOrderByDateDescCreatedAtDesc(
            UUID userId, UUID categoryId, Pageable pageable);

    // Queries by financial account
    Page<Transaction> findByUserIdAndFinancialAccountIdOrderByDateDescCreatedAtDesc(
            UUID userId, UUID financialAccountId, Pageable pageable);

    // AI categorization specific queries
    List<Transaction> findByUserIdAndStatusAndAiCategoryIdIsNull(
            UUID userId, Transaction.TransactionStatus status);

    List<Transaction> findByUserIdAndAiCategoryIdIsNotNull(UUID userId);

    List<Transaction> findByUserIdAndAiConfidenceGreaterThanEqual(UUID userId, BigDecimal minConfidence);

    // Complex search query
    @Query("SELECT t FROM Transaction t WHERE t.userId = :userId " +
           "AND (:status IS NULL OR t.status = :status) " +
           "AND (:type IS NULL OR t.type = :type) " +
           "AND (:categoryId IS NULL OR t.categoryId = :categoryId) " +
           "AND (:accountId IS NULL OR t.financialAccountId = :accountId) " +
           "AND (:startDate IS NULL OR t.date >= :startDate) " +
           "AND (:endDate IS NULL OR t.date <= :endDate) " +
           "AND (:search IS NULL OR LOWER(t.description) LIKE LOWER(CONCAT('%', :search, '%'))) " +
           "ORDER BY t.date DESC, t.createdAt DESC")
    Page<Transaction> findTransactionsWithFilters(
            @Param("userId") UUID userId,
            @Param("status") Transaction.TransactionStatus status,
            @Param("type") Transaction.TransactionType type,
            @Param("categoryId") UUID categoryId,
            @Param("accountId") UUID accountId,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("search") String search,
            Pageable pageable);

    // Statistics queries
    @Query("SELECT COUNT(t) FROM Transaction t WHERE t.userId = :userId AND t.status = :status")
    long countByUserIdAndStatus(@Param("userId") UUID userId, @Param("status") Transaction.TransactionStatus status);

    @Query("SELECT SUM(t.amount) FROM Transaction t WHERE t.userId = :userId AND t.type = :type " +
           "AND (:startDate IS NULL OR t.date >= :startDate) " +
           "AND (:endDate IS NULL OR t.date <= :endDate)")
    Optional<BigDecimal> sumAmountByUserIdAndTypeAndDateRange(
            @Param("userId") UUID userId,
            @Param("type") Transaction.TransactionType type,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate);

    @Query("SELECT t.categoryId, COUNT(t), SUM(t.amount) FROM Transaction t " +
           "WHERE t.userId = :userId AND t.categoryId IS NOT NULL " +
           "AND (:startDate IS NULL OR t.date >= :startDate) " +
           "AND (:endDate IS NULL OR t.date <= :endDate) " +
           "GROUP BY t.categoryId")
    List<Object[]> getCategoryStatistics(
            @Param("userId") UUID userId,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate);

    // Recent transactions
    List<Transaction> findTop10ByUserIdOrderByCreatedAtDesc(UUID userId);

    // AI learning queries
    @Query("SELECT t FROM Transaction t WHERE t.userId = :userId " +
           "AND t.aiCategoryId IS NOT NULL AND t.categoryId IS NOT NULL " +
           "AND t.aiCategoryId = t.categoryId")
    List<Transaction> findAcceptedAISuggestions(@Param("userId") UUID userId);

    @Query("SELECT t FROM Transaction t WHERE t.userId = :userId " +
           "AND t.aiCategoryId IS NOT NULL AND t.categoryId IS NOT NULL " +
           "AND t.aiCategoryId != t.categoryId")
    List<Transaction> findRejectedAISuggestions(@Param("userId") UUID userId);
}
