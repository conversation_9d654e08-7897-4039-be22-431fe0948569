"use client";

import React from 'react';
import { useAccounts } from '../../hooks/useAccounts';
import { Account } from '@intellifin/data-models';

interface AccountSelectorProps {
  value: string | null;
  onChange: (value: string) => void;
  accountType?: Account['type'];
}

const AccountSelector = ({ value, onChange, accountType }: AccountSelectorProps) => {
  const { accounts, loading } = useAccounts();

  const filteredAccounts = accountType
    ? accounts.filter(a => a.type === accountType)
    : accounts;

  return (
    <select
      value={value ?? ''}
      onChange={(e) => onChange(e.target.value)}
      disabled={loading}
      className="border rounded p-2"
    >
      <option value="">{loading ? 'Loading...' : 'Select an account'}</option>
      {filteredAccounts.map(account => (
        <option key={account.id} value={account.id}>
          {account.name} ({account.code})
        </option>
      ))}
    </select>
  );
};

export default AccountSelector;
