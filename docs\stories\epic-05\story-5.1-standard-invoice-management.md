# Story 5.1: Standard Invoice Management
## DETAILED READY-FOR-DEVELOPMENT SPECIFICATION

**Epic:** 5 - Core Business Document Management  
**Priority:** CRITICAL - Core V1 MVP Feature (Pillar 1)  
**Estimated Effort:** 6-7 developer days  
**Dependencies:** Story 2.1 (Chart of Accounts), Story 2.2 (Double-Entry Journal)

---

## User Story

**As a** business owner  
**I want** to create, manage, and track invoices with automatic accounting integration  
**So that** I can bill customers professionally and maintain accurate revenue records

---

## Business Context

This story implements **Standard Invoicing** as a core component of **Pillar 1 (Rock-Solid Accounting Engine)**. It provides essential invoice management capabilities that integrate seamlessly with the double-entry bookkeeping system, ensuring every invoice automatically creates proper accounting entries.

**Why This Matters:**
- Enables professional customer billing with proper invoice formatting
- Automatically creates revenue journal entries when invoices are issued
- Tracks invoice status (Draft, Sent, Paid, Overdue) for cash flow management
- Integrates with Chart of Accounts for proper revenue categorization
- Provides foundation for accounts receivable management
- Supports Zambian business requirements and tax compliance

**V1 MVP Scope:**
- Create and edit invoices with line items
- Professional PDF invoice generation
- Automatic journal entry creation for issued invoices
- Basic invoice status tracking (Draft, Sent, Paid)
- Integration with Chart of Accounts for revenue mapping
- Simple customer management for invoicing

**Explicitly Out of Scope for V1:**
- Advanced invoice templates and customization
- Recurring invoice automation
- Payment gateway integration
- Multi-currency support
- Advanced reporting and analytics

---

## Technical Implementation Details

### 1. Event-Driven Invoice Processing Flow

#### Invoice Lifecycle Events:

```mermaid
graph TD
    A[Create Invoice] --> B[InvoiceDraftCreatedEvent]
    B --> C[Edit Invoice]
    C --> D[Send Invoice]
    D --> E[InvoiceSentEvent]
    E --> F[Journal Entry Creation]
    F --> G[InvoiceIssuedEvent]
    G --> H[Payment Received]
    H --> I[InvoicePaidEvent]
    I --> J[Payment Journal Entry]
```

#### Invoice Event Handler:

**invoice_event_handler.py:**
```python
import asyncio
import logging
from typing import Dict, Any
from datetime import datetime
from decimal import Decimal

from .messaging_service import MessagingService
from .journal_service import JournalService
from .pdf_service import PDFService

class InvoiceEventHandler:
    """
    Handles invoice lifecycle events and coordinates accounting integration.
    """
    
    def __init__(self, messaging_service: MessagingService, 
                 journal_service: JournalService,
                 pdf_service: PDFService):
        self.messaging_service = messaging_service
        self.journal_service = journal_service
        self.pdf_service = pdf_service
        self.logger = logging.getLogger(__name__)
    
    async def handle_invoice_sent(self, event: Dict[str, Any]):
        """
        Process InvoiceSentEvent and create revenue journal entry.
        """
        try:
            invoice_id = event['invoiceId']
            user_id = event['userId']
            total_amount = Decimal(str(event['totalAmount']))
            customer_id = event['customerId']
            line_items = event['lineItems']
            
            self.logger.info(f"Processing invoice sent event for invoice {invoice_id}")
            
            # Create revenue journal entry
            journal_entry_id = await self.journal_service.create_revenue_entry(
                user_id=user_id,
                invoice_id=invoice_id,
                customer_id=customer_id,
                total_amount=total_amount,
                line_items=line_items,
                description=f"Revenue from Invoice #{event['invoiceNumber']}"
            )
            
            # Generate PDF invoice
            pdf_url = await self.pdf_service.generate_invoice_pdf(
                invoice_id=invoice_id,
                user_id=user_id
            )
            
            # Publish invoice issued event
            await self.messaging_service.publish_event('invoice.events', {
                'eventType': 'InvoiceIssuedEvent',
                'invoiceId': invoice_id,
                'userId': user_id,
                'journalEntryId': journal_entry_id,
                'pdfUrl': pdf_url,
                'totalAmount': float(total_amount),
                'timestamp': datetime.utcnow().isoformat()
            })
            
        except Exception as e:
            self.logger.error(f"Failed to process invoice sent event: {str(e)}")
            
            # Publish failure event
            await self.messaging_service.publish_event('invoice.events', {
                'eventType': 'InvoiceProcessingFailed',
                'invoiceId': invoice_id,
                'userId': user_id,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            })
    
    async def handle_invoice_paid(self, event: Dict[str, Any]):
        """
        Process InvoicePaidEvent and create payment journal entry.
        """
        try:
            invoice_id = event['invoiceId']
            user_id = event['userId']
            payment_amount = Decimal(str(event['paymentAmount']))
            payment_method = event['paymentMethod']
            payment_date = event['paymentDate']
            
            self.logger.info(f"Processing invoice payment for invoice {invoice_id}")
            
            # Create payment journal entry
            payment_journal_id = await self.journal_service.create_payment_entry(
                user_id=user_id,
                invoice_id=invoice_id,
                payment_amount=payment_amount,
                payment_method=payment_method,
                payment_date=payment_date,
                description=f"Payment received for Invoice #{event['invoiceNumber']}"
            )
            
            # Publish payment processed event
            await self.messaging_service.publish_event('invoice.events', {
                'eventType': 'InvoicePaymentProcessed',
                'invoiceId': invoice_id,
                'userId': user_id,
                'paymentJournalEntryId': payment_journal_id,
                'paymentAmount': float(payment_amount),
                'timestamp': datetime.utcnow().isoformat()
            })
            
        except Exception as e:
            self.logger.error(f"Failed to process invoice payment: {str(e)}")
```

### 2. Frontend Implementation - Invoice Management Interface

#### Invoice Management Dashboard:

**InvoiceManagementDashboard.tsx:**
```typescript
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Plus, FileText, Send, DollarSign, Calendar } from 'lucide-react';
import { invoiceService } from '@/services/invoiceService';

interface Invoice {
  id: string;
  invoiceNumber: string;
  customerId: string;
  customerName: string;
  totalAmount: number;
  status: 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE';
  issueDate: string;
  dueDate: string;
  pdfUrl?: string;
}

export const InvoiceManagementDashboard: React.FC = () => {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<string>('ALL');

  useEffect(() => {
    loadInvoices();
  }, [filter]);

  const loadInvoices = async () => {
    try {
      setLoading(true);
      const data = await invoiceService.getInvoices(filter);
      setInvoices(data);
    } catch (error) {
      console.error('Failed to load invoices:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      DRAFT: 'bg-gray-100 text-gray-800',
      SENT: 'bg-blue-100 text-blue-800',
      PAID: 'bg-green-100 text-green-800',
      OVERDUE: 'bg-red-100 text-red-800'
    };
    return <Badge className={variants[status as keyof typeof variants]}>{status}</Badge>;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZM', {
      style: 'currency',
      currency: 'ZMW',
      minimumFractionDigits: 2
    }).format(amount);
  };

  return (
    <div className="invoice-dashboard max-w-7xl mx-auto p-6">
      <div className="header mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Invoice Management</h1>
            <p className="text-gray-600 mt-2">Create and manage customer invoices</p>
          </div>
          <Button className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Create Invoice
          </Button>
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="filter-tabs mb-6">
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
          {['ALL', 'DRAFT', 'SENT', 'PAID', 'OVERDUE'].map((status) => (
            <button
              key={status}
              onClick={() => setFilter(status)}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                filter === status
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {status}
            </button>
          ))}
        </div>
      </div>

      {/* Invoice List */}
      <div className="invoice-list space-y-4">
        {invoices.map((invoice) => (
          <Card key={invoice.id} className="invoice-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="invoice-icon">
                    <FileText className="h-8 w-8 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">#{invoice.invoiceNumber}</h3>
                    <p className="text-gray-600">{invoice.customerName}</p>
                    <p className="text-sm text-gray-500">
                      Due: {new Date(invoice.dueDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <p className="text-2xl font-bold">{formatCurrency(invoice.totalAmount)}</p>
                    {getStatusBadge(invoice.status)}
                  </div>
                  
                  <div className="actions flex space-x-2">
                    <Button variant="outline" size="sm">
                      Edit
                    </Button>
                    {invoice.status === 'DRAFT' && (
                      <Button size="sm" className="flex items-center gap-1">
                        <Send className="h-3 w-3" />
                        Send
                      </Button>
                    )}
                    {invoice.pdfUrl && (
                      <Button variant="outline" size="sm">
                        Download PDF
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};
```

### 3. Backend Implementation - Invoice Service

#### Invoice Management Service:

**InvoiceService.java:**
```java
@Service
@Transactional
public class InvoiceService {

    private final InvoiceRepository invoiceRepository;
    private final CustomerRepository customerRepository;
    private final JournalEntryService journalEntryService;
    private final ApplicationEventPublisher eventPublisher;
    private final InvoiceNumberGenerator numberGenerator;

    public InvoiceService(InvoiceRepository invoiceRepository,
                         CustomerRepository customerRepository,
                         JournalEntryService journalEntryService,
                         ApplicationEventPublisher eventPublisher,
                         InvoiceNumberGenerator numberGenerator) {
        this.invoiceRepository = invoiceRepository;
        this.customerRepository = customerRepository;
        this.journalEntryService = journalEntryService;
        this.eventPublisher = eventPublisher;
        this.numberGenerator = numberGenerator;
    }

    public InvoiceDTO createInvoice(CreateInvoiceRequest request, String userId) {
        try {
            // Validate customer exists
            Customer customer = customerRepository.findByIdAndUserId(request.getCustomerId(), userId)
                .orElseThrow(() -> new EntityNotFoundException("Customer not found"));

            // Generate invoice number
            String invoiceNumber = numberGenerator.generateInvoiceNumber(userId);

            // Create invoice entity
            Invoice invoice = Invoice.builder()
                .userId(userId)
                .invoiceNumber(invoiceNumber)
                .customerId(request.getCustomerId())
                .issueDate(request.getIssueDate())
                .dueDate(request.getDueDate())
                .status(InvoiceStatus.DRAFT)
                .notes(request.getNotes())
                .build();

            // Add line items
            List<InvoiceLineItem> lineItems = request.getLineItems().stream()
                .map(item -> InvoiceLineItem.builder()
                    .invoice(invoice)
                    .description(item.getDescription())
                    .quantity(item.getQuantity())
                    .unitPrice(item.getUnitPrice())
                    .totalAmount(item.getQuantity().multiply(item.getUnitPrice()))
                    .revenueAccountId(item.getRevenueAccountId())
                    .build())
                .collect(Collectors.toList());

            invoice.setLineItems(lineItems);

            // Calculate total
            BigDecimal totalAmount = lineItems.stream()
                .map(InvoiceLineItem::getTotalAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            invoice.setTotalAmount(totalAmount);

            // Save invoice
            Invoice savedInvoice = invoiceRepository.save(invoice);

            // Publish draft created event
            eventPublisher.publishEvent(new InvoiceDraftCreatedEvent(
                savedInvoice.getId().toString(),
                userId,
                invoiceNumber,
                customer.getId().toString(),
                totalAmount
            ));

            return mapToDTO(savedInvoice);

        } catch (Exception e) {
            log.error("Failed to create invoice for user {}: {}", userId, e.getMessage(), e);
            throw new BusinessException("Failed to create invoice: " + e.getMessage());
        }
    }

    public InvoiceDTO sendInvoice(Long invoiceId, String userId) {
        try {
            Invoice invoice = invoiceRepository.findByIdAndUserId(invoiceId, userId)
                .orElseThrow(() -> new EntityNotFoundException("Invoice not found"));

            if (invoice.getStatus() != InvoiceStatus.DRAFT) {
                throw new BusinessException("Only draft invoices can be sent");
            }

            // Update status
            invoice.setStatus(InvoiceStatus.SENT);
            invoice.setSentDate(LocalDateTime.now());
            Invoice savedInvoice = invoiceRepository.save(invoice);

            // Publish invoice sent event
            eventPublisher.publishEvent(new InvoiceSentEvent(
                savedInvoice.getId().toString(),
                userId,
                savedInvoice.getInvoiceNumber(),
                savedInvoice.getCustomerId().toString(),
                savedInvoice.getTotalAmount(),
                savedInvoice.getLineItems().stream()
                    .map(this::mapLineItemToEvent)
                    .collect(Collectors.toList())
            ));

            return mapToDTO(savedInvoice);

        } catch (Exception e) {
            log.error("Failed to send invoice {}: {}", invoiceId, e.getMessage(), e);
            throw new BusinessException("Failed to send invoice: " + e.getMessage());
        }
    }

    public InvoiceDTO markAsPaid(Long invoiceId, MarkAsPaidRequest request, String userId) {
        try {
            Invoice invoice = invoiceRepository.findByIdAndUserId(invoiceId, userId)
                .orElseThrow(() -> new EntityNotFoundException("Invoice not found"));

            if (invoice.getStatus() != InvoiceStatus.SENT) {
                throw new BusinessException("Only sent invoices can be marked as paid");
            }

            // Update status
            invoice.setStatus(InvoiceStatus.PAID);
            invoice.setPaidDate(LocalDateTime.now());
            invoice.setPaidAmount(request.getPaymentAmount());
            invoice.setPaymentMethod(request.getPaymentMethod());
            Invoice savedInvoice = invoiceRepository.save(invoice);

            // Publish invoice paid event
            eventPublisher.publishEvent(new InvoicePaidEvent(
                savedInvoice.getId().toString(),
                userId,
                savedInvoice.getInvoiceNumber(),
                request.getPaymentAmount(),
                request.getPaymentMethod(),
                request.getPaymentDate()
            ));

            return mapToDTO(savedInvoice);

        } catch (Exception e) {
            log.error("Failed to mark invoice {} as paid: {}", invoiceId, e.getMessage(), e);
            throw new BusinessException("Failed to mark invoice as paid: " + e.getMessage());
        }
    }

    public List<InvoiceDTO> getInvoices(String userId, InvoiceStatus status) {
        List<Invoice> invoices;

        if (status != null) {
            invoices = invoiceRepository.findByUserIdAndStatusOrderByCreatedAtDesc(userId, status);
        } else {
            invoices = invoiceRepository.findByUserIdOrderByCreatedAtDesc(userId);
        }

        return invoices.stream()
            .map(this::mapToDTO)
            .collect(Collectors.toList());
    }

    private InvoiceDTO mapToDTO(Invoice invoice) {
        Customer customer = customerRepository.findById(invoice.getCustomerId())
            .orElse(null);

        return InvoiceDTO.builder()
            .id(invoice.getId())
            .invoiceNumber(invoice.getInvoiceNumber())
            .customerId(invoice.getCustomerId().toString())
            .customerName(customer != null ? customer.getName() : "Unknown Customer")
            .totalAmount(invoice.getTotalAmount())
            .status(invoice.getStatus())
            .issueDate(invoice.getIssueDate())
            .dueDate(invoice.getDueDate())
            .sentDate(invoice.getSentDate())
            .paidDate(invoice.getPaidDate())
            .pdfUrl(invoice.getPdfUrl())
            .lineItems(invoice.getLineItems().stream()
                .map(this::mapLineItemToDTO)
                .collect(Collectors.toList()))
            .build();
    }

    private InvoiceLineItemDTO mapLineItemToDTO(InvoiceLineItem item) {
        return InvoiceLineItemDTO.builder()
            .id(item.getId())
            .description(item.getDescription())
            .quantity(item.getQuantity())
            .unitPrice(item.getUnitPrice())
            .totalAmount(item.getTotalAmount())
            .revenueAccountId(item.getRevenueAccountId())
            .build();
    }
}
```

#### REST Controller for Invoice Management:

**InvoiceController.java:**
```java
@RestController
@RequestMapping("/api/v1/invoices")
@Validated
public class InvoiceController {

    private final InvoiceService invoiceService;
    private final SecurityService securityService;

    public InvoiceController(InvoiceService invoiceService, SecurityService securityService) {
        this.invoiceService = invoiceService;
        this.securityService = securityService;
    }

    @PostMapping
    public ResponseEntity<InvoiceDTO> createInvoice(
            @RequestBody @Valid CreateInvoiceRequest request,
            Authentication authentication) {

        String userId = securityService.getCurrentUserId(authentication);
        InvoiceDTO invoice = invoiceService.createInvoice(request, userId);
        return ResponseEntity.status(HttpStatus.CREATED).body(invoice);
    }

    @GetMapping
    public ResponseEntity<List<InvoiceDTO>> getInvoices(
            @RequestParam(required = false) String status,
            Authentication authentication) {

        String userId = securityService.getCurrentUserId(authentication);
        InvoiceStatus invoiceStatus = status != null ? InvoiceStatus.valueOf(status) : null;
        List<InvoiceDTO> invoices = invoiceService.getInvoices(userId, invoiceStatus);
        return ResponseEntity.ok(invoices);
    }

    @GetMapping("/{invoiceId}")
    public ResponseEntity<InvoiceDTO> getInvoice(
            @PathVariable Long invoiceId,
            Authentication authentication) {

        String userId = securityService.getCurrentUserId(authentication);
        InvoiceDTO invoice = invoiceService.getInvoice(invoiceId, userId);
        return ResponseEntity.ok(invoice);
    }

    @PutMapping("/{invoiceId}")
    public ResponseEntity<InvoiceDTO> updateInvoice(
            @PathVariable Long invoiceId,
            @RequestBody @Valid UpdateInvoiceRequest request,
            Authentication authentication) {

        String userId = securityService.getCurrentUserId(authentication);
        InvoiceDTO invoice = invoiceService.updateInvoice(invoiceId, request, userId);
        return ResponseEntity.ok(invoice);
    }

    @PostMapping("/{invoiceId}/send")
    public ResponseEntity<InvoiceDTO> sendInvoice(
            @PathVariable Long invoiceId,
            Authentication authentication) {

        String userId = securityService.getCurrentUserId(authentication);
        InvoiceDTO invoice = invoiceService.sendInvoice(invoiceId, userId);
        return ResponseEntity.ok(invoice);
    }

    @PostMapping("/{invoiceId}/mark-paid")
    public ResponseEntity<InvoiceDTO> markAsPaid(
            @PathVariable Long invoiceId,
            @RequestBody @Valid MarkAsPaidRequest request,
            Authentication authentication) {

        String userId = securityService.getCurrentUserId(authentication);
        InvoiceDTO invoice = invoiceService.markAsPaid(invoiceId, request, userId);
        return ResponseEntity.ok(invoice);
    }

    @GetMapping("/{invoiceId}/pdf")
    public ResponseEntity<byte[]> downloadInvoicePDF(
            @PathVariable Long invoiceId,
            Authentication authentication) {

        String userId = securityService.getCurrentUserId(authentication);

        try {
            byte[] pdfData = invoiceService.generateInvoicePDF(invoiceId, userId);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData("attachment", "invoice-" + invoiceId + ".pdf");

            return ResponseEntity.ok()
                .headers(headers)
                .body(pdfData);

        } catch (EntityNotFoundException e) {
            return ResponseEntity.notFound().build();
        }
    }
}
```

---

## Acceptance Criteria

### Core Invoice Management:
- [ ] **Invoice Creation**: Users can create invoices with multiple line items and customer details
- [ ] **Invoice Editing**: Draft invoices can be edited before sending
- [ ] **Invoice Sending**: Draft invoices can be sent to customers with status change to "SENT"
- [ ] **Payment Tracking**: Sent invoices can be marked as paid with payment details
- [ ] **Invoice Listing**: Users can view all invoices with filtering by status
- [ ] **PDF Generation**: Professional PDF invoices are generated automatically when sent

### Accounting Integration:
- [ ] **Revenue Journal Entries**: Sending an invoice automatically creates revenue journal entry
- [ ] **Payment Journal Entries**: Marking invoice as paid creates payment journal entry
- [ ] **Account Mapping**: Line items map to correct revenue accounts from Chart of Accounts
- [ ] **Double-Entry Validation**: All journal entries maintain proper debit/credit balance
- [ ] **Accounts Receivable**: Unpaid invoices create accounts receivable entries

### Event-Driven Architecture:
- [ ] **Invoice Events**: Publishes `InvoiceDraftCreated`, `InvoiceSent`, `InvoicePaid` events
- [ ] **Journal Integration**: Events trigger automatic journal entry creation
- [ ] **Status Tracking**: Invoice status updates are reflected in real-time
- [ ] **Error Handling**: Failed operations are properly logged and handled

### User Interface:
- [ ] **Clean Dashboard**: Intuitive invoice management interface with status filtering
- [ ] **Invoice Form**: User-friendly form for creating and editing invoices
- [ ] **Status Indicators**: Clear visual indicators for invoice status
- [ ] **PDF Download**: Easy access to generated invoice PDFs
- [ ] **Customer Management**: Basic customer information management for invoicing

### Business Requirements:
- [ ] **Invoice Numbering**: Automatic sequential invoice number generation
- [ ] **Due Date Tracking**: Support for invoice due dates and overdue status
- [ ] **Line Item Support**: Multiple line items with quantity, unit price, and totals
- [ ] **Tax Compliance**: Basic structure for Zambian tax requirements
- [ ] **Professional Format**: Clean, professional invoice PDF layout

---

## API Contracts

### Invoice Management API:

```typescript
interface InvoiceLineItemDTO {
  id?: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  revenueAccountId: string;
}

interface InvoiceDTO {
  id: string;
  invoiceNumber: string;
  customerId: string;
  customerName: string;
  totalAmount: number;
  status: 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE';
  issueDate: string;
  dueDate: string;
  sentDate?: string;
  paidDate?: string;
  pdfUrl?: string;
  lineItems: InvoiceLineItemDTO[];
  notes?: string;
}

interface CreateInvoiceRequest {
  customerId: string;
  issueDate: string;
  dueDate: string;
  lineItems: InvoiceLineItemDTO[];
  notes?: string;
}

interface UpdateInvoiceRequest {
  customerId?: string;
  issueDate?: string;
  dueDate?: string;
  lineItems?: InvoiceLineItemDTO[];
  notes?: string;
}

interface MarkAsPaidRequest {
  paymentAmount: number;
  paymentMethod: string;
  paymentDate: string;
  notes?: string;
}

interface InvoiceAPI {
  POST /api/v1/invoices: {
    body: CreateInvoiceRequest;
    response: InvoiceDTO;
    errors: {
      400: "Invalid request",
      404: "Customer not found"
    };
  };

  GET /api/v1/invoices: {
    query: { status?: string; };
    response: InvoiceDTO[];
    errors: { 401: "Unauthorized" };
  };

  GET /api/v1/invoices/{id}: {
    response: InvoiceDTO;
    errors: {
      404: "Invoice not found",
      403: "Access denied"
    };
  };

  PUT /api/v1/invoices/{id}: {
    body: UpdateInvoiceRequest;
    response: InvoiceDTO;
    errors: {
      400: "Invalid request",
      404: "Invoice not found",
      409: "Invoice cannot be modified"
    };
  };

  POST /api/v1/invoices/{id}/send: {
    response: InvoiceDTO;
    errors: {
      404: "Invoice not found",
      409: "Invoice cannot be sent"
    };
  };

  POST /api/v1/invoices/{id}/mark-paid: {
    body: MarkAsPaidRequest;
    response: InvoiceDTO;
    errors: {
      400: "Invalid payment details",
      404: "Invoice not found",
      409: "Invoice cannot be marked as paid"
    };
  };

  GET /api/v1/invoices/{id}/pdf: {
    response: "application/pdf";
    errors: {
      404: "Invoice not found",
      500: "PDF generation failed"
    };
  };
}
```

### Event Contracts:

```typescript
interface InvoiceDraftCreatedEvent {
  eventType: 'InvoiceDraftCreated';
  invoiceId: string;
  userId: string;
  invoiceNumber: string;
  customerId: string;
  totalAmount: number;
  timestamp: string;
}

interface InvoiceSentEvent {
  eventType: 'InvoiceSent';
  invoiceId: string;
  userId: string;
  invoiceNumber: string;
  customerId: string;
  totalAmount: number;
  lineItems: {
    description: string;
    quantity: number;
    unitPrice: number;
    totalAmount: number;
    revenueAccountId: string;
  }[];
  timestamp: string;
}

interface InvoiceIssuedEvent {
  eventType: 'InvoiceIssued';
  invoiceId: string;
  userId: string;
  journalEntryId: string;
  pdfUrl: string;
  totalAmount: number;
  timestamp: string;
}

interface InvoicePaidEvent {
  eventType: 'InvoicePaid';
  invoiceId: string;
  userId: string;
  invoiceNumber: string;
  paymentAmount: number;
  paymentMethod: string;
  paymentDate: string;
  timestamp: string;
}

interface InvoicePaymentProcessedEvent {
  eventType: 'InvoicePaymentProcessed';
  invoiceId: string;
  userId: string;
  paymentJournalEntryId: string;
  paymentAmount: number;
  timestamp: string;
}
```

---

## Error Handling & Edge Cases

### Invoice Creation Errors:
- **Invalid Customer**: Validate customer exists and belongs to user
- **Invalid Line Items**: Validate quantities, prices, and account mappings
- **Duplicate Invoice Numbers**: Ensure unique invoice numbering per user
- **Invalid Dates**: Validate issue and due dates are logical

### Journal Entry Failures:
- **Account Mapping Errors**: Fallback to default revenue accounts with manual review
- **Unbalanced Entries**: Reject and retry with error correction
- **Concurrent Modifications**: Optimistic locking to prevent race conditions
- **Journal Service Unavailable**: Queue operations for retry

### PDF Generation Issues:
- **Template Errors**: Fallback to basic template with error logging
- **File Storage Failures**: Retry with exponential backoff
- **Large Invoice Data**: Optimize for performance and memory usage
- **Network Issues**: Proper timeout and retry mechanisms

### Business Logic Validation:
- **Status Transitions**: Enforce valid invoice status transitions
- **Payment Validation**: Ensure payment amounts don't exceed invoice totals
- **Date Validation**: Prevent backdating beyond reasonable limits
- **Permission Checks**: Ensure users can only access their own invoices

---

## Performance Requirements

### Response Times:
- **Invoice Creation**: <2 seconds for invoice with up to 20 line items
- **Invoice Listing**: <1 second for up to 1000 invoices
- **PDF Generation**: <5 seconds for standard invoice
- **Status Updates**: <500ms for send/paid operations

### Scalability:
- **Concurrent Users**: Support 100+ simultaneous invoice operations
- **Database Performance**: <100ms query response times
- **PDF Generation**: Handle 50+ concurrent PDF requests
- **Event Processing**: Process 1000+ invoice events per hour

### Storage:
- **PDF Storage**: Efficient storage and retrieval of invoice PDFs
- **Database Optimization**: Proper indexing for invoice queries
- **Memory Usage**: <256MB per invoice operation
- **File Cleanup**: Automatic cleanup of old PDF files

---

## Definition of Done

### Core Functionality:
- [ ] **Complete Invoice Lifecycle**: Create, edit, send, and mark as paid workflows
- [ ] **Accounting Integration**: Automatic journal entry creation for revenue and payments
- [ ] **PDF Generation**: Professional invoice PDFs with proper formatting
- [ ] **Status Management**: Proper invoice status tracking and transitions
- [ ] **Customer Integration**: Basic customer management for invoicing

### Technical Implementation:
- [ ] **Event-Driven Architecture**: Proper event publishing and consumption
- [ ] **Database Design**: Optimized schema for invoice and line item storage
- [ ] **API Implementation**: Complete REST API with proper validation
- [ ] **Error Handling**: Comprehensive error handling with user feedback
- [ ] **Security**: Proper authorization and data protection

### Quality Assurance:
- [ ] **Unit Tests**: >90% code coverage for invoice business logic
- [ ] **Integration Tests**: End-to-end tests for complete invoice workflows
- [ ] **PDF Tests**: Validation of PDF generation and formatting
- [ ] **Performance Tests**: Load testing confirms performance requirements
- [ ] **Security Tests**: Authorization and data access validation

### Documentation & Deployment:
- [ ] **API Documentation**: Complete OpenAPI specification
- [ ] **User Documentation**: Invoice management user guide
- [ ] **Deployment Scripts**: Docker containers and configurations
- [ ] **Monitoring**: Application metrics and alerting setup
- [ ] **Database Migrations**: Proper schema migration scripts

### Business Validation:
- [ ] **Zambian Compliance**: Invoice format meets local business requirements
- [ ] **Accounting Accuracy**: All journal entries are mathematically correct
- [ ] **User Acceptance**: Stakeholder approval of invoice interface
- [ ] **Integration Testing**: Successful integration with Chart of Accounts and Journal systems
- [ ] **Performance Validation**: Meets business performance requirements

---

## Dependencies

### Required Stories (Must be completed first):
- **Story 2.1**: Chart of Accounts & Category Management - Required for revenue account mapping
- **Story 2.2**: Double-Entry Journal System - Required for automatic journal entry creation

### External Dependencies:
- **PDF Generation Library**: For creating professional invoice PDFs
- **Event Bus**: RabbitMQ (local) or Azure Service Bus (production)
- **Database**: PostgreSQL for invoice and customer data storage
- **File Storage**: For storing generated PDF invoices

---

## Implementation Notes

### Accounting Best Practices:
- Always create journal entries when invoices are sent (revenue recognition)
- Create payment journal entries when invoices are marked as paid
- Maintain proper audit trails for all invoice and payment operations
- Ensure integration with existing Chart of Accounts structure

### Event-Driven Considerations:
- Implement idempotency for all event handlers
- Use correlation IDs for tracking invoice operations across services
- Implement proper dead letter queue handling for failed operations
- Ensure event ordering for invoice state changes

### PDF Generation:
- Use professional invoice templates with company branding
- Include all required business information (tax numbers, addresses, etc.)
- Optimize for performance and memory usage
- Implement proper error handling and fallback templates

### User Experience:
- Provide clear status indicators for invoice lifecycle
- Implement real-time updates for invoice status changes
- Ensure intuitive invoice creation and editing workflows
- Include helpful validation messages and error feedback

---

**Status**: 🔄 **READY FOR DEVELOPMENT**
**Next Story**: [Story 5.2: Basic Bill Management](story-5.2-basic-bill-management.md)
```
