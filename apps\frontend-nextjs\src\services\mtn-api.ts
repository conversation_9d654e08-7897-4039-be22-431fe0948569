import { apiClient } from './apiClient';

export interface MTNConnectionRequest {
  userId: string;
  accountName: string;
  phoneNumber: string;
}

export interface MTNOAuthResponse {
  authorizationUrl: string;
  state: string;
  expiresAt: string;
}

export interface MTNConnectionStatus {
  accountId: string;
  status: 'CONNECTING' | 'CONNECTED' | 'DISCONNECTED' | 'ERROR';
  lastSync?: string;
  errorMessage?: string;
  accountInfo?: {
    phoneNumber: string;
    accountName: string;
    balance?: number;
    currency?: string;
  };
}

export interface DocumentUploadRequest {
  userId: string;
  accountType: 'MTN_MOBILE_MONEY';
  fileName: string;
  fileType: 'PDF' | 'CSV' | 'EXCEL';
  fileSizeBytes: number;
  accountName?: string;
}

export interface DocumentUploadResponse {
  uploadId: string;
  uploadUrl: string;
  expiresAt: string;
}

export interface DocumentProcessingStatus {
  uploadId: string;
  status: 'UPLOADING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  progress?: number;
  extractedTransactions?: number;
  errorMessage?: string;
  processedAt?: string;
  confidenceScore?: number;
}

export class MTNApiService {
  /**
   * Get all financial accounts for the authenticated user
   */
  static async getUserAccounts(): Promise<MTNConnectionStatus[]> {
    const response = await apiClient.get('/api/v1/financial-accounts');
    return response.data;
  }

  /**
   * Get MTN Mobile Money accounts for the authenticated user
   */
  static async getUserMTNAccounts(): Promise<MTNConnectionStatus[]> {
    const response = await apiClient.get('/api/v1/financial-accounts/mtn');
    return response.data;
  }

  /**
   * Get financial account status
   */
  static async getAccountStatus(accountId: string): Promise<MTNConnectionStatus> {
    const response = await apiClient.get(`/api/v1/financial-accounts/${accountId}/status`);
    return response.data;
  }

  /**
   * Initiate MTN Mobile Money connection
   */
  static async initiateMTNConnection(request: MTNConnectionRequest): Promise<MTNOAuthResponse> {
    const response = await apiClient.post('/api/v1/financial-accounts/connect/mtn', request);
    return response.data;
  }

  /**
   * Handle MTN OAuth callback
   */
  static async handleMTNCallback(code: string, state: string): Promise<{ success: boolean; message?: string; error?: string }> {
    const response = await apiClient.post('/api/v1/financial-accounts/mtn/callback', {
      code,
      state
    });
    return response.data;
  }

  /**
   * Disconnect financial account
   */
  static async disconnectAccount(accountId: string): Promise<{ success: boolean; message?: string; error?: string }> {
    const response = await apiClient.delete(`/api/v1/financial-accounts/${accountId}/disconnect`);
    return response.data;
  }

  /**
   * Initiate document upload
   */
  static async initiateDocumentUpload(request: DocumentUploadRequest): Promise<DocumentUploadResponse> {
    const response = await apiClient.post('/api/v1/financial-accounts/upload/initiate', request);
    return response.data;
  }

  /**
   * Get document upload status
   */
  static async getDocumentUploadStatus(uploadId: string): Promise<DocumentProcessingStatus> {
    const response = await apiClient.get(`/api/v1/financial-accounts/upload/${uploadId}/status`);
    return response.data;
  }

  /**
   * Confirm document upload completion
   */
  static async confirmDocumentUpload(uploadId: string): Promise<{ success: boolean; message?: string; error?: string }> {
    const response = await apiClient.post(`/api/v1/financial-accounts/upload/${uploadId}/confirm`);
    return response.data;
  }

  /**
   * Check if user has connected accounts
   */
  static async getConnectionStatus(): Promise<{ hasConnectedAccounts: boolean; userId: string }> {
    const response = await apiClient.get('/api/v1/financial-accounts/connected/status');
    return response.data;
  }

  /**
   * Upload file to pre-signed URL (mock implementation)
   */
  static async uploadFileToUrl(uploadUrl: string, file: File): Promise<boolean> {
    try {
      // In a real implementation, this would upload to the pre-signed URL
      // For now, we'll simulate the upload
      const formData = new FormData();
      formData.append('file', file);

      // Simulate upload progress
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      return true;
    } catch (error) {
      console.error('Failed to upload file:', error);
      return false;
    }
  }

  /**
   * Format phone number for MTN (Zambian format)
   */
  static formatPhoneNumber(phoneNumber: string): string {
    // Remove all non-digits
    const digits = phoneNumber.replace(/\D/g, '');
    
    // Format as +260 XX XXX XXXX for Zambian numbers
    if (digits.startsWith('260')) {
      return digits.replace(/(\d{3})(\d{2})(\d{3})(\d{4})/, '+$1 $2 $3 $4');
    } else if (digits.startsWith('0')) {
      // Convert local format (0XX) to international (+260XX)
      const international = '260' + digits.substring(1);
      return MTNApiService.formatPhoneNumber(international);
    }
    
    return phoneNumber;
  }

  /**
   * Validate phone number format
   */
  static isValidPhoneNumber(phoneNumber: string): boolean {
    const digits = phoneNumber.replace(/\D/g, '');
    
    // Check for Zambian mobile numbers
    if (digits.startsWith('260')) {
      return digits.length === 12; // +260 XX XXX XXXX
    } else if (digits.startsWith('0')) {
      return digits.length === 10; // 0XX XXX XXXX
    }
    
    return false;
  }

  /**
   * Get supported file types for document upload
   */
  static getSupportedFileTypes(): string[] {
    return ['PDF', 'CSV', 'EXCEL'];
  }

  /**
   * Get maximum file size for document upload (in bytes)
   */
  static getMaxFileSize(): number {
    return 10 * 1024 * 1024; // 10MB
  }

  /**
   * Validate file for upload
   */
  static validateFile(file: File): { valid: boolean; error?: string } {
    const maxSize = MTNApiService.getMaxFileSize();
    
    if (file.size > maxSize) {
      return {
        valid: false,
        error: `File size must be less than ${Math.round(maxSize / (1024 * 1024))}MB`
      };
    }

    const fileName = file.name.toLowerCase();
    const supportedExtensions = ['.pdf', '.csv', '.xls', '.xlsx'];
    const isValidType = supportedExtensions.some(ext => fileName.endsWith(ext));

    if (!isValidType) {
      return {
        valid: false,
        error: 'Please select a PDF, CSV, or Excel file'
      };
    }

    return { valid: true };
  }

  /**
   * Get file type from file
   */
  static getFileType(file: File): 'PDF' | 'CSV' | 'EXCEL' {
    const fileName = file.name.toLowerCase();
    if (fileName.endsWith('.pdf')) return 'PDF';
    if (fileName.endsWith('.csv')) return 'CSV';
    return 'EXCEL';
  }

  /**
   * Format file size for display
   */
  static formatFileSize(bytes: number): string {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  }

  /**
   * Format date for display
   */
  static formatDate(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minutes ago`;
    if (diffHours < 24) return `${diffHours} hours ago`;
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    
    return date.toLocaleDateString();
  }
}
