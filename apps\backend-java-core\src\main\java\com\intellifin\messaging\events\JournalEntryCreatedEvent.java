package com.intellifin.messaging.events;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Event published when a journal entry is created
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JournalEntryCreatedEvent {
    
    private UUID journalEntryId;
    private String entryNumber;
    private UUID userId;
    private UUID transactionId;
    private String description;
    private BigDecimal totalAmount;
    private String status;
    private LocalDateTime entryDate;
    private String sourceType;
    private String sourceId;
    private List<JournalEntryLineEvent> lines;
    private LocalDateTime timestamp;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class JournalEntryLineEvent {
        private UUID lineId;
        private UUID accountId;
        private String accountCode;
        private String accountName;
        private BigDecimal debitAmount;
        private BigDecimal creditAmount;
        private String description;
    }
}
