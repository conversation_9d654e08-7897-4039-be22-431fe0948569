# Story 2.1: Chart of Accounts & Category Mapping Implementation

**Epic:** Core Financial Engine & Accounting Integrity  
**Status:** Ready for Development  
**Priority:** Critical  
**Story Points:** 8

## User Story

**As a** business owner  
**I want** a proper Chart of Accounts with category mapping  
**So that** my financial transactions follow standard accounting principles and maintain audit integrity

## Acceptance Criteria

- [ ] Account data model is implemented with proper hierarchy (Assets, Liabilities, Equity, Income, Expenses)
- [ ] Standard Chart of Accounts is seeded in database with Zambian business context
- [ ] Category management UI allows users to create and manage custom categories
- [ ] Each user Category is mapped to a formal Account for proper accounting
- [ ] Account hierarchy supports sub-accounts (e.g., Current Assets > Cash > Bank Accounts)
- [ ] Account codes follow standard numbering convention (1000-1999 Assets, 2000-2999 Liabilities, etc.)
- [ ] Category-to-Account mapping is validated to ensure accounting integrity
- [ ] System prevents deletion of Accounts that have associated transactions
- [ ] Account balances are calculated correctly based on account type (debit/credit normal balance)
- [ ] API endpoints support CRUD operations for both Accounts and Categories
- [ ] Account search and filtering capabilities for large Chart of Accounts
- [ ] Audit trail tracks all changes to Chart of Accounts structure
- [ ] Data migration preserves existing transaction categories during Account implementation

## Technical Implementation

### Backend Changes
- `src/main/java/com/intellifin/model/Account.java` - Account entity with hierarchy support
- `src/main/java/com/intellifin/model/Category.java` - Enhanced Category entity with Account mapping
- `src/main/java/com/intellifin/repository/AccountRepository.java` - Account data access
- `src/main/java/com/intellifin/service/AccountService.java` - Account business logic
- `src/main/java/com/intellifin/service/CategoryService.java` - Enhanced Category service with Account mapping
- `src/main/java/com/intellifin/controller/AccountController.java` - Account API endpoints
- `src/main/resources/db/migration/` - Database migrations for Account and Category updates

### Frontend Changes
- `src/components/financial/ChartOfAccounts.tsx` - Chart of Accounts management interface
- `src/components/financial/AccountSelector.tsx` - Account selection component
- `src/components/financial/CategoryAccountMapping.tsx` - Category-to-Account mapping interface
- `src/hooks/useAccounts.ts` - Account data management
- `src/hooks/useCategories.ts` - Enhanced category management with Account mapping
- `src/stores/accountStore.ts` - Account state management

### Database Changes
- `accounts` table with hierarchy and account type support
- Enhanced `categories` table with `account_id` foreign key
- `account_balances` view for real-time balance calculations
- Indexes for performance on account hierarchy queries

## API Contracts

```typescript
interface Account {
  id: string;
  code: string; // e.g., "1100", "2000"
  name: string; // e.g., "Cash and Cash Equivalents"
  description?: string;
  type: 'ASSET' | 'LIABILITY' | 'EQUITY' | 'INCOME' | 'EXPENSE';
  subType: string; // e.g., "CURRENT_ASSET", "FIXED_ASSET"
  parentAccountId?: string;
  isSystemDefined: boolean;
  isActive: boolean;
  normalBalance: 'DEBIT' | 'CREDIT';
  currentBalance: number;
  userId?: string; // null for system accounts
  createdAt: string;
  updatedAt: string;
}

interface Category {
  id: string;
  userId?: string;
  name: string;
  description?: string;
  type: 'INCOME' | 'EXPENSE';
  accountId: string; // Required mapping to Account
  isSystemDefined: boolean;
  isActive: boolean;
  color: string;
  icon: string;
  createdAt: string;
  updatedAt: string;
  account?: Account; // Populated in responses
}

interface AccountAPI {
  GET /api/v1/accounts: {
    query: { type?: string; parentId?: string; includeInactive?: boolean; };
    response: { accounts: Account[]; total: number; };
    errors: { 401: "Unauthorized", 500: "Server error" };
  };
  
  POST /api/v1/accounts: {
    body: Omit<Account, 'id' | 'currentBalance' | 'createdAt' | 'updatedAt'>;
    response: Account;
    errors: { 400: "Invalid account data", 409: "Account code exists" };
  };
  
  PUT /api/v1/accounts/{id}: {
    body: Partial<Account>;
    response: Account;
    errors: { 400: "Invalid data", 404: "Account not found" };
  };
  
  DELETE /api/v1/accounts/{id}: {
    response: void;
    errors: { 400: "Account has transactions", 404: "Account not found" };
  };
  
  GET /api/v1/accounts/{id}/balance: {
    query: { asOfDate?: string; };
    response: { balance: number; asOfDate: string; };
  };
  
  GET /api/v1/accounts/chart: {
    response: { chartOfAccounts: AccountHierarchy[]; };
  };
}

interface CategoryAPI {
  PUT /api/v1/categories/{id}/account-mapping: {
    body: { accountId: string; };
    response: Category;
    errors: { 400: "Invalid account mapping", 404: "Category not found" };
  };
  
  GET /api/v1/categories/unmapped: {
    response: { categories: Category[]; };
  };
}

interface AccountHierarchy {
  account: Account;
  children: AccountHierarchy[];
  totalBalance: number;
}
```

## Standard Chart of Accounts (Zambian Context)

### Assets (1000-1999)
- **1100-1199: Current Assets**
  - 1100: Cash and Cash Equivalents
  - 1110: Bank Accounts - ZMW
  - 1115: Bank Accounts - USD
  - 1120: Petty Cash
  - 1130: Accounts Receivable
  - 1140: Inventory
  - 1150: Prepaid Expenses

- **1200-1299: Fixed Assets**
  - 1200: Property, Plant & Equipment
  - 1210: Land and Buildings
  - 1220: Vehicles
  - 1230: Equipment and Machinery
  - 1240: Furniture and Fixtures
  - 1250: Accumulated Depreciation

### Liabilities (2000-2999)
- **2100-2199: Current Liabilities**
  - 2100: Accounts Payable
  - 2110: Accrued Expenses
  - 2120: Short-term Loans
  - 2130: VAT Payable
  - 2140: PAYE Payable
  - 2150: NAPSA Contributions Payable

- **2200-2299: Long-term Liabilities**
  - 2200: Long-term Loans
  - 2210: Mortgages Payable

### Equity (3000-3999)
- 3100: Owner's Equity
- 3200: Retained Earnings
- 3300: Current Year Earnings

### Income (4000-4999)
- 4100: Sales Revenue
- 4200: Service Revenue
- 4300: Interest Income
- 4400: Other Income

### Expenses (5000-5999)
- **5100-5199: Cost of Goods Sold**
- **5200-5299: Operating Expenses**
  - 5200: Salaries and Wages
  - 5210: Employee Benefits
  - 5220: Rent Expense
  - 5230: Utilities (ZESCO, Water)
  - 5240: Telecommunications (Airtel, MTN)
  - 5250: Fuel and Transportation
  - 5260: Office Supplies
  - 5270: Professional Services
  - 5280: Insurance
  - 5290: Depreciation Expense

## Error Handling

- **Invalid Account Hierarchy:** Clear validation messages for circular references
- **Account Code Conflicts:** Prevent duplicate account codes with suggestions
- **Category Mapping Validation:** Ensure Categories map to appropriate Account types
- **Balance Calculation Errors:** Graceful handling of complex account hierarchies
- **Data Migration Issues:** Rollback capabilities for Chart of Accounts changes
- **Concurrent Modifications:** Optimistic locking for account structure changes

## Definition of Done

- [ ] Account data model supports full Chart of Accounts hierarchy
- [ ] Standard Zambian Chart of Accounts is seeded and functional
- [ ] Category management UI allows creation and Account mapping
- [ ] All existing Categories are successfully mapped to appropriate Accounts
- [ ] Account balance calculations work correctly for all account types
- [ ] API endpoints support full CRUD operations with proper validation
- [ ] Chart of Accounts displays hierarchical structure clearly
- [ ] Account search and filtering performs efficiently
- [ ] Audit trail captures all Chart of Accounts modifications
- [ ] Data migration completes without data loss
- [ ] Performance meets requirements (< 1 second for Chart of Accounts load)
- [ ] Tests cover account hierarchy, balance calculations, and category mapping
- [ ] No breaking changes to existing transaction categorization system
- [ ] Documentation includes Chart of Accounts setup guide

## Dependencies

- Database migration framework
- Existing Category and Transaction models
- [Story 3.1: Transaction Categorization](../epic-03/story-3.1-transaction-categorization.md) - Must integrate with Account mapping

## Notes

This story establishes the foundational accounting structure required for proper financial management. The Chart of Accounts must be comprehensive yet flexible enough to accommodate various Zambian business types while maintaining international accounting standards.

The Category-to-Account mapping is critical for ensuring that user-friendly transaction categorization translates into proper accounting entries for financial reporting and compliance.

---

**Related Stories:**
- [Story 2.2: Automated Double-Entry Journal System](story-2.2-double-entry-journal.md)

**Epic:** [Core Financial Engine & Accounting Integrity](../../epics-and-stories.md#epic-2-core-financial-engine--accounting-integrity)
