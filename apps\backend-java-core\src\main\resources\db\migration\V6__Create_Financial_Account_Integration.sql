-- V6__Create_Financial_Account_Integration.sql
-- Create tables for financial account integration (MTN Mobile Money, etc.)

-- Financial accounts table for external integrations
CREATE TABLE financial_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    account_type VARCHAR(50) NOT NULL CHECK (account_type IN ('MTN_MOBILE_MONEY', 'BANK_ACCOUNT', 'OTHER')),
    account_name VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20), -- For MTN Mobile Money
    account_number_last4 VARCHAR(4),
    provider VARCHAR(100) NOT NULL, -- 'MTN', 'ZANACO', etc.
    balance DECIMAL(15,2) DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'ZMW',
    connection_status VARCHAR(20) DEFAULT 'DISCONNECTED' CHECK (connection_status IN ('CONNECTING', 'CONNECTED', 'DISCONNECTED', 'ERROR')),
    last_sync_at TIMESTAMPTZ,
    error_message TEXT,
    account_info JSONB, -- Additional account information
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Account connections table for OAuth tokens and connection details
CREATE TABLE account_connections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    financial_account_id UUID NOT NULL REFERENCES financial_accounts(id) ON DELETE CASCADE,
    connection_type VARCHAR(20) NOT NULL CHECK (connection_type IN ('OAUTH', 'API_KEY', 'MANUAL')),
    encrypted_credentials TEXT, -- Encrypted OAuth tokens, API keys, etc.
    authorization_url TEXT,
    oauth_state VARCHAR(255),
    access_token_expires_at TIMESTAMPTZ,
    refresh_token_expires_at TIMESTAMPTZ,
    scopes TEXT[], -- OAuth scopes granted
    connection_metadata JSONB, -- Additional connection-specific data
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Document uploads table for fallback integration
CREATE TABLE document_uploads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    upload_id VARCHAR(255) NOT NULL UNIQUE, -- External upload ID for tracking
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    financial_account_id UUID REFERENCES financial_accounts(id) ON DELETE SET NULL,
    file_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(10) NOT NULL CHECK (file_type IN ('PDF', 'CSV', 'EXCEL')),
    file_size_bytes BIGINT NOT NULL,
    file_path TEXT, -- Encrypted file storage path
    upload_url TEXT, -- Pre-signed upload URL
    upload_url_expires_at TIMESTAMPTZ,
    processing_status VARCHAR(20) NOT NULL DEFAULT 'UPLOADING' CHECK (processing_status IN ('UPLOADING', 'PROCESSING', 'COMPLETED', 'FAILED')),
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    extracted_transactions_count INTEGER DEFAULT 0,
    confidence_score DECIMAL(3,2), -- AI parsing confidence (0.00-1.00)
    error_message TEXT,
    processing_started_at TIMESTAMPTZ,
    processing_completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Connection audit trail for security monitoring
CREATE TABLE connection_audit_trail (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    financial_account_id UUID NOT NULL REFERENCES financial_accounts(id) ON DELETE CASCADE,
    action VARCHAR(50) NOT NULL, -- 'CONNECT', 'DISCONNECT', 'SYNC', 'ERROR'
    status VARCHAR(20) NOT NULL, -- 'SUCCESS', 'FAILURE'
    ip_address INET,
    user_agent TEXT,
    error_details TEXT,
    metadata JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Document processing audit trail for compliance
CREATE TABLE document_processing_audit (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_upload_id UUID NOT NULL REFERENCES document_uploads(id) ON DELETE CASCADE,
    processing_stage VARCHAR(50) NOT NULL, -- 'UPLOAD', 'PARSE', 'EXTRACT', 'VALIDATE'
    status VARCHAR(20) NOT NULL, -- 'STARTED', 'COMPLETED', 'FAILED'
    processing_time_ms BIGINT,
    confidence_score DECIMAL(3,2),
    extracted_data JSONB, -- Extracted transaction data
    error_details TEXT,
    ai_model_version VARCHAR(50),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_financial_accounts_user_id ON financial_accounts(user_id);
CREATE INDEX idx_financial_accounts_type_status ON financial_accounts(account_type, connection_status);
CREATE INDEX idx_account_connections_financial_account_id ON account_connections(financial_account_id);
CREATE INDEX idx_document_uploads_user_id ON document_uploads(user_id);
CREATE INDEX idx_document_uploads_upload_id ON document_uploads(upload_id);
CREATE INDEX idx_document_uploads_status ON document_uploads(processing_status);
CREATE INDEX idx_connection_audit_trail_account_id ON connection_audit_trail(financial_account_id);
CREATE INDEX idx_connection_audit_trail_created_at ON connection_audit_trail(created_at);
CREATE INDEX idx_document_processing_audit_upload_id ON document_processing_audit(document_upload_id);
CREATE INDEX idx_document_processing_audit_created_at ON document_processing_audit(created_at);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to automatically update updated_at
CREATE TRIGGER trigger_financial_accounts_updated_at
    BEFORE UPDATE ON financial_accounts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_account_connections_updated_at
    BEFORE UPDATE ON account_connections
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_document_uploads_updated_at
    BEFORE UPDATE ON document_uploads
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
