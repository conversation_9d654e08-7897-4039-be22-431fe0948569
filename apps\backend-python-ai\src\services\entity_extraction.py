"""
Entity extraction service for extracting structured information from user commands
"""

import re
import json
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import dateutil.parser

from ..models.schemas import Entity, EntityType
from ..models.prompts import ENTITY_GUIDELINES, CONFIDENCE_THRESHOLDS
from ..config.ai_config import ENTITY_EXTRACTION_CONFIG


class EntityExtractionService:
    """Service for extracting entities from user commands"""
    
    def __init__(self):
        self.config = ENTITY_EXTRACTION_CONFIG
        self.confidence_threshold = CONFIDENCE_THRESHOLDS["entity_extraction"]
    
    def extract_entities(self, text: str, context: Optional[Dict[str, Any]] = None) -> Dict[EntityType, List[Entity]]:
        """Extract all entities from text"""
        entities = {}
        
        # Extract different types of entities
        entities.update(self._extract_amounts(text))
        entities.update(self._extract_dates(text))
        entities.update(self._extract_client_names(text))
        entities.update(self._extract_categories(text))
        entities.update(self._extract_account_types(text))
        entities.update(self._extract_transaction_types(text))
        
        # Filter by confidence threshold
        filtered_entities = {}
        for entity_type, entity_list in entities.items():
            high_confidence_entities = [
                entity for entity in entity_list 
                if entity.confidence >= self.confidence_threshold["low"]
            ]
            if high_confidence_entities:
                filtered_entities[entity_type] = high_confidence_entities
        
        return filtered_entities
    
    def _extract_amounts(self, text: str) -> Dict[EntityType, List[Entity]]:
        """Extract monetary amounts from text"""
        entities = []
        
        # Zambian Kwacha patterns
        patterns = [
            (r'K\s*(\d+(?:\.\d{2})?)', 'K{}'),  # K500, K 500.00
            (r'(\d+(?:\.\d{2})?)\s*kwacha', '{} kwacha'),  # 500 kwacha
            (r'ZMW\s*(\d+(?:\.\d{2})?)', 'ZMW {}'),  # ZMW 500.00
            (r'(\d+(?:\.\d{2})?)\s*ZMW', '{} ZMW'),  # 500.00 ZMW
        ]
        
        for pattern, format_str in patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                amount_str = match.group(1)
                try:
                    amount_value = float(amount_str)
                    entity = Entity(
                        value=amount_value,
                        confidence=0.9,  # High confidence for clear currency patterns
                        start_index=match.start(),
                        end_index=match.end(),
                        entity_type=EntityType.AMOUNT
                    )
                    entities.append(entity)
                except ValueError:
                    continue
        
        return {EntityType.AMOUNT: entities} if entities else {}
    
    def _extract_dates(self, text: str) -> Dict[EntityType, List[Entity]]:
        """Extract date ranges and specific dates from text"""
        entities = []
        
        # Relative date patterns
        relative_patterns = [
            (r'\btoday\b', self._get_relative_date(0)),
            (r'\byesterday\b', self._get_relative_date(-1)),
            (r'\btomorrow\b', self._get_relative_date(1)),
            (r'\bthis\s+week\b', self._get_week_range(0)),
            (r'\blast\s+week\b', self._get_week_range(-1)),
            (r'\bnext\s+week\b', self._get_week_range(1)),
            (r'\bthis\s+month\b', self._get_month_range(0)),
            (r'\blast\s+month\b', self._get_month_range(-1)),
            (r'\bnext\s+month\b', self._get_month_range(1)),
        ]
        
        for pattern, date_value in relative_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                entity = Entity(
                    value=date_value,
                    confidence=0.85,
                    start_index=match.start(),
                    end_index=match.end(),
                    entity_type=EntityType.DATE_RANGE
                )
                entities.append(entity)
        
        # Specific date patterns
        date_patterns = [
            r'\b\d{1,2}(?:st|nd|rd|th)?\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\w*\s*\d{4}?\b',
            r'\b\d{4}-\d{2}-\d{2}\b',
            r'\b\d{1,2}/\d{1,2}/\d{4}\b',
        ]
        
        for pattern in date_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                try:
                    parsed_date = dateutil.parser.parse(match.group(), fuzzy=True)
                    entity = Entity(
                        value=parsed_date.isoformat(),
                        confidence=0.8,
                        start_index=match.start(),
                        end_index=match.end(),
                        entity_type=EntityType.DATE_RANGE
                    )
                    entities.append(entity)
                except:
                    continue
        
        return {EntityType.DATE_RANGE: entities} if entities else {}
    
    def _extract_client_names(self, text: str) -> Dict[EntityType, List[Entity]]:
        """Extract client/business names from text"""
        entities = []
        
        # Common Zambian business patterns
        business_patterns = [
            r'\b(?:ZESCO|MTN|Airtel|Shoprite|Pick n Pay|Game|Checkers)\b',
            r'\b[A-Z][a-z]+\s+(?:Corp|Corporation|Ltd|Limited|Company|Co\.)\b',
            r'\b(?:Ministry|Department)\s+of\s+[A-Z][a-z]+\b',
            r'\bZRA\b',  # Zambia Revenue Authority
        ]
        
        for pattern in business_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                entity = Entity(
                    value=match.group().strip(),
                    confidence=0.8,
                    start_index=match.start(),
                    end_index=match.end(),
                    entity_type=EntityType.CLIENT_NAME
                )
                entities.append(entity)
        
        # Person name patterns (less confident)
        name_patterns = [
            r'\b[A-Z][a-z]+\s+[A-Z][a-z]+\b',  # First Last
        ]
        
        for pattern in name_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                # Skip if it's a common word combination
                name = match.group().strip()
                if not self._is_likely_person_name(name):
                    continue
                    
                entity = Entity(
                    value=name,
                    confidence=0.6,  # Lower confidence for person names
                    start_index=match.start(),
                    end_index=match.end(),
                    entity_type=EntityType.CLIENT_NAME
                )
                entities.append(entity)
        
        return {EntityType.CLIENT_NAME: entities} if entities else {}
    
    def _extract_categories(self, text: str) -> Dict[EntityType, List[Entity]]:
        """Extract expense/income categories from text"""
        entities = []
        
        # Common business categories
        categories = [
            'utilities', 'transport', 'office supplies', 'marketing',
            'professional services', 'inventory', 'telecommunications',
            'banking', 'insurance', 'rent', 'meals', 'entertainment',
            'travel', 'maintenance', 'repairs', 'licenses', 'permits',
            'fuel', 'electricity', 'water', 'internet', 'phone',
            'stationery', 'equipment', 'software', 'advertising',
            'accounting', 'legal', 'consulting'
        ]
        
        for category in categories:
            pattern = r'\b' + re.escape(category) + r'\b'
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                entity = Entity(
                    value=category,
                    confidence=0.85,
                    start_index=match.start(),
                    end_index=match.end(),
                    entity_type=EntityType.CATEGORY
                )
                entities.append(entity)
        
        return {EntityType.CATEGORY: entities} if entities else {}
    
    def _extract_account_types(self, text: str) -> Dict[EntityType, List[Entity]]:
        """Extract account types from text"""
        entities = []
        
        account_patterns = [
            (r'\bMTN\s+(?:Mobile\s+)?Money\b', 'MTN Mobile Money'),
            (r'\bAirtel\s+Money\b', 'Airtel Money'),
            (r'\b(?:bank|banking)\s+account\b', 'Bank Account'),
            (r'\b(?:Zanaco|FNB|Standard\s+Bank|Stanbic)\b', 'Bank Account'),
            (r'\bcash\b', 'Cash'),
            (r'\bpetty\s+cash\b', 'Petty Cash'),
        ]
        
        for pattern, account_type in account_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                entity = Entity(
                    value=account_type,
                    confidence=0.8,
                    start_index=match.start(),
                    end_index=match.end(),
                    entity_type=EntityType.ACCOUNT_TYPE
                )
                entities.append(entity)
        
        return {EntityType.ACCOUNT_TYPE: entities} if entities else {}
    
    def _extract_transaction_types(self, text: str) -> Dict[EntityType, List[Entity]]:
        """Extract transaction types from text"""
        entities = []
        
        transaction_patterns = [
            (r'\b(?:payment|pay|paid)\b', 'Payment'),
            (r'\b(?:expense|cost|spend|spent)\b', 'Expense'),
            (r'\b(?:income|revenue|sale|sold)\b', 'Income'),
            (r'\b(?:transfer|moved|sent)\b', 'Transfer'),
            (r'\b(?:deposit|deposited)\b', 'Deposit'),
            (r'\b(?:withdrawal|withdraw|withdrew)\b', 'Withdrawal'),
        ]
        
        for pattern, transaction_type in transaction_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                entity = Entity(
                    value=transaction_type,
                    confidence=0.7,
                    start_index=match.start(),
                    end_index=match.end(),
                    entity_type=EntityType.TRANSACTION_TYPE
                )
                entities.append(entity)
        
        return {EntityType.TRANSACTION_TYPE: entities} if entities else {}
    
    def _get_relative_date(self, days_offset: int) -> str:
        """Get relative date as ISO string"""
        target_date = datetime.now() + timedelta(days=days_offset)
        return target_date.isoformat()
    
    def _get_week_range(self, weeks_offset: int) -> Dict[str, str]:
        """Get week range"""
        today = datetime.now()
        start_of_week = today - timedelta(days=today.weekday()) + timedelta(weeks=weeks_offset)
        end_of_week = start_of_week + timedelta(days=6)
        
        return {
            "start": start_of_week.isoformat(),
            "end": end_of_week.isoformat(),
            "type": "week"
        }
    
    def _get_month_range(self, months_offset: int) -> Dict[str, str]:
        """Get month range"""
        today = datetime.now()
        if months_offset == 0:
            start_of_month = today.replace(day=1)
        else:
            # Simplified month calculation
            target_month = today.month + months_offset
            target_year = today.year
            
            while target_month <= 0:
                target_month += 12
                target_year -= 1
            while target_month > 12:
                target_month -= 12
                target_year += 1
                
            start_of_month = today.replace(year=target_year, month=target_month, day=1)
        
        # Get last day of month
        if start_of_month.month == 12:
            end_of_month = start_of_month.replace(year=start_of_month.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            end_of_month = start_of_month.replace(month=start_of_month.month + 1, day=1) - timedelta(days=1)
        
        return {
            "start": start_of_month.isoformat(),
            "end": end_of_month.isoformat(),
            "type": "month"
        }
    
    def _is_likely_person_name(self, name: str) -> bool:
        """Check if a string is likely a person name"""
        # Simple heuristic - avoid common word combinations
        common_non_names = [
            'This Month', 'Last Week', 'Next Year', 'New Invoice',
            'Show Me', 'Help Me', 'Thank You', 'Good Morning'
        ]
        
        return name not in common_non_names
